"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextStyle: () => (/* binding */ TextStyle),\n/* harmony export */   \"default\": () => (/* binding */ TextStyle)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\nconst mergeNestedSpanStyles = (element) => {\n    if (!element.children.length) {\n        return;\n    }\n    const childSpans = element.querySelectorAll('span');\n    if (!childSpans) {\n        return;\n    }\n    childSpans.forEach(childSpan => {\n        var _a, _b;\n        const childStyle = childSpan.getAttribute('style');\n        const closestParentSpanStyleOfChild = (_b = (_a = childSpan.parentElement) === null || _a === void 0 ? void 0 : _a.closest('span')) === null || _b === void 0 ? void 0 : _b.getAttribute('style');\n        childSpan.setAttribute('style', `${closestParentSpanStyleOfChild};${childStyle}`);\n    });\n};\n/**\n * This extension allows you to create text styles. It is required by default\n * for the `textColor` and `backgroundColor` extensions.\n * @see https://www.tiptap.dev/api/marks/text-style\n */\nconst TextStyle = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Mark.create({\n    name: 'textStyle',\n    priority: 101,\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n            mergeNestedSpanStyles: false,\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'span',\n                getAttrs: element => {\n                    const hasStyles = element.hasAttribute('style');\n                    if (!hasStyles) {\n                        return false;\n                    }\n                    if (this.options.mergeNestedSpanStyles) {\n                        mergeNestedSpanStyles(element);\n                    }\n                    return {};\n                },\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['span', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            removeEmptyTextStyle: () => ({ tr }) => {\n                const { selection } = tr;\n                // Gather all of the nodes within the selection range.\n                // We would need to go through each node individually\n                // to check if it has any inline style attributes.\n                // Otherwise, calling commands.unsetMark(this.name)\n                // removes everything from all the nodes\n                // within the selection range.\n                tr.doc.nodesBetween(selection.from, selection.to, (node, pos) => {\n                    // Check if it's a paragraph element, if so, skip this node as we apply\n                    // the text style to inline text nodes only (span).\n                    if (node.isTextblock) {\n                        return true;\n                    }\n                    // Check if the node has no inline style attributes.\n                    // Filter out non-`textStyle` marks.\n                    if (!node.marks.filter(mark => mark.type === this.type).some(mark => Object.values(mark.attrs).some(value => !!value))) {\n                        // Proceed with the removal of the `textStyle` mark for this node only\n                        tr.removeMark(pos, pos + node.nodeSize, this.type);\n                    }\n                });\n                return true;\n            },\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js\n");

/***/ })

};
;