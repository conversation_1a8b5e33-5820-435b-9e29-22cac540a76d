{"version": 3, "sources": ["../../../src/build/static-paths/utils.ts"], "sourcesContent": ["/**\n * Encodes a parameter value using the provided encoder.\n *\n * @param value - The value to encode.\n * @param encoder - The encoder to use.\n * @returns The encoded value.\n */\nexport function encodeParam(\n  value: string | string[],\n  encoder: (value: string) => string\n) {\n  let replaceValue: string\n  if (Array.isArray(value)) {\n    replaceValue = value.map(encoder).join('/')\n  } else {\n    replaceValue = encoder(value)\n  }\n\n  return replaceValue\n}\n\n/**\n * Normalizes a pathname to a consistent format.\n *\n * @param pathname - The pathname to normalize.\n * @returns The normalized pathname.\n */\nexport function normalizePathname(pathname: string) {\n  return pathname.replace(/\\\\/g, '/').replace(/(?!^)\\/$/, '')\n}\n"], "names": ["encodeParam", "normalizePathname", "value", "encoder", "replaceValue", "Array", "isArray", "map", "join", "pathname", "replace"], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;;;;;IACeA,WAAW;eAAXA;;IAoBAC,iBAAiB;eAAjBA;;;AApBT,SAASD,YACdE,KAAwB,EACxBC,OAAkC;IAElC,IAAIC;IACJ,IAAIC,MAAMC,OAAO,CAACJ,QAAQ;QACxBE,eAAeF,MAAMK,GAAG,CAACJ,SAASK,IAAI,CAAC;IACzC,OAAO;QACLJ,eAAeD,QAAQD;IACzB;IAEA,OAAOE;AACT;AAQO,SAASH,kBAAkBQ,QAAgB;IAChD,OAAOA,SAASC,OAAO,CAAC,OAAO,KAAKA,OAAO,CAAC,YAAY;AAC1D", "ignoreList": [0]}