"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-ordered-list/dist/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-ordered-list/dist/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderedList: () => (/* binding */ OrderedList),\n/* harmony export */   \"default\": () => (/* binding */ OrderedList),\n/* harmony export */   inputRegex: () => (/* binding */ inputRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\nconst ListItemName = 'listItem';\nconst TextStyleName = 'textStyle';\n/**\n * Matches an ordered list to a 1. on input (or any number followed by a dot).\n */\nconst inputRegex = /^(\\d+)\\.\\s$/;\n/**\n * This extension allows you to create ordered lists.\n * This requires the ListItem extension\n * @see https://www.tiptap.dev/api/nodes/ordered-list\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nconst OrderedList = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'orderedList',\n    addOptions() {\n        return {\n            itemTypeName: 'listItem',\n            HTMLAttributes: {},\n            keepMarks: false,\n            keepAttributes: false,\n        };\n    },\n    group: 'block list',\n    content() {\n        return `${this.options.itemTypeName}+`;\n    },\n    addAttributes() {\n        return {\n            start: {\n                default: 1,\n                parseHTML: element => {\n                    return element.hasAttribute('start')\n                        ? parseInt(element.getAttribute('start') || '', 10)\n                        : 1;\n                },\n            },\n            type: {\n                default: null,\n                parseHTML: element => element.getAttribute('type'),\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'ol',\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        const { start, ...attributesWithoutStart } = HTMLAttributes;\n        return start === 1\n            ? ['ol', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, attributesWithoutStart), 0]\n            : ['ol', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            toggleOrderedList: () => ({ commands, chain }) => {\n                if (this.options.keepAttributes) {\n                    return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run();\n                }\n                return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-Shift-7': () => this.editor.commands.toggleOrderedList(),\n        };\n    },\n    addInputRules() {\n        let inputRule = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.wrappingInputRule)({\n            find: inputRegex,\n            type: this.type,\n            getAttributes: match => ({ start: +match[1] }),\n            joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n        });\n        if (this.options.keepMarks || this.options.keepAttributes) {\n            inputRule = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.wrappingInputRule)({\n                find: inputRegex,\n                type: this.type,\n                keepMarks: this.options.keepMarks,\n                keepAttributes: this.options.keepAttributes,\n                getAttributes: match => ({ start: +match[1], ...this.editor.getAttributes(TextStyleName) }),\n                joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n                editor: this.editor,\n            });\n        }\n        return [\n            inputRule,\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-ordered-list/dist/index.js\n");

/***/ })

};
;