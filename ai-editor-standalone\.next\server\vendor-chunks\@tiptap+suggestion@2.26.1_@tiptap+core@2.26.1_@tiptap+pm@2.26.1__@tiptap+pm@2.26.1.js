"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+suggestion@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+suggestion@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+suggestion@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/suggestion/dist/index.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+suggestion@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/suggestion/dist/index.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Suggestion: () => (/* binding */ Suggestion),\n/* harmony export */   SuggestionPluginKey: () => (/* binding */ SuggestionPluginKey),\n/* harmony export */   \"default\": () => (/* binding */ Suggestion),\n/* harmony export */   findSuggestionMatch: () => (/* binding */ findSuggestionMatch)\n/* harmony export */ });\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/view */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/view/dist/index.js\");\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n\n\nfunction findSuggestionMatch(config) {\n    var _a;\n    const { char, allowSpaces: allowSpacesOption, allowToIncludeChar, allowedPrefixes, startOfLine, $position, } = config;\n    const allowSpaces = allowSpacesOption && !allowToIncludeChar;\n    const escapedChar = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.escapeForRegEx)(char);\n    const suffix = new RegExp(`\\\\s${escapedChar}$`);\n    const prefix = startOfLine ? '^' : '';\n    const finalEscapedChar = allowToIncludeChar ? '' : escapedChar;\n    const regexp = allowSpaces\n        ? new RegExp(`${prefix}${escapedChar}.*?(?=\\\\s${finalEscapedChar}|$)`, 'gm')\n        : new RegExp(`${prefix}(?:^)?${escapedChar}[^\\\\s${finalEscapedChar}]*`, 'gm');\n    const text = ((_a = $position.nodeBefore) === null || _a === void 0 ? void 0 : _a.isText) && $position.nodeBefore.text;\n    if (!text) {\n        return null;\n    }\n    const textFrom = $position.pos - text.length;\n    const match = Array.from(text.matchAll(regexp)).pop();\n    if (!match || match.input === undefined || match.index === undefined) {\n        return null;\n    }\n    // JavaScript doesn't have lookbehinds. This hacks a check that first character\n    // is a space or the start of the line\n    const matchPrefix = match.input.slice(Math.max(0, match.index - 1), match.index);\n    const matchPrefixIsAllowed = new RegExp(`^[${allowedPrefixes === null || allowedPrefixes === void 0 ? void 0 : allowedPrefixes.join('')}\\0]?$`).test(matchPrefix);\n    if (allowedPrefixes !== null && !matchPrefixIsAllowed) {\n        return null;\n    }\n    // The absolute position of the match in the document\n    const from = textFrom + match.index;\n    let to = from + match[0].length;\n    // Edge case handling; if spaces are allowed and we're directly in between\n    // two triggers\n    if (allowSpaces && suffix.test(text.slice(to - 1, to + 1))) {\n        match[0] += ' ';\n        to += 1;\n    }\n    // If the $position is located within the matched substring, return that range\n    if (from < $position.pos && to >= $position.pos) {\n        return {\n            range: {\n                from,\n                to,\n            },\n            query: match[0].slice(char.length),\n            text: match[0],\n        };\n    }\n    return null;\n}\n\nconst SuggestionPluginKey = new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('suggestion');\n/**\n * This utility allows you to create suggestions.\n * @see https://tiptap.dev/api/utilities/suggestion\n */\nfunction Suggestion({ pluginKey = SuggestionPluginKey, editor, char = '@', allowSpaces = false, allowToIncludeChar = false, allowedPrefixes = [' '], startOfLine = false, decorationTag = 'span', decorationClass = 'suggestion', decorationContent = '', decorationEmptyClass = 'is-empty', command = () => null, items = () => [], render = () => ({}), allow = () => true, findSuggestionMatch: findSuggestionMatch$1 = findSuggestionMatch, }) {\n    let props;\n    const renderer = render === null || render === void 0 ? void 0 : render();\n    const plugin = new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        key: pluginKey,\n        view() {\n            return {\n                update: async (view, prevState) => {\n                    var _a, _b, _c, _d, _e, _f, _g;\n                    const prev = (_a = this.key) === null || _a === void 0 ? void 0 : _a.getState(prevState);\n                    const next = (_b = this.key) === null || _b === void 0 ? void 0 : _b.getState(view.state);\n                    // See how the state changed\n                    const moved = prev.active && next.active && prev.range.from !== next.range.from;\n                    const started = !prev.active && next.active;\n                    const stopped = prev.active && !next.active;\n                    const changed = !started && !stopped && prev.query !== next.query;\n                    const handleStart = started || (moved && changed);\n                    const handleChange = changed || moved;\n                    const handleExit = stopped || (moved && changed);\n                    // Cancel when suggestion isn't active\n                    if (!handleStart && !handleChange && !handleExit) {\n                        return;\n                    }\n                    const state = handleExit && !handleStart ? prev : next;\n                    const decorationNode = view.dom.querySelector(`[data-decoration-id=\"${state.decorationId}\"]`);\n                    props = {\n                        editor,\n                        range: state.range,\n                        query: state.query,\n                        text: state.text,\n                        items: [],\n                        command: commandProps => {\n                            return command({\n                                editor,\n                                range: state.range,\n                                props: commandProps,\n                            });\n                        },\n                        decorationNode,\n                        // virtual node for popper.js or tippy.js\n                        // this can be used for building popups without a DOM node\n                        clientRect: decorationNode\n                            ? () => {\n                                var _a;\n                                // because of `items` can be asynchrounous we’ll search for the current decoration node\n                                const { decorationId } = (_a = this.key) === null || _a === void 0 ? void 0 : _a.getState(editor.state); // eslint-disable-line\n                                const currentDecorationNode = view.dom.querySelector(`[data-decoration-id=\"${decorationId}\"]`);\n                                return (currentDecorationNode === null || currentDecorationNode === void 0 ? void 0 : currentDecorationNode.getBoundingClientRect()) || null;\n                            }\n                            : null,\n                    };\n                    if (handleStart) {\n                        (_c = renderer === null || renderer === void 0 ? void 0 : renderer.onBeforeStart) === null || _c === void 0 ? void 0 : _c.call(renderer, props);\n                    }\n                    if (handleChange) {\n                        (_d = renderer === null || renderer === void 0 ? void 0 : renderer.onBeforeUpdate) === null || _d === void 0 ? void 0 : _d.call(renderer, props);\n                    }\n                    if (handleChange || handleStart) {\n                        props.items = await items({\n                            editor,\n                            query: state.query,\n                        });\n                    }\n                    if (handleExit) {\n                        (_e = renderer === null || renderer === void 0 ? void 0 : renderer.onExit) === null || _e === void 0 ? void 0 : _e.call(renderer, props);\n                    }\n                    if (handleChange) {\n                        (_f = renderer === null || renderer === void 0 ? void 0 : renderer.onUpdate) === null || _f === void 0 ? void 0 : _f.call(renderer, props);\n                    }\n                    if (handleStart) {\n                        (_g = renderer === null || renderer === void 0 ? void 0 : renderer.onStart) === null || _g === void 0 ? void 0 : _g.call(renderer, props);\n                    }\n                },\n                destroy: () => {\n                    var _a;\n                    if (!props) {\n                        return;\n                    }\n                    (_a = renderer === null || renderer === void 0 ? void 0 : renderer.onExit) === null || _a === void 0 ? void 0 : _a.call(renderer, props);\n                },\n            };\n        },\n        state: {\n            // Initialize the plugin's internal state.\n            init() {\n                const state = {\n                    active: false,\n                    range: {\n                        from: 0,\n                        to: 0,\n                    },\n                    query: null,\n                    text: null,\n                    composing: false,\n                };\n                return state;\n            },\n            // Apply changes to the plugin state from a view transaction.\n            apply(transaction, prev, _oldState, state) {\n                const { isEditable } = editor;\n                const { composing } = editor.view;\n                const { selection } = transaction;\n                const { empty, from } = selection;\n                const next = { ...prev };\n                next.composing = composing;\n                // We can only be suggesting if the view is editable, and:\n                //   * there is no selection, or\n                //   * a composition is active (see: https://github.com/ueberdosis/tiptap/issues/1449)\n                if (isEditable && (empty || editor.view.composing)) {\n                    // Reset active state if we just left the previous suggestion range\n                    if ((from < prev.range.from || from > prev.range.to)\n                        && !composing\n                        && !prev.composing) {\n                        next.active = false;\n                    }\n                    // Try to match against where our cursor currently is\n                    const match = findSuggestionMatch$1({\n                        char,\n                        allowSpaces,\n                        allowToIncludeChar,\n                        allowedPrefixes,\n                        startOfLine,\n                        $position: selection.$from,\n                    });\n                    const decorationId = `id_${Math.floor(Math.random() * 0xffffffff)}`;\n                    // If we found a match, update the current state to show it\n                    if (match\n                        && allow({\n                            editor,\n                            state,\n                            range: match.range,\n                            isActive: prev.active,\n                        })) {\n                        next.active = true;\n                        next.decorationId = prev.decorationId\n                            ? prev.decorationId\n                            : decorationId;\n                        next.range = match.range;\n                        next.query = match.query;\n                        next.text = match.text;\n                    }\n                    else {\n                        next.active = false;\n                    }\n                }\n                else {\n                    next.active = false;\n                }\n                // Make sure to empty the range if suggestion is inactive\n                if (!next.active) {\n                    next.decorationId = null;\n                    next.range = { from: 0, to: 0 };\n                    next.query = null;\n                    next.text = null;\n                }\n                return next;\n            },\n        },\n        props: {\n            // Call the keydown hook if suggestion is active.\n            handleKeyDown(view, event) {\n                var _a;\n                const { active, range } = plugin.getState(view.state);\n                if (!active) {\n                    return false;\n                }\n                return ((_a = renderer === null || renderer === void 0 ? void 0 : renderer.onKeyDown) === null || _a === void 0 ? void 0 : _a.call(renderer, { view, event, range })) || false;\n            },\n            // Setup decorator on the currently active suggestion.\n            decorations(state) {\n                const { active, range, decorationId, query, } = plugin.getState(state);\n                if (!active) {\n                    return null;\n                }\n                const isEmpty = !(query === null || query === void 0 ? void 0 : query.length);\n                const classNames = [decorationClass];\n                if (isEmpty) {\n                    classNames.push(decorationEmptyClass);\n                }\n                return _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__.DecorationSet.create(state.doc, [\n                    _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__.Decoration.inline(range.from, range.to, {\n                        nodeName: decorationTag,\n                        class: classNames.join(' '),\n                        'data-decoration-id': decorationId,\n                        'data-decoration-content': decorationContent,\n                    }),\n                ]);\n            },\n        },\n    });\n    return plugin;\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtzdWdnZXN0aW9uQDIuMjYuMV9AdGlwdGFwK2NvcmVAMi4yNi4xX0B0aXB0YXArcG1AMi4yNi4xX19AdGlwdGFwK3BtQDIuMjYuMS9ub2RlX21vZHVsZXMvQHRpcHRhcC9zdWdnZXN0aW9uL2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFxRDtBQUNPO0FBQ2Q7O0FBRTlDO0FBQ0E7QUFDQSxZQUFZLHFHQUFxRztBQUNqSDtBQUNBLHdCQUF3Qiw0REFBYztBQUN0QyxvQ0FBb0MsWUFBWTtBQUNoRDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsT0FBTyxFQUFFLFlBQVksV0FBVyxpQkFBaUI7QUFDekUsd0JBQXdCLE9BQU8sUUFBUSxZQUFZLE9BQU8saUJBQWlCO0FBQzNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCwyRkFBMkY7QUFDNUk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGdDQUFnQyx1REFBUztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixpVUFBaVUsMEZBQTBGO0FBQ2piO0FBQ0E7QUFDQSx1QkFBdUIsb0RBQU07QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBGQUEwRixtQkFBbUI7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3Qix5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsZUFBZSxrRkFBa0Y7QUFDekksNkdBQTZHLGFBQWE7QUFDMUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSx3QkFBd0IsYUFBYTtBQUNyQyx3QkFBd0IsWUFBWTtBQUNwQyx3QkFBd0IsWUFBWTtBQUNwQyx3QkFBd0IsY0FBYztBQUN0QywrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIsK0NBQStDLHVDQUF1QztBQUN0RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdCQUFnQjtBQUN4QztBQUNBO0FBQ0E7QUFDQSwrSkFBK0osb0JBQW9CO0FBQ25MLGFBQWE7QUFDYjtBQUNBO0FBQ0Esd0JBQXdCLHNDQUFzQztBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLDBEQUFhO0FBQ3BDLG9CQUFvQix1REFBVTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7O0FBRXVGO0FBQ3ZGIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHRpcHRhcCtzdWdnZXN0aW9uQDIuMjYuMV9AdGlwdGFwK2NvcmVAMi4yNi4xX0B0aXB0YXArcG1AMi4yNi4xX19AdGlwdGFwK3BtQDIuMjYuMVxcbm9kZV9tb2R1bGVzXFxAdGlwdGFwXFxzdWdnZXN0aW9uXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQbHVnaW5LZXksIFBsdWdpbiB9IGZyb20gJ0B0aXB0YXAvcG0vc3RhdGUnO1xuaW1wb3J0IHsgRGVjb3JhdGlvblNldCwgRGVjb3JhdGlvbiB9IGZyb20gJ0B0aXB0YXAvcG0vdmlldyc7XG5pbXBvcnQgeyBlc2NhcGVGb3JSZWdFeCB9IGZyb20gJ0B0aXB0YXAvY29yZSc7XG5cbmZ1bmN0aW9uIGZpbmRTdWdnZXN0aW9uTWF0Y2goY29uZmlnKSB7XG4gICAgdmFyIF9hO1xuICAgIGNvbnN0IHsgY2hhciwgYWxsb3dTcGFjZXM6IGFsbG93U3BhY2VzT3B0aW9uLCBhbGxvd1RvSW5jbHVkZUNoYXIsIGFsbG93ZWRQcmVmaXhlcywgc3RhcnRPZkxpbmUsICRwb3NpdGlvbiwgfSA9IGNvbmZpZztcbiAgICBjb25zdCBhbGxvd1NwYWNlcyA9IGFsbG93U3BhY2VzT3B0aW9uICYmICFhbGxvd1RvSW5jbHVkZUNoYXI7XG4gICAgY29uc3QgZXNjYXBlZENoYXIgPSBlc2NhcGVGb3JSZWdFeChjaGFyKTtcbiAgICBjb25zdCBzdWZmaXggPSBuZXcgUmVnRXhwKGBcXFxccyR7ZXNjYXBlZENoYXJ9JGApO1xuICAgIGNvbnN0IHByZWZpeCA9IHN0YXJ0T2ZMaW5lID8gJ14nIDogJyc7XG4gICAgY29uc3QgZmluYWxFc2NhcGVkQ2hhciA9IGFsbG93VG9JbmNsdWRlQ2hhciA/ICcnIDogZXNjYXBlZENoYXI7XG4gICAgY29uc3QgcmVnZXhwID0gYWxsb3dTcGFjZXNcbiAgICAgICAgPyBuZXcgUmVnRXhwKGAke3ByZWZpeH0ke2VzY2FwZWRDaGFyfS4qPyg/PVxcXFxzJHtmaW5hbEVzY2FwZWRDaGFyfXwkKWAsICdnbScpXG4gICAgICAgIDogbmV3IFJlZ0V4cChgJHtwcmVmaXh9KD86Xik/JHtlc2NhcGVkQ2hhcn1bXlxcXFxzJHtmaW5hbEVzY2FwZWRDaGFyfV0qYCwgJ2dtJyk7XG4gICAgY29uc3QgdGV4dCA9ICgoX2EgPSAkcG9zaXRpb24ubm9kZUJlZm9yZSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmlzVGV4dCkgJiYgJHBvc2l0aW9uLm5vZGVCZWZvcmUudGV4dDtcbiAgICBpZiAoIXRleHQpIHtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGNvbnN0IHRleHRGcm9tID0gJHBvc2l0aW9uLnBvcyAtIHRleHQubGVuZ3RoO1xuICAgIGNvbnN0IG1hdGNoID0gQXJyYXkuZnJvbSh0ZXh0Lm1hdGNoQWxsKHJlZ2V4cCkpLnBvcCgpO1xuICAgIGlmICghbWF0Y2ggfHwgbWF0Y2guaW5wdXQgPT09IHVuZGVmaW5lZCB8fCBtYXRjaC5pbmRleCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICAvLyBKYXZhU2NyaXB0IGRvZXNuJ3QgaGF2ZSBsb29rYmVoaW5kcy4gVGhpcyBoYWNrcyBhIGNoZWNrIHRoYXQgZmlyc3QgY2hhcmFjdGVyXG4gICAgLy8gaXMgYSBzcGFjZSBvciB0aGUgc3RhcnQgb2YgdGhlIGxpbmVcbiAgICBjb25zdCBtYXRjaFByZWZpeCA9IG1hdGNoLmlucHV0LnNsaWNlKE1hdGgubWF4KDAsIG1hdGNoLmluZGV4IC0gMSksIG1hdGNoLmluZGV4KTtcbiAgICBjb25zdCBtYXRjaFByZWZpeElzQWxsb3dlZCA9IG5ldyBSZWdFeHAoYF5bJHthbGxvd2VkUHJlZml4ZXMgPT09IG51bGwgfHwgYWxsb3dlZFByZWZpeGVzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBhbGxvd2VkUHJlZml4ZXMuam9pbignJyl9XFwwXT8kYCkudGVzdChtYXRjaFByZWZpeCk7XG4gICAgaWYgKGFsbG93ZWRQcmVmaXhlcyAhPT0gbnVsbCAmJiAhbWF0Y2hQcmVmaXhJc0FsbG93ZWQpIHtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIC8vIFRoZSBhYnNvbHV0ZSBwb3NpdGlvbiBvZiB0aGUgbWF0Y2ggaW4gdGhlIGRvY3VtZW50XG4gICAgY29uc3QgZnJvbSA9IHRleHRGcm9tICsgbWF0Y2guaW5kZXg7XG4gICAgbGV0IHRvID0gZnJvbSArIG1hdGNoWzBdLmxlbmd0aDtcbiAgICAvLyBFZGdlIGNhc2UgaGFuZGxpbmc7IGlmIHNwYWNlcyBhcmUgYWxsb3dlZCBhbmQgd2UncmUgZGlyZWN0bHkgaW4gYmV0d2VlblxuICAgIC8vIHR3byB0cmlnZ2Vyc1xuICAgIGlmIChhbGxvd1NwYWNlcyAmJiBzdWZmaXgudGVzdCh0ZXh0LnNsaWNlKHRvIC0gMSwgdG8gKyAxKSkpIHtcbiAgICAgICAgbWF0Y2hbMF0gKz0gJyAnO1xuICAgICAgICB0byArPSAxO1xuICAgIH1cbiAgICAvLyBJZiB0aGUgJHBvc2l0aW9uIGlzIGxvY2F0ZWQgd2l0aGluIHRoZSBtYXRjaGVkIHN1YnN0cmluZywgcmV0dXJuIHRoYXQgcmFuZ2VcbiAgICBpZiAoZnJvbSA8ICRwb3NpdGlvbi5wb3MgJiYgdG8gPj0gJHBvc2l0aW9uLnBvcykge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgcmFuZ2U6IHtcbiAgICAgICAgICAgICAgICBmcm9tLFxuICAgICAgICAgICAgICAgIHRvLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHF1ZXJ5OiBtYXRjaFswXS5zbGljZShjaGFyLmxlbmd0aCksXG4gICAgICAgICAgICB0ZXh0OiBtYXRjaFswXSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgcmV0dXJuIG51bGw7XG59XG5cbmNvbnN0IFN1Z2dlc3Rpb25QbHVnaW5LZXkgPSBuZXcgUGx1Z2luS2V5KCdzdWdnZXN0aW9uJyk7XG4vKipcbiAqIFRoaXMgdXRpbGl0eSBhbGxvd3MgeW91IHRvIGNyZWF0ZSBzdWdnZXN0aW9ucy5cbiAqIEBzZWUgaHR0cHM6Ly90aXB0YXAuZGV2L2FwaS91dGlsaXRpZXMvc3VnZ2VzdGlvblxuICovXG5mdW5jdGlvbiBTdWdnZXN0aW9uKHsgcGx1Z2luS2V5ID0gU3VnZ2VzdGlvblBsdWdpbktleSwgZWRpdG9yLCBjaGFyID0gJ0AnLCBhbGxvd1NwYWNlcyA9IGZhbHNlLCBhbGxvd1RvSW5jbHVkZUNoYXIgPSBmYWxzZSwgYWxsb3dlZFByZWZpeGVzID0gWycgJ10sIHN0YXJ0T2ZMaW5lID0gZmFsc2UsIGRlY29yYXRpb25UYWcgPSAnc3BhbicsIGRlY29yYXRpb25DbGFzcyA9ICdzdWdnZXN0aW9uJywgZGVjb3JhdGlvbkNvbnRlbnQgPSAnJywgZGVjb3JhdGlvbkVtcHR5Q2xhc3MgPSAnaXMtZW1wdHknLCBjb21tYW5kID0gKCkgPT4gbnVsbCwgaXRlbXMgPSAoKSA9PiBbXSwgcmVuZGVyID0gKCkgPT4gKHt9KSwgYWxsb3cgPSAoKSA9PiB0cnVlLCBmaW5kU3VnZ2VzdGlvbk1hdGNoOiBmaW5kU3VnZ2VzdGlvbk1hdGNoJDEgPSBmaW5kU3VnZ2VzdGlvbk1hdGNoLCB9KSB7XG4gICAgbGV0IHByb3BzO1xuICAgIGNvbnN0IHJlbmRlcmVyID0gcmVuZGVyID09PSBudWxsIHx8IHJlbmRlciA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVuZGVyKCk7XG4gICAgY29uc3QgcGx1Z2luID0gbmV3IFBsdWdpbih7XG4gICAgICAgIGtleTogcGx1Z2luS2V5LFxuICAgICAgICB2aWV3KCkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICB1cGRhdGU6IGFzeW5jICh2aWV3LCBwcmV2U3RhdGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIF9hLCBfYiwgX2MsIF9kLCBfZSwgX2YsIF9nO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBwcmV2ID0gKF9hID0gdGhpcy5rZXkpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5nZXRTdGF0ZShwcmV2U3RhdGUpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXh0ID0gKF9iID0gdGhpcy5rZXkpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5nZXRTdGF0ZSh2aWV3LnN0YXRlKTtcbiAgICAgICAgICAgICAgICAgICAgLy8gU2VlIGhvdyB0aGUgc3RhdGUgY2hhbmdlZFxuICAgICAgICAgICAgICAgICAgICBjb25zdCBtb3ZlZCA9IHByZXYuYWN0aXZlICYmIG5leHQuYWN0aXZlICYmIHByZXYucmFuZ2UuZnJvbSAhPT0gbmV4dC5yYW5nZS5mcm9tO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBzdGFydGVkID0gIXByZXYuYWN0aXZlICYmIG5leHQuYWN0aXZlO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBzdG9wcGVkID0gcHJldi5hY3RpdmUgJiYgIW5leHQuYWN0aXZlO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBjaGFuZ2VkID0gIXN0YXJ0ZWQgJiYgIXN0b3BwZWQgJiYgcHJldi5xdWVyeSAhPT0gbmV4dC5xdWVyeTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaGFuZGxlU3RhcnQgPSBzdGFydGVkIHx8IChtb3ZlZCAmJiBjaGFuZ2VkKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaGFuZGxlQ2hhbmdlID0gY2hhbmdlZCB8fCBtb3ZlZDtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaGFuZGxlRXhpdCA9IHN0b3BwZWQgfHwgKG1vdmVkICYmIGNoYW5nZWQpO1xuICAgICAgICAgICAgICAgICAgICAvLyBDYW5jZWwgd2hlbiBzdWdnZXN0aW9uIGlzbid0IGFjdGl2ZVxuICAgICAgICAgICAgICAgICAgICBpZiAoIWhhbmRsZVN0YXJ0ICYmICFoYW5kbGVDaGFuZ2UgJiYgIWhhbmRsZUV4aXQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBjb25zdCBzdGF0ZSA9IGhhbmRsZUV4aXQgJiYgIWhhbmRsZVN0YXJ0ID8gcHJldiA6IG5leHQ7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGRlY29yYXRpb25Ob2RlID0gdmlldy5kb20ucXVlcnlTZWxlY3RvcihgW2RhdGEtZGVjb3JhdGlvbi1pZD1cIiR7c3RhdGUuZGVjb3JhdGlvbklkfVwiXWApO1xuICAgICAgICAgICAgICAgICAgICBwcm9wcyA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGVkaXRvcixcbiAgICAgICAgICAgICAgICAgICAgICAgIHJhbmdlOiBzdGF0ZS5yYW5nZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHF1ZXJ5OiBzdGF0ZS5xdWVyeSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHRleHQ6IHN0YXRlLnRleHQsXG4gICAgICAgICAgICAgICAgICAgICAgICBpdGVtczogW10sXG4gICAgICAgICAgICAgICAgICAgICAgICBjb21tYW5kOiBjb21tYW5kUHJvcHMgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBjb21tYW5kKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZWRpdG9yLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByYW5nZTogc3RhdGUucmFuZ2UsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb3BzOiBjb21tYW5kUHJvcHMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgZGVjb3JhdGlvbk5vZGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyB2aXJ0dWFsIG5vZGUgZm9yIHBvcHBlci5qcyBvciB0aXBweS5qc1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gdGhpcyBjYW4gYmUgdXNlZCBmb3IgYnVpbGRpbmcgcG9wdXBzIHdpdGhvdXQgYSBET00gbm9kZVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xpZW50UmVjdDogZGVjb3JhdGlvbk5vZGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIF9hO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBiZWNhdXNlIG9mIGBpdGVtc2AgY2FuIGJlIGFzeW5jaHJvdW5vdXMgd2XigJlsbCBzZWFyY2ggZm9yIHRoZSBjdXJyZW50IGRlY29yYXRpb24gbm9kZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB7IGRlY29yYXRpb25JZCB9ID0gKF9hID0gdGhpcy5rZXkpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5nZXRTdGF0ZShlZGl0b3Iuc3RhdGUpOyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnREZWNvcmF0aW9uTm9kZSA9IHZpZXcuZG9tLnF1ZXJ5U2VsZWN0b3IoYFtkYXRhLWRlY29yYXRpb24taWQ9XCIke2RlY29yYXRpb25JZH1cIl1gKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChjdXJyZW50RGVjb3JhdGlvbk5vZGUgPT09IG51bGwgfHwgY3VycmVudERlY29yYXRpb25Ob2RlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBjdXJyZW50RGVjb3JhdGlvbk5vZGUuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkpIHx8IG51bGw7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogbnVsbCxcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGhhbmRsZVN0YXJ0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAoX2MgPSByZW5kZXJlciA9PT0gbnVsbCB8fCByZW5kZXJlciA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVuZGVyZXIub25CZWZvcmVTdGFydCkgPT09IG51bGwgfHwgX2MgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9jLmNhbGwocmVuZGVyZXIsIHByb3BzKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoaGFuZGxlQ2hhbmdlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAoX2QgPSByZW5kZXJlciA9PT0gbnVsbCB8fCByZW5kZXJlciA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVuZGVyZXIub25CZWZvcmVVcGRhdGUpID09PSBudWxsIHx8IF9kID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZC5jYWxsKHJlbmRlcmVyLCBwcm9wcyk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKGhhbmRsZUNoYW5nZSB8fCBoYW5kbGVTdGFydCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcHJvcHMuaXRlbXMgPSBhd2FpdCBpdGVtcyh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZWRpdG9yLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHF1ZXJ5OiBzdGF0ZS5xdWVyeSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmIChoYW5kbGVFeGl0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAoX2UgPSByZW5kZXJlciA9PT0gbnVsbCB8fCByZW5kZXJlciA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVuZGVyZXIub25FeGl0KSA9PT0gbnVsbCB8fCBfZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2UuY2FsbChyZW5kZXJlciwgcHJvcHMpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmIChoYW5kbGVDaGFuZ2UpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIChfZiA9IHJlbmRlcmVyID09PSBudWxsIHx8IHJlbmRlcmVyID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZW5kZXJlci5vblVwZGF0ZSkgPT09IG51bGwgfHwgX2YgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9mLmNhbGwocmVuZGVyZXIsIHByb3BzKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoaGFuZGxlU3RhcnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIChfZyA9IHJlbmRlcmVyID09PSBudWxsIHx8IHJlbmRlcmVyID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZW5kZXJlci5vblN0YXJ0KSA9PT0gbnVsbCB8fCBfZyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2cuY2FsbChyZW5kZXJlciwgcHJvcHMpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBkZXN0cm95OiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHZhciBfYTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFwcm9wcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIChfYSA9IHJlbmRlcmVyID09PSBudWxsIHx8IHJlbmRlcmVyID09PSB2b2lkIDAgPyB2b2lkIDAgOiByZW5kZXJlci5vbkV4aXQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKHJlbmRlcmVyLCBwcm9wcyk7XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0sXG4gICAgICAgIHN0YXRlOiB7XG4gICAgICAgICAgICAvLyBJbml0aWFsaXplIHRoZSBwbHVnaW4ncyBpbnRlcm5hbCBzdGF0ZS5cbiAgICAgICAgICAgIGluaXQoKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgc3RhdGUgPSB7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZTogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIHJhbmdlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmcm9tOiAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgdG86IDAsXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIHF1ZXJ5OiBudWxsLFxuICAgICAgICAgICAgICAgICAgICB0ZXh0OiBudWxsLFxuICAgICAgICAgICAgICAgICAgICBjb21wb3Npbmc6IGZhbHNlLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgcmV0dXJuIHN0YXRlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIC8vIEFwcGx5IGNoYW5nZXMgdG8gdGhlIHBsdWdpbiBzdGF0ZSBmcm9tIGEgdmlldyB0cmFuc2FjdGlvbi5cbiAgICAgICAgICAgIGFwcGx5KHRyYW5zYWN0aW9uLCBwcmV2LCBfb2xkU3RhdGUsIHN0YXRlKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBpc0VkaXRhYmxlIH0gPSBlZGl0b3I7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBjb21wb3NpbmcgfSA9IGVkaXRvci52aWV3O1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgc2VsZWN0aW9uIH0gPSB0cmFuc2FjdGlvbjtcbiAgICAgICAgICAgICAgICBjb25zdCB7IGVtcHR5LCBmcm9tIH0gPSBzZWxlY3Rpb247XG4gICAgICAgICAgICAgICAgY29uc3QgbmV4dCA9IHsgLi4ucHJldiB9O1xuICAgICAgICAgICAgICAgIG5leHQuY29tcG9zaW5nID0gY29tcG9zaW5nO1xuICAgICAgICAgICAgICAgIC8vIFdlIGNhbiBvbmx5IGJlIHN1Z2dlc3RpbmcgaWYgdGhlIHZpZXcgaXMgZWRpdGFibGUsIGFuZDpcbiAgICAgICAgICAgICAgICAvLyAgICogdGhlcmUgaXMgbm8gc2VsZWN0aW9uLCBvclxuICAgICAgICAgICAgICAgIC8vICAgKiBhIGNvbXBvc2l0aW9uIGlzIGFjdGl2ZSAoc2VlOiBodHRwczovL2dpdGh1Yi5jb20vdWViZXJkb3Npcy90aXB0YXAvaXNzdWVzLzE0NDkpXG4gICAgICAgICAgICAgICAgaWYgKGlzRWRpdGFibGUgJiYgKGVtcHR5IHx8IGVkaXRvci52aWV3LmNvbXBvc2luZykpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gUmVzZXQgYWN0aXZlIHN0YXRlIGlmIHdlIGp1c3QgbGVmdCB0aGUgcHJldmlvdXMgc3VnZ2VzdGlvbiByYW5nZVxuICAgICAgICAgICAgICAgICAgICBpZiAoKGZyb20gPCBwcmV2LnJhbmdlLmZyb20gfHwgZnJvbSA+IHByZXYucmFuZ2UudG8pXG4gICAgICAgICAgICAgICAgICAgICAgICAmJiAhY29tcG9zaW5nXG4gICAgICAgICAgICAgICAgICAgICAgICAmJiAhcHJldi5jb21wb3NpbmcpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5leHQuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLy8gVHJ5IHRvIG1hdGNoIGFnYWluc3Qgd2hlcmUgb3VyIGN1cnNvciBjdXJyZW50bHkgaXNcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWF0Y2ggPSBmaW5kU3VnZ2VzdGlvbk1hdGNoJDEoe1xuICAgICAgICAgICAgICAgICAgICAgICAgY2hhcixcbiAgICAgICAgICAgICAgICAgICAgICAgIGFsbG93U3BhY2VzLFxuICAgICAgICAgICAgICAgICAgICAgICAgYWxsb3dUb0luY2x1ZGVDaGFyLFxuICAgICAgICAgICAgICAgICAgICAgICAgYWxsb3dlZFByZWZpeGVzLFxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhcnRPZkxpbmUsXG4gICAgICAgICAgICAgICAgICAgICAgICAkcG9zaXRpb246IHNlbGVjdGlvbi4kZnJvbSxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGRlY29yYXRpb25JZCA9IGBpZF8ke01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDB4ZmZmZmZmZmYpfWA7XG4gICAgICAgICAgICAgICAgICAgIC8vIElmIHdlIGZvdW5kIGEgbWF0Y2gsIHVwZGF0ZSB0aGUgY3VycmVudCBzdGF0ZSB0byBzaG93IGl0XG4gICAgICAgICAgICAgICAgICAgIGlmIChtYXRjaFxuICAgICAgICAgICAgICAgICAgICAgICAgJiYgYWxsb3coe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVkaXRvcixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByYW5nZTogbWF0Y2gucmFuZ2UsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXNBY3RpdmU6IHByZXYuYWN0aXZlLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5leHQuYWN0aXZlID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5leHQuZGVjb3JhdGlvbklkID0gcHJldi5kZWNvcmF0aW9uSWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHByZXYuZGVjb3JhdGlvbklkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBkZWNvcmF0aW9uSWQ7XG4gICAgICAgICAgICAgICAgICAgICAgICBuZXh0LnJhbmdlID0gbWF0Y2gucmFuZ2U7XG4gICAgICAgICAgICAgICAgICAgICAgICBuZXh0LnF1ZXJ5ID0gbWF0Y2gucXVlcnk7XG4gICAgICAgICAgICAgICAgICAgICAgICBuZXh0LnRleHQgPSBtYXRjaC50ZXh0O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgbmV4dC5hY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgbmV4dC5hY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gTWFrZSBzdXJlIHRvIGVtcHR5IHRoZSByYW5nZSBpZiBzdWdnZXN0aW9uIGlzIGluYWN0aXZlXG4gICAgICAgICAgICAgICAgaWYgKCFuZXh0LmFjdGl2ZSkge1xuICAgICAgICAgICAgICAgICAgICBuZXh0LmRlY29yYXRpb25JZCA9IG51bGw7XG4gICAgICAgICAgICAgICAgICAgIG5leHQucmFuZ2UgPSB7IGZyb206IDAsIHRvOiAwIH07XG4gICAgICAgICAgICAgICAgICAgIG5leHQucXVlcnkgPSBudWxsO1xuICAgICAgICAgICAgICAgICAgICBuZXh0LnRleHQgPSBudWxsO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gbmV4dDtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICAgIHByb3BzOiB7XG4gICAgICAgICAgICAvLyBDYWxsIHRoZSBrZXlkb3duIGhvb2sgaWYgc3VnZ2VzdGlvbiBpcyBhY3RpdmUuXG4gICAgICAgICAgICBoYW5kbGVLZXlEb3duKHZpZXcsIGV2ZW50KSB7XG4gICAgICAgICAgICAgICAgdmFyIF9hO1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgYWN0aXZlLCByYW5nZSB9ID0gcGx1Z2luLmdldFN0YXRlKHZpZXcuc3RhdGUpO1xuICAgICAgICAgICAgICAgIGlmICghYWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuICgoX2EgPSByZW5kZXJlciA9PT0gbnVsbCB8fCByZW5kZXJlciA9PT0gdm9pZCAwID8gdm9pZCAwIDogcmVuZGVyZXIub25LZXlEb3duKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbChyZW5kZXJlciwgeyB2aWV3LCBldmVudCwgcmFuZ2UgfSkpIHx8IGZhbHNlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIC8vIFNldHVwIGRlY29yYXRvciBvbiB0aGUgY3VycmVudGx5IGFjdGl2ZSBzdWdnZXN0aW9uLlxuICAgICAgICAgICAgZGVjb3JhdGlvbnMoc3RhdGUpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB7IGFjdGl2ZSwgcmFuZ2UsIGRlY29yYXRpb25JZCwgcXVlcnksIH0gPSBwbHVnaW4uZ2V0U3RhdGUoc3RhdGUpO1xuICAgICAgICAgICAgICAgIGlmICghYWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCBpc0VtcHR5ID0gIShxdWVyeSA9PT0gbnVsbCB8fCBxdWVyeSA9PT0gdm9pZCAwID8gdm9pZCAwIDogcXVlcnkubGVuZ3RoKTtcbiAgICAgICAgICAgICAgICBjb25zdCBjbGFzc05hbWVzID0gW2RlY29yYXRpb25DbGFzc107XG4gICAgICAgICAgICAgICAgaWYgKGlzRW1wdHkpIHtcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lcy5wdXNoKGRlY29yYXRpb25FbXB0eUNsYXNzKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIERlY29yYXRpb25TZXQuY3JlYXRlKHN0YXRlLmRvYywgW1xuICAgICAgICAgICAgICAgICAgICBEZWNvcmF0aW9uLmlubGluZShyYW5nZS5mcm9tLCByYW5nZS50bywge1xuICAgICAgICAgICAgICAgICAgICAgICAgbm9kZU5hbWU6IGRlY29yYXRpb25UYWcsXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzczogY2xhc3NOYW1lcy5qb2luKCcgJyksXG4gICAgICAgICAgICAgICAgICAgICAgICAnZGF0YS1kZWNvcmF0aW9uLWlkJzogZGVjb3JhdGlvbklkLFxuICAgICAgICAgICAgICAgICAgICAgICAgJ2RhdGEtZGVjb3JhdGlvbi1jb250ZW50JzogZGVjb3JhdGlvbkNvbnRlbnQsXG4gICAgICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgIF0pO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICB9KTtcbiAgICByZXR1cm4gcGx1Z2luO1xufVxuXG5leHBvcnQgeyBTdWdnZXN0aW9uLCBTdWdnZXN0aW9uUGx1Z2luS2V5LCBTdWdnZXN0aW9uIGFzIGRlZmF1bHQsIGZpbmRTdWdnZXN0aW9uTWF0Y2ggfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+suggestion@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/suggestion/dist/index.js\n");

/***/ })

};
;