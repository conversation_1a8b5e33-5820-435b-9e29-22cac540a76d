"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/framework-utils@1.1.0";
exports.ids = ["vendor-chunks/framework-utils@1.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/framework-utils@1.1.0/node_modules/framework-utils/dist/utils.esm.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/framework-utils@1.1.0/node_modules/framework-utils/dist/utils.esm.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Properties: () => (/* binding */ Properties),\n/* harmony export */   prefixCSS: () => (/* binding */ prefixCSS),\n/* harmony export */   prefixNames: () => (/* binding */ prefixNames),\n/* harmony export */   ref: () => (/* binding */ ref),\n/* harmony export */   refs: () => (/* binding */ refs),\n/* harmony export */   withMethods: () => (/* binding */ withMethods)\n/* harmony export */ });\n/*\nCopyright (c) 2019 Daybrush\nname: framework-utils\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/framework-utils.git\nversion: 1.1.0\n*/\nfunction prefixNames(prefix) {\n  var classNames = [];\n\n  for (var _i = 1; _i < arguments.length; _i++) {\n    classNames[_i - 1] = arguments[_i];\n  }\n\n  return classNames.map(function (className) {\n    return className.split(\" \").map(function (name) {\n      return name ? \"\" + prefix + name : \"\";\n    }).join(\" \");\n  }).join(\" \");\n}\nfunction prefixCSS(prefix, css) {\n  return css.replace(/([^}{]*){/gm, function (_, selector) {\n    return selector.replace(/\\.([^{,\\s\\d.]+)/g, \".\" + prefix + \"$1\") + \"{\";\n  });\n}\n/* react */\n\nfunction ref(target, name) {\n  return function (e) {\n    e && (target[name] = e);\n  };\n}\nfunction refs(target, name, i) {\n  return function (e) {\n    e && (target[name][i] = e);\n  };\n}\n/* Class Decorator */\n\nfunction Properties(properties, action) {\n  return function (component) {\n    var prototype = component.prototype;\n    properties.forEach(function (property) {\n      action(prototype, property);\n    });\n  };\n}\n/* Property Decorator */\n\nfunction withMethods(methods, duplicate) {\n  if (duplicate === void 0) {\n    duplicate = {};\n  }\n\n  return function (prototype, propertyName) {\n    methods.forEach(function (name) {\n      var methodName = duplicate[name] || name;\n\n      if (methodName in prototype) {\n        return;\n      }\n\n      prototype[methodName] = function () {\n        var _a;\n\n        var args = [];\n\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n\n        var result = (_a = this[propertyName])[name].apply(_a, args);\n\n        if (result === this[propertyName]) {\n          return this;\n        } else {\n          return result;\n        }\n      };\n    });\n  };\n}\n\n\n//# sourceMappingURL=utils.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZnJhbWV3b3JrLXV0aWxzQDEuMS4wL25vZGVfbW9kdWxlcy9mcmFtZXdvcmstdXRpbHMvZGlzdC91dGlscy5lc20uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsbUJBQW1CLHVCQUF1QjtBQUMxQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7QUFDQTtBQUNBLDJCQUEyQixJQUFJO0FBQy9CLG1DQUFtQyxzQ0FBc0M7QUFDekUsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUEseUJBQXlCLHVCQUF1QjtBQUNoRDtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRXNFO0FBQ3RFIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZnJhbWV3b3JrLXV0aWxzQDEuMS4wXFxub2RlX21vZHVsZXNcXGZyYW1ld29yay11dGlsc1xcZGlzdFxcdXRpbHMuZXNtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qXG5Db3B5cmlnaHQgKGMpIDIwMTkgRGF5YnJ1c2hcbm5hbWU6IGZyYW1ld29yay11dGlsc1xubGljZW5zZTogTUlUXG5hdXRob3I6IERheWJydXNoXG5yZXBvc2l0b3J5OiBnaXQraHR0cHM6Ly9naXRodWIuY29tL2RheWJydXNoL2ZyYW1ld29yay11dGlscy5naXRcbnZlcnNpb246IDEuMS4wXG4qL1xuZnVuY3Rpb24gcHJlZml4TmFtZXMocHJlZml4KSB7XG4gIHZhciBjbGFzc05hbWVzID0gW107XG5cbiAgZm9yICh2YXIgX2kgPSAxOyBfaSA8IGFyZ3VtZW50cy5sZW5ndGg7IF9pKyspIHtcbiAgICBjbGFzc05hbWVzW19pIC0gMV0gPSBhcmd1bWVudHNbX2ldO1xuICB9XG5cbiAgcmV0dXJuIGNsYXNzTmFtZXMubWFwKGZ1bmN0aW9uIChjbGFzc05hbWUpIHtcbiAgICByZXR1cm4gY2xhc3NOYW1lLnNwbGl0KFwiIFwiKS5tYXAoZnVuY3Rpb24gKG5hbWUpIHtcbiAgICAgIHJldHVybiBuYW1lID8gXCJcIiArIHByZWZpeCArIG5hbWUgOiBcIlwiO1xuICAgIH0pLmpvaW4oXCIgXCIpO1xuICB9KS5qb2luKFwiIFwiKTtcbn1cbmZ1bmN0aW9uIHByZWZpeENTUyhwcmVmaXgsIGNzcykge1xuICByZXR1cm4gY3NzLnJlcGxhY2UoLyhbXn17XSopey9nbSwgZnVuY3Rpb24gKF8sIHNlbGVjdG9yKSB7XG4gICAgcmV0dXJuIHNlbGVjdG9yLnJlcGxhY2UoL1xcLihbXnssXFxzXFxkLl0rKS9nLCBcIi5cIiArIHByZWZpeCArIFwiJDFcIikgKyBcIntcIjtcbiAgfSk7XG59XG4vKiByZWFjdCAqL1xuXG5mdW5jdGlvbiByZWYodGFyZ2V0LCBuYW1lKSB7XG4gIHJldHVybiBmdW5jdGlvbiAoZSkge1xuICAgIGUgJiYgKHRhcmdldFtuYW1lXSA9IGUpO1xuICB9O1xufVxuZnVuY3Rpb24gcmVmcyh0YXJnZXQsIG5hbWUsIGkpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIChlKSB7XG4gICAgZSAmJiAodGFyZ2V0W25hbWVdW2ldID0gZSk7XG4gIH07XG59XG4vKiBDbGFzcyBEZWNvcmF0b3IgKi9cblxuZnVuY3Rpb24gUHJvcGVydGllcyhwcm9wZXJ0aWVzLCBhY3Rpb24pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIChjb21wb25lbnQpIHtcbiAgICB2YXIgcHJvdG90eXBlID0gY29tcG9uZW50LnByb3RvdHlwZTtcbiAgICBwcm9wZXJ0aWVzLmZvckVhY2goZnVuY3Rpb24gKHByb3BlcnR5KSB7XG4gICAgICBhY3Rpb24ocHJvdG90eXBlLCBwcm9wZXJ0eSk7XG4gICAgfSk7XG4gIH07XG59XG4vKiBQcm9wZXJ0eSBEZWNvcmF0b3IgKi9cblxuZnVuY3Rpb24gd2l0aE1ldGhvZHMobWV0aG9kcywgZHVwbGljYXRlKSB7XG4gIGlmIChkdXBsaWNhdGUgPT09IHZvaWQgMCkge1xuICAgIGR1cGxpY2F0ZSA9IHt9O1xuICB9XG5cbiAgcmV0dXJuIGZ1bmN0aW9uIChwcm90b3R5cGUsIHByb3BlcnR5TmFtZSkge1xuICAgIG1ldGhvZHMuZm9yRWFjaChmdW5jdGlvbiAobmFtZSkge1xuICAgICAgdmFyIG1ldGhvZE5hbWUgPSBkdXBsaWNhdGVbbmFtZV0gfHwgbmFtZTtcblxuICAgICAgaWYgKG1ldGhvZE5hbWUgaW4gcHJvdG90eXBlKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgcHJvdG90eXBlW21ldGhvZE5hbWVdID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgX2E7XG5cbiAgICAgICAgdmFyIGFyZ3MgPSBbXTtcblxuICAgICAgICBmb3IgKHZhciBfaSA9IDA7IF9pIDwgYXJndW1lbnRzLmxlbmd0aDsgX2krKykge1xuICAgICAgICAgIGFyZ3NbX2ldID0gYXJndW1lbnRzW19pXTtcbiAgICAgICAgfVxuXG4gICAgICAgIHZhciByZXN1bHQgPSAoX2EgPSB0aGlzW3Byb3BlcnR5TmFtZV0pW25hbWVdLmFwcGx5KF9hLCBhcmdzKTtcblxuICAgICAgICBpZiAocmVzdWx0ID09PSB0aGlzW3Byb3BlcnR5TmFtZV0pIHtcbiAgICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgICB9XG4gICAgICB9O1xuICAgIH0pO1xuICB9O1xufVxuXG5leHBvcnQgeyBQcm9wZXJ0aWVzLCBwcmVmaXhDU1MsIHByZWZpeE5hbWVzLCByZWYsIHJlZnMsIHdpdGhNZXRob2RzIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlscy5lc20uanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/framework-utils@1.1.0/node_modules/framework-utils/dist/utils.esm.js\n");

/***/ })

};
;