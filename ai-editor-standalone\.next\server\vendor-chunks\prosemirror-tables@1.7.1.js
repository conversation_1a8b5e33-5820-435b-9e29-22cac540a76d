"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-tables@1.7.1";
exports.ids = ["vendor-chunks/prosemirror-tables@1.7.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/prosemirror-tables@1.7.1/node_modules/prosemirror-tables/dist/index.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/.pnpm/prosemirror-tables@1.7.1/node_modules/prosemirror-tables/dist/index.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CellBookmark: () => (/* binding */ CellBookmark),\n/* harmony export */   CellSelection: () => (/* binding */ CellSelection),\n/* harmony export */   ResizeState: () => (/* binding */ ResizeState),\n/* harmony export */   TableMap: () => (/* binding */ TableMap),\n/* harmony export */   TableView: () => (/* binding */ TableView),\n/* harmony export */   __clipCells: () => (/* binding */ clipCells),\n/* harmony export */   __insertCells: () => (/* binding */ insertCells),\n/* harmony export */   __pastedCells: () => (/* binding */ pastedCells),\n/* harmony export */   addColSpan: () => (/* binding */ addColSpan),\n/* harmony export */   addColumn: () => (/* binding */ addColumn),\n/* harmony export */   addColumnAfter: () => (/* binding */ addColumnAfter),\n/* harmony export */   addColumnBefore: () => (/* binding */ addColumnBefore),\n/* harmony export */   addRow: () => (/* binding */ addRow),\n/* harmony export */   addRowAfter: () => (/* binding */ addRowAfter),\n/* harmony export */   addRowBefore: () => (/* binding */ addRowBefore),\n/* harmony export */   cellAround: () => (/* binding */ cellAround),\n/* harmony export */   cellNear: () => (/* binding */ cellNear),\n/* harmony export */   colCount: () => (/* binding */ colCount),\n/* harmony export */   columnIsHeader: () => (/* binding */ columnIsHeader),\n/* harmony export */   columnResizing: () => (/* binding */ columnResizing),\n/* harmony export */   columnResizingPluginKey: () => (/* binding */ columnResizingPluginKey),\n/* harmony export */   deleteCellSelection: () => (/* binding */ deleteCellSelection),\n/* harmony export */   deleteColumn: () => (/* binding */ deleteColumn),\n/* harmony export */   deleteRow: () => (/* binding */ deleteRow),\n/* harmony export */   deleteTable: () => (/* binding */ deleteTable),\n/* harmony export */   findCell: () => (/* binding */ findCell),\n/* harmony export */   fixTables: () => (/* binding */ fixTables),\n/* harmony export */   fixTablesKey: () => (/* binding */ fixTablesKey),\n/* harmony export */   goToNextCell: () => (/* binding */ goToNextCell),\n/* harmony export */   handlePaste: () => (/* binding */ handlePaste),\n/* harmony export */   inSameTable: () => (/* binding */ inSameTable),\n/* harmony export */   isInTable: () => (/* binding */ isInTable),\n/* harmony export */   mergeCells: () => (/* binding */ mergeCells),\n/* harmony export */   moveCellForward: () => (/* binding */ moveCellForward),\n/* harmony export */   nextCell: () => (/* binding */ nextCell),\n/* harmony export */   pointsAtCell: () => (/* binding */ pointsAtCell),\n/* harmony export */   removeColSpan: () => (/* binding */ removeColSpan),\n/* harmony export */   removeColumn: () => (/* binding */ removeColumn),\n/* harmony export */   removeRow: () => (/* binding */ removeRow),\n/* harmony export */   rowIsHeader: () => (/* binding */ rowIsHeader),\n/* harmony export */   selectedRect: () => (/* binding */ selectedRect),\n/* harmony export */   selectionCell: () => (/* binding */ selectionCell),\n/* harmony export */   setCellAttr: () => (/* binding */ setCellAttr),\n/* harmony export */   splitCell: () => (/* binding */ splitCell),\n/* harmony export */   splitCellWithType: () => (/* binding */ splitCellWithType),\n/* harmony export */   tableEditing: () => (/* binding */ tableEditing),\n/* harmony export */   tableEditingKey: () => (/* binding */ tableEditingKey),\n/* harmony export */   tableNodeTypes: () => (/* binding */ tableNodeTypes),\n/* harmony export */   tableNodes: () => (/* binding */ tableNodes),\n/* harmony export */   toggleHeader: () => (/* binding */ toggleHeader),\n/* harmony export */   toggleHeaderCell: () => (/* binding */ toggleHeaderCell),\n/* harmony export */   toggleHeaderColumn: () => (/* binding */ toggleHeaderColumn),\n/* harmony export */   toggleHeaderRow: () => (/* binding */ toggleHeaderRow),\n/* harmony export */   updateColumnsOnResize: () => (/* binding */ updateColumnsOnResize)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/.pnpm/prosemirror-state@1.4.3/node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/.pnpm/prosemirror-model@1.25.2/node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-view */ \"(ssr)/./node_modules/.pnpm/prosemirror-view@1.40.1/node_modules/prosemirror-view/dist/index.js\");\n/* harmony import */ var prosemirror_keymap__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prosemirror-keymap */ \"(ssr)/./node_modules/.pnpm/prosemirror-keymap@1.2.3/node_modules/prosemirror-keymap/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/.pnpm/prosemirror-transform@1.10.4/node_modules/prosemirror-transform/dist/index.js\");\n// src/index.ts\n\n\n// src/cellselection.ts\n\n\n\n\n// src/tablemap.ts\nvar readFromCache;\nvar addToCache;\nif (typeof WeakMap != \"undefined\") {\n  let cache = /* @__PURE__ */ new WeakMap();\n  readFromCache = (key) => cache.get(key);\n  addToCache = (key, value) => {\n    cache.set(key, value);\n    return value;\n  };\n} else {\n  const cache = [];\n  const cacheSize = 10;\n  let cachePos = 0;\n  readFromCache = (key) => {\n    for (let i = 0; i < cache.length; i += 2)\n      if (cache[i] == key) return cache[i + 1];\n  };\n  addToCache = (key, value) => {\n    if (cachePos == cacheSize) cachePos = 0;\n    cache[cachePos++] = key;\n    return cache[cachePos++] = value;\n  };\n}\nvar TableMap = class {\n  constructor(width, height, map, problems) {\n    this.width = width;\n    this.height = height;\n    this.map = map;\n    this.problems = problems;\n  }\n  // Find the dimensions of the cell at the given position.\n  findCell(pos) {\n    for (let i = 0; i < this.map.length; i++) {\n      const curPos = this.map[i];\n      if (curPos != pos) continue;\n      const left = i % this.width;\n      const top = i / this.width | 0;\n      let right = left + 1;\n      let bottom = top + 1;\n      for (let j = 1; right < this.width && this.map[i + j] == curPos; j++) {\n        right++;\n      }\n      for (let j = 1; bottom < this.height && this.map[i + this.width * j] == curPos; j++) {\n        bottom++;\n      }\n      return { left, top, right, bottom };\n    }\n    throw new RangeError(`No cell with offset ${pos} found`);\n  }\n  // Find the left side of the cell at the given position.\n  colCount(pos) {\n    for (let i = 0; i < this.map.length; i++) {\n      if (this.map[i] == pos) {\n        return i % this.width;\n      }\n    }\n    throw new RangeError(`No cell with offset ${pos} found`);\n  }\n  // Find the next cell in the given direction, starting from the cell\n  // at `pos`, if any.\n  nextCell(pos, axis, dir) {\n    const { left, right, top, bottom } = this.findCell(pos);\n    if (axis == \"horiz\") {\n      if (dir < 0 ? left == 0 : right == this.width) return null;\n      return this.map[top * this.width + (dir < 0 ? left - 1 : right)];\n    } else {\n      if (dir < 0 ? top == 0 : bottom == this.height) return null;\n      return this.map[left + this.width * (dir < 0 ? top - 1 : bottom)];\n    }\n  }\n  // Get the rectangle spanning the two given cells.\n  rectBetween(a, b) {\n    const {\n      left: leftA,\n      right: rightA,\n      top: topA,\n      bottom: bottomA\n    } = this.findCell(a);\n    const {\n      left: leftB,\n      right: rightB,\n      top: topB,\n      bottom: bottomB\n    } = this.findCell(b);\n    return {\n      left: Math.min(leftA, leftB),\n      top: Math.min(topA, topB),\n      right: Math.max(rightA, rightB),\n      bottom: Math.max(bottomA, bottomB)\n    };\n  }\n  // Return the position of all cells that have the top left corner in\n  // the given rectangle.\n  cellsInRect(rect) {\n    const result = [];\n    const seen = {};\n    for (let row = rect.top; row < rect.bottom; row++) {\n      for (let col = rect.left; col < rect.right; col++) {\n        const index = row * this.width + col;\n        const pos = this.map[index];\n        if (seen[pos]) continue;\n        seen[pos] = true;\n        if (col == rect.left && col && this.map[index - 1] == pos || row == rect.top && row && this.map[index - this.width] == pos) {\n          continue;\n        }\n        result.push(pos);\n      }\n    }\n    return result;\n  }\n  // Return the position at which the cell at the given row and column\n  // starts, or would start, if a cell started there.\n  positionAt(row, col, table) {\n    for (let i = 0, rowStart = 0; ; i++) {\n      const rowEnd = rowStart + table.child(i).nodeSize;\n      if (i == row) {\n        let index = col + row * this.width;\n        const rowEndIndex = (row + 1) * this.width;\n        while (index < rowEndIndex && this.map[index] < rowStart) index++;\n        return index == rowEndIndex ? rowEnd - 1 : this.map[index];\n      }\n      rowStart = rowEnd;\n    }\n  }\n  // Find the table map for the given table node.\n  static get(table) {\n    return readFromCache(table) || addToCache(table, computeMap(table));\n  }\n};\nfunction computeMap(table) {\n  if (table.type.spec.tableRole != \"table\")\n    throw new RangeError(\"Not a table node: \" + table.type.name);\n  const width = findWidth(table), height = table.childCount;\n  const map = [];\n  let mapPos = 0;\n  let problems = null;\n  const colWidths = [];\n  for (let i = 0, e = width * height; i < e; i++) map[i] = 0;\n  for (let row = 0, pos = 0; row < height; row++) {\n    const rowNode = table.child(row);\n    pos++;\n    for (let i = 0; ; i++) {\n      while (mapPos < map.length && map[mapPos] != 0) mapPos++;\n      if (i == rowNode.childCount) break;\n      const cellNode = rowNode.child(i);\n      const { colspan, rowspan, colwidth } = cellNode.attrs;\n      for (let h = 0; h < rowspan; h++) {\n        if (h + row >= height) {\n          (problems || (problems = [])).push({\n            type: \"overlong_rowspan\",\n            pos,\n            n: rowspan - h\n          });\n          break;\n        }\n        const start = mapPos + h * width;\n        for (let w = 0; w < colspan; w++) {\n          if (map[start + w] == 0) map[start + w] = pos;\n          else\n            (problems || (problems = [])).push({\n              type: \"collision\",\n              row,\n              pos,\n              n: colspan - w\n            });\n          const colW = colwidth && colwidth[w];\n          if (colW) {\n            const widthIndex = (start + w) % width * 2, prev = colWidths[widthIndex];\n            if (prev == null || prev != colW && colWidths[widthIndex + 1] == 1) {\n              colWidths[widthIndex] = colW;\n              colWidths[widthIndex + 1] = 1;\n            } else if (prev == colW) {\n              colWidths[widthIndex + 1]++;\n            }\n          }\n        }\n      }\n      mapPos += colspan;\n      pos += cellNode.nodeSize;\n    }\n    const expectedPos = (row + 1) * width;\n    let missing = 0;\n    while (mapPos < expectedPos) if (map[mapPos++] == 0) missing++;\n    if (missing)\n      (problems || (problems = [])).push({ type: \"missing\", row, n: missing });\n    pos++;\n  }\n  if (width === 0 || height === 0)\n    (problems || (problems = [])).push({ type: \"zero_sized\" });\n  const tableMap = new TableMap(width, height, map, problems);\n  let badWidths = false;\n  for (let i = 0; !badWidths && i < colWidths.length; i += 2)\n    if (colWidths[i] != null && colWidths[i + 1] < height) badWidths = true;\n  if (badWidths) findBadColWidths(tableMap, colWidths, table);\n  return tableMap;\n}\nfunction findWidth(table) {\n  let width = -1;\n  let hasRowSpan = false;\n  for (let row = 0; row < table.childCount; row++) {\n    const rowNode = table.child(row);\n    let rowWidth = 0;\n    if (hasRowSpan)\n      for (let j = 0; j < row; j++) {\n        const prevRow = table.child(j);\n        for (let i = 0; i < prevRow.childCount; i++) {\n          const cell = prevRow.child(i);\n          if (j + cell.attrs.rowspan > row) rowWidth += cell.attrs.colspan;\n        }\n      }\n    for (let i = 0; i < rowNode.childCount; i++) {\n      const cell = rowNode.child(i);\n      rowWidth += cell.attrs.colspan;\n      if (cell.attrs.rowspan > 1) hasRowSpan = true;\n    }\n    if (width == -1) width = rowWidth;\n    else if (width != rowWidth) width = Math.max(width, rowWidth);\n  }\n  return width;\n}\nfunction findBadColWidths(map, colWidths, table) {\n  if (!map.problems) map.problems = [];\n  const seen = {};\n  for (let i = 0; i < map.map.length; i++) {\n    const pos = map.map[i];\n    if (seen[pos]) continue;\n    seen[pos] = true;\n    const node = table.nodeAt(pos);\n    if (!node) {\n      throw new RangeError(`No cell with offset ${pos} found`);\n    }\n    let updated = null;\n    const attrs = node.attrs;\n    for (let j = 0; j < attrs.colspan; j++) {\n      const col = (i + j) % map.width;\n      const colWidth = colWidths[col * 2];\n      if (colWidth != null && (!attrs.colwidth || attrs.colwidth[j] != colWidth))\n        (updated || (updated = freshColWidth(attrs)))[j] = colWidth;\n    }\n    if (updated)\n      map.problems.unshift({\n        type: \"colwidth mismatch\",\n        pos,\n        colwidth: updated\n      });\n  }\n}\nfunction freshColWidth(attrs) {\n  if (attrs.colwidth) return attrs.colwidth.slice();\n  const result = [];\n  for (let i = 0; i < attrs.colspan; i++) result.push(0);\n  return result;\n}\n\n// src/util.ts\n\n\n// src/schema.ts\nfunction getCellAttrs(dom, extraAttrs) {\n  if (typeof dom === \"string\") {\n    return {};\n  }\n  const widthAttr = dom.getAttribute(\"data-colwidth\");\n  const widths = widthAttr && /^\\d+(,\\d+)*$/.test(widthAttr) ? widthAttr.split(\",\").map((s) => Number(s)) : null;\n  const colspan = Number(dom.getAttribute(\"colspan\") || 1);\n  const result = {\n    colspan,\n    rowspan: Number(dom.getAttribute(\"rowspan\") || 1),\n    colwidth: widths && widths.length == colspan ? widths : null\n  };\n  for (const prop in extraAttrs) {\n    const getter = extraAttrs[prop].getFromDOM;\n    const value = getter && getter(dom);\n    if (value != null) {\n      result[prop] = value;\n    }\n  }\n  return result;\n}\nfunction setCellAttrs(node, extraAttrs) {\n  const attrs = {};\n  if (node.attrs.colspan != 1) attrs.colspan = node.attrs.colspan;\n  if (node.attrs.rowspan != 1) attrs.rowspan = node.attrs.rowspan;\n  if (node.attrs.colwidth)\n    attrs[\"data-colwidth\"] = node.attrs.colwidth.join(\",\");\n  for (const prop in extraAttrs) {\n    const setter = extraAttrs[prop].setDOMAttr;\n    if (setter) setter(node.attrs[prop], attrs);\n  }\n  return attrs;\n}\nfunction validateColwidth(value) {\n  if (value === null) {\n    return;\n  }\n  if (!Array.isArray(value)) {\n    throw new TypeError(\"colwidth must be null or an array\");\n  }\n  for (const item of value) {\n    if (typeof item !== \"number\") {\n      throw new TypeError(\"colwidth must be null or an array of numbers\");\n    }\n  }\n}\nfunction tableNodes(options) {\n  const extraAttrs = options.cellAttributes || {};\n  const cellAttrs = {\n    colspan: { default: 1, validate: \"number\" },\n    rowspan: { default: 1, validate: \"number\" },\n    colwidth: { default: null, validate: validateColwidth }\n  };\n  for (const prop in extraAttrs)\n    cellAttrs[prop] = {\n      default: extraAttrs[prop].default,\n      validate: extraAttrs[prop].validate\n    };\n  return {\n    table: {\n      content: \"table_row+\",\n      tableRole: \"table\",\n      isolating: true,\n      group: options.tableGroup,\n      parseDOM: [{ tag: \"table\" }],\n      toDOM() {\n        return [\"table\", [\"tbody\", 0]];\n      }\n    },\n    table_row: {\n      content: \"(table_cell | table_header)*\",\n      tableRole: \"row\",\n      parseDOM: [{ tag: \"tr\" }],\n      toDOM() {\n        return [\"tr\", 0];\n      }\n    },\n    table_cell: {\n      content: options.cellContent,\n      attrs: cellAttrs,\n      tableRole: \"cell\",\n      isolating: true,\n      parseDOM: [\n        { tag: \"td\", getAttrs: (dom) => getCellAttrs(dom, extraAttrs) }\n      ],\n      toDOM(node) {\n        return [\"td\", setCellAttrs(node, extraAttrs), 0];\n      }\n    },\n    table_header: {\n      content: options.cellContent,\n      attrs: cellAttrs,\n      tableRole: \"header_cell\",\n      isolating: true,\n      parseDOM: [\n        { tag: \"th\", getAttrs: (dom) => getCellAttrs(dom, extraAttrs) }\n      ],\n      toDOM(node) {\n        return [\"th\", setCellAttrs(node, extraAttrs), 0];\n      }\n    }\n  };\n}\nfunction tableNodeTypes(schema) {\n  let result = schema.cached.tableNodeTypes;\n  if (!result) {\n    result = schema.cached.tableNodeTypes = {};\n    for (const name in schema.nodes) {\n      const type = schema.nodes[name], role = type.spec.tableRole;\n      if (role) result[role] = type;\n    }\n  }\n  return result;\n}\n\n// src/util.ts\nvar tableEditingKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(\"selectingCells\");\nfunction cellAround($pos) {\n  for (let d = $pos.depth - 1; d > 0; d--)\n    if ($pos.node(d).type.spec.tableRole == \"row\")\n      return $pos.node(0).resolve($pos.before(d + 1));\n  return null;\n}\nfunction cellWrapping($pos) {\n  for (let d = $pos.depth; d > 0; d--) {\n    const role = $pos.node(d).type.spec.tableRole;\n    if (role === \"cell\" || role === \"header_cell\") return $pos.node(d);\n  }\n  return null;\n}\nfunction isInTable(state) {\n  const $head = state.selection.$head;\n  for (let d = $head.depth; d > 0; d--)\n    if ($head.node(d).type.spec.tableRole == \"row\") return true;\n  return false;\n}\nfunction selectionCell(state) {\n  const sel = state.selection;\n  if (\"$anchorCell\" in sel && sel.$anchorCell) {\n    return sel.$anchorCell.pos > sel.$headCell.pos ? sel.$anchorCell : sel.$headCell;\n  } else if (\"node\" in sel && sel.node && sel.node.type.spec.tableRole == \"cell\") {\n    return sel.$anchor;\n  }\n  const $cell = cellAround(sel.$head) || cellNear(sel.$head);\n  if ($cell) {\n    return $cell;\n  }\n  throw new RangeError(`No cell found around position ${sel.head}`);\n}\nfunction cellNear($pos) {\n  for (let after = $pos.nodeAfter, pos = $pos.pos; after; after = after.firstChild, pos++) {\n    const role = after.type.spec.tableRole;\n    if (role == \"cell\" || role == \"header_cell\") return $pos.doc.resolve(pos);\n  }\n  for (let before = $pos.nodeBefore, pos = $pos.pos; before; before = before.lastChild, pos--) {\n    const role = before.type.spec.tableRole;\n    if (role == \"cell\" || role == \"header_cell\")\n      return $pos.doc.resolve(pos - before.nodeSize);\n  }\n}\nfunction pointsAtCell($pos) {\n  return $pos.parent.type.spec.tableRole == \"row\" && !!$pos.nodeAfter;\n}\nfunction moveCellForward($pos) {\n  return $pos.node(0).resolve($pos.pos + $pos.nodeAfter.nodeSize);\n}\nfunction inSameTable($cellA, $cellB) {\n  return $cellA.depth == $cellB.depth && $cellA.pos >= $cellB.start(-1) && $cellA.pos <= $cellB.end(-1);\n}\nfunction findCell($pos) {\n  return TableMap.get($pos.node(-1)).findCell($pos.pos - $pos.start(-1));\n}\nfunction colCount($pos) {\n  return TableMap.get($pos.node(-1)).colCount($pos.pos - $pos.start(-1));\n}\nfunction nextCell($pos, axis, dir) {\n  const table = $pos.node(-1);\n  const map = TableMap.get(table);\n  const tableStart = $pos.start(-1);\n  const moved = map.nextCell($pos.pos - tableStart, axis, dir);\n  return moved == null ? null : $pos.node(0).resolve(tableStart + moved);\n}\nfunction removeColSpan(attrs, pos, n = 1) {\n  const result = { ...attrs, colspan: attrs.colspan - n };\n  if (result.colwidth) {\n    result.colwidth = result.colwidth.slice();\n    result.colwidth.splice(pos, n);\n    if (!result.colwidth.some((w) => w > 0)) result.colwidth = null;\n  }\n  return result;\n}\nfunction addColSpan(attrs, pos, n = 1) {\n  const result = { ...attrs, colspan: attrs.colspan + n };\n  if (result.colwidth) {\n    result.colwidth = result.colwidth.slice();\n    for (let i = 0; i < n; i++) result.colwidth.splice(pos, 0, 0);\n  }\n  return result;\n}\nfunction columnIsHeader(map, table, col) {\n  const headerCell = tableNodeTypes(table.type.schema).header_cell;\n  for (let row = 0; row < map.height; row++)\n    if (table.nodeAt(map.map[col + row * map.width]).type != headerCell)\n      return false;\n  return true;\n}\n\n// src/cellselection.ts\nvar CellSelection = class _CellSelection extends prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection {\n  // A table selection is identified by its anchor and head cells. The\n  // positions given to this constructor should point _before_ two\n  // cells in the same table. They may be the same, to select a single\n  // cell.\n  constructor($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const rect = map.rectBetween(\n      $anchorCell.pos - tableStart,\n      $headCell.pos - tableStart\n    );\n    const doc = $anchorCell.node(0);\n    const cells = map.cellsInRect(rect).filter((p) => p != $headCell.pos - tableStart);\n    cells.unshift($headCell.pos - tableStart);\n    const ranges = cells.map((pos) => {\n      const cell = table.nodeAt(pos);\n      if (!cell) {\n        throw RangeError(`No cell with offset ${pos} found`);\n      }\n      const from = tableStart + pos + 1;\n      return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.SelectionRange(\n        doc.resolve(from),\n        doc.resolve(from + cell.content.size)\n      );\n    });\n    super(ranges[0].$from, ranges[0].$to, ranges);\n    this.$anchorCell = $anchorCell;\n    this.$headCell = $headCell;\n  }\n  map(doc, mapping) {\n    const $anchorCell = doc.resolve(mapping.map(this.$anchorCell.pos));\n    const $headCell = doc.resolve(mapping.map(this.$headCell.pos));\n    if (pointsAtCell($anchorCell) && pointsAtCell($headCell) && inSameTable($anchorCell, $headCell)) {\n      const tableChanged = this.$anchorCell.node(-1) != $anchorCell.node(-1);\n      if (tableChanged && this.isRowSelection())\n        return _CellSelection.rowSelection($anchorCell, $headCell);\n      else if (tableChanged && this.isColSelection())\n        return _CellSelection.colSelection($anchorCell, $headCell);\n      else return new _CellSelection($anchorCell, $headCell);\n    }\n    return prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.between($anchorCell, $headCell);\n  }\n  // Returns a rectangular slice of table rows containing the selected\n  // cells.\n  content() {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const rect = map.rectBetween(\n      this.$anchorCell.pos - tableStart,\n      this.$headCell.pos - tableStart\n    );\n    const seen = {};\n    const rows = [];\n    for (let row = rect.top; row < rect.bottom; row++) {\n      const rowContent = [];\n      for (let index = row * map.width + rect.left, col = rect.left; col < rect.right; col++, index++) {\n        const pos = map.map[index];\n        if (seen[pos]) continue;\n        seen[pos] = true;\n        const cellRect = map.findCell(pos);\n        let cell = table.nodeAt(pos);\n        if (!cell) {\n          throw RangeError(`No cell with offset ${pos} found`);\n        }\n        const extraLeft = rect.left - cellRect.left;\n        const extraRight = cellRect.right - rect.right;\n        if (extraLeft > 0 || extraRight > 0) {\n          let attrs = cell.attrs;\n          if (extraLeft > 0) {\n            attrs = removeColSpan(attrs, 0, extraLeft);\n          }\n          if (extraRight > 0) {\n            attrs = removeColSpan(\n              attrs,\n              attrs.colspan - extraRight,\n              extraRight\n            );\n          }\n          if (cellRect.left < rect.left) {\n            cell = cell.type.createAndFill(attrs);\n            if (!cell) {\n              throw RangeError(\n                `Could not create cell with attrs ${JSON.stringify(attrs)}`\n              );\n            }\n          } else {\n            cell = cell.type.create(attrs, cell.content);\n          }\n        }\n        if (cellRect.top < rect.top || cellRect.bottom > rect.bottom) {\n          const attrs = {\n            ...cell.attrs,\n            rowspan: Math.min(cellRect.bottom, rect.bottom) - Math.max(cellRect.top, rect.top)\n          };\n          if (cellRect.top < rect.top) {\n            cell = cell.type.createAndFill(attrs);\n          } else {\n            cell = cell.type.create(attrs, cell.content);\n          }\n        }\n        rowContent.push(cell);\n      }\n      rows.push(table.child(row).copy(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(rowContent)));\n    }\n    const fragment = this.isColSelection() && this.isRowSelection() ? table : rows;\n    return new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(fragment), 1, 1);\n  }\n  replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice.empty) {\n    const mapFrom = tr.steps.length, ranges = this.ranges;\n    for (let i = 0; i < ranges.length; i++) {\n      const { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n      tr.replace(\n        mapping.map($from.pos),\n        mapping.map($to.pos),\n        i ? prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice.empty : content\n      );\n    }\n    const sel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.findFrom(\n      tr.doc.resolve(tr.mapping.slice(mapFrom).map(this.to)),\n      -1\n    );\n    if (sel) tr.setSelection(sel);\n  }\n  replaceWith(tr, node) {\n    this.replace(tr, new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(node), 0, 0));\n  }\n  forEachCell(f) {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const cells = map.cellsInRect(\n      map.rectBetween(\n        this.$anchorCell.pos - tableStart,\n        this.$headCell.pos - tableStart\n      )\n    );\n    for (let i = 0; i < cells.length; i++) {\n      f(table.nodeAt(cells[i]), tableStart + cells[i]);\n    }\n  }\n  // True if this selection goes all the way from the top to the\n  // bottom of the table.\n  isColSelection() {\n    const anchorTop = this.$anchorCell.index(-1);\n    const headTop = this.$headCell.index(-1);\n    if (Math.min(anchorTop, headTop) > 0) return false;\n    const anchorBottom = anchorTop + this.$anchorCell.nodeAfter.attrs.rowspan;\n    const headBottom = headTop + this.$headCell.nodeAfter.attrs.rowspan;\n    return Math.max(anchorBottom, headBottom) == this.$headCell.node(-1).childCount;\n  }\n  // Returns the smallest column selection that covers the given anchor\n  // and head cell.\n  static colSelection($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const anchorRect = map.findCell($anchorCell.pos - tableStart);\n    const headRect = map.findCell($headCell.pos - tableStart);\n    const doc = $anchorCell.node(0);\n    if (anchorRect.top <= headRect.top) {\n      if (anchorRect.top > 0)\n        $anchorCell = doc.resolve(tableStart + map.map[anchorRect.left]);\n      if (headRect.bottom < map.height)\n        $headCell = doc.resolve(\n          tableStart + map.map[map.width * (map.height - 1) + headRect.right - 1]\n        );\n    } else {\n      if (headRect.top > 0)\n        $headCell = doc.resolve(tableStart + map.map[headRect.left]);\n      if (anchorRect.bottom < map.height)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[map.width * (map.height - 1) + anchorRect.right - 1]\n        );\n    }\n    return new _CellSelection($anchorCell, $headCell);\n  }\n  // True if this selection goes all the way from the left to the\n  // right of the table.\n  isRowSelection() {\n    const table = this.$anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = this.$anchorCell.start(-1);\n    const anchorLeft = map.colCount(this.$anchorCell.pos - tableStart);\n    const headLeft = map.colCount(this.$headCell.pos - tableStart);\n    if (Math.min(anchorLeft, headLeft) > 0) return false;\n    const anchorRight = anchorLeft + this.$anchorCell.nodeAfter.attrs.colspan;\n    const headRight = headLeft + this.$headCell.nodeAfter.attrs.colspan;\n    return Math.max(anchorRight, headRight) == map.width;\n  }\n  eq(other) {\n    return other instanceof _CellSelection && other.$anchorCell.pos == this.$anchorCell.pos && other.$headCell.pos == this.$headCell.pos;\n  }\n  // Returns the smallest row selection that covers the given anchor\n  // and head cell.\n  static rowSelection($anchorCell, $headCell = $anchorCell) {\n    const table = $anchorCell.node(-1);\n    const map = TableMap.get(table);\n    const tableStart = $anchorCell.start(-1);\n    const anchorRect = map.findCell($anchorCell.pos - tableStart);\n    const headRect = map.findCell($headCell.pos - tableStart);\n    const doc = $anchorCell.node(0);\n    if (anchorRect.left <= headRect.left) {\n      if (anchorRect.left > 0)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[anchorRect.top * map.width]\n        );\n      if (headRect.right < map.width)\n        $headCell = doc.resolve(\n          tableStart + map.map[map.width * (headRect.top + 1) - 1]\n        );\n    } else {\n      if (headRect.left > 0)\n        $headCell = doc.resolve(tableStart + map.map[headRect.top * map.width]);\n      if (anchorRect.right < map.width)\n        $anchorCell = doc.resolve(\n          tableStart + map.map[map.width * (anchorRect.top + 1) - 1]\n        );\n    }\n    return new _CellSelection($anchorCell, $headCell);\n  }\n  toJSON() {\n    return {\n      type: \"cell\",\n      anchor: this.$anchorCell.pos,\n      head: this.$headCell.pos\n    };\n  }\n  static fromJSON(doc, json) {\n    return new _CellSelection(doc.resolve(json.anchor), doc.resolve(json.head));\n  }\n  static create(doc, anchorCell, headCell = anchorCell) {\n    return new _CellSelection(doc.resolve(anchorCell), doc.resolve(headCell));\n  }\n  getBookmark() {\n    return new CellBookmark(this.$anchorCell.pos, this.$headCell.pos);\n  }\n};\nCellSelection.prototype.visible = false;\nprosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.jsonID(\"cell\", CellSelection);\nvar CellBookmark = class _CellBookmark {\n  constructor(anchor, head) {\n    this.anchor = anchor;\n    this.head = head;\n  }\n  map(mapping) {\n    return new _CellBookmark(mapping.map(this.anchor), mapping.map(this.head));\n  }\n  resolve(doc) {\n    const $anchorCell = doc.resolve(this.anchor), $headCell = doc.resolve(this.head);\n    if ($anchorCell.parent.type.spec.tableRole == \"row\" && $headCell.parent.type.spec.tableRole == \"row\" && $anchorCell.index() < $anchorCell.parent.childCount && $headCell.index() < $headCell.parent.childCount && inSameTable($anchorCell, $headCell))\n      return new CellSelection($anchorCell, $headCell);\n    else return prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near($headCell, 1);\n  }\n};\nfunction drawCellSelection(state) {\n  if (!(state.selection instanceof CellSelection)) return null;\n  const cells = [];\n  state.selection.forEachCell((node, pos) => {\n    cells.push(\n      prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.node(pos, pos + node.nodeSize, { class: \"selectedCell\" })\n    );\n  });\n  return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.create(state.doc, cells);\n}\nfunction isCellBoundarySelection({ $from, $to }) {\n  if ($from.pos == $to.pos || $from.pos < $to.pos - 6) return false;\n  let afterFrom = $from.pos;\n  let beforeTo = $to.pos;\n  let depth = $from.depth;\n  for (; depth >= 0; depth--, afterFrom++)\n    if ($from.after(depth + 1) < $from.end(depth)) break;\n  for (let d = $to.depth; d >= 0; d--, beforeTo--)\n    if ($to.before(d + 1) > $to.start(d)) break;\n  return afterFrom == beforeTo && /row|table/.test($from.node(depth).type.spec.tableRole);\n}\nfunction isTextSelectionAcrossCells({ $from, $to }) {\n  let fromCellBoundaryNode;\n  let toCellBoundaryNode;\n  for (let i = $from.depth; i > 0; i--) {\n    const node = $from.node(i);\n    if (node.type.spec.tableRole === \"cell\" || node.type.spec.tableRole === \"header_cell\") {\n      fromCellBoundaryNode = node;\n      break;\n    }\n  }\n  for (let i = $to.depth; i > 0; i--) {\n    const node = $to.node(i);\n    if (node.type.spec.tableRole === \"cell\" || node.type.spec.tableRole === \"header_cell\") {\n      toCellBoundaryNode = node;\n      break;\n    }\n  }\n  return fromCellBoundaryNode !== toCellBoundaryNode && $to.parentOffset === 0;\n}\nfunction normalizeSelection(state, tr, allowTableNodeSelection) {\n  const sel = (tr || state).selection;\n  const doc = (tr || state).doc;\n  let normalize;\n  let role;\n  if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection && (role = sel.node.type.spec.tableRole)) {\n    if (role == \"cell\" || role == \"header_cell\") {\n      normalize = CellSelection.create(doc, sel.from);\n    } else if (role == \"row\") {\n      const $cell = doc.resolve(sel.from + 1);\n      normalize = CellSelection.rowSelection($cell, $cell);\n    } else if (!allowTableNodeSelection) {\n      const map = TableMap.get(sel.node);\n      const start = sel.from + 1;\n      const lastCell = start + map.map[map.width * map.height - 1];\n      normalize = CellSelection.create(doc, start + 1, lastCell);\n    }\n  } else if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection && isCellBoundarySelection(sel)) {\n    normalize = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(doc, sel.from);\n  } else if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection && isTextSelectionAcrossCells(sel)) {\n    normalize = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(doc, sel.$from.start(), sel.$from.end());\n  }\n  if (normalize) (tr || (tr = state.tr)).setSelection(normalize);\n  return tr;\n}\n\n// src/fixtables.ts\n\nvar fixTablesKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(\"fix-tables\");\nfunction changedDescendants(old, cur, offset, f) {\n  const oldSize = old.childCount, curSize = cur.childCount;\n  outer: for (let i = 0, j = 0; i < curSize; i++) {\n    const child = cur.child(i);\n    for (let scan = j, e = Math.min(oldSize, i + 3); scan < e; scan++) {\n      if (old.child(scan) == child) {\n        j = scan + 1;\n        offset += child.nodeSize;\n        continue outer;\n      }\n    }\n    f(child, offset);\n    if (j < oldSize && old.child(j).sameMarkup(child))\n      changedDescendants(old.child(j), child, offset + 1, f);\n    else child.nodesBetween(0, child.content.size, f, offset + 1);\n    offset += child.nodeSize;\n  }\n}\nfunction fixTables(state, oldState) {\n  let tr;\n  const check = (node, pos) => {\n    if (node.type.spec.tableRole == \"table\")\n      tr = fixTable(state, node, pos, tr);\n  };\n  if (!oldState) state.doc.descendants(check);\n  else if (oldState.doc != state.doc)\n    changedDescendants(oldState.doc, state.doc, 0, check);\n  return tr;\n}\nfunction fixTable(state, table, tablePos, tr) {\n  const map = TableMap.get(table);\n  if (!map.problems) return tr;\n  if (!tr) tr = state.tr;\n  const mustAdd = [];\n  for (let i = 0; i < map.height; i++) mustAdd.push(0);\n  for (let i = 0; i < map.problems.length; i++) {\n    const prob = map.problems[i];\n    if (prob.type == \"collision\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell) continue;\n      const attrs = cell.attrs;\n      for (let j = 0; j < attrs.rowspan; j++) mustAdd[prob.row + j] += prob.n;\n      tr.setNodeMarkup(\n        tr.mapping.map(tablePos + 1 + prob.pos),\n        null,\n        removeColSpan(attrs, attrs.colspan - prob.n, prob.n)\n      );\n    } else if (prob.type == \"missing\") {\n      mustAdd[prob.row] += prob.n;\n    } else if (prob.type == \"overlong_rowspan\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell) continue;\n      tr.setNodeMarkup(tr.mapping.map(tablePos + 1 + prob.pos), null, {\n        ...cell.attrs,\n        rowspan: cell.attrs.rowspan - prob.n\n      });\n    } else if (prob.type == \"colwidth mismatch\") {\n      const cell = table.nodeAt(prob.pos);\n      if (!cell) continue;\n      tr.setNodeMarkup(tr.mapping.map(tablePos + 1 + prob.pos), null, {\n        ...cell.attrs,\n        colwidth: prob.colwidth\n      });\n    } else if (prob.type == \"zero_sized\") {\n      const pos = tr.mapping.map(tablePos);\n      tr.delete(pos, pos + table.nodeSize);\n    }\n  }\n  let first, last;\n  for (let i = 0; i < mustAdd.length; i++)\n    if (mustAdd[i]) {\n      if (first == null) first = i;\n      last = i;\n    }\n  for (let i = 0, pos = tablePos + 1; i < map.height; i++) {\n    const row = table.child(i);\n    const end = pos + row.nodeSize;\n    const add = mustAdd[i];\n    if (add > 0) {\n      let role = \"cell\";\n      if (row.firstChild) {\n        role = row.firstChild.type.spec.tableRole;\n      }\n      const nodes = [];\n      for (let j = 0; j < add; j++) {\n        const node = tableNodeTypes(state.schema)[role].createAndFill();\n        if (node) nodes.push(node);\n      }\n      const side = (i == 0 || first == i - 1) && last == i ? pos + 1 : end - 1;\n      tr.insert(tr.mapping.map(side), nodes);\n    }\n    pos = end;\n  }\n  return tr.setMeta(fixTablesKey, { fixTables: true });\n}\n\n// src/input.ts\n\n\n\n\n// src/commands.ts\n\n\nfunction selectedRect(state) {\n  const sel = state.selection;\n  const $pos = selectionCell(state);\n  const table = $pos.node(-1);\n  const tableStart = $pos.start(-1);\n  const map = TableMap.get(table);\n  const rect = sel instanceof CellSelection ? map.rectBetween(\n    sel.$anchorCell.pos - tableStart,\n    sel.$headCell.pos - tableStart\n  ) : map.findCell($pos.pos - tableStart);\n  return { ...rect, tableStart, map, table };\n}\nfunction addColumn(tr, { map, tableStart, table }, col) {\n  let refColumn = col > 0 ? -1 : 0;\n  if (columnIsHeader(map, table, col + refColumn)) {\n    refColumn = col == 0 || col == map.width ? null : 0;\n  }\n  for (let row = 0; row < map.height; row++) {\n    const index = row * map.width + col;\n    if (col > 0 && col < map.width && map.map[index - 1] == map.map[index]) {\n      const pos = map.map[index];\n      const cell = table.nodeAt(pos);\n      tr.setNodeMarkup(\n        tr.mapping.map(tableStart + pos),\n        null,\n        addColSpan(cell.attrs, col - map.colCount(pos))\n      );\n      row += cell.attrs.rowspan - 1;\n    } else {\n      const type = refColumn == null ? tableNodeTypes(table.type.schema).cell : table.nodeAt(map.map[index + refColumn]).type;\n      const pos = map.positionAt(row, col, table);\n      tr.insert(tr.mapping.map(tableStart + pos), type.createAndFill());\n    }\n  }\n  return tr;\n}\nfunction addColumnBefore(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addColumn(state.tr, rect, rect.left));\n  }\n  return true;\n}\nfunction addColumnAfter(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addColumn(state.tr, rect, rect.right));\n  }\n  return true;\n}\nfunction removeColumn(tr, { map, table, tableStart }, col) {\n  const mapStart = tr.mapping.maps.length;\n  for (let row = 0; row < map.height; ) {\n    const index = row * map.width + col;\n    const pos = map.map[index];\n    const cell = table.nodeAt(pos);\n    const attrs = cell.attrs;\n    if (col > 0 && map.map[index - 1] == pos || col < map.width - 1 && map.map[index + 1] == pos) {\n      tr.setNodeMarkup(\n        tr.mapping.slice(mapStart).map(tableStart + pos),\n        null,\n        removeColSpan(attrs, col - map.colCount(pos))\n      );\n    } else {\n      const start = tr.mapping.slice(mapStart).map(tableStart + pos);\n      tr.delete(start, start + cell.nodeSize);\n    }\n    row += attrs.rowspan;\n  }\n}\nfunction deleteColumn(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    const tr = state.tr;\n    if (rect.left == 0 && rect.right == rect.map.width) return false;\n    for (let i = rect.right - 1; ; i--) {\n      removeColumn(tr, rect, i);\n      if (i == rect.left) break;\n      const table = rect.tableStart ? tr.doc.nodeAt(rect.tableStart - 1) : tr.doc;\n      if (!table) {\n        throw RangeError(\"No table found\");\n      }\n      rect.table = table;\n      rect.map = TableMap.get(table);\n    }\n    dispatch(tr);\n  }\n  return true;\n}\nfunction rowIsHeader(map, table, row) {\n  var _a;\n  const headerCell = tableNodeTypes(table.type.schema).header_cell;\n  for (let col = 0; col < map.width; col++)\n    if (((_a = table.nodeAt(map.map[col + row * map.width])) == null ? void 0 : _a.type) != headerCell)\n      return false;\n  return true;\n}\nfunction addRow(tr, { map, tableStart, table }, row) {\n  var _a;\n  let rowPos = tableStart;\n  for (let i = 0; i < row; i++) rowPos += table.child(i).nodeSize;\n  const cells = [];\n  let refRow = row > 0 ? -1 : 0;\n  if (rowIsHeader(map, table, row + refRow))\n    refRow = row == 0 || row == map.height ? null : 0;\n  for (let col = 0, index = map.width * row; col < map.width; col++, index++) {\n    if (row > 0 && row < map.height && map.map[index] == map.map[index - map.width]) {\n      const pos = map.map[index];\n      const attrs = table.nodeAt(pos).attrs;\n      tr.setNodeMarkup(tableStart + pos, null, {\n        ...attrs,\n        rowspan: attrs.rowspan + 1\n      });\n      col += attrs.colspan - 1;\n    } else {\n      const type = refRow == null ? tableNodeTypes(table.type.schema).cell : (_a = table.nodeAt(map.map[index + refRow * map.width])) == null ? void 0 : _a.type;\n      const node = type == null ? void 0 : type.createAndFill();\n      if (node) cells.push(node);\n    }\n  }\n  tr.insert(rowPos, tableNodeTypes(table.type.schema).row.create(null, cells));\n  return tr;\n}\nfunction addRowBefore(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addRow(state.tr, rect, rect.top));\n  }\n  return true;\n}\nfunction addRowAfter(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state);\n    dispatch(addRow(state.tr, rect, rect.bottom));\n  }\n  return true;\n}\nfunction removeRow(tr, { map, table, tableStart }, row) {\n  let rowPos = 0;\n  for (let i = 0; i < row; i++) rowPos += table.child(i).nodeSize;\n  const nextRow = rowPos + table.child(row).nodeSize;\n  const mapFrom = tr.mapping.maps.length;\n  tr.delete(rowPos + tableStart, nextRow + tableStart);\n  const seen = /* @__PURE__ */ new Set();\n  for (let col = 0, index = row * map.width; col < map.width; col++, index++) {\n    const pos = map.map[index];\n    if (seen.has(pos)) continue;\n    seen.add(pos);\n    if (row > 0 && pos == map.map[index - map.width]) {\n      const attrs = table.nodeAt(pos).attrs;\n      tr.setNodeMarkup(tr.mapping.slice(mapFrom).map(pos + tableStart), null, {\n        ...attrs,\n        rowspan: attrs.rowspan - 1\n      });\n      col += attrs.colspan - 1;\n    } else if (row < map.height && pos == map.map[index + map.width]) {\n      const cell = table.nodeAt(pos);\n      const attrs = cell.attrs;\n      const copy = cell.type.create(\n        { ...attrs, rowspan: cell.attrs.rowspan - 1 },\n        cell.content\n      );\n      const newPos = map.positionAt(row + 1, col, table);\n      tr.insert(tr.mapping.slice(mapFrom).map(tableStart + newPos), copy);\n      col += attrs.colspan - 1;\n    }\n  }\n}\nfunction deleteRow(state, dispatch) {\n  if (!isInTable(state)) return false;\n  if (dispatch) {\n    const rect = selectedRect(state), tr = state.tr;\n    if (rect.top == 0 && rect.bottom == rect.map.height) return false;\n    for (let i = rect.bottom - 1; ; i--) {\n      removeRow(tr, rect, i);\n      if (i == rect.top) break;\n      const table = rect.tableStart ? tr.doc.nodeAt(rect.tableStart - 1) : tr.doc;\n      if (!table) {\n        throw RangeError(\"No table found\");\n      }\n      rect.table = table;\n      rect.map = TableMap.get(rect.table);\n    }\n    dispatch(tr);\n  }\n  return true;\n}\nfunction isEmpty(cell) {\n  const c = cell.content;\n  return c.childCount == 1 && c.child(0).isTextblock && c.child(0).childCount == 0;\n}\nfunction cellsOverlapRectangle({ width, height, map }, rect) {\n  let indexTop = rect.top * width + rect.left, indexLeft = indexTop;\n  let indexBottom = (rect.bottom - 1) * width + rect.left, indexRight = indexTop + (rect.right - rect.left - 1);\n  for (let i = rect.top; i < rect.bottom; i++) {\n    if (rect.left > 0 && map[indexLeft] == map[indexLeft - 1] || rect.right < width && map[indexRight] == map[indexRight + 1])\n      return true;\n    indexLeft += width;\n    indexRight += width;\n  }\n  for (let i = rect.left; i < rect.right; i++) {\n    if (rect.top > 0 && map[indexTop] == map[indexTop - width] || rect.bottom < height && map[indexBottom] == map[indexBottom + width])\n      return true;\n    indexTop++;\n    indexBottom++;\n  }\n  return false;\n}\nfunction mergeCells(state, dispatch) {\n  const sel = state.selection;\n  if (!(sel instanceof CellSelection) || sel.$anchorCell.pos == sel.$headCell.pos)\n    return false;\n  const rect = selectedRect(state), { map } = rect;\n  if (cellsOverlapRectangle(map, rect)) return false;\n  if (dispatch) {\n    const tr = state.tr;\n    const seen = {};\n    let content = prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.empty;\n    let mergedPos;\n    let mergedCell;\n    for (let row = rect.top; row < rect.bottom; row++) {\n      for (let col = rect.left; col < rect.right; col++) {\n        const cellPos = map.map[row * map.width + col];\n        const cell = rect.table.nodeAt(cellPos);\n        if (seen[cellPos] || !cell) continue;\n        seen[cellPos] = true;\n        if (mergedPos == null) {\n          mergedPos = cellPos;\n          mergedCell = cell;\n        } else {\n          if (!isEmpty(cell)) content = content.append(cell.content);\n          const mapped = tr.mapping.map(cellPos + rect.tableStart);\n          tr.delete(mapped, mapped + cell.nodeSize);\n        }\n      }\n    }\n    if (mergedPos == null || mergedCell == null) {\n      return true;\n    }\n    tr.setNodeMarkup(mergedPos + rect.tableStart, null, {\n      ...addColSpan(\n        mergedCell.attrs,\n        mergedCell.attrs.colspan,\n        rect.right - rect.left - mergedCell.attrs.colspan\n      ),\n      rowspan: rect.bottom - rect.top\n    });\n    if (content.size) {\n      const end = mergedPos + 1 + mergedCell.content.size;\n      const start = isEmpty(mergedCell) ? mergedPos + 1 : end;\n      tr.replaceWith(start + rect.tableStart, end + rect.tableStart, content);\n    }\n    tr.setSelection(\n      new CellSelection(tr.doc.resolve(mergedPos + rect.tableStart))\n    );\n    dispatch(tr);\n  }\n  return true;\n}\nfunction splitCell(state, dispatch) {\n  const nodeTypes = tableNodeTypes(state.schema);\n  return splitCellWithType(({ node }) => {\n    return nodeTypes[node.type.spec.tableRole];\n  })(state, dispatch);\n}\nfunction splitCellWithType(getCellType) {\n  return (state, dispatch) => {\n    var _a;\n    const sel = state.selection;\n    let cellNode;\n    let cellPos;\n    if (!(sel instanceof CellSelection)) {\n      cellNode = cellWrapping(sel.$from);\n      if (!cellNode) return false;\n      cellPos = (_a = cellAround(sel.$from)) == null ? void 0 : _a.pos;\n    } else {\n      if (sel.$anchorCell.pos != sel.$headCell.pos) return false;\n      cellNode = sel.$anchorCell.nodeAfter;\n      cellPos = sel.$anchorCell.pos;\n    }\n    if (cellNode == null || cellPos == null) {\n      return false;\n    }\n    if (cellNode.attrs.colspan == 1 && cellNode.attrs.rowspan == 1) {\n      return false;\n    }\n    if (dispatch) {\n      let baseAttrs = cellNode.attrs;\n      const attrs = [];\n      const colwidth = baseAttrs.colwidth;\n      if (baseAttrs.rowspan > 1) baseAttrs = { ...baseAttrs, rowspan: 1 };\n      if (baseAttrs.colspan > 1) baseAttrs = { ...baseAttrs, colspan: 1 };\n      const rect = selectedRect(state), tr = state.tr;\n      for (let i = 0; i < rect.right - rect.left; i++)\n        attrs.push(\n          colwidth ? {\n            ...baseAttrs,\n            colwidth: colwidth && colwidth[i] ? [colwidth[i]] : null\n          } : baseAttrs\n        );\n      let lastCell;\n      for (let row = rect.top; row < rect.bottom; row++) {\n        let pos = rect.map.positionAt(row, rect.left, rect.table);\n        if (row == rect.top) pos += cellNode.nodeSize;\n        for (let col = rect.left, i = 0; col < rect.right; col++, i++) {\n          if (col == rect.left && row == rect.top) continue;\n          tr.insert(\n            lastCell = tr.mapping.map(pos + rect.tableStart, 1),\n            getCellType({ node: cellNode, row, col }).createAndFill(attrs[i])\n          );\n        }\n      }\n      tr.setNodeMarkup(\n        cellPos,\n        getCellType({ node: cellNode, row: rect.top, col: rect.left }),\n        attrs[0]\n      );\n      if (sel instanceof CellSelection)\n        tr.setSelection(\n          new CellSelection(\n            tr.doc.resolve(sel.$anchorCell.pos),\n            lastCell ? tr.doc.resolve(lastCell) : void 0\n          )\n        );\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction setCellAttr(name, value) {\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    const $cell = selectionCell(state);\n    if ($cell.nodeAfter.attrs[name] === value) return false;\n    if (dispatch) {\n      const tr = state.tr;\n      if (state.selection instanceof CellSelection)\n        state.selection.forEachCell((node, pos) => {\n          if (node.attrs[name] !== value)\n            tr.setNodeMarkup(pos, null, {\n              ...node.attrs,\n              [name]: value\n            });\n        });\n      else\n        tr.setNodeMarkup($cell.pos, null, {\n          ...$cell.nodeAfter.attrs,\n          [name]: value\n        });\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction deprecated_toggleHeader(type) {\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    if (dispatch) {\n      const types = tableNodeTypes(state.schema);\n      const rect = selectedRect(state), tr = state.tr;\n      const cells = rect.map.cellsInRect(\n        type == \"column\" ? {\n          left: rect.left,\n          top: 0,\n          right: rect.right,\n          bottom: rect.map.height\n        } : type == \"row\" ? {\n          left: 0,\n          top: rect.top,\n          right: rect.map.width,\n          bottom: rect.bottom\n        } : rect\n      );\n      const nodes = cells.map((pos) => rect.table.nodeAt(pos));\n      for (let i = 0; i < cells.length; i++)\n        if (nodes[i].type == types.header_cell)\n          tr.setNodeMarkup(\n            rect.tableStart + cells[i],\n            types.cell,\n            nodes[i].attrs\n          );\n      if (tr.steps.length == 0)\n        for (let i = 0; i < cells.length; i++)\n          tr.setNodeMarkup(\n            rect.tableStart + cells[i],\n            types.header_cell,\n            nodes[i].attrs\n          );\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nfunction isHeaderEnabledByType(type, rect, types) {\n  const cellPositions = rect.map.cellsInRect({\n    left: 0,\n    top: 0,\n    right: type == \"row\" ? rect.map.width : 1,\n    bottom: type == \"column\" ? rect.map.height : 1\n  });\n  for (let i = 0; i < cellPositions.length; i++) {\n    const cell = rect.table.nodeAt(cellPositions[i]);\n    if (cell && cell.type !== types.header_cell) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction toggleHeader(type, options) {\n  options = options || { useDeprecatedLogic: false };\n  if (options.useDeprecatedLogic) return deprecated_toggleHeader(type);\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    if (dispatch) {\n      const types = tableNodeTypes(state.schema);\n      const rect = selectedRect(state), tr = state.tr;\n      const isHeaderRowEnabled = isHeaderEnabledByType(\"row\", rect, types);\n      const isHeaderColumnEnabled = isHeaderEnabledByType(\n        \"column\",\n        rect,\n        types\n      );\n      const isHeaderEnabled = type === \"column\" ? isHeaderRowEnabled : type === \"row\" ? isHeaderColumnEnabled : false;\n      const selectionStartsAt = isHeaderEnabled ? 1 : 0;\n      const cellsRect = type == \"column\" ? {\n        left: 0,\n        top: selectionStartsAt,\n        right: 1,\n        bottom: rect.map.height\n      } : type == \"row\" ? {\n        left: selectionStartsAt,\n        top: 0,\n        right: rect.map.width,\n        bottom: 1\n      } : rect;\n      const newType = type == \"column\" ? isHeaderColumnEnabled ? types.cell : types.header_cell : type == \"row\" ? isHeaderRowEnabled ? types.cell : types.header_cell : types.cell;\n      rect.map.cellsInRect(cellsRect).forEach((relativeCellPos) => {\n        const cellPos = relativeCellPos + rect.tableStart;\n        const cell = tr.doc.nodeAt(cellPos);\n        if (cell) {\n          tr.setNodeMarkup(cellPos, newType, cell.attrs);\n        }\n      });\n      dispatch(tr);\n    }\n    return true;\n  };\n}\nvar toggleHeaderRow = toggleHeader(\"row\", {\n  useDeprecatedLogic: true\n});\nvar toggleHeaderColumn = toggleHeader(\"column\", {\n  useDeprecatedLogic: true\n});\nvar toggleHeaderCell = toggleHeader(\"cell\", {\n  useDeprecatedLogic: true\n});\nfunction findNextCell($cell, dir) {\n  if (dir < 0) {\n    const before = $cell.nodeBefore;\n    if (before) return $cell.pos - before.nodeSize;\n    for (let row = $cell.index(-1) - 1, rowEnd = $cell.before(); row >= 0; row--) {\n      const rowNode = $cell.node(-1).child(row);\n      const lastChild = rowNode.lastChild;\n      if (lastChild) {\n        return rowEnd - 1 - lastChild.nodeSize;\n      }\n      rowEnd -= rowNode.nodeSize;\n    }\n  } else {\n    if ($cell.index() < $cell.parent.childCount - 1) {\n      return $cell.pos + $cell.nodeAfter.nodeSize;\n    }\n    const table = $cell.node(-1);\n    for (let row = $cell.indexAfter(-1), rowStart = $cell.after(); row < table.childCount; row++) {\n      const rowNode = table.child(row);\n      if (rowNode.childCount) return rowStart + 1;\n      rowStart += rowNode.nodeSize;\n    }\n  }\n  return null;\n}\nfunction goToNextCell(direction) {\n  return function(state, dispatch) {\n    if (!isInTable(state)) return false;\n    const cell = findNextCell(selectionCell(state), direction);\n    if (cell == null) return false;\n    if (dispatch) {\n      const $cell = state.doc.resolve(cell);\n      dispatch(\n        state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.between($cell, moveCellForward($cell))).scrollIntoView()\n      );\n    }\n    return true;\n  };\n}\nfunction deleteTable(state, dispatch) {\n  const $pos = state.selection.$anchor;\n  for (let d = $pos.depth; d > 0; d--) {\n    const node = $pos.node(d);\n    if (node.type.spec.tableRole == \"table\") {\n      if (dispatch)\n        dispatch(\n          state.tr.delete($pos.before(d), $pos.after(d)).scrollIntoView()\n        );\n      return true;\n    }\n  }\n  return false;\n}\nfunction deleteCellSelection(state, dispatch) {\n  const sel = state.selection;\n  if (!(sel instanceof CellSelection)) return false;\n  if (dispatch) {\n    const tr = state.tr;\n    const baseContent = tableNodeTypes(state.schema).cell.createAndFill().content;\n    sel.forEachCell((cell, pos) => {\n      if (!cell.content.eq(baseContent))\n        tr.replace(\n          tr.mapping.map(pos + 1),\n          tr.mapping.map(pos + cell.nodeSize - 1),\n          new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(baseContent, 0, 0)\n        );\n    });\n    if (tr.docChanged) dispatch(tr);\n  }\n  return true;\n}\n\n// src/copypaste.ts\n\n\nfunction pastedCells(slice) {\n  if (!slice.size) return null;\n  let { content, openStart, openEnd } = slice;\n  while (content.childCount == 1 && (openStart > 0 && openEnd > 0 || content.child(0).type.spec.tableRole == \"table\")) {\n    openStart--;\n    openEnd--;\n    content = content.child(0).content;\n  }\n  const first = content.child(0);\n  const role = first.type.spec.tableRole;\n  const schema = first.type.schema, rows = [];\n  if (role == \"row\") {\n    for (let i = 0; i < content.childCount; i++) {\n      let cells = content.child(i).content;\n      const left = i ? 0 : Math.max(0, openStart - 1);\n      const right = i < content.childCount - 1 ? 0 : Math.max(0, openEnd - 1);\n      if (left || right)\n        cells = fitSlice(\n          tableNodeTypes(schema).row,\n          new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(cells, left, right)\n        ).content;\n      rows.push(cells);\n    }\n  } else if (role == \"cell\" || role == \"header_cell\") {\n    rows.push(\n      openStart || openEnd ? fitSlice(\n        tableNodeTypes(schema).row,\n        new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(content, openStart, openEnd)\n      ).content : content\n    );\n  } else {\n    return null;\n  }\n  return ensureRectangular(schema, rows);\n}\nfunction ensureRectangular(schema, rows) {\n  const widths = [];\n  for (let i = 0; i < rows.length; i++) {\n    const row = rows[i];\n    for (let j = row.childCount - 1; j >= 0; j--) {\n      const { rowspan, colspan } = row.child(j).attrs;\n      for (let r = i; r < i + rowspan; r++)\n        widths[r] = (widths[r] || 0) + colspan;\n    }\n  }\n  let width = 0;\n  for (let r = 0; r < widths.length; r++) width = Math.max(width, widths[r]);\n  for (let r = 0; r < widths.length; r++) {\n    if (r >= rows.length) rows.push(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.empty);\n    if (widths[r] < width) {\n      const empty = tableNodeTypes(schema).cell.createAndFill();\n      const cells = [];\n      for (let i = widths[r]; i < width; i++) {\n        cells.push(empty);\n      }\n      rows[r] = rows[r].append(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells));\n    }\n  }\n  return { height: rows.length, width, rows };\n}\nfunction fitSlice(nodeType, slice) {\n  const node = nodeType.createAndFill();\n  const tr = new prosemirror_transform__WEBPACK_IMPORTED_MODULE_3__.Transform(node).replace(0, node.content.size, slice);\n  return tr.doc;\n}\nfunction clipCells({ width, height, rows }, newWidth, newHeight) {\n  if (width != newWidth) {\n    const added = [];\n    const newRows = [];\n    for (let row = 0; row < rows.length; row++) {\n      const frag = rows[row], cells = [];\n      for (let col = added[row] || 0, i = 0; col < newWidth; i++) {\n        let cell = frag.child(i % frag.childCount);\n        if (col + cell.attrs.colspan > newWidth)\n          cell = cell.type.createChecked(\n            removeColSpan(\n              cell.attrs,\n              cell.attrs.colspan,\n              col + cell.attrs.colspan - newWidth\n            ),\n            cell.content\n          );\n        cells.push(cell);\n        col += cell.attrs.colspan;\n        for (let j = 1; j < cell.attrs.rowspan; j++)\n          added[row + j] = (added[row + j] || 0) + cell.attrs.colspan;\n      }\n      newRows.push(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells));\n    }\n    rows = newRows;\n    width = newWidth;\n  }\n  if (height != newHeight) {\n    const newRows = [];\n    for (let row = 0, i = 0; row < newHeight; row++, i++) {\n      const cells = [], source = rows[i % height];\n      for (let j = 0; j < source.childCount; j++) {\n        let cell = source.child(j);\n        if (row + cell.attrs.rowspan > newHeight)\n          cell = cell.type.create(\n            {\n              ...cell.attrs,\n              rowspan: Math.max(1, newHeight - cell.attrs.rowspan)\n            },\n            cell.content\n          );\n        cells.push(cell);\n      }\n      newRows.push(prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells));\n    }\n    rows = newRows;\n    height = newHeight;\n  }\n  return { width, height, rows };\n}\nfunction growTable(tr, map, table, start, width, height, mapFrom) {\n  const schema = tr.doc.type.schema;\n  const types = tableNodeTypes(schema);\n  let empty;\n  let emptyHead;\n  if (width > map.width) {\n    for (let row = 0, rowEnd = 0; row < map.height; row++) {\n      const rowNode = table.child(row);\n      rowEnd += rowNode.nodeSize;\n      const cells = [];\n      let add;\n      if (rowNode.lastChild == null || rowNode.lastChild.type == types.cell)\n        add = empty || (empty = types.cell.createAndFill());\n      else add = emptyHead || (emptyHead = types.header_cell.createAndFill());\n      for (let i = map.width; i < width; i++) cells.push(add);\n      tr.insert(tr.mapping.slice(mapFrom).map(rowEnd - 1 + start), cells);\n    }\n  }\n  if (height > map.height) {\n    const cells = [];\n    for (let i = 0, start2 = (map.height - 1) * map.width; i < Math.max(map.width, width); i++) {\n      const header = i >= map.width ? false : table.nodeAt(map.map[start2 + i]).type == types.header_cell;\n      cells.push(\n        header ? emptyHead || (emptyHead = types.header_cell.createAndFill()) : empty || (empty = types.cell.createAndFill())\n      );\n    }\n    const emptyRow = types.row.create(null, prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(cells)), rows = [];\n    for (let i = map.height; i < height; i++) rows.push(emptyRow);\n    tr.insert(tr.mapping.slice(mapFrom).map(start + table.nodeSize - 2), rows);\n  }\n  return !!(empty || emptyHead);\n}\nfunction isolateHorizontal(tr, map, table, start, left, right, top, mapFrom) {\n  if (top == 0 || top == map.height) return false;\n  let found = false;\n  for (let col = left; col < right; col++) {\n    const index = top * map.width + col, pos = map.map[index];\n    if (map.map[index - map.width] == pos) {\n      found = true;\n      const cell = table.nodeAt(pos);\n      const { top: cellTop, left: cellLeft } = map.findCell(pos);\n      tr.setNodeMarkup(tr.mapping.slice(mapFrom).map(pos + start), null, {\n        ...cell.attrs,\n        rowspan: top - cellTop\n      });\n      tr.insert(\n        tr.mapping.slice(mapFrom).map(map.positionAt(top, cellLeft, table)),\n        cell.type.createAndFill({\n          ...cell.attrs,\n          rowspan: cellTop + cell.attrs.rowspan - top\n        })\n      );\n      col += cell.attrs.colspan - 1;\n    }\n  }\n  return found;\n}\nfunction isolateVertical(tr, map, table, start, top, bottom, left, mapFrom) {\n  if (left == 0 || left == map.width) return false;\n  let found = false;\n  for (let row = top; row < bottom; row++) {\n    const index = row * map.width + left, pos = map.map[index];\n    if (map.map[index - 1] == pos) {\n      found = true;\n      const cell = table.nodeAt(pos);\n      const cellLeft = map.colCount(pos);\n      const updatePos = tr.mapping.slice(mapFrom).map(pos + start);\n      tr.setNodeMarkup(\n        updatePos,\n        null,\n        removeColSpan(\n          cell.attrs,\n          left - cellLeft,\n          cell.attrs.colspan - (left - cellLeft)\n        )\n      );\n      tr.insert(\n        updatePos + cell.nodeSize,\n        cell.type.createAndFill(\n          removeColSpan(cell.attrs, 0, left - cellLeft)\n        )\n      );\n      row += cell.attrs.rowspan - 1;\n    }\n  }\n  return found;\n}\nfunction insertCells(state, dispatch, tableStart, rect, cells) {\n  let table = tableStart ? state.doc.nodeAt(tableStart - 1) : state.doc;\n  if (!table) {\n    throw new Error(\"No table found\");\n  }\n  let map = TableMap.get(table);\n  const { top, left } = rect;\n  const right = left + cells.width, bottom = top + cells.height;\n  const tr = state.tr;\n  let mapFrom = 0;\n  function recomp() {\n    table = tableStart ? tr.doc.nodeAt(tableStart - 1) : tr.doc;\n    if (!table) {\n      throw new Error(\"No table found\");\n    }\n    map = TableMap.get(table);\n    mapFrom = tr.mapping.maps.length;\n  }\n  if (growTable(tr, map, table, tableStart, right, bottom, mapFrom)) recomp();\n  if (isolateHorizontal(tr, map, table, tableStart, left, right, top, mapFrom))\n    recomp();\n  if (isolateHorizontal(tr, map, table, tableStart, left, right, bottom, mapFrom))\n    recomp();\n  if (isolateVertical(tr, map, table, tableStart, top, bottom, left, mapFrom))\n    recomp();\n  if (isolateVertical(tr, map, table, tableStart, top, bottom, right, mapFrom))\n    recomp();\n  for (let row = top; row < bottom; row++) {\n    const from = map.positionAt(row, left, table), to = map.positionAt(row, right, table);\n    tr.replace(\n      tr.mapping.slice(mapFrom).map(from + tableStart),\n      tr.mapping.slice(mapFrom).map(to + tableStart),\n      new prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Slice(cells.rows[row - top], 0, 0)\n    );\n  }\n  recomp();\n  tr.setSelection(\n    new CellSelection(\n      tr.doc.resolve(tableStart + map.positionAt(top, left, table)),\n      tr.doc.resolve(tableStart + map.positionAt(bottom - 1, right - 1, table))\n    )\n  );\n  dispatch(tr);\n}\n\n// src/input.ts\nvar handleKeyDown = (0,prosemirror_keymap__WEBPACK_IMPORTED_MODULE_4__.keydownHandler)({\n  ArrowLeft: arrow(\"horiz\", -1),\n  ArrowRight: arrow(\"horiz\", 1),\n  ArrowUp: arrow(\"vert\", -1),\n  ArrowDown: arrow(\"vert\", 1),\n  \"Shift-ArrowLeft\": shiftArrow(\"horiz\", -1),\n  \"Shift-ArrowRight\": shiftArrow(\"horiz\", 1),\n  \"Shift-ArrowUp\": shiftArrow(\"vert\", -1),\n  \"Shift-ArrowDown\": shiftArrow(\"vert\", 1),\n  Backspace: deleteCellSelection,\n  \"Mod-Backspace\": deleteCellSelection,\n  Delete: deleteCellSelection,\n  \"Mod-Delete\": deleteCellSelection\n});\nfunction maybeSetSelection(state, dispatch, selection) {\n  if (selection.eq(state.selection)) return false;\n  if (dispatch) dispatch(state.tr.setSelection(selection).scrollIntoView());\n  return true;\n}\nfunction arrow(axis, dir) {\n  return (state, dispatch, view) => {\n    if (!view) return false;\n    const sel = state.selection;\n    if (sel instanceof CellSelection) {\n      return maybeSetSelection(\n        state,\n        dispatch,\n        prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(sel.$headCell, dir)\n      );\n    }\n    if (axis != \"horiz\" && !sel.empty) return false;\n    const end = atEndOfCell(view, axis, dir);\n    if (end == null) return false;\n    if (axis == \"horiz\") {\n      return maybeSetSelection(\n        state,\n        dispatch,\n        prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(state.doc.resolve(sel.head + dir), dir)\n      );\n    } else {\n      const $cell = state.doc.resolve(end);\n      const $next = nextCell($cell, axis, dir);\n      let newSel;\n      if ($next) newSel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near($next, 1);\n      else if (dir < 0)\n        newSel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(state.doc.resolve($cell.before(-1)), -1);\n      else newSel = prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(state.doc.resolve($cell.after(-1)), 1);\n      return maybeSetSelection(state, dispatch, newSel);\n    }\n  };\n}\nfunction shiftArrow(axis, dir) {\n  return (state, dispatch, view) => {\n    if (!view) return false;\n    const sel = state.selection;\n    let cellSel;\n    if (sel instanceof CellSelection) {\n      cellSel = sel;\n    } else {\n      const end = atEndOfCell(view, axis, dir);\n      if (end == null) return false;\n      cellSel = new CellSelection(state.doc.resolve(end));\n    }\n    const $head = nextCell(cellSel.$headCell, axis, dir);\n    if (!$head) return false;\n    return maybeSetSelection(\n      state,\n      dispatch,\n      new CellSelection(cellSel.$anchorCell, $head)\n    );\n  };\n}\nfunction handleTripleClick(view, pos) {\n  const doc = view.state.doc, $cell = cellAround(doc.resolve(pos));\n  if (!$cell) return false;\n  view.dispatch(view.state.tr.setSelection(new CellSelection($cell)));\n  return true;\n}\nfunction handlePaste(view, _, slice) {\n  if (!isInTable(view.state)) return false;\n  let cells = pastedCells(slice);\n  const sel = view.state.selection;\n  if (sel instanceof CellSelection) {\n    if (!cells)\n      cells = {\n        width: 1,\n        height: 1,\n        rows: [\n          prosemirror_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(\n            fitSlice(tableNodeTypes(view.state.schema).cell, slice)\n          )\n        ]\n      };\n    const table = sel.$anchorCell.node(-1);\n    const start = sel.$anchorCell.start(-1);\n    const rect = TableMap.get(table).rectBetween(\n      sel.$anchorCell.pos - start,\n      sel.$headCell.pos - start\n    );\n    cells = clipCells(cells, rect.right - rect.left, rect.bottom - rect.top);\n    insertCells(view.state, view.dispatch, start, rect, cells);\n    return true;\n  } else if (cells) {\n    const $cell = selectionCell(view.state);\n    const start = $cell.start(-1);\n    insertCells(\n      view.state,\n      view.dispatch,\n      start,\n      TableMap.get($cell.node(-1)).findCell($cell.pos - start),\n      cells\n    );\n    return true;\n  } else {\n    return false;\n  }\n}\nfunction handleMouseDown(view, startEvent) {\n  var _a;\n  if (startEvent.ctrlKey || startEvent.metaKey) return;\n  const startDOMCell = domInCell(view, startEvent.target);\n  let $anchor;\n  if (startEvent.shiftKey && view.state.selection instanceof CellSelection) {\n    setCellSelection(view.state.selection.$anchorCell, startEvent);\n    startEvent.preventDefault();\n  } else if (startEvent.shiftKey && startDOMCell && ($anchor = cellAround(view.state.selection.$anchor)) != null && ((_a = cellUnderMouse(view, startEvent)) == null ? void 0 : _a.pos) != $anchor.pos) {\n    setCellSelection($anchor, startEvent);\n    startEvent.preventDefault();\n  } else if (!startDOMCell) {\n    return;\n  }\n  function setCellSelection($anchor2, event) {\n    let $head = cellUnderMouse(view, event);\n    const starting = tableEditingKey.getState(view.state) == null;\n    if (!$head || !inSameTable($anchor2, $head)) {\n      if (starting) $head = $anchor2;\n      else return;\n    }\n    const selection = new CellSelection($anchor2, $head);\n    if (starting || !view.state.selection.eq(selection)) {\n      const tr = view.state.tr.setSelection(selection);\n      if (starting) tr.setMeta(tableEditingKey, $anchor2.pos);\n      view.dispatch(tr);\n    }\n  }\n  function stop() {\n    view.root.removeEventListener(\"mouseup\", stop);\n    view.root.removeEventListener(\"dragstart\", stop);\n    view.root.removeEventListener(\"mousemove\", move);\n    if (tableEditingKey.getState(view.state) != null)\n      view.dispatch(view.state.tr.setMeta(tableEditingKey, -1));\n  }\n  function move(_event) {\n    const event = _event;\n    const anchor = tableEditingKey.getState(view.state);\n    let $anchor2;\n    if (anchor != null) {\n      $anchor2 = view.state.doc.resolve(anchor);\n    } else if (domInCell(view, event.target) != startDOMCell) {\n      $anchor2 = cellUnderMouse(view, startEvent);\n      if (!$anchor2) return stop();\n    }\n    if ($anchor2) setCellSelection($anchor2, event);\n  }\n  view.root.addEventListener(\"mouseup\", stop);\n  view.root.addEventListener(\"dragstart\", stop);\n  view.root.addEventListener(\"mousemove\", move);\n}\nfunction atEndOfCell(view, axis, dir) {\n  if (!(view.state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection)) return null;\n  const { $head } = view.state.selection;\n  for (let d = $head.depth - 1; d >= 0; d--) {\n    const parent = $head.node(d), index = dir < 0 ? $head.index(d) : $head.indexAfter(d);\n    if (index != (dir < 0 ? 0 : parent.childCount)) return null;\n    if (parent.type.spec.tableRole == \"cell\" || parent.type.spec.tableRole == \"header_cell\") {\n      const cellPos = $head.before(d);\n      const dirStr = axis == \"vert\" ? dir > 0 ? \"down\" : \"up\" : dir > 0 ? \"right\" : \"left\";\n      return view.endOfTextblock(dirStr) ? cellPos : null;\n    }\n  }\n  return null;\n}\nfunction domInCell(view, dom) {\n  for (; dom && dom != view.dom; dom = dom.parentNode) {\n    if (dom.nodeName == \"TD\" || dom.nodeName == \"TH\") {\n      return dom;\n    }\n  }\n  return null;\n}\nfunction cellUnderMouse(view, event) {\n  const mousePos = view.posAtCoords({\n    left: event.clientX,\n    top: event.clientY\n  });\n  if (!mousePos) return null;\n  return mousePos ? cellAround(view.state.doc.resolve(mousePos.pos)) : null;\n}\n\n// src/columnresizing.ts\n\n\n\n// src/tableview.ts\nvar TableView = class {\n  constructor(node, defaultCellMinWidth) {\n    this.node = node;\n    this.defaultCellMinWidth = defaultCellMinWidth;\n    this.dom = document.createElement(\"div\");\n    this.dom.className = \"tableWrapper\";\n    this.table = this.dom.appendChild(document.createElement(\"table\"));\n    this.table.style.setProperty(\n      \"--default-cell-min-width\",\n      `${defaultCellMinWidth}px`\n    );\n    this.colgroup = this.table.appendChild(document.createElement(\"colgroup\"));\n    updateColumnsOnResize(node, this.colgroup, this.table, defaultCellMinWidth);\n    this.contentDOM = this.table.appendChild(document.createElement(\"tbody\"));\n  }\n  update(node) {\n    if (node.type != this.node.type) return false;\n    this.node = node;\n    updateColumnsOnResize(\n      node,\n      this.colgroup,\n      this.table,\n      this.defaultCellMinWidth\n    );\n    return true;\n  }\n  ignoreMutation(record) {\n    return record.type == \"attributes\" && (record.target == this.table || this.colgroup.contains(record.target));\n  }\n};\nfunction updateColumnsOnResize(node, colgroup, table, defaultCellMinWidth, overrideCol, overrideValue) {\n  var _a;\n  let totalWidth = 0;\n  let fixedWidth = true;\n  let nextDOM = colgroup.firstChild;\n  const row = node.firstChild;\n  if (!row) return;\n  for (let i = 0, col = 0; i < row.childCount; i++) {\n    const { colspan, colwidth } = row.child(i).attrs;\n    for (let j = 0; j < colspan; j++, col++) {\n      const hasWidth = overrideCol == col ? overrideValue : colwidth && colwidth[j];\n      const cssWidth = hasWidth ? hasWidth + \"px\" : \"\";\n      totalWidth += hasWidth || defaultCellMinWidth;\n      if (!hasWidth) fixedWidth = false;\n      if (!nextDOM) {\n        const col2 = document.createElement(\"col\");\n        col2.style.width = cssWidth;\n        colgroup.appendChild(col2);\n      } else {\n        if (nextDOM.style.width != cssWidth) {\n          nextDOM.style.width = cssWidth;\n        }\n        nextDOM = nextDOM.nextSibling;\n      }\n    }\n  }\n  while (nextDOM) {\n    const after = nextDOM.nextSibling;\n    (_a = nextDOM.parentNode) == null ? void 0 : _a.removeChild(nextDOM);\n    nextDOM = after;\n  }\n  if (fixedWidth) {\n    table.style.width = totalWidth + \"px\";\n    table.style.minWidth = \"\";\n  } else {\n    table.style.width = \"\";\n    table.style.minWidth = totalWidth + \"px\";\n  }\n}\n\n// src/columnresizing.ts\nvar columnResizingPluginKey = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(\n  \"tableColumnResizing\"\n);\nfunction columnResizing({\n  handleWidth = 5,\n  cellMinWidth = 25,\n  defaultCellMinWidth = 100,\n  View = TableView,\n  lastColumnResizable = true\n} = {}) {\n  const plugin = new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n    key: columnResizingPluginKey,\n    state: {\n      init(_, state) {\n        var _a, _b;\n        const nodeViews = (_b = (_a = plugin.spec) == null ? void 0 : _a.props) == null ? void 0 : _b.nodeViews;\n        const tableName = tableNodeTypes(state.schema).table.name;\n        if (View && nodeViews) {\n          nodeViews[tableName] = (node, view) => {\n            return new View(node, defaultCellMinWidth, view);\n          };\n        }\n        return new ResizeState(-1, false);\n      },\n      apply(tr, prev) {\n        return prev.apply(tr);\n      }\n    },\n    props: {\n      attributes: (state) => {\n        const pluginState = columnResizingPluginKey.getState(state);\n        return pluginState && pluginState.activeHandle > -1 ? { class: \"resize-cursor\" } : {};\n      },\n      handleDOMEvents: {\n        mousemove: (view, event) => {\n          handleMouseMove(view, event, handleWidth, lastColumnResizable);\n        },\n        mouseleave: (view) => {\n          handleMouseLeave(view);\n        },\n        mousedown: (view, event) => {\n          handleMouseDown2(view, event, cellMinWidth, defaultCellMinWidth);\n        }\n      },\n      decorations: (state) => {\n        const pluginState = columnResizingPluginKey.getState(state);\n        if (pluginState && pluginState.activeHandle > -1) {\n          return handleDecorations(state, pluginState.activeHandle);\n        }\n      },\n      nodeViews: {}\n    }\n  });\n  return plugin;\n}\nvar ResizeState = class _ResizeState {\n  constructor(activeHandle, dragging) {\n    this.activeHandle = activeHandle;\n    this.dragging = dragging;\n  }\n  apply(tr) {\n    const state = this;\n    const action = tr.getMeta(columnResizingPluginKey);\n    if (action && action.setHandle != null)\n      return new _ResizeState(action.setHandle, false);\n    if (action && action.setDragging !== void 0)\n      return new _ResizeState(state.activeHandle, action.setDragging);\n    if (state.activeHandle > -1 && tr.docChanged) {\n      let handle = tr.mapping.map(state.activeHandle, -1);\n      if (!pointsAtCell(tr.doc.resolve(handle))) {\n        handle = -1;\n      }\n      return new _ResizeState(handle, state.dragging);\n    }\n    return state;\n  }\n};\nfunction handleMouseMove(view, event, handleWidth, lastColumnResizable) {\n  if (!view.editable) return;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (!pluginState) return;\n  if (!pluginState.dragging) {\n    const target = domCellAround(event.target);\n    let cell = -1;\n    if (target) {\n      const { left, right } = target.getBoundingClientRect();\n      if (event.clientX - left <= handleWidth)\n        cell = edgeCell(view, event, \"left\", handleWidth);\n      else if (right - event.clientX <= handleWidth)\n        cell = edgeCell(view, event, \"right\", handleWidth);\n    }\n    if (cell != pluginState.activeHandle) {\n      if (!lastColumnResizable && cell !== -1) {\n        const $cell = view.state.doc.resolve(cell);\n        const table = $cell.node(-1);\n        const map = TableMap.get(table);\n        const tableStart = $cell.start(-1);\n        const col = map.colCount($cell.pos - tableStart) + $cell.nodeAfter.attrs.colspan - 1;\n        if (col == map.width - 1) {\n          return;\n        }\n      }\n      updateHandle(view, cell);\n    }\n  }\n}\nfunction handleMouseLeave(view) {\n  if (!view.editable) return;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (pluginState && pluginState.activeHandle > -1 && !pluginState.dragging)\n    updateHandle(view, -1);\n}\nfunction handleMouseDown2(view, event, cellMinWidth, defaultCellMinWidth) {\n  var _a;\n  if (!view.editable) return false;\n  const win = (_a = view.dom.ownerDocument.defaultView) != null ? _a : window;\n  const pluginState = columnResizingPluginKey.getState(view.state);\n  if (!pluginState || pluginState.activeHandle == -1 || pluginState.dragging)\n    return false;\n  const cell = view.state.doc.nodeAt(pluginState.activeHandle);\n  const width = currentColWidth(view, pluginState.activeHandle, cell.attrs);\n  view.dispatch(\n    view.state.tr.setMeta(columnResizingPluginKey, {\n      setDragging: { startX: event.clientX, startWidth: width }\n    })\n  );\n  function finish(event2) {\n    win.removeEventListener(\"mouseup\", finish);\n    win.removeEventListener(\"mousemove\", move);\n    const pluginState2 = columnResizingPluginKey.getState(view.state);\n    if (pluginState2 == null ? void 0 : pluginState2.dragging) {\n      updateColumnWidth(\n        view,\n        pluginState2.activeHandle,\n        draggedWidth(pluginState2.dragging, event2, cellMinWidth)\n      );\n      view.dispatch(\n        view.state.tr.setMeta(columnResizingPluginKey, { setDragging: null })\n      );\n    }\n  }\n  function move(event2) {\n    if (!event2.which) return finish(event2);\n    const pluginState2 = columnResizingPluginKey.getState(view.state);\n    if (!pluginState2) return;\n    if (pluginState2.dragging) {\n      const dragged = draggedWidth(pluginState2.dragging, event2, cellMinWidth);\n      displayColumnWidth(\n        view,\n        pluginState2.activeHandle,\n        dragged,\n        defaultCellMinWidth\n      );\n    }\n  }\n  displayColumnWidth(\n    view,\n    pluginState.activeHandle,\n    width,\n    defaultCellMinWidth\n  );\n  win.addEventListener(\"mouseup\", finish);\n  win.addEventListener(\"mousemove\", move);\n  event.preventDefault();\n  return true;\n}\nfunction currentColWidth(view, cellPos, { colspan, colwidth }) {\n  const width = colwidth && colwidth[colwidth.length - 1];\n  if (width) return width;\n  const dom = view.domAtPos(cellPos);\n  const node = dom.node.childNodes[dom.offset];\n  let domWidth = node.offsetWidth, parts = colspan;\n  if (colwidth) {\n    for (let i = 0; i < colspan; i++)\n      if (colwidth[i]) {\n        domWidth -= colwidth[i];\n        parts--;\n      }\n  }\n  return domWidth / parts;\n}\nfunction domCellAround(target) {\n  while (target && target.nodeName != \"TD\" && target.nodeName != \"TH\")\n    target = target.classList && target.classList.contains(\"ProseMirror\") ? null : target.parentNode;\n  return target;\n}\nfunction edgeCell(view, event, side, handleWidth) {\n  const offset = side == \"right\" ? -handleWidth : handleWidth;\n  const found = view.posAtCoords({\n    left: event.clientX + offset,\n    top: event.clientY\n  });\n  if (!found) return -1;\n  const { pos } = found;\n  const $cell = cellAround(view.state.doc.resolve(pos));\n  if (!$cell) return -1;\n  if (side == \"right\") return $cell.pos;\n  const map = TableMap.get($cell.node(-1)), start = $cell.start(-1);\n  const index = map.map.indexOf($cell.pos - start);\n  return index % map.width == 0 ? -1 : start + map.map[index - 1];\n}\nfunction draggedWidth(dragging, event, resizeMinWidth) {\n  const offset = event.clientX - dragging.startX;\n  return Math.max(resizeMinWidth, dragging.startWidth + offset);\n}\nfunction updateHandle(view, value) {\n  view.dispatch(\n    view.state.tr.setMeta(columnResizingPluginKey, { setHandle: value })\n  );\n}\nfunction updateColumnWidth(view, cell, width) {\n  const $cell = view.state.doc.resolve(cell);\n  const table = $cell.node(-1), map = TableMap.get(table), start = $cell.start(-1);\n  const col = map.colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  const tr = view.state.tr;\n  for (let row = 0; row < map.height; row++) {\n    const mapIndex = row * map.width + col;\n    if (row && map.map[mapIndex] == map.map[mapIndex - map.width]) continue;\n    const pos = map.map[mapIndex];\n    const attrs = table.nodeAt(pos).attrs;\n    const index = attrs.colspan == 1 ? 0 : col - map.colCount(pos);\n    if (attrs.colwidth && attrs.colwidth[index] == width) continue;\n    const colwidth = attrs.colwidth ? attrs.colwidth.slice() : zeroes(attrs.colspan);\n    colwidth[index] = width;\n    tr.setNodeMarkup(start + pos, null, { ...attrs, colwidth });\n  }\n  if (tr.docChanged) view.dispatch(tr);\n}\nfunction displayColumnWidth(view, cell, width, defaultCellMinWidth) {\n  const $cell = view.state.doc.resolve(cell);\n  const table = $cell.node(-1), start = $cell.start(-1);\n  const col = TableMap.get(table).colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  let dom = view.domAtPos($cell.start(-1)).node;\n  while (dom && dom.nodeName != \"TABLE\") {\n    dom = dom.parentNode;\n  }\n  if (!dom) return;\n  updateColumnsOnResize(\n    table,\n    dom.firstChild,\n    dom,\n    defaultCellMinWidth,\n    col,\n    width\n  );\n}\nfunction zeroes(n) {\n  return Array(n).fill(0);\n}\nfunction handleDecorations(state, cell) {\n  var _a;\n  const decorations = [];\n  const $cell = state.doc.resolve(cell);\n  const table = $cell.node(-1);\n  if (!table) {\n    return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.empty;\n  }\n  const map = TableMap.get(table);\n  const start = $cell.start(-1);\n  const col = map.colCount($cell.pos - start) + $cell.nodeAfter.attrs.colspan - 1;\n  for (let row = 0; row < map.height; row++) {\n    const index = col + row * map.width;\n    if ((col == map.width - 1 || map.map[index] != map.map[index + 1]) && (row == 0 || map.map[index] != map.map[index - map.width])) {\n      const cellPos = map.map[index];\n      const pos = start + cellPos + table.nodeAt(cellPos).nodeSize - 1;\n      const dom = document.createElement(\"div\");\n      dom.className = \"column-resize-handle\";\n      if ((_a = columnResizingPluginKey.getState(state)) == null ? void 0 : _a.dragging) {\n        decorations.push(\n          prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.node(\n            start + cellPos,\n            start + cellPos + table.nodeAt(cellPos).nodeSize,\n            {\n              class: \"column-resize-dragging\"\n            }\n          )\n        );\n      }\n      decorations.push(prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.widget(pos, dom));\n    }\n  }\n  return prosemirror_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.create(state.doc, decorations);\n}\n\n// src/index.ts\nfunction tableEditing({\n  allowTableNodeSelection = false\n} = {}) {\n  return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n    key: tableEditingKey,\n    // This piece of state is used to remember when a mouse-drag\n    // cell-selection is happening, so that it can continue even as\n    // transactions (which might move its anchor cell) come in.\n    state: {\n      init() {\n        return null;\n      },\n      apply(tr, cur) {\n        const set = tr.getMeta(tableEditingKey);\n        if (set != null) return set == -1 ? null : set;\n        if (cur == null || !tr.docChanged) return cur;\n        const { deleted, pos } = tr.mapping.mapResult(cur);\n        return deleted ? null : pos;\n      }\n    },\n    props: {\n      decorations: drawCellSelection,\n      handleDOMEvents: {\n        mousedown: handleMouseDown\n      },\n      createSelectionBetween(view) {\n        return tableEditingKey.getState(view.state) != null ? view.state.selection : null;\n      },\n      handleTripleClick,\n      handleKeyDown,\n      handlePaste\n    },\n    appendTransaction(_, oldState, state) {\n      return normalizeSelection(\n        state,\n        fixTables(state, oldState),\n        allowTableNodeSelection\n      );\n    }\n  });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/prosemirror-tables@1.7.1/node_modules/prosemirror-tables/dist/index.js\n");

/***/ })

};
;