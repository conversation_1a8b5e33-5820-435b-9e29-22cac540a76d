"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bullet-list/dist/index.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bullet-list/dist/index.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BulletList: () => (/* binding */ BulletList),\n/* harmony export */   \"default\": () => (/* binding */ BulletList),\n/* harmony export */   inputRegex: () => (/* binding */ inputRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\nconst ListItemName = 'listItem';\nconst TextStyleName = 'textStyle';\n/**\n * Matches a bullet list to a dash or asterisk.\n */\nconst inputRegex = /^\\s*([-+*])\\s$/;\n/**\n * This extension allows you to create bullet lists.\n * This requires the ListItem extension\n * @see https://tiptap.dev/api/nodes/bullet-list\n * @see https://tiptap.dev/api/nodes/list-item.\n */\nconst BulletList = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'bulletList',\n    addOptions() {\n        return {\n            itemTypeName: 'listItem',\n            HTMLAttributes: {},\n            keepMarks: false,\n            keepAttributes: false,\n        };\n    },\n    group: 'block list',\n    content() {\n        return `${this.options.itemTypeName}+`;\n    },\n    parseHTML() {\n        return [\n            { tag: 'ul' },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['ul', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            toggleBulletList: () => ({ commands, chain }) => {\n                if (this.options.keepAttributes) {\n                    return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run();\n                }\n                return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-Shift-8': () => this.editor.commands.toggleBulletList(),\n        };\n    },\n    addInputRules() {\n        let inputRule = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.wrappingInputRule)({\n            find: inputRegex,\n            type: this.type,\n        });\n        if (this.options.keepMarks || this.options.keepAttributes) {\n            inputRule = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.wrappingInputRule)({\n                find: inputRegex,\n                type: this.type,\n                keepMarks: this.options.keepMarks,\n                keepAttributes: this.options.keepAttributes,\n                getAttributes: () => { return this.editor.getAttributes(TextStyleName); },\n                editor: this.editor,\n            });\n        }\n        return [\n            inputRule,\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bullet-list/dist/index.js\n");

/***/ })

};
;