"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-document@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-document@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-document@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-document/dist/index.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-document@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-document/dist/index.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Document: () => (/* binding */ Document),\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * The default document node which represents the top level node of the editor.\n * @see https://tiptap.dev/api/nodes/document\n */\nconst Document = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'doc',\n    topNode: true,\n    content: 'block+',\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtleHRlbnNpb24tZG9jdW1lbnRAMi4yNi4xX0B0aXB0YXArY29yZUAyLjI2LjFfQHRpcHRhcCtwbUAyLjI2LjFfL25vZGVfbW9kdWxlcy9AdGlwdGFwL2V4dGVuc2lvbi1kb2N1bWVudC9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvQzs7QUFFcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsOENBQUk7QUFDckI7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFd0M7QUFDekMiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAdGlwdGFwK2V4dGVuc2lvbi1kb2N1bWVudEAyLjI2LjFfQHRpcHRhcCtjb3JlQDIuMjYuMV9AdGlwdGFwK3BtQDIuMjYuMV9cXG5vZGVfbW9kdWxlc1xcQHRpcHRhcFxcZXh0ZW5zaW9uLWRvY3VtZW50XFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOb2RlIH0gZnJvbSAnQHRpcHRhcC9jb3JlJztcblxuLyoqXG4gKiBUaGUgZGVmYXVsdCBkb2N1bWVudCBub2RlIHdoaWNoIHJlcHJlc2VudHMgdGhlIHRvcCBsZXZlbCBub2RlIG9mIHRoZSBlZGl0b3IuXG4gKiBAc2VlIGh0dHBzOi8vdGlwdGFwLmRldi9hcGkvbm9kZXMvZG9jdW1lbnRcbiAqL1xuY29uc3QgRG9jdW1lbnQgPSBOb2RlLmNyZWF0ZSh7XG4gICAgbmFtZTogJ2RvYycsXG4gICAgdG9wTm9kZTogdHJ1ZSxcbiAgICBjb250ZW50OiAnYmxvY2srJyxcbn0pO1xuXG5leHBvcnQgeyBEb2N1bWVudCwgRG9jdW1lbnQgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-document@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-document/dist/index.js\n");

/***/ })

};
;