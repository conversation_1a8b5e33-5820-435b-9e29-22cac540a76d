"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-table-cell@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-table-cell@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-table-cell@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-table-cell/dist/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-table-cell@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-table-cell/dist/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TableCell: () => (/* binding */ TableCell),\n/* harmony export */   \"default\": () => (/* binding */ TableCell)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * This extension allows you to create table cells.\n * @see https://www.tiptap.dev/api/nodes/table-cell\n */\nconst TableCell = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'tableCell',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    content: 'block+',\n    addAttributes() {\n        return {\n            colspan: {\n                default: 1,\n            },\n            rowspan: {\n                default: 1,\n            },\n            colwidth: {\n                default: null,\n                parseHTML: element => {\n                    const colwidth = element.getAttribute('colwidth');\n                    const value = colwidth\n                        ? colwidth.split(',').map(width => parseInt(width, 10))\n                        : null;\n                    return value;\n                },\n            },\n        };\n    },\n    tableRole: 'cell',\n    isolating: true,\n    parseHTML() {\n        return [\n            { tag: 'td' },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['td', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-table-cell@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-table-cell/dist/index.js\n");

/***/ })

};
;