/**
 * Add Dropbox login to your page.
 *
 * ### Setup
 *
 * #### Callback URL
 * ```
 * https://example.com/api/auth/callback/dropbox
 * ```
 *
 * #### Configuration
 *```ts
 * import { Auth } from "@auth/core"
 * import Dropbox from "@auth/core/providers/dropbox"
 *
 * const request = new Request(origin)
 * const response = await Auth(request, {
 *   providers: [
 *     Dropbox({
 *       clientId: DROPBOX_CLIENT_ID,
 *       clientSecret: DROPBOX_CLIENT_SECRET,
 *     }),
 *   ],
 * })
 * ```
 *
 * ### Resources
 *
 *  - [Dropbox OAuth documentation](https://developers.dropbox.com/oauth-guide)
 *
 * ### Notes
 *
 * By default, Auth.js assumes that the Dropbox provider is
 * based on the [OAuth 2](https://www.rfc-editor.org/rfc/rfc6749.html) specification.
 *
 * :::tip
 *
 * The Dropbox provider comes with a [default configuration](https://github.com/nextauthjs/next-auth/blob/main/packages/core/src/providers/dropbox.ts).
 * To override the defaults for your use case, check out [customizing a built-in OAuth provider](https://authjs.dev/guides/configuring-oauth-providers).
 *
 * :::
 *
 * :::info **Disclaimer**
 *
 * If you think you found a bug in the default configuration, you can [open an issue](https://authjs.dev/new/provider-issue).
 *
 * Auth.js strictly adheres to the specification and it cannot take responsibility for any deviation from
 * the spec by the provider. You can open an issue, but if the problem is non-compliance with the spec,
 * we might not pursue a resolution. You can ask for more help in [Discussions](https://authjs.dev/new/github-discussions).
 *
 * :::
 */
export default function Dropbox(options) {
    return {
        id: "dropbox",
        name: "Dropbox",
        type: "oauth",
        authorization: {
            url: "https://www.dropbox.com/oauth2/authorize",
            params: {
                token_access_type: "offline",
                scope: "account_info.read",
            },
        },
        token: "https://api.dropboxapi.com/oauth2/token",
        userinfo: {
            url: "https://api.dropboxapi.com/2/users/get_current_account",
            async request({ tokens, provider }) {
                return await fetch(provider.userinfo?.url, {
                    method: "POST",
                    headers: {
                        Authorization: `Bearer ${tokens.access_token}`,
                    },
                }).then(async (res) => await res.json());
            },
        },
        profile(profile) {
            return {
                id: profile.account_id,
                name: profile.name.display_name,
                email: profile.email,
                image: profile.profile_photo_url,
            };
        },
        style: { brandColor: "#0061fe" },
        options,
    };
}
