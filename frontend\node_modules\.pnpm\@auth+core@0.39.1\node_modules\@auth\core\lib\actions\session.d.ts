import type { InternalOptions, ResponseInternal, Session } from "../../types.js";
import type { <PERSON><PERSON>, SessionStore } from "../utils/cookie.js";
/** Return a session object filtered via `callbacks.session` */
export declare function session(options: InternalOptions, sessionStore: SessionStore, cookies: <PERSON>ie[], isUpdate?: boolean, newSession?: any): Promise<ResponseInternal<Session | null>>;
//# sourceMappingURL=session.d.ts.map