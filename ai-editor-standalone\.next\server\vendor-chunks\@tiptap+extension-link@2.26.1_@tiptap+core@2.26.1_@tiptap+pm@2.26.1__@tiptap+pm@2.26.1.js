"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   \"default\": () => (/* binding */ Link),\n/* harmony export */   isAllowedUri: () => (/* binding */ isAllowedUri),\n/* harmony export */   pasteRegex: () => (/* binding */ pasteRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var linkifyjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! linkifyjs */ \"(ssr)/./node_modules/.pnpm/linkifyjs@4.3.2/node_modules/linkifyjs/dist/linkify.mjs\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n\n\n\n\n// From DOMPurify\n// https://github.com/cure53/DOMPurify/blob/main/src/regexp.ts\nconst UNICODE_WHITESPACE_PATTERN = '[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]';\nconst UNICODE_WHITESPACE_REGEX = new RegExp(UNICODE_WHITESPACE_PATTERN);\nconst UNICODE_WHITESPACE_REGEX_END = new RegExp(`${UNICODE_WHITESPACE_PATTERN}$`);\nconst UNICODE_WHITESPACE_REGEX_GLOBAL = new RegExp(UNICODE_WHITESPACE_PATTERN, 'g');\n\n/**\n * Check if the provided tokens form a valid link structure, which can either be a single link token\n * or a link token surrounded by parentheses or square brackets.\n *\n * This ensures that only complete and valid text is hyperlinked, preventing cases where a valid\n * top-level domain (TLD) is immediately followed by an invalid character, like a number. For\n * example, with the `find` method from Linkify, entering `example.com1` would result in\n * `example.com` being linked and the trailing `1` left as plain text. By using the `tokenize`\n * method, we can perform more comprehensive validation on the input text.\n */\nfunction isValidLinkStructure(tokens) {\n    if (tokens.length === 1) {\n        return tokens[0].isLink;\n    }\n    if (tokens.length === 3 && tokens[1].isLink) {\n        return ['()', '[]'].includes(tokens[0].value + tokens[2].value);\n    }\n    return false;\n}\n/**\n * This plugin allows you to automatically add links to your editor.\n * @param options The plugin options\n * @returns The plugin instance\n */\nfunction autolink(options) {\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.Plugin({\n        key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.PluginKey('autolink'),\n        appendTransaction: (transactions, oldState, newState) => {\n            /**\n             * Does the transaction change the document?\n             */\n            const docChanges = transactions.some(transaction => transaction.docChanged) && !oldState.doc.eq(newState.doc);\n            /**\n             * Prevent autolink if the transaction is not a document change or if the transaction has the meta `preventAutolink`.\n             */\n            const preventAutolink = transactions.some(transaction => transaction.getMeta('preventAutolink'));\n            /**\n             * Prevent autolink if the transaction is not a document change\n             * or if the transaction has the meta `preventAutolink`.\n             */\n            if (!docChanges || preventAutolink) {\n                return;\n            }\n            const { tr } = newState;\n            const transform = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.combineTransactionSteps)(oldState.doc, [...transactions]);\n            const changes = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getChangedRanges)(transform);\n            changes.forEach(({ newRange }) => {\n                // Now let’s see if we can add new links.\n                const nodesInChangedRanges = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findChildrenInRange)(newState.doc, newRange, node => node.isTextblock);\n                let textBlock;\n                let textBeforeWhitespace;\n                if (nodesInChangedRanges.length > 1) {\n                    // Grab the first node within the changed ranges (ex. the first of two paragraphs when hitting enter).\n                    textBlock = nodesInChangedRanges[0];\n                    textBeforeWhitespace = newState.doc.textBetween(textBlock.pos, textBlock.pos + textBlock.node.nodeSize, undefined, ' ');\n                }\n                else if (nodesInChangedRanges.length) {\n                    const endText = newState.doc.textBetween(newRange.from, newRange.to, ' ', ' ');\n                    if (!UNICODE_WHITESPACE_REGEX_END.test(endText)) {\n                        return;\n                    }\n                    textBlock = nodesInChangedRanges[0];\n                    textBeforeWhitespace = newState.doc.textBetween(textBlock.pos, newRange.to, undefined, ' ');\n                }\n                if (textBlock && textBeforeWhitespace) {\n                    const wordsBeforeWhitespace = textBeforeWhitespace.split(UNICODE_WHITESPACE_REGEX).filter(Boolean);\n                    if (wordsBeforeWhitespace.length <= 0) {\n                        return false;\n                    }\n                    const lastWordBeforeSpace = wordsBeforeWhitespace[wordsBeforeWhitespace.length - 1];\n                    const lastWordAndBlockOffset = textBlock.pos + textBeforeWhitespace.lastIndexOf(lastWordBeforeSpace);\n                    if (!lastWordBeforeSpace) {\n                        return false;\n                    }\n                    const linksBeforeSpace = (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.tokenize)(lastWordBeforeSpace).map(t => t.toObject(options.defaultProtocol));\n                    if (!isValidLinkStructure(linksBeforeSpace)) {\n                        return false;\n                    }\n                    linksBeforeSpace\n                        .filter(link => link.isLink)\n                        // Calculate link position.\n                        .map(link => ({\n                        ...link,\n                        from: lastWordAndBlockOffset + link.start + 1,\n                        to: lastWordAndBlockOffset + link.end + 1,\n                    }))\n                        // ignore link inside code mark\n                        .filter(link => {\n                        if (!newState.schema.marks.code) {\n                            return true;\n                        }\n                        return !newState.doc.rangeHasMark(link.from, link.to, newState.schema.marks.code);\n                    })\n                        // validate link\n                        .filter(link => options.validate(link.value))\n                        // check whether should autolink\n                        .filter(link => options.shouldAutoLink(link.value))\n                        // Add link mark.\n                        .forEach(link => {\n                        if ((0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getMarksBetween)(link.from, link.to, newState.doc).some(item => item.mark.type === options.type)) {\n                            return;\n                        }\n                        tr.addMark(link.from, link.to, options.type.create({\n                            href: link.href,\n                        }));\n                    });\n                }\n            });\n            if (!tr.steps.length) {\n                return;\n            }\n            return tr;\n        },\n    });\n}\n\nfunction clickHandler(options) {\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.Plugin({\n        key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.PluginKey('handleClickLink'),\n        props: {\n            handleClick: (view, pos, event) => {\n                var _a, _b;\n                if (event.button !== 0) {\n                    return false;\n                }\n                if (!view.editable) {\n                    return false;\n                }\n                let a = event.target;\n                const els = [];\n                while (a.nodeName !== 'DIV') {\n                    els.push(a);\n                    a = a.parentNode;\n                }\n                if (!els.find(value => value.nodeName === 'A')) {\n                    return false;\n                }\n                const attrs = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getAttributes)(view.state, options.type.name);\n                const link = event.target;\n                const href = (_a = link === null || link === void 0 ? void 0 : link.href) !== null && _a !== void 0 ? _a : attrs.href;\n                const target = (_b = link === null || link === void 0 ? void 0 : link.target) !== null && _b !== void 0 ? _b : attrs.target;\n                if (link && href) {\n                    window.open(href, target);\n                    return true;\n                }\n                return false;\n            },\n        },\n    });\n}\n\nfunction pasteHandler(options) {\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.Plugin({\n        key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.PluginKey('handlePasteLink'),\n        props: {\n            handlePaste: (view, event, slice) => {\n                const { state } = view;\n                const { selection } = state;\n                const { empty } = selection;\n                if (empty) {\n                    return false;\n                }\n                let textContent = '';\n                slice.content.forEach(node => {\n                    textContent += node.textContent;\n                });\n                const link = (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.find)(textContent, { defaultProtocol: options.defaultProtocol }).find(item => item.isLink && item.value === textContent);\n                if (!textContent || !link) {\n                    return false;\n                }\n                return options.editor.commands.setMark(options.type, {\n                    href: link.href,\n                });\n            },\n        },\n    });\n}\n\nconst pasteRegex = /https?:\\/\\/(?:www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z]{2,}\\b(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)/gi;\nfunction isAllowedUri(uri, protocols) {\n    const allowedProtocols = [\n        'http',\n        'https',\n        'ftp',\n        'ftps',\n        'mailto',\n        'tel',\n        'callto',\n        'sms',\n        'cid',\n        'xmpp',\n    ];\n    if (protocols) {\n        protocols.forEach(protocol => {\n            const nextProtocol = typeof protocol === 'string' ? protocol : protocol.scheme;\n            if (nextProtocol) {\n                allowedProtocols.push(nextProtocol);\n            }\n        });\n    }\n    return (!uri\n        || uri.replace(UNICODE_WHITESPACE_REGEX_GLOBAL, '').match(new RegExp(\n        // eslint-disable-next-line no-useless-escape\n        `^(?:(?:${allowedProtocols.join('|')}):|[^a-z]|[a-z0-9+.\\-]+(?:[^a-z+.\\-:]|$))`, 'i')));\n}\n/**\n * This extension allows you to create links.\n * @see https://www.tiptap.dev/api/marks/link\n */\nconst Link = _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Mark.create({\n    name: 'link',\n    priority: 1000,\n    keepOnSplit: false,\n    exitable: true,\n    onCreate() {\n        if (this.options.validate && !this.options.shouldAutoLink) {\n            // Copy the validate function to the shouldAutoLink option\n            this.options.shouldAutoLink = this.options.validate;\n            console.warn('The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.');\n        }\n        this.options.protocols.forEach(protocol => {\n            if (typeof protocol === 'string') {\n                (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.registerCustomProtocol)(protocol);\n                return;\n            }\n            (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.registerCustomProtocol)(protocol.scheme, protocol.optionalSlashes);\n        });\n    },\n    onDestroy() {\n        (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.reset)();\n    },\n    inclusive() {\n        return this.options.autolink;\n    },\n    addOptions() {\n        return {\n            openOnClick: true,\n            linkOnPaste: true,\n            autolink: true,\n            protocols: [],\n            defaultProtocol: 'http',\n            HTMLAttributes: {\n                target: '_blank',\n                rel: 'noopener noreferrer nofollow',\n                class: null,\n            },\n            isAllowedUri: (url, ctx) => !!isAllowedUri(url, ctx.protocols),\n            validate: url => !!url,\n            shouldAutoLink: url => !!url,\n        };\n    },\n    addAttributes() {\n        return {\n            href: {\n                default: null,\n                parseHTML(element) {\n                    return element.getAttribute('href');\n                },\n            },\n            target: {\n                default: this.options.HTMLAttributes.target,\n            },\n            rel: {\n                default: this.options.HTMLAttributes.rel,\n            },\n            class: {\n                default: this.options.HTMLAttributes.class,\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'a[href]',\n                getAttrs: dom => {\n                    const href = dom.getAttribute('href');\n                    // prevent XSS attacks\n                    if (!href\n                        || !this.options.isAllowedUri(href, {\n                            defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n                            protocols: this.options.protocols,\n                            defaultProtocol: this.options.defaultProtocol,\n                        })) {\n                        return false;\n                    }\n                    return null;\n                },\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        // prevent XSS attacks\n        if (!this.options.isAllowedUri(HTMLAttributes.href, {\n            defaultValidate: href => !!isAllowedUri(href, this.options.protocols),\n            protocols: this.options.protocols,\n            defaultProtocol: this.options.defaultProtocol,\n        })) {\n            // strip out the href\n            return [\n                'a',\n                (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.mergeAttributes)(this.options.HTMLAttributes, { ...HTMLAttributes, href: '' }),\n                0,\n            ];\n        }\n        return ['a', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setLink: attributes => ({ chain }) => {\n                const { href } = attributes;\n                if (!this.options.isAllowedUri(href, {\n                    defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n                    protocols: this.options.protocols,\n                    defaultProtocol: this.options.defaultProtocol,\n                })) {\n                    return false;\n                }\n                return chain().setMark(this.name, attributes).setMeta('preventAutolink', true).run();\n            },\n            toggleLink: attributes => ({ chain }) => {\n                const { href } = attributes;\n                if (!this.options.isAllowedUri(href, {\n                    defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n                    protocols: this.options.protocols,\n                    defaultProtocol: this.options.defaultProtocol,\n                })) {\n                    return false;\n                }\n                return chain()\n                    .toggleMark(this.name, attributes, { extendEmptyMarkRange: true })\n                    .setMeta('preventAutolink', true)\n                    .run();\n            },\n            unsetLink: () => ({ chain }) => {\n                return chain()\n                    .unsetMark(this.name, { extendEmptyMarkRange: true })\n                    .setMeta('preventAutolink', true)\n                    .run();\n            },\n        };\n    },\n    addPasteRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.markPasteRule)({\n                find: text => {\n                    const foundLinks = [];\n                    if (text) {\n                        const { protocols, defaultProtocol } = this.options;\n                        const links = (0,linkifyjs__WEBPACK_IMPORTED_MODULE_0__.find)(text).filter(item => item.isLink\n                            && this.options.isAllowedUri(item.value, {\n                                defaultValidate: href => !!isAllowedUri(href, protocols),\n                                protocols,\n                                defaultProtocol,\n                            }));\n                        if (links.length) {\n                            links.forEach(link => foundLinks.push({\n                                text: link.value,\n                                data: {\n                                    href: link.href,\n                                },\n                                index: link.start,\n                            }));\n                        }\n                    }\n                    return foundLinks;\n                },\n                type: this.type,\n                getAttributes: match => {\n                    var _a;\n                    return {\n                        href: (_a = match.data) === null || _a === void 0 ? void 0 : _a.href,\n                    };\n                },\n            }),\n        ];\n    },\n    addProseMirrorPlugins() {\n        const plugins = [];\n        const { protocols, defaultProtocol } = this.options;\n        if (this.options.autolink) {\n            plugins.push(autolink({\n                type: this.type,\n                defaultProtocol: this.options.defaultProtocol,\n                validate: url => this.options.isAllowedUri(url, {\n                    defaultValidate: href => !!isAllowedUri(href, protocols),\n                    protocols,\n                    defaultProtocol,\n                }),\n                shouldAutoLink: this.options.shouldAutoLink,\n            }));\n        }\n        if (this.options.openOnClick === true) {\n            plugins.push(clickHandler({\n                type: this.type,\n            }));\n        }\n        if (this.options.linkOnPaste) {\n            plugins.push(pasteHandler({\n                editor: this.editor,\n                defaultProtocol: this.options.defaultProtocol,\n                type: this.type,\n            }));\n        }\n        return plugins;\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtleHRlbnNpb24tbGlua0AyLjI2LjFfQHRpcHRhcCtjb3JlQDIuMjYuMV9AdGlwdGFwK3BtQDIuMjYuMV9fQHRpcHRhcCtwbUAyLjI2LjEvbm9kZV9tb2R1bGVzL0B0aXB0YXAvZXh0ZW5zaW9uLWxpbmsvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQW9LO0FBQzFGO0FBQ3JCOztBQUVyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRCwyQkFBMkI7QUFDOUU7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG9EQUFNO0FBQ3JCLGlCQUFpQix1REFBUztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixLQUFLO0FBQ3pCLDhCQUE4QixxRUFBdUI7QUFDckQsNEJBQTRCLDhEQUFnQjtBQUM1QywrQkFBK0IsVUFBVTtBQUN6QztBQUNBLDZDQUE2QyxpRUFBbUI7QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsbURBQVE7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qiw2REFBZTtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QixxQkFBcUI7QUFDckI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMOztBQUVBO0FBQ0EsZUFBZSxvREFBTTtBQUNyQixpQkFBaUIsdURBQVM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLDJEQUFhO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNULEtBQUs7QUFDTDs7QUFFQTtBQUNBLGVBQWUsb0RBQU07QUFDckIsaUJBQWlCLHVEQUFTO0FBQzFCO0FBQ0E7QUFDQSx3QkFBd0IsUUFBUTtBQUNoQyx3QkFBd0IsWUFBWTtBQUNwQyx3QkFBd0IsUUFBUTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsNkJBQTZCLCtDQUFJLGdCQUFnQiwwQ0FBMEM7QUFDM0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2IsU0FBUztBQUNULEtBQUs7QUFDTDs7QUFFQSwrREFBK0QsTUFBTSxXQUFXLEdBQUc7QUFDbkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsMkJBQTJCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLDhDQUFJO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixpRUFBc0I7QUFDdEM7QUFDQTtBQUNBLFlBQVksaUVBQXNCO0FBQ2xDLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQSxRQUFRLGdEQUFLO0FBQ2IsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixnQkFBZ0I7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsNkRBQWUsZ0NBQWdDLDZCQUE2QjtBQUM1RjtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsNkRBQWU7QUFDcEMsS0FBSztBQUNMO0FBQ0E7QUFDQSxzQ0FBc0MsT0FBTztBQUM3Qyx3QkFBd0IsT0FBTztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IseUNBQXlDLE9BQU87QUFDaEQsd0JBQXdCLE9BQU87QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0EseURBQXlELDRCQUE0QjtBQUNyRjtBQUNBO0FBQ0EsYUFBYTtBQUNiLGdDQUFnQyxPQUFPO0FBQ3ZDO0FBQ0EsNENBQTRDLDRCQUE0QjtBQUN4RTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZLDJEQUFhO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyw2QkFBNkI7QUFDN0Qsc0NBQXNDLCtDQUFJO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGdCQUFnQiw2QkFBNkI7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLEtBQUs7QUFDTCxDQUFDOztBQUUwRDtBQUMzRCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEB0aXB0YXArZXh0ZW5zaW9uLWxpbmtAMi4yNi4xX0B0aXB0YXArY29yZUAyLjI2LjFfQHRpcHRhcCtwbUAyLjI2LjFfX0B0aXB0YXArcG1AMi4yNi4xXFxub2RlX21vZHVsZXNcXEB0aXB0YXBcXGV4dGVuc2lvbi1saW5rXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb21iaW5lVHJhbnNhY3Rpb25TdGVwcywgZ2V0Q2hhbmdlZFJhbmdlcywgZmluZENoaWxkcmVuSW5SYW5nZSwgZ2V0TWFya3NCZXR3ZWVuLCBnZXRBdHRyaWJ1dGVzLCBNYXJrLCBtZXJnZUF0dHJpYnV0ZXMsIG1hcmtQYXN0ZVJ1bGUgfSBmcm9tICdAdGlwdGFwL2NvcmUnO1xuaW1wb3J0IHsgdG9rZW5pemUsIGZpbmQsIHJlZ2lzdGVyQ3VzdG9tUHJvdG9jb2wsIHJlc2V0IH0gZnJvbSAnbGlua2lmeWpzJztcbmltcG9ydCB7IFBsdWdpbiwgUGx1Z2luS2V5IH0gZnJvbSAnQHRpcHRhcC9wbS9zdGF0ZSc7XG5cbi8vIEZyb20gRE9NUHVyaWZ5XG4vLyBodHRwczovL2dpdGh1Yi5jb20vY3VyZTUzL0RPTVB1cmlmeS9ibG9iL21haW4vc3JjL3JlZ2V4cC50c1xuY29uc3QgVU5JQ09ERV9XSElURVNQQUNFX1BBVFRFUk4gPSAnW1xcdTAwMDAtXFx1MDAyMFxcdTAwQTBcXHUxNjgwXFx1MTgwRVxcdTIwMDAtXFx1MjAyOVxcdTIwNUZcXHUzMDAwXSc7XG5jb25zdCBVTklDT0RFX1dISVRFU1BBQ0VfUkVHRVggPSBuZXcgUmVnRXhwKFVOSUNPREVfV0hJVEVTUEFDRV9QQVRURVJOKTtcbmNvbnN0IFVOSUNPREVfV0hJVEVTUEFDRV9SRUdFWF9FTkQgPSBuZXcgUmVnRXhwKGAke1VOSUNPREVfV0hJVEVTUEFDRV9QQVRURVJOfSRgKTtcbmNvbnN0IFVOSUNPREVfV0hJVEVTUEFDRV9SRUdFWF9HTE9CQUwgPSBuZXcgUmVnRXhwKFVOSUNPREVfV0hJVEVTUEFDRV9QQVRURVJOLCAnZycpO1xuXG4vKipcbiAqIENoZWNrIGlmIHRoZSBwcm92aWRlZCB0b2tlbnMgZm9ybSBhIHZhbGlkIGxpbmsgc3RydWN0dXJlLCB3aGljaCBjYW4gZWl0aGVyIGJlIGEgc2luZ2xlIGxpbmsgdG9rZW5cbiAqIG9yIGEgbGluayB0b2tlbiBzdXJyb3VuZGVkIGJ5IHBhcmVudGhlc2VzIG9yIHNxdWFyZSBicmFja2V0cy5cbiAqXG4gKiBUaGlzIGVuc3VyZXMgdGhhdCBvbmx5IGNvbXBsZXRlIGFuZCB2YWxpZCB0ZXh0IGlzIGh5cGVybGlua2VkLCBwcmV2ZW50aW5nIGNhc2VzIHdoZXJlIGEgdmFsaWRcbiAqIHRvcC1sZXZlbCBkb21haW4gKFRMRCkgaXMgaW1tZWRpYXRlbHkgZm9sbG93ZWQgYnkgYW4gaW52YWxpZCBjaGFyYWN0ZXIsIGxpa2UgYSBudW1iZXIuIEZvclxuICogZXhhbXBsZSwgd2l0aCB0aGUgYGZpbmRgIG1ldGhvZCBmcm9tIExpbmtpZnksIGVudGVyaW5nIGBleGFtcGxlLmNvbTFgIHdvdWxkIHJlc3VsdCBpblxuICogYGV4YW1wbGUuY29tYCBiZWluZyBsaW5rZWQgYW5kIHRoZSB0cmFpbGluZyBgMWAgbGVmdCBhcyBwbGFpbiB0ZXh0LiBCeSB1c2luZyB0aGUgYHRva2VuaXplYFxuICogbWV0aG9kLCB3ZSBjYW4gcGVyZm9ybSBtb3JlIGNvbXByZWhlbnNpdmUgdmFsaWRhdGlvbiBvbiB0aGUgaW5wdXQgdGV4dC5cbiAqL1xuZnVuY3Rpb24gaXNWYWxpZExpbmtTdHJ1Y3R1cmUodG9rZW5zKSB7XG4gICAgaWYgKHRva2Vucy5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgcmV0dXJuIHRva2Vuc1swXS5pc0xpbms7XG4gICAgfVxuICAgIGlmICh0b2tlbnMubGVuZ3RoID09PSAzICYmIHRva2Vuc1sxXS5pc0xpbmspIHtcbiAgICAgICAgcmV0dXJuIFsnKCknLCAnW10nXS5pbmNsdWRlcyh0b2tlbnNbMF0udmFsdWUgKyB0b2tlbnNbMl0udmFsdWUpO1xuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG59XG4vKipcbiAqIFRoaXMgcGx1Z2luIGFsbG93cyB5b3UgdG8gYXV0b21hdGljYWxseSBhZGQgbGlua3MgdG8geW91ciBlZGl0b3IuXG4gKiBAcGFyYW0gb3B0aW9ucyBUaGUgcGx1Z2luIG9wdGlvbnNcbiAqIEByZXR1cm5zIFRoZSBwbHVnaW4gaW5zdGFuY2VcbiAqL1xuZnVuY3Rpb24gYXV0b2xpbmsob3B0aW9ucykge1xuICAgIHJldHVybiBuZXcgUGx1Z2luKHtcbiAgICAgICAga2V5OiBuZXcgUGx1Z2luS2V5KCdhdXRvbGluaycpLFxuICAgICAgICBhcHBlbmRUcmFuc2FjdGlvbjogKHRyYW5zYWN0aW9ucywgb2xkU3RhdGUsIG5ld1N0YXRlKSA9PiB7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIERvZXMgdGhlIHRyYW5zYWN0aW9uIGNoYW5nZSB0aGUgZG9jdW1lbnQ/XG4gICAgICAgICAgICAgKi9cbiAgICAgICAgICAgIGNvbnN0IGRvY0NoYW5nZXMgPSB0cmFuc2FjdGlvbnMuc29tZSh0cmFuc2FjdGlvbiA9PiB0cmFuc2FjdGlvbi5kb2NDaGFuZ2VkKSAmJiAhb2xkU3RhdGUuZG9jLmVxKG5ld1N0YXRlLmRvYyk7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFByZXZlbnQgYXV0b2xpbmsgaWYgdGhlIHRyYW5zYWN0aW9uIGlzIG5vdCBhIGRvY3VtZW50IGNoYW5nZSBvciBpZiB0aGUgdHJhbnNhY3Rpb24gaGFzIHRoZSBtZXRhIGBwcmV2ZW50QXV0b2xpbmtgLlxuICAgICAgICAgICAgICovXG4gICAgICAgICAgICBjb25zdCBwcmV2ZW50QXV0b2xpbmsgPSB0cmFuc2FjdGlvbnMuc29tZSh0cmFuc2FjdGlvbiA9PiB0cmFuc2FjdGlvbi5nZXRNZXRhKCdwcmV2ZW50QXV0b2xpbmsnKSk7XG4gICAgICAgICAgICAvKipcbiAgICAgICAgICAgICAqIFByZXZlbnQgYXV0b2xpbmsgaWYgdGhlIHRyYW5zYWN0aW9uIGlzIG5vdCBhIGRvY3VtZW50IGNoYW5nZVxuICAgICAgICAgICAgICogb3IgaWYgdGhlIHRyYW5zYWN0aW9uIGhhcyB0aGUgbWV0YSBgcHJldmVudEF1dG9saW5rYC5cbiAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgaWYgKCFkb2NDaGFuZ2VzIHx8IHByZXZlbnRBdXRvbGluaykge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHsgdHIgfSA9IG5ld1N0YXRlO1xuICAgICAgICAgICAgY29uc3QgdHJhbnNmb3JtID0gY29tYmluZVRyYW5zYWN0aW9uU3RlcHMob2xkU3RhdGUuZG9jLCBbLi4udHJhbnNhY3Rpb25zXSk7XG4gICAgICAgICAgICBjb25zdCBjaGFuZ2VzID0gZ2V0Q2hhbmdlZFJhbmdlcyh0cmFuc2Zvcm0pO1xuICAgICAgICAgICAgY2hhbmdlcy5mb3JFYWNoKCh7IG5ld1JhbmdlIH0pID0+IHtcbiAgICAgICAgICAgICAgICAvLyBOb3cgbGV04oCZcyBzZWUgaWYgd2UgY2FuIGFkZCBuZXcgbGlua3MuXG4gICAgICAgICAgICAgICAgY29uc3Qgbm9kZXNJbkNoYW5nZWRSYW5nZXMgPSBmaW5kQ2hpbGRyZW5JblJhbmdlKG5ld1N0YXRlLmRvYywgbmV3UmFuZ2UsIG5vZGUgPT4gbm9kZS5pc1RleHRibG9jayk7XG4gICAgICAgICAgICAgICAgbGV0IHRleHRCbG9jaztcbiAgICAgICAgICAgICAgICBsZXQgdGV4dEJlZm9yZVdoaXRlc3BhY2U7XG4gICAgICAgICAgICAgICAgaWYgKG5vZGVzSW5DaGFuZ2VkUmFuZ2VzLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gR3JhYiB0aGUgZmlyc3Qgbm9kZSB3aXRoaW4gdGhlIGNoYW5nZWQgcmFuZ2VzIChleC4gdGhlIGZpcnN0IG9mIHR3byBwYXJhZ3JhcGhzIHdoZW4gaGl0dGluZyBlbnRlcikuXG4gICAgICAgICAgICAgICAgICAgIHRleHRCbG9jayA9IG5vZGVzSW5DaGFuZ2VkUmFuZ2VzWzBdO1xuICAgICAgICAgICAgICAgICAgICB0ZXh0QmVmb3JlV2hpdGVzcGFjZSA9IG5ld1N0YXRlLmRvYy50ZXh0QmV0d2Vlbih0ZXh0QmxvY2sucG9zLCB0ZXh0QmxvY2sucG9zICsgdGV4dEJsb2NrLm5vZGUubm9kZVNpemUsIHVuZGVmaW5lZCwgJyAnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAobm9kZXNJbkNoYW5nZWRSYW5nZXMubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGVuZFRleHQgPSBuZXdTdGF0ZS5kb2MudGV4dEJldHdlZW4obmV3UmFuZ2UuZnJvbSwgbmV3UmFuZ2UudG8sICcgJywgJyAnKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFVTklDT0RFX1dISVRFU1BBQ0VfUkVHRVhfRU5ELnRlc3QoZW5kVGV4dCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB0ZXh0QmxvY2sgPSBub2Rlc0luQ2hhbmdlZFJhbmdlc1swXTtcbiAgICAgICAgICAgICAgICAgICAgdGV4dEJlZm9yZVdoaXRlc3BhY2UgPSBuZXdTdGF0ZS5kb2MudGV4dEJldHdlZW4odGV4dEJsb2NrLnBvcywgbmV3UmFuZ2UudG8sIHVuZGVmaW5lZCwgJyAnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHRleHRCbG9jayAmJiB0ZXh0QmVmb3JlV2hpdGVzcGFjZSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB3b3Jkc0JlZm9yZVdoaXRlc3BhY2UgPSB0ZXh0QmVmb3JlV2hpdGVzcGFjZS5zcGxpdChVTklDT0RFX1dISVRFU1BBQ0VfUkVHRVgpLmZpbHRlcihCb29sZWFuKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHdvcmRzQmVmb3JlV2hpdGVzcGFjZS5sZW5ndGggPD0gMCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxhc3RXb3JkQmVmb3JlU3BhY2UgPSB3b3Jkc0JlZm9yZVdoaXRlc3BhY2Vbd29yZHNCZWZvcmVXaGl0ZXNwYWNlLmxlbmd0aCAtIDFdO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBsYXN0V29yZEFuZEJsb2NrT2Zmc2V0ID0gdGV4dEJsb2NrLnBvcyArIHRleHRCZWZvcmVXaGl0ZXNwYWNlLmxhc3RJbmRleE9mKGxhc3RXb3JkQmVmb3JlU3BhY2UpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWxhc3RXb3JkQmVmb3JlU3BhY2UpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBjb25zdCBsaW5rc0JlZm9yZVNwYWNlID0gdG9rZW5pemUobGFzdFdvcmRCZWZvcmVTcGFjZSkubWFwKHQgPT4gdC50b09iamVjdChvcHRpb25zLmRlZmF1bHRQcm90b2NvbCkpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIWlzVmFsaWRMaW5rU3RydWN0dXJlKGxpbmtzQmVmb3JlU3BhY2UpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgbGlua3NCZWZvcmVTcGFjZVxuICAgICAgICAgICAgICAgICAgICAgICAgLmZpbHRlcihsaW5rID0+IGxpbmsuaXNMaW5rKVxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQ2FsY3VsYXRlIGxpbmsgcG9zaXRpb24uXG4gICAgICAgICAgICAgICAgICAgICAgICAubWFwKGxpbmsgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmxpbmssXG4gICAgICAgICAgICAgICAgICAgICAgICBmcm9tOiBsYXN0V29yZEFuZEJsb2NrT2Zmc2V0ICsgbGluay5zdGFydCArIDEsXG4gICAgICAgICAgICAgICAgICAgICAgICB0bzogbGFzdFdvcmRBbmRCbG9ja09mZnNldCArIGxpbmsuZW5kICsgMSxcbiAgICAgICAgICAgICAgICAgICAgfSkpXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBpZ25vcmUgbGluayBpbnNpZGUgY29kZSBtYXJrXG4gICAgICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKGxpbmsgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFuZXdTdGF0ZS5zY2hlbWEubWFya3MuY29kZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuICFuZXdTdGF0ZS5kb2MucmFuZ2VIYXNNYXJrKGxpbmsuZnJvbSwgbGluay50bywgbmV3U3RhdGUuc2NoZW1hLm1hcmtzLmNvZGUpO1xuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gdmFsaWRhdGUgbGlua1xuICAgICAgICAgICAgICAgICAgICAgICAgLmZpbHRlcihsaW5rID0+IG9wdGlvbnMudmFsaWRhdGUobGluay52YWx1ZSkpXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBjaGVjayB3aGV0aGVyIHNob3VsZCBhdXRvbGlua1xuICAgICAgICAgICAgICAgICAgICAgICAgLmZpbHRlcihsaW5rID0+IG9wdGlvbnMuc2hvdWxkQXV0b0xpbmsobGluay52YWx1ZSkpXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBBZGQgbGluayBtYXJrLlxuICAgICAgICAgICAgICAgICAgICAgICAgLmZvckVhY2gobGluayA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoZ2V0TWFya3NCZXR3ZWVuKGxpbmsuZnJvbSwgbGluay50bywgbmV3U3RhdGUuZG9jKS5zb21lKGl0ZW0gPT4gaXRlbS5tYXJrLnR5cGUgPT09IG9wdGlvbnMudHlwZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB0ci5hZGRNYXJrKGxpbmsuZnJvbSwgbGluay50bywgb3B0aW9ucy50eXBlLmNyZWF0ZSh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZjogbGluay5ocmVmLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmICghdHIuc3RlcHMubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHRyO1xuICAgICAgICB9LFxuICAgIH0pO1xufVxuXG5mdW5jdGlvbiBjbGlja0hhbmRsZXIob3B0aW9ucykge1xuICAgIHJldHVybiBuZXcgUGx1Z2luKHtcbiAgICAgICAga2V5OiBuZXcgUGx1Z2luS2V5KCdoYW5kbGVDbGlja0xpbmsnKSxcbiAgICAgICAgcHJvcHM6IHtcbiAgICAgICAgICAgIGhhbmRsZUNsaWNrOiAodmlldywgcG9zLCBldmVudCkgPT4ge1xuICAgICAgICAgICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgICAgICAgICAgaWYgKGV2ZW50LmJ1dHRvbiAhPT0gMCkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICghdmlldy5lZGl0YWJsZSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGxldCBhID0gZXZlbnQudGFyZ2V0O1xuICAgICAgICAgICAgICAgIGNvbnN0IGVscyA9IFtdO1xuICAgICAgICAgICAgICAgIHdoaWxlIChhLm5vZGVOYW1lICE9PSAnRElWJykge1xuICAgICAgICAgICAgICAgICAgICBlbHMucHVzaChhKTtcbiAgICAgICAgICAgICAgICAgICAgYSA9IGEucGFyZW50Tm9kZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKCFlbHMuZmluZCh2YWx1ZSA9PiB2YWx1ZS5ub2RlTmFtZSA9PT0gJ0EnKSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IGF0dHJzID0gZ2V0QXR0cmlidXRlcyh2aWV3LnN0YXRlLCBvcHRpb25zLnR5cGUubmFtZSk7XG4gICAgICAgICAgICAgICAgY29uc3QgbGluayA9IGV2ZW50LnRhcmdldDtcbiAgICAgICAgICAgICAgICBjb25zdCBocmVmID0gKF9hID0gbGluayA9PT0gbnVsbCB8fCBsaW5rID09PSB2b2lkIDAgPyB2b2lkIDAgOiBsaW5rLmhyZWYpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IGF0dHJzLmhyZWY7XG4gICAgICAgICAgICAgICAgY29uc3QgdGFyZ2V0ID0gKF9iID0gbGluayA9PT0gbnVsbCB8fCBsaW5rID09PSB2b2lkIDAgPyB2b2lkIDAgOiBsaW5rLnRhcmdldCkgIT09IG51bGwgJiYgX2IgIT09IHZvaWQgMCA/IF9iIDogYXR0cnMudGFyZ2V0O1xuICAgICAgICAgICAgICAgIGlmIChsaW5rICYmIGhyZWYpIHtcbiAgICAgICAgICAgICAgICAgICAgd2luZG93Lm9wZW4oaHJlZiwgdGFyZ2V0KTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgfSk7XG59XG5cbmZ1bmN0aW9uIHBhc3RlSGFuZGxlcihvcHRpb25zKSB7XG4gICAgcmV0dXJuIG5ldyBQbHVnaW4oe1xuICAgICAgICBrZXk6IG5ldyBQbHVnaW5LZXkoJ2hhbmRsZVBhc3RlTGluaycpLFxuICAgICAgICBwcm9wczoge1xuICAgICAgICAgICAgaGFuZGxlUGFzdGU6ICh2aWV3LCBldmVudCwgc2xpY2UpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCB7IHN0YXRlIH0gPSB2aWV3O1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgc2VsZWN0aW9uIH0gPSBzdGF0ZTtcbiAgICAgICAgICAgICAgICBjb25zdCB7IGVtcHR5IH0gPSBzZWxlY3Rpb247XG4gICAgICAgICAgICAgICAgaWYgKGVtcHR5KSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgbGV0IHRleHRDb250ZW50ID0gJyc7XG4gICAgICAgICAgICAgICAgc2xpY2UuY29udGVudC5mb3JFYWNoKG5vZGUgPT4ge1xuICAgICAgICAgICAgICAgICAgICB0ZXh0Q29udGVudCArPSBub2RlLnRleHRDb250ZW50O1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IGxpbmsgPSBmaW5kKHRleHRDb250ZW50LCB7IGRlZmF1bHRQcm90b2NvbDogb3B0aW9ucy5kZWZhdWx0UHJvdG9jb2wgfSkuZmluZChpdGVtID0+IGl0ZW0uaXNMaW5rICYmIGl0ZW0udmFsdWUgPT09IHRleHRDb250ZW50KTtcbiAgICAgICAgICAgICAgICBpZiAoIXRleHRDb250ZW50IHx8ICFsaW5rKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIG9wdGlvbnMuZWRpdG9yLmNvbW1hbmRzLnNldE1hcmsob3B0aW9ucy50eXBlLCB7XG4gICAgICAgICAgICAgICAgICAgIGhyZWY6IGxpbmsuaHJlZixcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgfSk7XG59XG5cbmNvbnN0IHBhc3RlUmVnZXggPSAvaHR0cHM/OlxcL1xcLyg/Ond3d1xcLik/Wy1hLXpBLVowLTlAOiUuXyt+Iz1dezEsMjU2fVxcLlthLXpBLVpdezIsfVxcYig/OlstYS16QS1aMC05QDolLl8rfiM9PyEmL10qKSg/OlstYS16QS1aMC05QDolLl8rfiM9PyEmL10qKS9naTtcbmZ1bmN0aW9uIGlzQWxsb3dlZFVyaSh1cmksIHByb3RvY29scykge1xuICAgIGNvbnN0IGFsbG93ZWRQcm90b2NvbHMgPSBbXG4gICAgICAgICdodHRwJyxcbiAgICAgICAgJ2h0dHBzJyxcbiAgICAgICAgJ2Z0cCcsXG4gICAgICAgICdmdHBzJyxcbiAgICAgICAgJ21haWx0bycsXG4gICAgICAgICd0ZWwnLFxuICAgICAgICAnY2FsbHRvJyxcbiAgICAgICAgJ3NtcycsXG4gICAgICAgICdjaWQnLFxuICAgICAgICAneG1wcCcsXG4gICAgXTtcbiAgICBpZiAocHJvdG9jb2xzKSB7XG4gICAgICAgIHByb3RvY29scy5mb3JFYWNoKHByb3RvY29sID0+IHtcbiAgICAgICAgICAgIGNvbnN0IG5leHRQcm90b2NvbCA9IHR5cGVvZiBwcm90b2NvbCA9PT0gJ3N0cmluZycgPyBwcm90b2NvbCA6IHByb3RvY29sLnNjaGVtZTtcbiAgICAgICAgICAgIGlmIChuZXh0UHJvdG9jb2wpIHtcbiAgICAgICAgICAgICAgICBhbGxvd2VkUHJvdG9jb2xzLnB1c2gobmV4dFByb3RvY29sKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiAoIXVyaVxuICAgICAgICB8fCB1cmkucmVwbGFjZShVTklDT0RFX1dISVRFU1BBQ0VfUkVHRVhfR0xPQkFMLCAnJykubWF0Y2gobmV3IFJlZ0V4cChcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVzZWxlc3MtZXNjYXBlXG4gICAgICAgIGBeKD86KD86JHthbGxvd2VkUHJvdG9jb2xzLmpvaW4oJ3wnKX0pOnxbXmEtel18W2EtejAtOSsuXFwtXSsoPzpbXmEteisuXFwtOl18JCkpYCwgJ2knKSkpO1xufVxuLyoqXG4gKiBUaGlzIGV4dGVuc2lvbiBhbGxvd3MgeW91IHRvIGNyZWF0ZSBsaW5rcy5cbiAqIEBzZWUgaHR0cHM6Ly93d3cudGlwdGFwLmRldi9hcGkvbWFya3MvbGlua1xuICovXG5jb25zdCBMaW5rID0gTWFyay5jcmVhdGUoe1xuICAgIG5hbWU6ICdsaW5rJyxcbiAgICBwcmlvcml0eTogMTAwMCxcbiAgICBrZWVwT25TcGxpdDogZmFsc2UsXG4gICAgZXhpdGFibGU6IHRydWUsXG4gICAgb25DcmVhdGUoKSB7XG4gICAgICAgIGlmICh0aGlzLm9wdGlvbnMudmFsaWRhdGUgJiYgIXRoaXMub3B0aW9ucy5zaG91bGRBdXRvTGluaykge1xuICAgICAgICAgICAgLy8gQ29weSB0aGUgdmFsaWRhdGUgZnVuY3Rpb24gdG8gdGhlIHNob3VsZEF1dG9MaW5rIG9wdGlvblxuICAgICAgICAgICAgdGhpcy5vcHRpb25zLnNob3VsZEF1dG9MaW5rID0gdGhpcy5vcHRpb25zLnZhbGlkYXRlO1xuICAgICAgICAgICAgY29uc29sZS53YXJuKCdUaGUgYHZhbGlkYXRlYCBvcHRpb24gaXMgZGVwcmVjYXRlZC4gUmVuYW1lIHRvIHRoZSBgc2hvdWxkQXV0b0xpbmtgIG9wdGlvbiBpbnN0ZWFkLicpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMub3B0aW9ucy5wcm90b2NvbHMuZm9yRWFjaChwcm90b2NvbCA9PiB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHByb3RvY29sID09PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgICAgIHJlZ2lzdGVyQ3VzdG9tUHJvdG9jb2wocHJvdG9jb2wpO1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJlZ2lzdGVyQ3VzdG9tUHJvdG9jb2wocHJvdG9jb2wuc2NoZW1lLCBwcm90b2NvbC5vcHRpb25hbFNsYXNoZXMpO1xuICAgICAgICB9KTtcbiAgICB9LFxuICAgIG9uRGVzdHJveSgpIHtcbiAgICAgICAgcmVzZXQoKTtcbiAgICB9LFxuICAgIGluY2x1c2l2ZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMub3B0aW9ucy5hdXRvbGluaztcbiAgICB9LFxuICAgIGFkZE9wdGlvbnMoKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBvcGVuT25DbGljazogdHJ1ZSxcbiAgICAgICAgICAgIGxpbmtPblBhc3RlOiB0cnVlLFxuICAgICAgICAgICAgYXV0b2xpbms6IHRydWUsXG4gICAgICAgICAgICBwcm90b2NvbHM6IFtdLFxuICAgICAgICAgICAgZGVmYXVsdFByb3RvY29sOiAnaHR0cCcsXG4gICAgICAgICAgICBIVE1MQXR0cmlidXRlczoge1xuICAgICAgICAgICAgICAgIHRhcmdldDogJ19ibGFuaycsXG4gICAgICAgICAgICAgICAgcmVsOiAnbm9vcGVuZXIgbm9yZWZlcnJlciBub2ZvbGxvdycsXG4gICAgICAgICAgICAgICAgY2xhc3M6IG51bGwsXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgaXNBbGxvd2VkVXJpOiAodXJsLCBjdHgpID0+ICEhaXNBbGxvd2VkVXJpKHVybCwgY3R4LnByb3RvY29scyksXG4gICAgICAgICAgICB2YWxpZGF0ZTogdXJsID0+ICEhdXJsLFxuICAgICAgICAgICAgc2hvdWxkQXV0b0xpbms6IHVybCA9PiAhIXVybCxcbiAgICAgICAgfTtcbiAgICB9LFxuICAgIGFkZEF0dHJpYnV0ZXMoKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBocmVmOiB7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDogbnVsbCxcbiAgICAgICAgICAgICAgICBwYXJzZUhUTUwoZWxlbWVudCkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZWxlbWVudC5nZXRBdHRyaWJ1dGUoJ2hyZWYnKTtcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHRhcmdldDoge1xuICAgICAgICAgICAgICAgIGRlZmF1bHQ6IHRoaXMub3B0aW9ucy5IVE1MQXR0cmlidXRlcy50YXJnZXQsXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgcmVsOiB7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDogdGhpcy5vcHRpb25zLkhUTUxBdHRyaWJ1dGVzLnJlbCxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBjbGFzczoge1xuICAgICAgICAgICAgICAgIGRlZmF1bHQ6IHRoaXMub3B0aW9ucy5IVE1MQXR0cmlidXRlcy5jbGFzcyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH07XG4gICAgfSxcbiAgICBwYXJzZUhUTUwoKSB7XG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgdGFnOiAnYVtocmVmXScsXG4gICAgICAgICAgICAgICAgZ2V0QXR0cnM6IGRvbSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGhyZWYgPSBkb20uZ2V0QXR0cmlidXRlKCdocmVmJyk7XG4gICAgICAgICAgICAgICAgICAgIC8vIHByZXZlbnQgWFNTIGF0dGFja3NcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFocmVmXG4gICAgICAgICAgICAgICAgICAgICAgICB8fCAhdGhpcy5vcHRpb25zLmlzQWxsb3dlZFVyaShocmVmLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbGlkYXRlOiB1cmwgPT4gISFpc0FsbG93ZWRVcmkodXJsLCB0aGlzLm9wdGlvbnMucHJvdG9jb2xzKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm90b2NvbHM6IHRoaXMub3B0aW9ucy5wcm90b2NvbHMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFByb3RvY29sOiB0aGlzLm9wdGlvbnMuZGVmYXVsdFByb3RvY29sLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfSxcbiAgICAgICAgXTtcbiAgICB9LFxuICAgIHJlbmRlckhUTUwoeyBIVE1MQXR0cmlidXRlcyB9KSB7XG4gICAgICAgIC8vIHByZXZlbnQgWFNTIGF0dGFja3NcbiAgICAgICAgaWYgKCF0aGlzLm9wdGlvbnMuaXNBbGxvd2VkVXJpKEhUTUxBdHRyaWJ1dGVzLmhyZWYsIHtcbiAgICAgICAgICAgIGRlZmF1bHRWYWxpZGF0ZTogaHJlZiA9PiAhIWlzQWxsb3dlZFVyaShocmVmLCB0aGlzLm9wdGlvbnMucHJvdG9jb2xzKSxcbiAgICAgICAgICAgIHByb3RvY29sczogdGhpcy5vcHRpb25zLnByb3RvY29scyxcbiAgICAgICAgICAgIGRlZmF1bHRQcm90b2NvbDogdGhpcy5vcHRpb25zLmRlZmF1bHRQcm90b2NvbCxcbiAgICAgICAgfSkpIHtcbiAgICAgICAgICAgIC8vIHN0cmlwIG91dCB0aGUgaHJlZlxuICAgICAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgICAgICAnYScsXG4gICAgICAgICAgICAgICAgbWVyZ2VBdHRyaWJ1dGVzKHRoaXMub3B0aW9ucy5IVE1MQXR0cmlidXRlcywgeyAuLi5IVE1MQXR0cmlidXRlcywgaHJlZjogJycgfSksXG4gICAgICAgICAgICAgICAgMCxcbiAgICAgICAgICAgIF07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIFsnYScsIG1lcmdlQXR0cmlidXRlcyh0aGlzLm9wdGlvbnMuSFRNTEF0dHJpYnV0ZXMsIEhUTUxBdHRyaWJ1dGVzKSwgMF07XG4gICAgfSxcbiAgICBhZGRDb21tYW5kcygpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHNldExpbms6IGF0dHJpYnV0ZXMgPT4gKHsgY2hhaW4gfSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgaHJlZiB9ID0gYXR0cmlidXRlcztcbiAgICAgICAgICAgICAgICBpZiAoIXRoaXMub3B0aW9ucy5pc0FsbG93ZWRVcmkoaHJlZiwge1xuICAgICAgICAgICAgICAgICAgICBkZWZhdWx0VmFsaWRhdGU6IHVybCA9PiAhIWlzQWxsb3dlZFVyaSh1cmwsIHRoaXMub3B0aW9ucy5wcm90b2NvbHMpLFxuICAgICAgICAgICAgICAgICAgICBwcm90b2NvbHM6IHRoaXMub3B0aW9ucy5wcm90b2NvbHMsXG4gICAgICAgICAgICAgICAgICAgIGRlZmF1bHRQcm90b2NvbDogdGhpcy5vcHRpb25zLmRlZmF1bHRQcm90b2NvbCxcbiAgICAgICAgICAgICAgICB9KSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiBjaGFpbigpLnNldE1hcmsodGhpcy5uYW1lLCBhdHRyaWJ1dGVzKS5zZXRNZXRhKCdwcmV2ZW50QXV0b2xpbmsnLCB0cnVlKS5ydW4oKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB0b2dnbGVMaW5rOiBhdHRyaWJ1dGVzID0+ICh7IGNoYWluIH0pID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCB7IGhyZWYgfSA9IGF0dHJpYnV0ZXM7XG4gICAgICAgICAgICAgICAgaWYgKCF0aGlzLm9wdGlvbnMuaXNBbGxvd2VkVXJpKGhyZWYsIHtcbiAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbGlkYXRlOiB1cmwgPT4gISFpc0FsbG93ZWRVcmkodXJsLCB0aGlzLm9wdGlvbnMucHJvdG9jb2xzKSxcbiAgICAgICAgICAgICAgICAgICAgcHJvdG9jb2xzOiB0aGlzLm9wdGlvbnMucHJvdG9jb2xzLFxuICAgICAgICAgICAgICAgICAgICBkZWZhdWx0UHJvdG9jb2w6IHRoaXMub3B0aW9ucy5kZWZhdWx0UHJvdG9jb2wsXG4gICAgICAgICAgICAgICAgfSkpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gY2hhaW4oKVxuICAgICAgICAgICAgICAgICAgICAudG9nZ2xlTWFyayh0aGlzLm5hbWUsIGF0dHJpYnV0ZXMsIHsgZXh0ZW5kRW1wdHlNYXJrUmFuZ2U6IHRydWUgfSlcbiAgICAgICAgICAgICAgICAgICAgLnNldE1ldGEoJ3ByZXZlbnRBdXRvbGluaycsIHRydWUpXG4gICAgICAgICAgICAgICAgICAgIC5ydW4oKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB1bnNldExpbms6ICgpID0+ICh7IGNoYWluIH0pID0+IHtcbiAgICAgICAgICAgICAgICByZXR1cm4gY2hhaW4oKVxuICAgICAgICAgICAgICAgICAgICAudW5zZXRNYXJrKHRoaXMubmFtZSwgeyBleHRlbmRFbXB0eU1hcmtSYW5nZTogdHJ1ZSB9KVxuICAgICAgICAgICAgICAgICAgICAuc2V0TWV0YSgncHJldmVudEF1dG9saW5rJywgdHJ1ZSlcbiAgICAgICAgICAgICAgICAgICAgLnJ1bigpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICB9LFxuICAgIGFkZFBhc3RlUnVsZXMoKSB7XG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICBtYXJrUGFzdGVSdWxlKHtcbiAgICAgICAgICAgICAgICBmaW5kOiB0ZXh0ID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZm91bmRMaW5rcyA9IFtdO1xuICAgICAgICAgICAgICAgICAgICBpZiAodGV4dCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgeyBwcm90b2NvbHMsIGRlZmF1bHRQcm90b2NvbCB9ID0gdGhpcy5vcHRpb25zO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbGlua3MgPSBmaW5kKHRleHQpLmZpbHRlcihpdGVtID0+IGl0ZW0uaXNMaW5rXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJiYgdGhpcy5vcHRpb25zLmlzQWxsb3dlZFVyaShpdGVtLnZhbHVlLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlZmF1bHRWYWxpZGF0ZTogaHJlZiA9PiAhIWlzQWxsb3dlZFVyaShocmVmLCBwcm90b2NvbHMpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm90b2NvbHMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlZmF1bHRQcm90b2NvbCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAobGlua3MubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGlua3MuZm9yRWFjaChsaW5rID0+IGZvdW5kTGlua3MucHVzaCh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQ6IGxpbmsudmFsdWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY6IGxpbmsuaHJlZixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXg6IGxpbmsuc3RhcnQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBmb3VuZExpbmtzO1xuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgdHlwZTogdGhpcy50eXBlLFxuICAgICAgICAgICAgICAgIGdldEF0dHJpYnV0ZXM6IG1hdGNoID0+IHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIF9hO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgaHJlZjogKF9hID0gbWF0Y2guZGF0YSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmhyZWYsXG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0pLFxuICAgICAgICBdO1xuICAgIH0sXG4gICAgYWRkUHJvc2VNaXJyb3JQbHVnaW5zKCkge1xuICAgICAgICBjb25zdCBwbHVnaW5zID0gW107XG4gICAgICAgIGNvbnN0IHsgcHJvdG9jb2xzLCBkZWZhdWx0UHJvdG9jb2wgfSA9IHRoaXMub3B0aW9ucztcbiAgICAgICAgaWYgKHRoaXMub3B0aW9ucy5hdXRvbGluaykge1xuICAgICAgICAgICAgcGx1Z2lucy5wdXNoKGF1dG9saW5rKHtcbiAgICAgICAgICAgICAgICB0eXBlOiB0aGlzLnR5cGUsXG4gICAgICAgICAgICAgICAgZGVmYXVsdFByb3RvY29sOiB0aGlzLm9wdGlvbnMuZGVmYXVsdFByb3RvY29sLFxuICAgICAgICAgICAgICAgIHZhbGlkYXRlOiB1cmwgPT4gdGhpcy5vcHRpb25zLmlzQWxsb3dlZFVyaSh1cmwsIHtcbiAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbGlkYXRlOiBocmVmID0+ICEhaXNBbGxvd2VkVXJpKGhyZWYsIHByb3RvY29scyksXG4gICAgICAgICAgICAgICAgICAgIHByb3RvY29scyxcbiAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFByb3RvY29sLFxuICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgIHNob3VsZEF1dG9MaW5rOiB0aGlzLm9wdGlvbnMuc2hvdWxkQXV0b0xpbmssXG4gICAgICAgICAgICB9KSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMub3B0aW9ucy5vcGVuT25DbGljayA9PT0gdHJ1ZSkge1xuICAgICAgICAgICAgcGx1Z2lucy5wdXNoKGNsaWNrSGFuZGxlcih7XG4gICAgICAgICAgICAgICAgdHlwZTogdGhpcy50eXBlLFxuICAgICAgICAgICAgfSkpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLm9wdGlvbnMubGlua09uUGFzdGUpIHtcbiAgICAgICAgICAgIHBsdWdpbnMucHVzaChwYXN0ZUhhbmRsZXIoe1xuICAgICAgICAgICAgICAgIGVkaXRvcjogdGhpcy5lZGl0b3IsXG4gICAgICAgICAgICAgICAgZGVmYXVsdFByb3RvY29sOiB0aGlzLm9wdGlvbnMuZGVmYXVsdFByb3RvY29sLFxuICAgICAgICAgICAgICAgIHR5cGU6IHRoaXMudHlwZSxcbiAgICAgICAgICAgIH0pKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcGx1Z2lucztcbiAgICB9LFxufSk7XG5cbmV4cG9ydCB7IExpbmssIExpbmsgYXMgZGVmYXVsdCwgaXNBbGxvd2VkVXJpLCBwYXN0ZVJlZ2V4IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-link@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-link/dist/index.js\n");

/***/ })

};
;