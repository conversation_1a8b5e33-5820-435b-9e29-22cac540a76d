{"version": 3, "sources": ["../../../src/build/static-paths/pages.ts"], "sourcesContent": ["import type { GetStaticPaths } from '../../types'\nimport type { PrerenderedRoute, StaticPathsResult } from './types'\n\nimport { normalizeLocalePath } from '../../shared/lib/i18n/normalize-locale-path'\nimport { parseStaticPathsResult } from '../../lib/fallback'\nimport escapePathDelimiters from '../../shared/lib/router/utils/escape-path-delimiters'\nimport { removeTrailingSlash } from '../../shared/lib/router/utils/remove-trailing-slash'\nimport { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher'\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex'\nimport { encodeParam, normalizePathname } from './utils'\n\nexport async function buildPagesStaticPaths({\n  page,\n  getStaticPaths,\n  configFileName,\n  locales,\n  defaultLocale,\n}: {\n  page: string\n  getStaticPaths: GetStaticPaths\n  configFileName: string\n  locales?: readonly string[]\n  defaultLocale?: string\n}): Promise<StaticPathsResult> {\n  const prerenderedRoutesByPathname = new Map<string, PrerenderedRoute>()\n  const _routeRegex = getRouteRegex(page)\n  const _routeMatcher = getRouteMatcher(_routeRegex)\n\n  // Get the default list of allowed params.\n  const routeParameterKeys = Object.keys(_routeMatcher(page))\n  const staticPathsResult = await getStaticPaths({\n    // We create a copy here to avoid having the types of `getStaticPaths`\n    // change. This ensures that users can't mutate this array and have it\n    // poison the reference.\n    locales: [...(locales ?? [])],\n    defaultLocale,\n  })\n\n  const expectedReturnVal =\n    `Expected: { paths: [], fallback: boolean }\\n` +\n    `See here for more info: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n\n  if (\n    !staticPathsResult ||\n    typeof staticPathsResult !== 'object' ||\n    Array.isArray(staticPathsResult)\n  ) {\n    throw new Error(\n      `Invalid value returned from getStaticPaths in ${page}. Received ${typeof staticPathsResult} ${expectedReturnVal}`\n    )\n  }\n\n  const invalidStaticPathKeys = Object.keys(staticPathsResult).filter(\n    (key) => !(key === 'paths' || key === 'fallback')\n  )\n\n  if (invalidStaticPathKeys.length > 0) {\n    throw new Error(\n      `Extra keys returned from getStaticPaths in ${page} (${invalidStaticPathKeys.join(\n        ', '\n      )}) ${expectedReturnVal}`\n    )\n  }\n\n  if (\n    !(\n      typeof staticPathsResult.fallback === 'boolean' ||\n      staticPathsResult.fallback === 'blocking'\n    )\n  ) {\n    throw new Error(\n      `The \\`fallback\\` key must be returned from getStaticPaths in ${page}.\\n` +\n        expectedReturnVal\n    )\n  }\n\n  const toPrerender = staticPathsResult.paths\n\n  if (!Array.isArray(toPrerender)) {\n    throw new Error(\n      `Invalid \\`paths\\` value returned from getStaticPaths in ${page}.\\n` +\n        `\\`paths\\` must be an array of strings or objects of shape { params: [key: string]: string }`\n    )\n  }\n\n  toPrerender.forEach((entry) => {\n    // For a string-provided path, we must make sure it matches the dynamic\n    // route.\n    if (typeof entry === 'string') {\n      entry = removeTrailingSlash(entry)\n\n      const localePathResult = normalizeLocalePath(entry, locales)\n      let cleanedEntry = entry\n\n      if (localePathResult.detectedLocale) {\n        cleanedEntry = entry.slice(localePathResult.detectedLocale.length + 1)\n      } else if (defaultLocale) {\n        entry = `/${defaultLocale}${entry}`\n      }\n\n      const params = _routeMatcher(cleanedEntry)\n      if (!params) {\n        throw new Error(\n          `The provided path \\`${cleanedEntry}\\` does not match the page: \\`${page}\\`.`\n        )\n      }\n\n      // If leveraging the string paths variant the entry should already be\n      // encoded so we decode the segments ensuring we only escape path\n      // delimiters\n      const pathname = entry\n        .split('/')\n        .map((segment) =>\n          escapePathDelimiters(decodeURIComponent(segment), true)\n        )\n        .join('/')\n\n      if (!prerenderedRoutesByPathname.has(pathname)) {\n        prerenderedRoutesByPathname.set(pathname, {\n          params,\n          pathname,\n          encodedPathname: entry,\n          fallbackRouteParams: undefined,\n          fallbackMode: parseStaticPathsResult(staticPathsResult.fallback),\n          fallbackRootParams: undefined,\n          throwOnEmptyStaticShell: undefined,\n        })\n      }\n    }\n    // For the object-provided path, we must make sure it specifies all\n    // required keys.\n    else {\n      const invalidKeys = Object.keys(entry).filter(\n        (key) => key !== 'params' && key !== 'locale'\n      )\n\n      if (invalidKeys.length) {\n        throw new Error(\n          `Additional keys were returned from \\`getStaticPaths\\` in page \"${page}\". ` +\n            `URL Parameters intended for this dynamic route must be nested under the \\`params\\` key, i.e.:` +\n            `\\n\\n\\treturn { params: { ${routeParameterKeys\n              .map((k) => `${k}: ...`)\n              .join(', ')} } }` +\n            `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.\\n`\n        )\n      }\n\n      const { params = {} } = entry\n      let builtPage = page\n      let encodedBuiltPage = page\n\n      routeParameterKeys.forEach((validParamKey) => {\n        const { repeat, optional } = _routeRegex.groups[validParamKey]\n        let paramValue = params[validParamKey]\n        if (\n          optional &&\n          params.hasOwnProperty(validParamKey) &&\n          (paramValue === null ||\n            paramValue === undefined ||\n            (paramValue as any) === false)\n        ) {\n          paramValue = []\n        }\n\n        if (\n          (repeat && !Array.isArray(paramValue)) ||\n          (!repeat && typeof paramValue !== 'string') ||\n          typeof paramValue === 'undefined'\n        ) {\n          throw new Error(\n            `A required parameter (${validParamKey}) was not provided as ${\n              repeat ? 'an array' : 'a string'\n            } received ${typeof paramValue} in getStaticPaths for ${page}`\n          )\n        }\n\n        let replaced = `[${repeat ? '...' : ''}${validParamKey}]`\n        if (optional) {\n          replaced = `[${replaced}]`\n        }\n\n        builtPage = builtPage.replace(\n          replaced,\n          encodeParam(paramValue, (value) => escapePathDelimiters(value, true))\n        )\n\n        encodedBuiltPage = encodedBuiltPage.replace(\n          replaced,\n          encodeParam(paramValue, encodeURIComponent)\n        )\n      })\n\n      if (!builtPage && !encodedBuiltPage) {\n        return\n      }\n\n      if (entry.locale && !locales?.includes(entry.locale)) {\n        throw new Error(\n          `Invalid locale returned from getStaticPaths for ${page}, the locale ${entry.locale} is not specified in ${configFileName}`\n        )\n      }\n      const curLocale = entry.locale || defaultLocale || ''\n\n      const pathname = normalizePathname(\n        `${curLocale ? `/${curLocale}` : ''}${curLocale && builtPage === '/' ? '' : builtPage}`\n      )\n\n      if (!prerenderedRoutesByPathname.has(pathname)) {\n        prerenderedRoutesByPathname.set(pathname, {\n          params,\n          pathname,\n          encodedPathname: normalizePathname(\n            `${curLocale ? `/${curLocale}` : ''}${\n              curLocale && encodedBuiltPage === '/' ? '' : encodedBuiltPage\n            }`\n          ),\n          fallbackRouteParams: undefined,\n          fallbackMode: parseStaticPathsResult(staticPathsResult.fallback),\n          fallbackRootParams: undefined,\n          throwOnEmptyStaticShell: undefined,\n        })\n      }\n    }\n  })\n\n  return {\n    fallbackMode: parseStaticPathsResult(staticPathsResult.fallback),\n    prerenderedRoutes: [...prerenderedRoutesByPathname.values()],\n  }\n}\n"], "names": ["buildPagesStaticPaths", "page", "getStaticPaths", "configFileName", "locales", "defaultLocale", "prerenderedRoutesByPathname", "Map", "_routeRegex", "getRouteRegex", "_routeMatcher", "getRouteMatcher", "routeParameterKeys", "Object", "keys", "staticPathsResult", "expectedReturnVal", "Array", "isArray", "Error", "invalidStatic<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "key", "length", "join", "fallback", "<PERSON><PERSON><PERSON><PERSON>", "paths", "for<PERSON>ach", "entry", "removeTrailingSlash", "localePathResult", "normalizeLocalePath", "cleanedEntry", "detectedLocale", "slice", "params", "pathname", "split", "map", "segment", "escapePathDelimiters", "decodeURIComponent", "has", "set", "encodedPathname", "fallbackRouteParams", "undefined", "fallbackMode", "parseStaticPathsResult", "fallbackRootParams", "throwOnEmptyStaticShell", "<PERSON><PERSON><PERSON><PERSON>", "k", "builtPage", "encodedBuiltPage", "validParamKey", "repeat", "optional", "groups", "paramValue", "hasOwnProperty", "replaced", "replace", "encodeParam", "value", "encodeURIComponent", "locale", "includes", "cur<PERSON><PERSON><PERSON>", "normalizePathname", "prerenderedRoutes", "values"], "mappings": ";;;;+BA<PERSON>sBA;;;eAAAA;;;qCARc;0BACG;6EACN;qCACG;8BACJ;4BACF;uBACiB;;;;;;AAExC,eAAeA,sBAAsB,EAC1CC,IAAI,EACJC,cAAc,EACdC,cAAc,EACdC,OAAO,EACPC,aAAa,EAOd;IACC,MAAMC,8BAA8B,IAAIC;IACxC,MAAMC,cAAcC,IAAAA,yBAAa,EAACR;IAClC,MAAMS,gBAAgBC,IAAAA,6BAAe,EAACH;IAEtC,0CAA0C;IAC1C,MAAMI,qBAAqBC,OAAOC,IAAI,CAACJ,cAAcT;IACrD,MAAMc,oBAAoB,MAAMb,eAAe;QAC7C,sEAAsE;QACtE,sEAAsE;QACtE,wBAAwB;QACxBE,SAAS;eAAKA,WAAW,EAAE;SAAE;QAC7BC;IACF;IAEA,MAAMW,oBACJ,CAAC,4CAA4C,CAAC,GAC9C,CAAC,qFAAqF,CAAC;IAEzF,IACE,CAACD,qBACD,OAAOA,sBAAsB,YAC7BE,MAAMC,OAAO,CAACH,oBACd;QACA,MAAM,qBAEL,CAFK,IAAII,MACR,CAAC,8CAA8C,EAAElB,KAAK,WAAW,EAAE,OAAOc,kBAAkB,CAAC,EAAEC,mBAAmB,GAD9G,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMI,wBAAwBP,OAAOC,IAAI,CAACC,mBAAmBM,MAAM,CACjE,CAACC,MAAQ,CAAEA,CAAAA,QAAQ,WAAWA,QAAQ,UAAS;IAGjD,IAAIF,sBAAsBG,MAAM,GAAG,GAAG;QACpC,MAAM,qBAIL,CAJK,IAAIJ,MACR,CAAC,2CAA2C,EAAElB,KAAK,EAAE,EAAEmB,sBAAsBI,IAAI,CAC/E,MACA,EAAE,EAAER,mBAAmB,GAHrB,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,IACE,CACE,CAAA,OAAOD,kBAAkBU,QAAQ,KAAK,aACtCV,kBAAkBU,QAAQ,KAAK,UAAS,GAE1C;QACA,MAAM,qBAGL,CAHK,IAAIN,MACR,CAAC,6DAA6D,EAAElB,KAAK,GAAG,CAAC,GACvEe,oBAFE,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,MAAMU,cAAcX,kBAAkBY,KAAK;IAE3C,IAAI,CAACV,MAAMC,OAAO,CAACQ,cAAc;QAC/B,MAAM,qBAGL,CAHK,IAAIP,MACR,CAAC,wDAAwD,EAAElB,KAAK,GAAG,CAAC,GAClE,CAAC,2FAA2F,CAAC,GAF3F,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEAyB,YAAYE,OAAO,CAAC,CAACC;QACnB,uEAAuE;QACvE,SAAS;QACT,IAAI,OAAOA,UAAU,UAAU;YAC7BA,QAAQC,IAAAA,wCAAmB,EAACD;YAE5B,MAAME,mBAAmBC,IAAAA,wCAAmB,EAACH,OAAOzB;YACpD,IAAI6B,eAAeJ;YAEnB,IAAIE,iBAAiBG,cAAc,EAAE;gBACnCD,eAAeJ,MAAMM,KAAK,CAACJ,iBAAiBG,cAAc,CAACX,MAAM,GAAG;YACtE,OAAO,IAAIlB,eAAe;gBACxBwB,QAAQ,CAAC,CAAC,EAAExB,gBAAgBwB,OAAO;YACrC;YAEA,MAAMO,SAAS1B,cAAcuB;YAC7B,IAAI,CAACG,QAAQ;gBACX,MAAM,qBAEL,CAFK,IAAIjB,MACR,CAAC,oBAAoB,EAAEc,aAAa,8BAA8B,EAAEhC,KAAK,GAAG,CAAC,GADzE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,qEAAqE;YACrE,iEAAiE;YACjE,aAAa;YACb,MAAMoC,WAAWR,MACdS,KAAK,CAAC,KACNC,GAAG,CAAC,CAACC,UACJC,IAAAA,6BAAoB,EAACC,mBAAmBF,UAAU,OAEnDhB,IAAI,CAAC;YAER,IAAI,CAAClB,4BAA4BqC,GAAG,CAACN,WAAW;gBAC9C/B,4BAA4BsC,GAAG,CAACP,UAAU;oBACxCD;oBACAC;oBACAQ,iBAAiBhB;oBACjBiB,qBAAqBC;oBACrBC,cAAcC,IAAAA,gCAAsB,EAAClC,kBAAkBU,QAAQ;oBAC/DyB,oBAAoBH;oBACpBI,yBAAyBJ;gBAC3B;YACF;QACF,OAGK;YACH,MAAMK,cAAcvC,OAAOC,IAAI,CAACe,OAAOR,MAAM,CAC3C,CAACC,MAAQA,QAAQ,YAAYA,QAAQ;YAGvC,IAAI8B,YAAY7B,MAAM,EAAE;gBACtB,MAAM,qBAOL,CAPK,IAAIJ,MACR,CAAC,+DAA+D,EAAElB,KAAK,GAAG,CAAC,GACzE,CAAC,6FAA6F,CAAC,GAC/F,CAAC,yBAAyB,EAAEW,mBACzB2B,GAAG,CAAC,CAACc,IAAM,GAAGA,EAAE,KAAK,CAAC,EACtB7B,IAAI,CAAC,MAAM,IAAI,CAAC,GACnB,CAAC,gCAAgC,EAAE4B,YAAY5B,IAAI,CAAC,MAAM,GAAG,CAAC,GAN5D,qBAAA;2BAAA;gCAAA;kCAAA;gBAON;YACF;YAEA,MAAM,EAAEY,SAAS,CAAC,CAAC,EAAE,GAAGP;YACxB,IAAIyB,YAAYrD;YAChB,IAAIsD,mBAAmBtD;YAEvBW,mBAAmBgB,OAAO,CAAC,CAAC4B;gBAC1B,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAGlD,YAAYmD,MAAM,CAACH,cAAc;gBAC9D,IAAII,aAAaxB,MAAM,CAACoB,cAAc;gBACtC,IACEE,YACAtB,OAAOyB,cAAc,CAACL,kBACrBI,CAAAA,eAAe,QACdA,eAAeb,aACf,AAACa,eAAuB,KAAI,GAC9B;oBACAA,aAAa,EAAE;gBACjB;gBAEA,IACE,AAACH,UAAU,CAACxC,MAAMC,OAAO,CAAC0C,eACzB,CAACH,UAAU,OAAOG,eAAe,YAClC,OAAOA,eAAe,aACtB;oBACA,MAAM,qBAIL,CAJK,IAAIzC,MACR,CAAC,sBAAsB,EAAEqC,cAAc,sBAAsB,EAC3DC,SAAS,aAAa,WACvB,UAAU,EAAE,OAAOG,WAAW,uBAAuB,EAAE3D,MAAM,GAH1D,qBAAA;+BAAA;oCAAA;sCAAA;oBAIN;gBACF;gBAEA,IAAI6D,WAAW,CAAC,CAAC,EAAEL,SAAS,QAAQ,KAAKD,cAAc,CAAC,CAAC;gBACzD,IAAIE,UAAU;oBACZI,WAAW,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC;gBAC5B;gBAEAR,YAAYA,UAAUS,OAAO,CAC3BD,UACAE,IAAAA,kBAAW,EAACJ,YAAY,CAACK,QAAUxB,IAAAA,6BAAoB,EAACwB,OAAO;gBAGjEV,mBAAmBA,iBAAiBQ,OAAO,CACzCD,UACAE,IAAAA,kBAAW,EAACJ,YAAYM;YAE5B;YAEA,IAAI,CAACZ,aAAa,CAACC,kBAAkB;gBACnC;YACF;YAEA,IAAI1B,MAAMsC,MAAM,IAAI,EAAC/D,2BAAAA,QAASgE,QAAQ,CAACvC,MAAMsC,MAAM,IAAG;gBACpD,MAAM,qBAEL,CAFK,IAAIhD,MACR,CAAC,gDAAgD,EAAElB,KAAK,aAAa,EAAE4B,MAAMsC,MAAM,CAAC,qBAAqB,EAAEhE,gBAAgB,GADvH,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAMkE,YAAYxC,MAAMsC,MAAM,IAAI9D,iBAAiB;YAEnD,MAAMgC,WAAWiC,IAAAA,wBAAiB,EAChC,GAAGD,YAAY,CAAC,CAAC,EAAEA,WAAW,GAAG,KAAKA,aAAaf,cAAc,MAAM,KAAKA,WAAW;YAGzF,IAAI,CAAChD,4BAA4BqC,GAAG,CAACN,WAAW;gBAC9C/B,4BAA4BsC,GAAG,CAACP,UAAU;oBACxCD;oBACAC;oBACAQ,iBAAiByB,IAAAA,wBAAiB,EAChC,GAAGD,YAAY,CAAC,CAAC,EAAEA,WAAW,GAAG,KAC/BA,aAAad,qBAAqB,MAAM,KAAKA,kBAC7C;oBAEJT,qBAAqBC;oBACrBC,cAAcC,IAAAA,gCAAsB,EAAClC,kBAAkBU,QAAQ;oBAC/DyB,oBAAoBH;oBACpBI,yBAAyBJ;gBAC3B;YACF;QACF;IACF;IAEA,OAAO;QACLC,cAAcC,IAAAA,gCAAsB,EAAClC,kBAAkBU,QAAQ;QAC/D8C,mBAAmB;eAAIjE,4BAA4BkE,MAAM;SAAG;IAC9D;AACF", "ignoreList": [0]}