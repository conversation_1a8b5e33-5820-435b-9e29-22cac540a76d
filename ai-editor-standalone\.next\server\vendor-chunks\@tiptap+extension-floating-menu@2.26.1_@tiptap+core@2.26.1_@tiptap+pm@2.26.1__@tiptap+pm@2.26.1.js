"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-floating-menu/dist/index.js":
/*!***************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-floating-menu/dist/index.js ***!
  \***************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingMenu: () => (/* binding */ FloatingMenu),\n/* harmony export */   FloatingMenuPlugin: () => (/* binding */ FloatingMenuPlugin),\n/* harmony export */   FloatingMenuView: () => (/* binding */ FloatingMenuView),\n/* harmony export */   \"default\": () => (/* binding */ FloatingMenu)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var tippy_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tippy.js */ \"(ssr)/./node_modules/.pnpm/tippy.js@6.3.7/node_modules/tippy.js/dist/tippy.esm.js\");\n\n\n\n\nclass FloatingMenuView {\n    getTextContent(node) {\n        return (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.getText)(node, { textSerializers: (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.getTextSerializersFromSchema)(this.editor.schema) });\n    }\n    constructor({ editor, element, view, tippyOptions = {}, shouldShow, }) {\n        this.preventHide = false;\n        this.shouldShow = ({ view, state }) => {\n            const { selection } = state;\n            const { $anchor, empty } = selection;\n            const isRootDepth = $anchor.depth === 1;\n            const isEmptyTextBlock = $anchor.parent.isTextblock && !$anchor.parent.type.spec.code && !$anchor.parent.textContent && $anchor.parent.childCount === 0 && !this.getTextContent($anchor.parent);\n            if (!view.hasFocus()\n                || !empty\n                || !isRootDepth\n                || !isEmptyTextBlock\n                || !this.editor.isEditable) {\n                return false;\n            }\n            return true;\n        };\n        this.mousedownHandler = () => {\n            this.preventHide = true;\n        };\n        this.focusHandler = () => {\n            // we use `setTimeout` to make sure `selection` is already updated\n            setTimeout(() => this.update(this.editor.view));\n        };\n        this.blurHandler = ({ event }) => {\n            var _a;\n            if (this.preventHide) {\n                this.preventHide = false;\n                return;\n            }\n            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) && ((_a = this.element.parentNode) === null || _a === void 0 ? void 0 : _a.contains(event.relatedTarget))) {\n                return;\n            }\n            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) === this.editor.view.dom) {\n                return;\n            }\n            this.hide();\n        };\n        this.tippyBlurHandler = (event) => {\n            this.blurHandler({ event });\n        };\n        this.editor = editor;\n        this.element = element;\n        this.view = view;\n        if (shouldShow) {\n            this.shouldShow = shouldShow;\n        }\n        this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true });\n        this.editor.on('focus', this.focusHandler);\n        this.editor.on('blur', this.blurHandler);\n        this.tippyOptions = tippyOptions;\n        // Detaches menu content from its current parent\n        this.element.remove();\n        this.element.style.visibility = 'visible';\n    }\n    createTooltip() {\n        const { element: editorElement } = this.editor.options;\n        const editorIsAttached = !!editorElement.parentElement;\n        this.element.tabIndex = 0;\n        if (this.tippy || !editorIsAttached) {\n            return;\n        }\n        this.tippy = (0,tippy_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(editorElement, {\n            duration: 0,\n            getReferenceClientRect: null,\n            content: this.element,\n            interactive: true,\n            trigger: 'manual',\n            placement: 'right',\n            hideOnClick: 'toggle',\n            ...this.tippyOptions,\n        });\n        // maybe we have to hide tippy on its own blur event as well\n        if (this.tippy.popper.firstChild) {\n            this.tippy.popper.firstChild.addEventListener('blur', this.tippyBlurHandler);\n        }\n    }\n    update(view, oldState) {\n        var _a, _b, _c;\n        const { state } = view;\n        const { doc, selection } = state;\n        const { from, to } = selection;\n        const isSame = oldState && oldState.doc.eq(doc) && oldState.selection.eq(selection);\n        if (isSame) {\n            return;\n        }\n        this.createTooltip();\n        const shouldShow = (_a = this.shouldShow) === null || _a === void 0 ? void 0 : _a.call(this, {\n            editor: this.editor,\n            view,\n            state,\n            oldState,\n        });\n        if (!shouldShow) {\n            this.hide();\n            return;\n        }\n        (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.setProps({\n            getReferenceClientRect: ((_c = this.tippyOptions) === null || _c === void 0 ? void 0 : _c.getReferenceClientRect) || (() => (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.posToDOMRect)(view, from, to)),\n        });\n        this.show();\n    }\n    show() {\n        var _a;\n        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.show();\n    }\n    hide() {\n        var _a;\n        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.hide();\n    }\n    destroy() {\n        var _a, _b;\n        if ((_a = this.tippy) === null || _a === void 0 ? void 0 : _a.popper.firstChild) {\n            this.tippy.popper.firstChild.removeEventListener('blur', this.tippyBlurHandler);\n        }\n        (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.destroy();\n        this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true });\n        this.editor.off('focus', this.focusHandler);\n        this.editor.off('blur', this.blurHandler);\n    }\n}\nconst FloatingMenuPlugin = (options) => {\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        key: typeof options.pluginKey === 'string' ? new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(options.pluginKey) : options.pluginKey,\n        view: view => new FloatingMenuView({ view, ...options }),\n    });\n};\n\n/**\n * This extension allows you to create a floating menu.\n * @see https://tiptap.dev/api/extensions/floating-menu\n */\nconst FloatingMenu = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'floatingMenu',\n    addOptions() {\n        return {\n            element: null,\n            tippyOptions: {},\n            pluginKey: 'floatingMenu',\n            shouldShow: null,\n        };\n    },\n    addProseMirrorPlugins() {\n        if (!this.options.element) {\n            return [];\n        }\n        return [\n            FloatingMenuPlugin({\n                pluginKey: this.options.pluginKey,\n                editor: this.editor,\n                element: this.options.element,\n                tippyOptions: this.options.tippyOptions,\n                shouldShow: this.options.shouldShow,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-floating-menu/dist/index.js\n");

/***/ })

};
;