{"version": 3, "sources": ["../../../../src/build/babel/loader/transform.ts"], "sourcesContent": ["/*\n * Partially adapted from @babel/core (MIT license).\n */\n\nimport traverse from 'next/dist/compiled/babel/traverse'\nimport generate from 'next/dist/compiled/babel/generator'\nimport normalizeFile from 'next/dist/compiled/babel/core-lib-normalize-file'\nimport normalizeOpts from 'next/dist/compiled/babel/core-lib-normalize-opts'\nimport loadBlockHoistPlugin from 'next/dist/compiled/babel/core-lib-block-hoist-plugin'\nimport PluginPass from 'next/dist/compiled/babel/core-lib-plugin-pass'\n\nimport getConfig from './get-config'\nimport { consumeIterator } from './util'\nimport type { Span } from '../../../trace'\nimport type { NextJsLoaderContext } from './types'\n\nfunction getTraversalParams(file: any, pluginPairs: any[]) {\n  const passPairs = []\n  const passes = []\n  const visitors = []\n\n  for (const plugin of pluginPairs.concat(loadBlockHoistPlugin())) {\n    const pass = new PluginPass(file, plugin.key, plugin.options)\n    passPairs.push([plugin, pass])\n    passes.push(pass)\n    visitors.push(plugin.visitor)\n  }\n\n  return { passPairs, passes, visitors }\n}\n\nfunction invokePluginPre(file: any, passPairs: any[]) {\n  for (const [{ pre }, pass] of passPairs) {\n    if (pre) {\n      pre.call(pass, file)\n    }\n  }\n}\n\nfunction invokePluginPost(file: any, passPairs: any[]) {\n  for (const [{ post }, pass] of passPairs) {\n    if (post) {\n      post.call(pass, file)\n    }\n  }\n}\n\nfunction transformAstPass(file: any, pluginPairs: any[], parentSpan: Span) {\n  const { passPairs, passes, visitors } = getTraversalParams(file, pluginPairs)\n\n  invokePluginPre(file, passPairs)\n  const visitor = traverse.visitors.merge(\n    visitors,\n    passes,\n    // @ts-ignore - the exported types are incorrect here\n    file.opts.wrapPluginVisitorMethod\n  )\n\n  parentSpan\n    .traceChild('babel-turbo-traverse')\n    .traceFn(() => traverse(file.ast, visitor, file.scope))\n\n  invokePluginPost(file, passPairs)\n}\n\nfunction transformAst(file: any, babelConfig: any, parentSpan: Span) {\n  for (const pluginPairs of babelConfig.passes) {\n    transformAstPass(file, pluginPairs, parentSpan)\n  }\n}\n\nexport default async function transform(\n  this: NextJsLoaderContext,\n  source: string,\n  inputSourceMap: object | null | undefined,\n  loaderOptions: any,\n  filename: string,\n  target: string,\n  parentSpan: Span\n) {\n  const getConfigSpan = parentSpan.traceChild('babel-turbo-get-config')\n  const babelConfig = await getConfig.call(this, {\n    source,\n    loaderOptions,\n    inputSourceMap,\n    target,\n    filename,\n  })\n  if (!babelConfig) {\n    return { code: source, map: inputSourceMap }\n  }\n  getConfigSpan.stop()\n\n  const normalizeSpan = parentSpan.traceChild('babel-turbo-normalize-file')\n  const file = consumeIterator(\n    normalizeFile(babelConfig.passes, normalizeOpts(babelConfig), source)\n  )\n  normalizeSpan.stop()\n\n  const transformSpan = parentSpan.traceChild('babel-turbo-transform')\n  transformAst(file, babelConfig, transformSpan)\n  transformSpan.stop()\n\n  const generateSpan = parentSpan.traceChild('babel-turbo-generate')\n  const { code, map } = generate(file.ast, file.opts.generatorOpts, file.code)\n  generateSpan.stop()\n\n  return { code, map }\n}\n"], "names": ["transform", "getTraversalParams", "file", "pluginPairs", "passPairs", "passes", "visitors", "plugin", "concat", "loadBlockHoistPlugin", "pass", "Plug<PERSON><PERSON><PERSON>", "key", "options", "push", "visitor", "invokePluginPre", "pre", "call", "invokePluginPost", "post", "transformAstPass", "parentSpan", "traverse", "merge", "opts", "wrapPluginVisitorMethod", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "ast", "scope", "transformAst", "babelConfig", "source", "inputSourceMap", "loaderOptions", "filename", "target", "getConfigSpan", "getConfig", "code", "map", "stop", "normalizeSpan", "consumeIterator", "normalizeFile", "normalizeOpts", "transformSpan", "generateSpan", "generate", "generatorOpts"], "mappings": "AAAA;;CAEC;;;;+BAqED;;;eAA8BA;;;iEAnET;kEACA;6EACK;6EACA;gFACO;0EACV;kEAED;sBACU;;;;;;AAIhC,SAASC,mBAAmBC,IAAS,EAAEC,WAAkB;IACvD,MAAMC,YAAY,EAAE;IACpB,MAAMC,SAAS,EAAE;IACjB,MAAMC,WAAW,EAAE;IAEnB,KAAK,MAAMC,UAAUJ,YAAYK,MAAM,CAACC,IAAAA,gCAAoB,KAAK;QAC/D,MAAMC,OAAO,IAAIC,0BAAU,CAACT,MAAMK,OAAOK,GAAG,EAAEL,OAAOM,OAAO;QAC5DT,UAAUU,IAAI,CAAC;YAACP;YAAQG;SAAK;QAC7BL,OAAOS,IAAI,CAACJ;QACZJ,SAASQ,IAAI,CAACP,OAAOQ,OAAO;IAC9B;IAEA,OAAO;QAAEX;QAAWC;QAAQC;IAAS;AACvC;AAEA,SAASU,gBAAgBd,IAAS,EAAEE,SAAgB;IAClD,KAAK,MAAM,CAAC,EAAEa,GAAG,EAAE,EAAEP,KAAK,IAAIN,UAAW;QACvC,IAAIa,KAAK;YACPA,IAAIC,IAAI,CAACR,MAAMR;QACjB;IACF;AACF;AAEA,SAASiB,iBAAiBjB,IAAS,EAAEE,SAAgB;IACnD,KAAK,MAAM,CAAC,EAAEgB,IAAI,EAAE,EAAEV,KAAK,IAAIN,UAAW;QACxC,IAAIgB,MAAM;YACRA,KAAKF,IAAI,CAACR,MAAMR;QAClB;IACF;AACF;AAEA,SAASmB,iBAAiBnB,IAAS,EAAEC,WAAkB,EAAEmB,UAAgB;IACvE,MAAM,EAAElB,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAGL,mBAAmBC,MAAMC;IAEjEa,gBAAgBd,MAAME;IACtB,MAAMW,UAAUQ,iBAAQ,CAACjB,QAAQ,CAACkB,KAAK,CACrClB,UACAD,QACA,qDAAqD;IACrDH,KAAKuB,IAAI,CAACC,uBAAuB;IAGnCJ,WACGK,UAAU,CAAC,wBACXC,OAAO,CAAC,IAAML,IAAAA,iBAAQ,EAACrB,KAAK2B,GAAG,EAAEd,SAASb,KAAK4B,KAAK;IAEvDX,iBAAiBjB,MAAME;AACzB;AAEA,SAAS2B,aAAa7B,IAAS,EAAE8B,WAAgB,EAAEV,UAAgB;IACjE,KAAK,MAAMnB,eAAe6B,YAAY3B,MAAM,CAAE;QAC5CgB,iBAAiBnB,MAAMC,aAAamB;IACtC;AACF;AAEe,eAAetB,UAE5BiC,MAAc,EACdC,cAAyC,EACzCC,aAAkB,EAClBC,QAAgB,EAChBC,MAAc,EACdf,UAAgB;IAEhB,MAAMgB,gBAAgBhB,WAAWK,UAAU,CAAC;IAC5C,MAAMK,cAAc,MAAMO,kBAAS,CAACrB,IAAI,CAAC,IAAI,EAAE;QAC7Ce;QACAE;QACAD;QACAG;QACAD;IACF;IACA,IAAI,CAACJ,aAAa;QAChB,OAAO;YAAEQ,MAAMP;YAAQQ,KAAKP;QAAe;IAC7C;IACAI,cAAcI,IAAI;IAElB,MAAMC,gBAAgBrB,WAAWK,UAAU,CAAC;IAC5C,MAAMzB,OAAO0C,IAAAA,qBAAe,EAC1BC,IAAAA,6BAAa,EAACb,YAAY3B,MAAM,EAAEyC,IAAAA,6BAAa,EAACd,cAAcC;IAEhEU,cAAcD,IAAI;IAElB,MAAMK,gBAAgBzB,WAAWK,UAAU,CAAC;IAC5CI,aAAa7B,MAAM8B,aAAae;IAChCA,cAAcL,IAAI;IAElB,MAAMM,eAAe1B,WAAWK,UAAU,CAAC;IAC3C,MAAM,EAAEa,IAAI,EAAEC,GAAG,EAAE,GAAGQ,IAAAA,kBAAQ,EAAC/C,KAAK2B,GAAG,EAAE3B,KAAKuB,IAAI,CAACyB,aAAa,EAAEhD,KAAKsC,IAAI;IAC3EQ,aAAaN,IAAI;IAEjB,OAAO;QAAEF;QAAMC;IAAI;AACrB", "ignoreList": [0]}