"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-paragraph@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-paragraph@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-paragraph@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-paragraph/dist/index.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-paragraph@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-paragraph/dist/index.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Paragraph: () => (/* binding */ Paragraph),\n/* harmony export */   \"default\": () => (/* binding */ Paragraph)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * This extension allows you to create paragraphs.\n * @see https://www.tiptap.dev/api/nodes/paragraph\n */\nconst Paragraph = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'paragraph',\n    priority: 1000,\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    group: 'block',\n    content: 'inline*',\n    parseHTML() {\n        return [\n            { tag: 'p' },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['p', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setParagraph: () => ({ commands }) => {\n                return commands.setNode(this.name);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-Alt-0': () => this.editor.commands.setParagraph(),\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-paragraph@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-paragraph/dist/index.js\n");

/***/ })

};
;