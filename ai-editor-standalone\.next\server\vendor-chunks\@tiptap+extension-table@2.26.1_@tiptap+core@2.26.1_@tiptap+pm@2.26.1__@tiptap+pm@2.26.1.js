"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-table@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-table@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-table@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-table/dist/index.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-table@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-table/dist/index.js ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Table: () => (/* binding */ Table),\n/* harmony export */   TableView: () => (/* binding */ TableView),\n/* harmony export */   createColGroup: () => (/* binding */ createColGroup),\n/* harmony export */   createTable: () => (/* binding */ createTable),\n/* harmony export */   \"default\": () => (/* binding */ Table),\n/* harmony export */   updateColumns: () => (/* binding */ updateColumns)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var _tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/tables */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/tables/dist/index.js\");\n\n\n\n\nfunction getColStyleDeclaration(minWidth, width) {\n    if (width) {\n        // apply the stored width unless it is below the configured minimum cell width\n        return ['width', `${Math.max(width, minWidth)}px`];\n    }\n    // set the minimum with on the column if it has no stored width\n    return ['min-width', `${minWidth}px`];\n}\n\nfunction updateColumns(node, colgroup, // <colgroup> has the same prototype as <col>\ntable, cellMinWidth, overrideCol, overrideValue) {\n    var _a;\n    let totalWidth = 0;\n    let fixedWidth = true;\n    let nextDOM = colgroup.firstChild;\n    const row = node.firstChild;\n    if (row !== null) {\n        for (let i = 0, col = 0; i < row.childCount; i += 1) {\n            const { colspan, colwidth } = row.child(i).attrs;\n            for (let j = 0; j < colspan; j += 1, col += 1) {\n                const hasWidth = overrideCol === col ? overrideValue : (colwidth && colwidth[j]);\n                const cssWidth = hasWidth ? `${hasWidth}px` : '';\n                totalWidth += hasWidth || cellMinWidth;\n                if (!hasWidth) {\n                    fixedWidth = false;\n                }\n                if (!nextDOM) {\n                    const colElement = document.createElement('col');\n                    const [propertyKey, propertyValue] = getColStyleDeclaration(cellMinWidth, hasWidth);\n                    colElement.style.setProperty(propertyKey, propertyValue);\n                    colgroup.appendChild(colElement);\n                }\n                else {\n                    if (nextDOM.style.width !== cssWidth) {\n                        const [propertyKey, propertyValue] = getColStyleDeclaration(cellMinWidth, hasWidth);\n                        nextDOM.style.setProperty(propertyKey, propertyValue);\n                    }\n                    nextDOM = nextDOM.nextSibling;\n                }\n            }\n        }\n    }\n    while (nextDOM) {\n        const after = nextDOM.nextSibling;\n        (_a = nextDOM.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(nextDOM);\n        nextDOM = after;\n    }\n    if (fixedWidth) {\n        table.style.width = `${totalWidth}px`;\n        table.style.minWidth = '';\n    }\n    else {\n        table.style.width = '';\n        table.style.minWidth = `${totalWidth}px`;\n    }\n}\nclass TableView {\n    constructor(node, cellMinWidth) {\n        this.node = node;\n        this.cellMinWidth = cellMinWidth;\n        this.dom = document.createElement('div');\n        this.dom.className = 'tableWrapper';\n        this.table = this.dom.appendChild(document.createElement('table'));\n        this.colgroup = this.table.appendChild(document.createElement('colgroup'));\n        updateColumns(node, this.colgroup, this.table, cellMinWidth);\n        this.contentDOM = this.table.appendChild(document.createElement('tbody'));\n    }\n    update(node) {\n        if (node.type !== this.node.type) {\n            return false;\n        }\n        this.node = node;\n        updateColumns(node, this.colgroup, this.table, this.cellMinWidth);\n        return true;\n    }\n    ignoreMutation(mutation) {\n        return (mutation.type === 'attributes'\n            && (mutation.target === this.table || this.colgroup.contains(mutation.target)));\n    }\n}\n\nfunction createColGroup(node, cellMinWidth, overrideCol, overrideValue) {\n    let totalWidth = 0;\n    let fixedWidth = true;\n    const cols = [];\n    const row = node.firstChild;\n    if (!row) {\n        return {};\n    }\n    for (let i = 0, col = 0; i < row.childCount; i += 1) {\n        const { colspan, colwidth } = row.child(i).attrs;\n        for (let j = 0; j < colspan; j += 1, col += 1) {\n            const hasWidth = overrideCol === col ? overrideValue : colwidth && colwidth[j];\n            totalWidth += hasWidth || cellMinWidth;\n            if (!hasWidth) {\n                fixedWidth = false;\n            }\n            const [property, value] = getColStyleDeclaration(cellMinWidth, hasWidth);\n            cols.push([\n                'col',\n                { style: `${property}: ${value}` },\n            ]);\n        }\n    }\n    const tableWidth = fixedWidth ? `${totalWidth}px` : '';\n    const tableMinWidth = fixedWidth ? '' : `${totalWidth}px`;\n    const colgroup = ['colgroup', {}, ...cols];\n    return { colgroup, tableWidth, tableMinWidth };\n}\n\nfunction createCell(cellType, cellContent) {\n    if (cellContent) {\n        return cellType.createChecked(null, cellContent);\n    }\n    return cellType.createAndFill();\n}\n\nfunction getTableNodeTypes(schema) {\n    if (schema.cached.tableNodeTypes) {\n        return schema.cached.tableNodeTypes;\n    }\n    const roles = {};\n    Object.keys(schema.nodes).forEach(type => {\n        const nodeType = schema.nodes[type];\n        if (nodeType.spec.tableRole) {\n            roles[nodeType.spec.tableRole] = nodeType;\n        }\n    });\n    schema.cached.tableNodeTypes = roles;\n    return roles;\n}\n\nfunction createTable(schema, rowsCount, colsCount, withHeaderRow, cellContent) {\n    const types = getTableNodeTypes(schema);\n    const headerCells = [];\n    const cells = [];\n    for (let index = 0; index < colsCount; index += 1) {\n        const cell = createCell(types.cell, cellContent);\n        if (cell) {\n            cells.push(cell);\n        }\n        if (withHeaderRow) {\n            const headerCell = createCell(types.header_cell, cellContent);\n            if (headerCell) {\n                headerCells.push(headerCell);\n            }\n        }\n    }\n    const rows = [];\n    for (let index = 0; index < rowsCount; index += 1) {\n        rows.push(types.row.createChecked(null, withHeaderRow && index === 0 ? headerCells : cells));\n    }\n    return types.table.createChecked(null, rows);\n}\n\nfunction isCellSelection(value) {\n    return value instanceof _tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.CellSelection;\n}\n\nconst deleteTableWhenAllCellsSelected = ({ editor }) => {\n    const { selection } = editor.state;\n    if (!isCellSelection(selection)) {\n        return false;\n    }\n    let cellCount = 0;\n    const table = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findParentNodeClosestToPos)(selection.ranges[0].$from, node => {\n        return node.type.name === 'table';\n    });\n    table === null || table === void 0 ? void 0 : table.node.descendants(node => {\n        if (node.type.name === 'table') {\n            return false;\n        }\n        if (['tableCell', 'tableHeader'].includes(node.type.name)) {\n            cellCount += 1;\n        }\n    });\n    const allCellsSelected = cellCount === selection.ranges.length;\n    if (!allCellsSelected) {\n        return false;\n    }\n    editor.commands.deleteTable();\n    return true;\n};\n\n/**\n * This extension allows you to create tables.\n * @see https://www.tiptap.dev/api/nodes/table\n */\nconst Table = _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Node.create({\n    name: 'table',\n    // @ts-ignore\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n            resizable: false,\n            handleWidth: 5,\n            cellMinWidth: 25,\n            // TODO: fix\n            View: TableView,\n            lastColumnResizable: true,\n            allowTableNodeSelection: false,\n        };\n    },\n    content: 'tableRow+',\n    tableRole: 'table',\n    isolating: true,\n    group: 'block',\n    parseHTML() {\n        return [{ tag: 'table' }];\n    },\n    renderHTML({ node, HTMLAttributes }) {\n        const { colgroup, tableWidth, tableMinWidth } = createColGroup(node, this.options.cellMinWidth);\n        const table = [\n            'table',\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes, {\n                style: tableWidth\n                    ? `width: ${tableWidth}`\n                    : `min-width: ${tableMinWidth}`,\n            }),\n            colgroup,\n            ['tbody', 0],\n        ];\n        return table;\n    },\n    addCommands() {\n        return {\n            insertTable: ({ rows = 3, cols = 3, withHeaderRow = true } = {}) => ({ tr, dispatch, editor }) => {\n                const node = createTable(editor.schema, rows, cols, withHeaderRow);\n                if (dispatch) {\n                    const offset = tr.selection.from + 1;\n                    tr.replaceSelectionWith(node)\n                        .scrollIntoView()\n                        .setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.near(tr.doc.resolve(offset)));\n                }\n                return true;\n            },\n            addColumnBefore: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.addColumnBefore)(state, dispatch);\n            },\n            addColumnAfter: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.addColumnAfter)(state, dispatch);\n            },\n            deleteColumn: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.deleteColumn)(state, dispatch);\n            },\n            addRowBefore: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.addRowBefore)(state, dispatch);\n            },\n            addRowAfter: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.addRowAfter)(state, dispatch);\n            },\n            deleteRow: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.deleteRow)(state, dispatch);\n            },\n            deleteTable: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.deleteTable)(state, dispatch);\n            },\n            mergeCells: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.mergeCells)(state, dispatch);\n            },\n            splitCell: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.splitCell)(state, dispatch);\n            },\n            toggleHeaderColumn: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.toggleHeader)('column')(state, dispatch);\n            },\n            toggleHeaderRow: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.toggleHeader)('row')(state, dispatch);\n            },\n            toggleHeaderCell: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.toggleHeaderCell)(state, dispatch);\n            },\n            mergeOrSplit: () => ({ state, dispatch }) => {\n                if ((0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.mergeCells)(state, dispatch)) {\n                    return true;\n                }\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.splitCell)(state, dispatch);\n            },\n            setCellAttribute: (name, value) => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.setCellAttr)(name, value)(state, dispatch);\n            },\n            goToNextCell: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.goToNextCell)(1)(state, dispatch);\n            },\n            goToPreviousCell: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.goToNextCell)(-1)(state, dispatch);\n            },\n            fixTables: () => ({ state, dispatch }) => {\n                if (dispatch) {\n                    (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.fixTables)(state);\n                }\n                return true;\n            },\n            setCellSelection: position => ({ tr, dispatch }) => {\n                if (dispatch) {\n                    const selection = _tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.CellSelection.create(tr.doc, position.anchorCell, position.headCell);\n                    // @ts-ignore\n                    tr.setSelection(selection);\n                }\n                return true;\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            Tab: () => {\n                if (this.editor.commands.goToNextCell()) {\n                    return true;\n                }\n                if (!this.editor.can().addRowAfter()) {\n                    return false;\n                }\n                return this.editor.chain().addRowAfter().goToNextCell().run();\n            },\n            'Shift-Tab': () => this.editor.commands.goToPreviousCell(),\n            Backspace: deleteTableWhenAllCellsSelected,\n            'Mod-Backspace': deleteTableWhenAllCellsSelected,\n            Delete: deleteTableWhenAllCellsSelected,\n            'Mod-Delete': deleteTableWhenAllCellsSelected,\n        };\n    },\n    addProseMirrorPlugins() {\n        const isResizable = this.options.resizable && this.editor.isEditable;\n        return [\n            ...(isResizable\n                ? [\n                    (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.columnResizing)({\n                        handleWidth: this.options.handleWidth,\n                        cellMinWidth: this.options.cellMinWidth,\n                        defaultCellMinWidth: this.options.cellMinWidth,\n                        View: this.options.View,\n                        lastColumnResizable: this.options.lastColumnResizable,\n                    }),\n                ]\n                : []),\n            (0,_tiptap_pm_tables__WEBPACK_IMPORTED_MODULE_1__.tableEditing)({\n                allowTableNodeSelection: this.options.allowTableNodeSelection,\n            }),\n        ];\n    },\n    extendNodeSchema(extension) {\n        const context = {\n            name: extension.name,\n            options: extension.options,\n            storage: extension.storage,\n        };\n        return {\n            tableRole: (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.callOrReturn)((0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getExtensionField)(extension, 'tableRole', context)),\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-table@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-table/dist/index.js\n");

/***/ })

};
;