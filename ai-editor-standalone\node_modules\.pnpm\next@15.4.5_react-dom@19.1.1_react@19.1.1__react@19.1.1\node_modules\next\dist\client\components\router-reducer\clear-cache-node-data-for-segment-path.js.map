{"version": 3, "sources": ["../../../../src/client/components/router-reducer/clear-cache-node-data-for-segment-path.ts"], "sourcesContent": ["import type { FlightSegmentPath } from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { createRouterCacheKey } from './create-router-cache-key'\n\n/**\n * This will clear the CacheNode data for a particular segment path. This will cause a lazy-fetch in layout router to fill in new data.\n */\nexport function clearCacheNodeDataForSegmentPath(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath\n): void {\n  const isLastEntry = flightSegmentPath.length <= 2\n\n  const [parallelRouteKey, segment] = flightSegmentPath\n  const cacheKey = createRouterCacheKey(segment)\n\n  const existingChildSegmentMap =\n    existingCache.parallelRoutes.get(parallelRouteKey)\n\n  let childSegmentMap = newCache.parallelRoutes.get(parallelRouteKey)\n\n  if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n    childSegmentMap = new Map(existingChildSegmentMap)\n    newCache.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n  }\n\n  const existingChildCacheNode = existingChildSegmentMap?.get(cacheKey)\n  let childCacheNode = childSegmentMap.get(cacheKey)\n\n  // In case of last segment start off the fetch at this level and don't copy further down.\n  if (isLastEntry) {\n    if (\n      !childCacheNode ||\n      !childCacheNode.lazyData ||\n      childCacheNode === existingChildCacheNode\n    ) {\n      childSegmentMap.set(cacheKey, {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      })\n    }\n    return\n  }\n\n  if (!childCacheNode || !existingChildCacheNode) {\n    // Start fetch in the place where the existing cache doesn't have the data yet.\n    if (!childCacheNode) {\n      childSegmentMap.set(cacheKey, {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      })\n    }\n    return\n  }\n\n  if (childCacheNode === existingChildCacheNode) {\n    childCacheNode = {\n      lazyData: childCacheNode.lazyData,\n      rsc: childCacheNode.rsc,\n      prefetchRsc: childCacheNode.prefetchRsc,\n      head: childCacheNode.head,\n      prefetchHead: childCacheNode.prefetchHead,\n      parallelRoutes: new Map(childCacheNode.parallelRoutes),\n      loading: childCacheNode.loading,\n    } as CacheNode\n    childSegmentMap.set(cacheKey, childCacheNode)\n  }\n\n  return clearCacheNodeDataForSegmentPath(\n    childCacheNode,\n    existingChildCacheNode,\n    getNextFlightSegmentPath(flightSegmentPath)\n  )\n}\n"], "names": ["clearCacheNodeDataForSegmentPath", "newCache", "existingCache", "flightSegmentPath", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "loading", "navigatedAt", "getNextFlightSegmentPath"], "mappings": ";;;;+BAQgBA;;;eAAAA;;;mCANyB;sCACJ;AAK9B,SAASA,iCACdC,QAAmB,EACnBC,aAAwB,EACxBC,iBAAoC;IAEpC,MAAMC,cAAcD,kBAAkBE,MAAM,IAAI;IAEhD,MAAM,CAACC,kBAAkBC,QAAQ,GAAGJ;IACpC,MAAMK,WAAWC,IAAAA,0CAAoB,EAACF;IAEtC,MAAMG,0BACJR,cAAcS,cAAc,CAACC,GAAG,CAACN;IAEnC,IAAIO,kBAAkBZ,SAASU,cAAc,CAACC,GAAG,CAACN;IAElD,IAAI,CAACO,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BT,SAASU,cAAc,CAACI,GAAG,CAACT,kBAAkBO;IAChD;IAEA,MAAMG,yBAAyBN,2CAAAA,wBAAyBE,GAAG,CAACJ;IAC5D,IAAIS,iBAAiBJ,gBAAgBD,GAAG,CAACJ;IAEzC,yFAAyF;IACzF,IAAIJ,aAAa;QACf,IACE,CAACa,kBACD,CAACA,eAAeC,QAAQ,IACxBD,mBAAmBD,wBACnB;YACAH,gBAAgBE,GAAG,CAACP,UAAU;gBAC5BU,UAAU;gBACVC,KAAK;gBACLC,aAAa;gBACbC,MAAM;gBACNC,cAAc;gBACdX,gBAAgB,IAAIG;gBACpBS,SAAS;gBACTC,aAAa,CAAC;YAChB;QACF;QACA;IACF;IAEA,IAAI,CAACP,kBAAkB,CAACD,wBAAwB;QAC9C,+EAA+E;QAC/E,IAAI,CAACC,gBAAgB;YACnBJ,gBAAgBE,GAAG,CAACP,UAAU;gBAC5BU,UAAU;gBACVC,KAAK;gBACLC,aAAa;gBACbC,MAAM;gBACNC,cAAc;gBACdX,gBAAgB,IAAIG;gBACpBS,SAAS;gBACTC,aAAa,CAAC;YAChB;QACF;QACA;IACF;IAEA,IAAIP,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfC,UAAUD,eAAeC,QAAQ;YACjCC,KAAKF,eAAeE,GAAG;YACvBC,aAAaH,eAAeG,WAAW;YACvCC,MAAMJ,eAAeI,IAAI;YACzBC,cAAcL,eAAeK,YAAY;YACzCX,gBAAgB,IAAIG,IAAIG,eAAeN,cAAc;YACrDY,SAASN,eAAeM,OAAO;QACjC;QACAV,gBAAgBE,GAAG,CAACP,UAAUS;IAChC;IAEA,OAAOjB,iCACLiB,gBACAD,wBACAS,IAAAA,2CAAwB,EAACtB;AAE7B", "ignoreList": [0]}