"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-character-count@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-character-count@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-character-count@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-character-count/dist/index.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-character-count@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-character-count/dist/index.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CharacterCount: () => (/* binding */ CharacterCount),\n/* harmony export */   \"default\": () => (/* binding */ CharacterCount)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n\n\n\n/**\n * This extension allows you to count the characters and words of your document.\n * @see https://tiptap.dev/api/extensions/character-count\n */\nconst CharacterCount = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'characterCount',\n    addOptions() {\n        return {\n            limit: null,\n            mode: 'textSize',\n            textCounter: text => text.length,\n            wordCounter: text => text.split(' ').filter(word => word !== '').length,\n        };\n    },\n    addStorage() {\n        return {\n            characters: () => 0,\n            words: () => 0,\n        };\n    },\n    onBeforeCreate() {\n        this.storage.characters = options => {\n            const node = (options === null || options === void 0 ? void 0 : options.node) || this.editor.state.doc;\n            const mode = (options === null || options === void 0 ? void 0 : options.mode) || this.options.mode;\n            if (mode === 'textSize') {\n                const text = node.textBetween(0, node.content.size, undefined, ' ');\n                return this.options.textCounter(text);\n            }\n            return node.nodeSize;\n        };\n        this.storage.words = options => {\n            const node = (options === null || options === void 0 ? void 0 : options.node) || this.editor.state.doc;\n            const text = node.textBetween(0, node.content.size, ' ', ' ');\n            return this.options.wordCounter(text);\n        };\n    },\n    addProseMirrorPlugins() {\n        let initialEvaluationDone = false;\n        return [\n            new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n                key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('characterCount'),\n                appendTransaction: (transactions, oldState, newState) => {\n                    if (initialEvaluationDone) {\n                        return;\n                    }\n                    const limit = this.options.limit;\n                    if (limit === null || limit === undefined || limit === 0) {\n                        initialEvaluationDone = true;\n                        return;\n                    }\n                    const initialContentSize = this.storage.characters({ node: newState.doc });\n                    if (initialContentSize > limit) {\n                        const over = initialContentSize - limit;\n                        const from = 0;\n                        const to = over;\n                        console.warn(`[CharacterCount] Initial content exceeded limit of ${limit} characters. Content was automatically trimmed.`);\n                        const tr = newState.tr.deleteRange(from, to);\n                        initialEvaluationDone = true;\n                        return tr;\n                    }\n                    initialEvaluationDone = true;\n                },\n                filterTransaction: (transaction, state) => {\n                    const limit = this.options.limit;\n                    // Nothing has changed or no limit is defined. Ignore it.\n                    if (!transaction.docChanged || limit === 0 || limit === null || limit === undefined) {\n                        return true;\n                    }\n                    const oldSize = this.storage.characters({ node: state.doc });\n                    const newSize = this.storage.characters({ node: transaction.doc });\n                    // Everything is in the limit. Good.\n                    if (newSize <= limit) {\n                        return true;\n                    }\n                    // The limit has already been exceeded but will be reduced.\n                    if (oldSize > limit && newSize > limit && newSize <= oldSize) {\n                        return true;\n                    }\n                    // The limit has already been exceeded and will be increased further.\n                    if (oldSize > limit && newSize > limit && newSize > oldSize) {\n                        return false;\n                    }\n                    const isPaste = transaction.getMeta('paste');\n                    // Block all exceeding transactions that were not pasted.\n                    if (!isPaste) {\n                        return false;\n                    }\n                    // For pasted content, we try to remove the exceeding content.\n                    const pos = transaction.selection.$head.pos;\n                    const over = newSize - limit;\n                    const from = pos - over;\n                    const to = pos;\n                    // It’s probably a bad idea to mutate transactions within `filterTransaction`\n                    // but for now this is working fine.\n                    transaction.deleteRange(from, to);\n                    // In some situations, the limit will continue to be exceeded after trimming.\n                    // This happens e.g. when truncating within a complex node (e.g. table)\n                    // and ProseMirror has to close this node again.\n                    // If this is the case, we prevent the transaction completely.\n                    const updatedSize = this.storage.characters({ node: transaction.doc });\n                    if (updatedSize > limit) {\n                        return false;\n                    }\n                    return true;\n                },\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-character-count@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-character-count/dist/index.js\n");

/***/ })

};
;