{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/getCssModuleLocalIdent.ts"], "sourcesContent": ["import loaderUtils from 'next/dist/compiled/loader-utils3'\nimport path from 'path'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\n\nconst regexLikeIndexModule = /(?<!pages[\\\\/])index\\.module\\.(scss|sass|css)$/\n\nexport function getCssModuleLocalIdent(\n  context: webpack.LoaderContext<{}>,\n  _: any,\n  exportName: string,\n  options: object\n) {\n  const relativePath = path\n    .relative(context.rootContext, context.resourcePath)\n    .replace(/\\\\+/g, '/')\n\n  // Generate a more meaningful name (parent folder) when the user names the\n  // file `index.module.css`.\n  const fileNameOrFolder = regexLikeIndexModule.test(relativePath)\n    ? '[folder]'\n    : '[name]'\n\n  // Generate a hash to make the class name unique.\n  const hash = loaderUtils.getHashDigest(\n    Buffer.from(`filePath:${relativePath}#className:${exportName}`),\n    'sha1',\n    'base64',\n    5\n  )\n\n  // Have webpack interpolate the `[folder]` or `[name]` to its real value.\n  return (\n    loaderUtils\n      .interpolateName(\n        context,\n        fileNameOrFolder + '_' + exportName + '__' + hash,\n        options\n      )\n      .replace(\n        // Webpack name interpolation returns `about.module_root__2oFM9` for\n        // `.root {}` inside a file named `about.module.css`. Let's simplify\n        // this.\n        /\\.module_/,\n        '_'\n      )\n      // Replace invalid symbols with underscores instead of escaping\n      // https://mathiasbynens.be/notes/css-escapes#identifiers-strings\n      .replace(/[^a-zA-Z0-9-_]/g, '_')\n      // \"they cannot start with a digit, two hyphens, or a hyphen followed by a digit [sic]\"\n      // https://www.w3.org/TR/CSS21/syndata.html#characters\n      .replace(/^(\\d|--|-\\d)/, '__$1')\n  )\n}\n"], "names": ["getCssModuleLocalIdent", "regexLikeIndexModule", "context", "_", "exportName", "options", "relativePath", "path", "relative", "rootContext", "resourcePath", "replace", "fileNameOrFolder", "test", "hash", "loaderUtils", "getHashDigest", "<PERSON><PERSON><PERSON>", "from", "interpolateName"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;qEANQ;6DACP;;;;;;AAGjB,MAAMC,uBAAuB;AAEtB,SAASD,uBACdE,OAAkC,EAClCC,CAAM,EACNC,UAAkB,EAClBC,OAAe;IAEf,MAAMC,eAAeC,aAAI,CACtBC,QAAQ,CAACN,QAAQO,WAAW,EAAEP,QAAQQ,YAAY,EAClDC,OAAO,CAAC,QAAQ;IAEnB,0EAA0E;IAC1E,2BAA2B;IAC3B,MAAMC,mBAAmBX,qBAAqBY,IAAI,CAACP,gBAC/C,aACA;IAEJ,iDAAiD;IACjD,MAAMQ,OAAOC,qBAAW,CAACC,aAAa,CACpCC,OAAOC,IAAI,CAAC,CAAC,SAAS,EAAEZ,aAAa,WAAW,EAAEF,YAAY,GAC9D,QACA,UACA;IAGF,yEAAyE;IACzE,OACEW,qBAAW,CACRI,eAAe,CACdjB,SACAU,mBAAmB,MAAMR,aAAa,OAAOU,MAC7CT,SAEDM,OAAO,CACN,oEAAoE;IACpE,oEAAoE;IACpE,QAAQ;IACR,aACA,IAEF,+DAA+D;IAC/D,iEAAiE;KAChEA,OAAO,CAAC,mBAAmB,IAC5B,uFAAuF;IACvF,sDAAsD;KACrDA,OAAO,CAAC,gBAAgB;AAE/B", "ignoreList": [0]}