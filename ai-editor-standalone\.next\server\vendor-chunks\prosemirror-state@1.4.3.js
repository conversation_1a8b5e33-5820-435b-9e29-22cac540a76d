"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-state@1.4.3";
exports.ids = ["vendor-chunks/prosemirror-state@1.4.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/prosemirror-state@1.4.3/node_modules/prosemirror-state/dist/index.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/prosemirror-state@1.4.3/node_modules/prosemirror-state/dist/index.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllSelection: () => (/* binding */ AllSelection),\n/* harmony export */   EditorState: () => (/* binding */ EditorState),\n/* harmony export */   NodeSelection: () => (/* binding */ NodeSelection),\n/* harmony export */   Plugin: () => (/* binding */ Plugin),\n/* harmony export */   PluginKey: () => (/* binding */ PluginKey),\n/* harmony export */   Selection: () => (/* binding */ Selection),\n/* harmony export */   SelectionRange: () => (/* binding */ SelectionRange),\n/* harmony export */   TextSelection: () => (/* binding */ TextSelection),\n/* harmony export */   Transaction: () => (/* binding */ Transaction)\n/* harmony export */ });\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/.pnpm/prosemirror-model@1.25.2/node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/.pnpm/prosemirror-transform@1.10.4/node_modules/prosemirror-transform/dist/index.js\");\n\n\n\nconst classesById = Object.create(null);\n/**\nSuperclass for editor selections. Every selection type should\nextend this. Should not be instantiated directly.\n*/\nclass Selection {\n    /**\n    Initialize a selection with the head and anchor and ranges. If no\n    ranges are given, constructs a single range across `$anchor` and\n    `$head`.\n    */\n    constructor(\n    /**\n    The resolved anchor of the selection (the side that stays in\n    place when the selection is modified).\n    */\n    $anchor, \n    /**\n    The resolved head of the selection (the side that moves when\n    the selection is modified).\n    */\n    $head, ranges) {\n        this.$anchor = $anchor;\n        this.$head = $head;\n        this.ranges = ranges || [new SelectionRange($anchor.min($head), $anchor.max($head))];\n    }\n    /**\n    The selection's anchor, as an unresolved position.\n    */\n    get anchor() { return this.$anchor.pos; }\n    /**\n    The selection's head.\n    */\n    get head() { return this.$head.pos; }\n    /**\n    The lower bound of the selection's main range.\n    */\n    get from() { return this.$from.pos; }\n    /**\n    The upper bound of the selection's main range.\n    */\n    get to() { return this.$to.pos; }\n    /**\n    The resolved lower  bound of the selection's main range.\n    */\n    get $from() {\n        return this.ranges[0].$from;\n    }\n    /**\n    The resolved upper bound of the selection's main range.\n    */\n    get $to() {\n        return this.ranges[0].$to;\n    }\n    /**\n    Indicates whether the selection contains any content.\n    */\n    get empty() {\n        let ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++)\n            if (ranges[i].$from.pos != ranges[i].$to.pos)\n                return false;\n        return true;\n    }\n    /**\n    Get the content of this selection as a slice.\n    */\n    content() {\n        return this.$from.doc.slice(this.from, this.to, true);\n    }\n    /**\n    Replace the selection with a slice or, if no slice is given,\n    delete the selection. Will append to the given transaction.\n    */\n    replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        // Put the new selection at the position after the inserted\n        // content. When that ended in an inline node, search backwards,\n        // to get the position after that node. If not, search forward.\n        let lastNode = content.content.lastChild, lastParent = null;\n        for (let i = 0; i < content.openEnd; i++) {\n            lastParent = lastNode;\n            lastNode = lastNode.lastChild;\n        }\n        let mapFrom = tr.steps.length, ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++) {\n            let { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n            tr.replaceRange(mapping.map($from.pos), mapping.map($to.pos), i ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty : content);\n            if (i == 0)\n                selectionToInsertionEnd(tr, mapFrom, (lastNode ? lastNode.isInline : lastParent && lastParent.isTextblock) ? -1 : 1);\n        }\n    }\n    /**\n    Replace the selection with the given node, appending the changes\n    to the given transaction.\n    */\n    replaceWith(tr, node) {\n        let mapFrom = tr.steps.length, ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++) {\n            let { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n            let from = mapping.map($from.pos), to = mapping.map($to.pos);\n            if (i) {\n                tr.deleteRange(from, to);\n            }\n            else {\n                tr.replaceRangeWith(from, to, node);\n                selectionToInsertionEnd(tr, mapFrom, node.isInline ? -1 : 1);\n            }\n        }\n    }\n    /**\n    Find a valid cursor or leaf node selection starting at the given\n    position and searching back if `dir` is negative, and forward if\n    positive. When `textOnly` is true, only consider cursor\n    selections. Will return null when no valid selection position is\n    found.\n    */\n    static findFrom($pos, dir, textOnly = false) {\n        let inner = $pos.parent.inlineContent ? new TextSelection($pos)\n            : findSelectionIn($pos.node(0), $pos.parent, $pos.pos, $pos.index(), dir, textOnly);\n        if (inner)\n            return inner;\n        for (let depth = $pos.depth - 1; depth >= 0; depth--) {\n            let found = dir < 0\n                ? findSelectionIn($pos.node(0), $pos.node(depth), $pos.before(depth + 1), $pos.index(depth), dir, textOnly)\n                : findSelectionIn($pos.node(0), $pos.node(depth), $pos.after(depth + 1), $pos.index(depth) + 1, dir, textOnly);\n            if (found)\n                return found;\n        }\n        return null;\n    }\n    /**\n    Find a valid cursor or leaf node selection near the given\n    position. Searches forward first by default, but if `bias` is\n    negative, it will search backwards first.\n    */\n    static near($pos, bias = 1) {\n        return this.findFrom($pos, bias) || this.findFrom($pos, -bias) || new AllSelection($pos.node(0));\n    }\n    /**\n    Find the cursor or leaf node selection closest to the start of\n    the given document. Will return an\n    [`AllSelection`](https://prosemirror.net/docs/ref/#state.AllSelection) if no valid position\n    exists.\n    */\n    static atStart(doc) {\n        return findSelectionIn(doc, doc, 0, 0, 1) || new AllSelection(doc);\n    }\n    /**\n    Find the cursor or leaf node selection closest to the end of the\n    given document.\n    */\n    static atEnd(doc) {\n        return findSelectionIn(doc, doc, doc.content.size, doc.childCount, -1) || new AllSelection(doc);\n    }\n    /**\n    Deserialize the JSON representation of a selection. Must be\n    implemented for custom classes (as a static class method).\n    */\n    static fromJSON(doc, json) {\n        if (!json || !json.type)\n            throw new RangeError(\"Invalid input for Selection.fromJSON\");\n        let cls = classesById[json.type];\n        if (!cls)\n            throw new RangeError(`No selection type ${json.type} defined`);\n        return cls.fromJSON(doc, json);\n    }\n    /**\n    To be able to deserialize selections from JSON, custom selection\n    classes must register themselves with an ID string, so that they\n    can be disambiguated. Try to pick something that's unlikely to\n    clash with classes from other modules.\n    */\n    static jsonID(id, selectionClass) {\n        if (id in classesById)\n            throw new RangeError(\"Duplicate use of selection JSON ID \" + id);\n        classesById[id] = selectionClass;\n        selectionClass.prototype.jsonID = id;\n        return selectionClass;\n    }\n    /**\n    Get a [bookmark](https://prosemirror.net/docs/ref/#state.SelectionBookmark) for this selection,\n    which is a value that can be mapped without having access to a\n    current document, and later resolved to a real selection for a\n    given document again. (This is used mostly by the history to\n    track and restore old selections.) The default implementation of\n    this method just converts the selection to a text selection and\n    returns the bookmark for that.\n    */\n    getBookmark() {\n        return TextSelection.between(this.$anchor, this.$head).getBookmark();\n    }\n}\nSelection.prototype.visible = true;\n/**\nRepresents a selected range in a document.\n*/\nclass SelectionRange {\n    /**\n    Create a range.\n    */\n    constructor(\n    /**\n    The lower bound of the range.\n    */\n    $from, \n    /**\n    The upper bound of the range.\n    */\n    $to) {\n        this.$from = $from;\n        this.$to = $to;\n    }\n}\nlet warnedAboutTextSelection = false;\nfunction checkTextSelection($pos) {\n    if (!warnedAboutTextSelection && !$pos.parent.inlineContent) {\n        warnedAboutTextSelection = true;\n        console[\"warn\"](\"TextSelection endpoint not pointing into a node with inline content (\" + $pos.parent.type.name + \")\");\n    }\n}\n/**\nA text selection represents a classical editor selection, with a\nhead (the moving side) and anchor (immobile side), both of which\npoint into textblock nodes. It can be empty (a regular cursor\nposition).\n*/\nclass TextSelection extends Selection {\n    /**\n    Construct a text selection between the given points.\n    */\n    constructor($anchor, $head = $anchor) {\n        checkTextSelection($anchor);\n        checkTextSelection($head);\n        super($anchor, $head);\n    }\n    /**\n    Returns a resolved position if this is a cursor selection (an\n    empty text selection), and null otherwise.\n    */\n    get $cursor() { return this.$anchor.pos == this.$head.pos ? this.$head : null; }\n    map(doc, mapping) {\n        let $head = doc.resolve(mapping.map(this.head));\n        if (!$head.parent.inlineContent)\n            return Selection.near($head);\n        let $anchor = doc.resolve(mapping.map(this.anchor));\n        return new TextSelection($anchor.parent.inlineContent ? $anchor : $head, $head);\n    }\n    replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        super.replace(tr, content);\n        if (content == prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n            let marks = this.$from.marksAcross(this.$to);\n            if (marks)\n                tr.ensureMarks(marks);\n        }\n    }\n    eq(other) {\n        return other instanceof TextSelection && other.anchor == this.anchor && other.head == this.head;\n    }\n    getBookmark() {\n        return new TextBookmark(this.anchor, this.head);\n    }\n    toJSON() {\n        return { type: \"text\", anchor: this.anchor, head: this.head };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.anchor != \"number\" || typeof json.head != \"number\")\n            throw new RangeError(\"Invalid input for TextSelection.fromJSON\");\n        return new TextSelection(doc.resolve(json.anchor), doc.resolve(json.head));\n    }\n    /**\n    Create a text selection from non-resolved positions.\n    */\n    static create(doc, anchor, head = anchor) {\n        let $anchor = doc.resolve(anchor);\n        return new this($anchor, head == anchor ? $anchor : doc.resolve(head));\n    }\n    /**\n    Return a text selection that spans the given positions or, if\n    they aren't text positions, find a text selection near them.\n    `bias` determines whether the method searches forward (default)\n    or backwards (negative number) first. Will fall back to calling\n    [`Selection.near`](https://prosemirror.net/docs/ref/#state.Selection^near) when the document\n    doesn't contain a valid text position.\n    */\n    static between($anchor, $head, bias) {\n        let dPos = $anchor.pos - $head.pos;\n        if (!bias || dPos)\n            bias = dPos >= 0 ? 1 : -1;\n        if (!$head.parent.inlineContent) {\n            let found = Selection.findFrom($head, bias, true) || Selection.findFrom($head, -bias, true);\n            if (found)\n                $head = found.$head;\n            else\n                return Selection.near($head, bias);\n        }\n        if (!$anchor.parent.inlineContent) {\n            if (dPos == 0) {\n                $anchor = $head;\n            }\n            else {\n                $anchor = (Selection.findFrom($anchor, -bias, true) || Selection.findFrom($anchor, bias, true)).$anchor;\n                if (($anchor.pos < $head.pos) != (dPos < 0))\n                    $anchor = $head;\n            }\n        }\n        return new TextSelection($anchor, $head);\n    }\n}\nSelection.jsonID(\"text\", TextSelection);\nclass TextBookmark {\n    constructor(anchor, head) {\n        this.anchor = anchor;\n        this.head = head;\n    }\n    map(mapping) {\n        return new TextBookmark(mapping.map(this.anchor), mapping.map(this.head));\n    }\n    resolve(doc) {\n        return TextSelection.between(doc.resolve(this.anchor), doc.resolve(this.head));\n    }\n}\n/**\nA node selection is a selection that points at a single node. All\nnodes marked [selectable](https://prosemirror.net/docs/ref/#model.NodeSpec.selectable) can be the\ntarget of a node selection. In such a selection, `from` and `to`\npoint directly before and after the selected node, `anchor` equals\n`from`, and `head` equals `to`..\n*/\nclass NodeSelection extends Selection {\n    /**\n    Create a node selection. Does not verify the validity of its\n    argument.\n    */\n    constructor($pos) {\n        let node = $pos.nodeAfter;\n        let $end = $pos.node(0).resolve($pos.pos + node.nodeSize);\n        super($pos, $end);\n        this.node = node;\n    }\n    map(doc, mapping) {\n        let { deleted, pos } = mapping.mapResult(this.anchor);\n        let $pos = doc.resolve(pos);\n        if (deleted)\n            return Selection.near($pos);\n        return new NodeSelection($pos);\n    }\n    content() {\n        return new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(this.node), 0, 0);\n    }\n    eq(other) {\n        return other instanceof NodeSelection && other.anchor == this.anchor;\n    }\n    toJSON() {\n        return { type: \"node\", anchor: this.anchor };\n    }\n    getBookmark() { return new NodeBookmark(this.anchor); }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.anchor != \"number\")\n            throw new RangeError(\"Invalid input for NodeSelection.fromJSON\");\n        return new NodeSelection(doc.resolve(json.anchor));\n    }\n    /**\n    Create a node selection from non-resolved positions.\n    */\n    static create(doc, from) {\n        return new NodeSelection(doc.resolve(from));\n    }\n    /**\n    Determines whether the given node may be selected as a node\n    selection.\n    */\n    static isSelectable(node) {\n        return !node.isText && node.type.spec.selectable !== false;\n    }\n}\nNodeSelection.prototype.visible = false;\nSelection.jsonID(\"node\", NodeSelection);\nclass NodeBookmark {\n    constructor(anchor) {\n        this.anchor = anchor;\n    }\n    map(mapping) {\n        let { deleted, pos } = mapping.mapResult(this.anchor);\n        return deleted ? new TextBookmark(pos, pos) : new NodeBookmark(pos);\n    }\n    resolve(doc) {\n        let $pos = doc.resolve(this.anchor), node = $pos.nodeAfter;\n        if (node && NodeSelection.isSelectable(node))\n            return new NodeSelection($pos);\n        return Selection.near($pos);\n    }\n}\n/**\nA selection type that represents selecting the whole document\n(which can not necessarily be expressed with a text selection, when\nthere are for example leaf block nodes at the start or end of the\ndocument).\n*/\nclass AllSelection extends Selection {\n    /**\n    Create an all-selection over the given document.\n    */\n    constructor(doc) {\n        super(doc.resolve(0), doc.resolve(doc.content.size));\n    }\n    replace(tr, content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n        if (content == prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice.empty) {\n            tr.delete(0, tr.doc.content.size);\n            let sel = Selection.atStart(tr.doc);\n            if (!sel.eq(tr.selection))\n                tr.setSelection(sel);\n        }\n        else {\n            super.replace(tr, content);\n        }\n    }\n    toJSON() { return { type: \"all\" }; }\n    /**\n    @internal\n    */\n    static fromJSON(doc) { return new AllSelection(doc); }\n    map(doc) { return new AllSelection(doc); }\n    eq(other) { return other instanceof AllSelection; }\n    getBookmark() { return AllBookmark; }\n}\nSelection.jsonID(\"all\", AllSelection);\nconst AllBookmark = {\n    map() { return this; },\n    resolve(doc) { return new AllSelection(doc); }\n};\n// FIXME we'll need some awareness of text direction when scanning for selections\n// Try to find a selection inside the given node. `pos` points at the\n// position where the search starts. When `text` is true, only return\n// text selections.\nfunction findSelectionIn(doc, node, pos, index, dir, text = false) {\n    if (node.inlineContent)\n        return TextSelection.create(doc, pos);\n    for (let i = index - (dir > 0 ? 0 : 1); dir > 0 ? i < node.childCount : i >= 0; i += dir) {\n        let child = node.child(i);\n        if (!child.isAtom) {\n            let inner = findSelectionIn(doc, child, pos + dir, dir < 0 ? child.childCount : 0, dir, text);\n            if (inner)\n                return inner;\n        }\n        else if (!text && NodeSelection.isSelectable(child)) {\n            return NodeSelection.create(doc, pos - (dir < 0 ? child.nodeSize : 0));\n        }\n        pos += child.nodeSize * dir;\n    }\n    return null;\n}\nfunction selectionToInsertionEnd(tr, startLen, bias) {\n    let last = tr.steps.length - 1;\n    if (last < startLen)\n        return;\n    let step = tr.steps[last];\n    if (!(step instanceof prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceStep || step instanceof prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceAroundStep))\n        return;\n    let map = tr.mapping.maps[last], end;\n    map.forEach((_from, _to, _newFrom, newTo) => { if (end == null)\n        end = newTo; });\n    tr.setSelection(Selection.near(tr.doc.resolve(end), bias));\n}\n\nconst UPDATED_SEL = 1, UPDATED_MARKS = 2, UPDATED_SCROLL = 4;\n/**\nAn editor state transaction, which can be applied to a state to\ncreate an updated state. Use\n[`EditorState.tr`](https://prosemirror.net/docs/ref/#state.EditorState.tr) to create an instance.\n\nTransactions track changes to the document (they are a subclass of\n[`Transform`](https://prosemirror.net/docs/ref/#transform.Transform)), but also other state changes,\nlike selection updates and adjustments of the set of [stored\nmarks](https://prosemirror.net/docs/ref/#state.EditorState.storedMarks). In addition, you can store\nmetadata properties in a transaction, which are extra pieces of\ninformation that client code or plugins can use to describe what a\ntransaction represents, so that they can update their [own\nstate](https://prosemirror.net/docs/ref/#state.StateField) accordingly.\n\nThe [editor view](https://prosemirror.net/docs/ref/#view.EditorView) uses a few metadata\nproperties: it will attach a property `\"pointer\"` with the value\n`true` to selection transactions directly caused by mouse or touch\ninput, a `\"composition\"` property holding an ID identifying the\ncomposition that caused it to transactions caused by composed DOM\ninput, and a `\"uiEvent\"` property of that may be `\"paste\"`,\n`\"cut\"`, or `\"drop\"`.\n*/\nclass Transaction extends prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.Transform {\n    /**\n    @internal\n    */\n    constructor(state) {\n        super(state.doc);\n        // The step count for which the current selection is valid.\n        this.curSelectionFor = 0;\n        // Bitfield to track which aspects of the state were updated by\n        // this transaction.\n        this.updated = 0;\n        // Object used to store metadata properties for the transaction.\n        this.meta = Object.create(null);\n        this.time = Date.now();\n        this.curSelection = state.selection;\n        this.storedMarks = state.storedMarks;\n    }\n    /**\n    The transaction's current selection. This defaults to the editor\n    selection [mapped](https://prosemirror.net/docs/ref/#state.Selection.map) through the steps in the\n    transaction, but can be overwritten with\n    [`setSelection`](https://prosemirror.net/docs/ref/#state.Transaction.setSelection).\n    */\n    get selection() {\n        if (this.curSelectionFor < this.steps.length) {\n            this.curSelection = this.curSelection.map(this.doc, this.mapping.slice(this.curSelectionFor));\n            this.curSelectionFor = this.steps.length;\n        }\n        return this.curSelection;\n    }\n    /**\n    Update the transaction's current selection. Will determine the\n    selection that the editor gets when the transaction is applied.\n    */\n    setSelection(selection) {\n        if (selection.$from.doc != this.doc)\n            throw new RangeError(\"Selection passed to setSelection must point at the current document\");\n        this.curSelection = selection;\n        this.curSelectionFor = this.steps.length;\n        this.updated = (this.updated | UPDATED_SEL) & ~UPDATED_MARKS;\n        this.storedMarks = null;\n        return this;\n    }\n    /**\n    Whether the selection was explicitly updated by this transaction.\n    */\n    get selectionSet() {\n        return (this.updated & UPDATED_SEL) > 0;\n    }\n    /**\n    Set the current stored marks.\n    */\n    setStoredMarks(marks) {\n        this.storedMarks = marks;\n        this.updated |= UPDATED_MARKS;\n        return this;\n    }\n    /**\n    Make sure the current stored marks or, if that is null, the marks\n    at the selection, match the given set of marks. Does nothing if\n    this is already the case.\n    */\n    ensureMarks(marks) {\n        if (!prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Mark.sameSet(this.storedMarks || this.selection.$from.marks(), marks))\n            this.setStoredMarks(marks);\n        return this;\n    }\n    /**\n    Add a mark to the set of stored marks.\n    */\n    addStoredMark(mark) {\n        return this.ensureMarks(mark.addToSet(this.storedMarks || this.selection.$head.marks()));\n    }\n    /**\n    Remove a mark or mark type from the set of stored marks.\n    */\n    removeStoredMark(mark) {\n        return this.ensureMarks(mark.removeFromSet(this.storedMarks || this.selection.$head.marks()));\n    }\n    /**\n    Whether the stored marks were explicitly set for this transaction.\n    */\n    get storedMarksSet() {\n        return (this.updated & UPDATED_MARKS) > 0;\n    }\n    /**\n    @internal\n    */\n    addStep(step, doc) {\n        super.addStep(step, doc);\n        this.updated = this.updated & ~UPDATED_MARKS;\n        this.storedMarks = null;\n    }\n    /**\n    Update the timestamp for the transaction.\n    */\n    setTime(time) {\n        this.time = time;\n        return this;\n    }\n    /**\n    Replace the current selection with the given slice.\n    */\n    replaceSelection(slice) {\n        this.selection.replace(this, slice);\n        return this;\n    }\n    /**\n    Replace the selection with the given node. When `inheritMarks` is\n    true and the content is inline, it inherits the marks from the\n    place where it is inserted.\n    */\n    replaceSelectionWith(node, inheritMarks = true) {\n        let selection = this.selection;\n        if (inheritMarks)\n            node = node.mark(this.storedMarks || (selection.empty ? selection.$from.marks() : (selection.$from.marksAcross(selection.$to) || prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Mark.none)));\n        selection.replaceWith(this, node);\n        return this;\n    }\n    /**\n    Delete the selection.\n    */\n    deleteSelection() {\n        this.selection.replace(this);\n        return this;\n    }\n    /**\n    Replace the given range, or the selection if no range is given,\n    with a text node containing the given string.\n    */\n    insertText(text, from, to) {\n        let schema = this.doc.type.schema;\n        if (from == null) {\n            if (!text)\n                return this.deleteSelection();\n            return this.replaceSelectionWith(schema.text(text), true);\n        }\n        else {\n            if (to == null)\n                to = from;\n            to = to == null ? from : to;\n            if (!text)\n                return this.deleteRange(from, to);\n            let marks = this.storedMarks;\n            if (!marks) {\n                let $from = this.doc.resolve(from);\n                marks = to == from ? $from.marks() : $from.marksAcross(this.doc.resolve(to));\n            }\n            this.replaceRangeWith(from, to, schema.text(text, marks));\n            if (!this.selection.empty)\n                this.setSelection(Selection.near(this.selection.$to));\n            return this;\n        }\n    }\n    /**\n    Store a metadata property in this transaction, keyed either by\n    name or by plugin.\n    */\n    setMeta(key, value) {\n        this.meta[typeof key == \"string\" ? key : key.key] = value;\n        return this;\n    }\n    /**\n    Retrieve a metadata property for a given name or plugin.\n    */\n    getMeta(key) {\n        return this.meta[typeof key == \"string\" ? key : key.key];\n    }\n    /**\n    Returns true if this transaction doesn't contain any metadata,\n    and can thus safely be extended.\n    */\n    get isGeneric() {\n        for (let _ in this.meta)\n            return false;\n        return true;\n    }\n    /**\n    Indicate that the editor should scroll the selection into view\n    when updated to the state produced by this transaction.\n    */\n    scrollIntoView() {\n        this.updated |= UPDATED_SCROLL;\n        return this;\n    }\n    /**\n    True when this transaction has had `scrollIntoView` called on it.\n    */\n    get scrolledIntoView() {\n        return (this.updated & UPDATED_SCROLL) > 0;\n    }\n}\n\nfunction bind(f, self) {\n    return !self || !f ? f : f.bind(self);\n}\nclass FieldDesc {\n    constructor(name, desc, self) {\n        this.name = name;\n        this.init = bind(desc.init, self);\n        this.apply = bind(desc.apply, self);\n    }\n}\nconst baseFields = [\n    new FieldDesc(\"doc\", {\n        init(config) { return config.doc || config.schema.topNodeType.createAndFill(); },\n        apply(tr) { return tr.doc; }\n    }),\n    new FieldDesc(\"selection\", {\n        init(config, instance) { return config.selection || Selection.atStart(instance.doc); },\n        apply(tr) { return tr.selection; }\n    }),\n    new FieldDesc(\"storedMarks\", {\n        init(config) { return config.storedMarks || null; },\n        apply(tr, _marks, _old, state) { return state.selection.$cursor ? tr.storedMarks : null; }\n    }),\n    new FieldDesc(\"scrollToSelection\", {\n        init() { return 0; },\n        apply(tr, prev) { return tr.scrolledIntoView ? prev + 1 : prev; }\n    })\n];\n// Object wrapping the part of a state object that stays the same\n// across transactions. Stored in the state's `config` property.\nclass Configuration {\n    constructor(schema, plugins) {\n        this.schema = schema;\n        this.plugins = [];\n        this.pluginsByKey = Object.create(null);\n        this.fields = baseFields.slice();\n        if (plugins)\n            plugins.forEach(plugin => {\n                if (this.pluginsByKey[plugin.key])\n                    throw new RangeError(\"Adding different instances of a keyed plugin (\" + plugin.key + \")\");\n                this.plugins.push(plugin);\n                this.pluginsByKey[plugin.key] = plugin;\n                if (plugin.spec.state)\n                    this.fields.push(new FieldDesc(plugin.key, plugin.spec.state, plugin));\n            });\n    }\n}\n/**\nThe state of a ProseMirror editor is represented by an object of\nthis type. A state is a persistent data structure—it isn't\nupdated, but rather a new state value is computed from an old one\nusing the [`apply`](https://prosemirror.net/docs/ref/#state.EditorState.apply) method.\n\nA state holds a number of built-in fields, and plugins can\n[define](https://prosemirror.net/docs/ref/#state.PluginSpec.state) additional fields.\n*/\nclass EditorState {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    config) {\n        this.config = config;\n    }\n    /**\n    The schema of the state's document.\n    */\n    get schema() {\n        return this.config.schema;\n    }\n    /**\n    The plugins that are active in this state.\n    */\n    get plugins() {\n        return this.config.plugins;\n    }\n    /**\n    Apply the given transaction to produce a new state.\n    */\n    apply(tr) {\n        return this.applyTransaction(tr).state;\n    }\n    /**\n    @internal\n    */\n    filterTransaction(tr, ignore = -1) {\n        for (let i = 0; i < this.config.plugins.length; i++)\n            if (i != ignore) {\n                let plugin = this.config.plugins[i];\n                if (plugin.spec.filterTransaction && !plugin.spec.filterTransaction.call(plugin, tr, this))\n                    return false;\n            }\n        return true;\n    }\n    /**\n    Verbose variant of [`apply`](https://prosemirror.net/docs/ref/#state.EditorState.apply) that\n    returns the precise transactions that were applied (which might\n    be influenced by the [transaction\n    hooks](https://prosemirror.net/docs/ref/#state.PluginSpec.filterTransaction) of\n    plugins) along with the new state.\n    */\n    applyTransaction(rootTr) {\n        if (!this.filterTransaction(rootTr))\n            return { state: this, transactions: [] };\n        let trs = [rootTr], newState = this.applyInner(rootTr), seen = null;\n        // This loop repeatedly gives plugins a chance to respond to\n        // transactions as new transactions are added, making sure to only\n        // pass the transactions the plugin did not see before.\n        for (;;) {\n            let haveNew = false;\n            for (let i = 0; i < this.config.plugins.length; i++) {\n                let plugin = this.config.plugins[i];\n                if (plugin.spec.appendTransaction) {\n                    let n = seen ? seen[i].n : 0, oldState = seen ? seen[i].state : this;\n                    let tr = n < trs.length &&\n                        plugin.spec.appendTransaction.call(plugin, n ? trs.slice(n) : trs, oldState, newState);\n                    if (tr && newState.filterTransaction(tr, i)) {\n                        tr.setMeta(\"appendedTransaction\", rootTr);\n                        if (!seen) {\n                            seen = [];\n                            for (let j = 0; j < this.config.plugins.length; j++)\n                                seen.push(j < i ? { state: newState, n: trs.length } : { state: this, n: 0 });\n                        }\n                        trs.push(tr);\n                        newState = newState.applyInner(tr);\n                        haveNew = true;\n                    }\n                    if (seen)\n                        seen[i] = { state: newState, n: trs.length };\n                }\n            }\n            if (!haveNew)\n                return { state: newState, transactions: trs };\n        }\n    }\n    /**\n    @internal\n    */\n    applyInner(tr) {\n        if (!tr.before.eq(this.doc))\n            throw new RangeError(\"Applying a mismatched transaction\");\n        let newInstance = new EditorState(this.config), fields = this.config.fields;\n        for (let i = 0; i < fields.length; i++) {\n            let field = fields[i];\n            newInstance[field.name] = field.apply(tr, this[field.name], this, newInstance);\n        }\n        return newInstance;\n    }\n    /**\n    Start a [transaction](https://prosemirror.net/docs/ref/#state.Transaction) from this state.\n    */\n    get tr() { return new Transaction(this); }\n    /**\n    Create a new state.\n    */\n    static create(config) {\n        let $config = new Configuration(config.doc ? config.doc.type.schema : config.schema, config.plugins);\n        let instance = new EditorState($config);\n        for (let i = 0; i < $config.fields.length; i++)\n            instance[$config.fields[i].name] = $config.fields[i].init(config, instance);\n        return instance;\n    }\n    /**\n    Create a new state based on this one, but with an adjusted set\n    of active plugins. State fields that exist in both sets of\n    plugins are kept unchanged. Those that no longer exist are\n    dropped, and those that are new are initialized using their\n    [`init`](https://prosemirror.net/docs/ref/#state.StateField.init) method, passing in the new\n    configuration object..\n    */\n    reconfigure(config) {\n        let $config = new Configuration(this.schema, config.plugins);\n        let fields = $config.fields, instance = new EditorState($config);\n        for (let i = 0; i < fields.length; i++) {\n            let name = fields[i].name;\n            instance[name] = this.hasOwnProperty(name) ? this[name] : fields[i].init(config, instance);\n        }\n        return instance;\n    }\n    /**\n    Serialize this state to JSON. If you want to serialize the state\n    of plugins, pass an object mapping property names to use in the\n    resulting JSON object to plugin objects. The argument may also be\n    a string or number, in which case it is ignored, to support the\n    way `JSON.stringify` calls `toString` methods.\n    */\n    toJSON(pluginFields) {\n        let result = { doc: this.doc.toJSON(), selection: this.selection.toJSON() };\n        if (this.storedMarks)\n            result.storedMarks = this.storedMarks.map(m => m.toJSON());\n        if (pluginFields && typeof pluginFields == 'object')\n            for (let prop in pluginFields) {\n                if (prop == \"doc\" || prop == \"selection\")\n                    throw new RangeError(\"The JSON fields `doc` and `selection` are reserved\");\n                let plugin = pluginFields[prop], state = plugin.spec.state;\n                if (state && state.toJSON)\n                    result[prop] = state.toJSON.call(plugin, this[plugin.key]);\n            }\n        return result;\n    }\n    /**\n    Deserialize a JSON representation of a state. `config` should\n    have at least a `schema` field, and should contain array of\n    plugins to initialize the state with. `pluginFields` can be used\n    to deserialize the state of plugins, by associating plugin\n    instances with the property names they use in the JSON object.\n    */\n    static fromJSON(config, json, pluginFields) {\n        if (!json)\n            throw new RangeError(\"Invalid input for EditorState.fromJSON\");\n        if (!config.schema)\n            throw new RangeError(\"Required config field 'schema' missing\");\n        let $config = new Configuration(config.schema, config.plugins);\n        let instance = new EditorState($config);\n        $config.fields.forEach(field => {\n            if (field.name == \"doc\") {\n                instance.doc = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Node.fromJSON(config.schema, json.doc);\n            }\n            else if (field.name == \"selection\") {\n                instance.selection = Selection.fromJSON(instance.doc, json.selection);\n            }\n            else if (field.name == \"storedMarks\") {\n                if (json.storedMarks)\n                    instance.storedMarks = json.storedMarks.map(config.schema.markFromJSON);\n            }\n            else {\n                if (pluginFields)\n                    for (let prop in pluginFields) {\n                        let plugin = pluginFields[prop], state = plugin.spec.state;\n                        if (plugin.key == field.name && state && state.fromJSON &&\n                            Object.prototype.hasOwnProperty.call(json, prop)) {\n                            instance[field.name] = state.fromJSON.call(plugin, config, json[prop], instance);\n                            return;\n                        }\n                    }\n                instance[field.name] = field.init(config, instance);\n            }\n        });\n        return instance;\n    }\n}\n\nfunction bindProps(obj, self, target) {\n    for (let prop in obj) {\n        let val = obj[prop];\n        if (val instanceof Function)\n            val = val.bind(self);\n        else if (prop == \"handleDOMEvents\")\n            val = bindProps(val, self, {});\n        target[prop] = val;\n    }\n    return target;\n}\n/**\nPlugins bundle functionality that can be added to an editor.\nThey are part of the [editor state](https://prosemirror.net/docs/ref/#state.EditorState) and\nmay influence that state and the view that contains it.\n*/\nclass Plugin {\n    /**\n    Create a plugin.\n    */\n    constructor(\n    /**\n    The plugin's [spec object](https://prosemirror.net/docs/ref/#state.PluginSpec).\n    */\n    spec) {\n        this.spec = spec;\n        /**\n        The [props](https://prosemirror.net/docs/ref/#view.EditorProps) exported by this plugin.\n        */\n        this.props = {};\n        if (spec.props)\n            bindProps(spec.props, this, this.props);\n        this.key = spec.key ? spec.key.key : createKey(\"plugin\");\n    }\n    /**\n    Extract the plugin's state field from an editor state.\n    */\n    getState(state) { return state[this.key]; }\n}\nconst keys = Object.create(null);\nfunction createKey(name) {\n    if (name in keys)\n        return name + \"$\" + ++keys[name];\n    keys[name] = 0;\n    return name + \"$\";\n}\n/**\nA key is used to [tag](https://prosemirror.net/docs/ref/#state.PluginSpec.key) plugins in a way\nthat makes it possible to find them, given an editor state.\nAssigning a key does mean only one plugin of that type can be\nactive in a state.\n*/\nclass PluginKey {\n    /**\n    Create a plugin key.\n    */\n    constructor(name = \"key\") { this.key = createKey(name); }\n    /**\n    Get the active plugin with this key, if any, from an editor\n    state.\n    */\n    get(state) { return state.config.pluginsByKey[this.key]; }\n    /**\n    Get the plugin's state from an editor state.\n    */\n    getState(state) { return state[this.key]; }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/prosemirror-state@1.4.3/node_modules/prosemirror-state/dist/index.js\n");

/***/ })

};
;