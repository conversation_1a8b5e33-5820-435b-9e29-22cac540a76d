"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-bubble-menu/dist/index.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-bubble-menu/dist/index.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BubbleMenu: () => (/* binding */ BubbleMenu),\n/* harmony export */   BubbleMenuPlugin: () => (/* binding */ BubbleMenuPlugin),\n/* harmony export */   BubbleMenuView: () => (/* binding */ BubbleMenuView),\n/* harmony export */   \"default\": () => (/* binding */ BubbleMenu)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var tippy_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tippy.js */ \"(ssr)/./node_modules/.pnpm/tippy.js@6.3.7/node_modules/tippy.js/dist/tippy.esm.js\");\n\n\n\n\nclass BubbleMenuView {\n    constructor({ editor, element, view, tippyOptions = {}, updateDelay = 250, shouldShow, }) {\n        this.preventHide = false;\n        this.shouldShow = ({ view, state, from, to, }) => {\n            const { doc, selection } = state;\n            const { empty } = selection;\n            // Sometime check for `empty` is not enough.\n            // Doubleclick an empty paragraph returns a node size of 2.\n            // So we check also for an empty text size.\n            const isEmptyTextBlock = !doc.textBetween(from, to).length && (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.isTextSelection)(state.selection);\n            // When clicking on a element inside the bubble menu the editor \"blur\" event\n            // is called and the bubble menu item is focussed. In this case we should\n            // consider the menu as part of the editor and keep showing the menu\n            const isChildOfMenu = this.element.contains(document.activeElement);\n            const hasEditorFocus = view.hasFocus() || isChildOfMenu;\n            if (!hasEditorFocus || empty || isEmptyTextBlock || !this.editor.isEditable) {\n                return false;\n            }\n            return true;\n        };\n        this.mousedownHandler = () => {\n            this.preventHide = true;\n        };\n        this.dragstartHandler = () => {\n            this.hide();\n        };\n        this.focusHandler = () => {\n            // we use `setTimeout` to make sure `selection` is already updated\n            setTimeout(() => this.update(this.editor.view));\n        };\n        this.blurHandler = ({ event }) => {\n            var _a;\n            if (this.preventHide) {\n                this.preventHide = false;\n                return;\n            }\n            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) && ((_a = this.element.parentNode) === null || _a === void 0 ? void 0 : _a.contains(event.relatedTarget))) {\n                return;\n            }\n            if ((event === null || event === void 0 ? void 0 : event.relatedTarget) === this.editor.view.dom) {\n                return;\n            }\n            this.hide();\n        };\n        this.tippyBlurHandler = (event) => {\n            this.blurHandler({ event });\n        };\n        this.handleDebouncedUpdate = (view, oldState) => {\n            const selectionChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.selection.eq(view.state.selection));\n            const docChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.doc.eq(view.state.doc));\n            if (!selectionChanged && !docChanged) {\n                return;\n            }\n            if (this.updateDebounceTimer) {\n                clearTimeout(this.updateDebounceTimer);\n            }\n            this.updateDebounceTimer = window.setTimeout(() => {\n                this.updateHandler(view, selectionChanged, docChanged, oldState);\n            }, this.updateDelay);\n        };\n        this.updateHandler = (view, selectionChanged, docChanged, oldState) => {\n            var _a, _b, _c;\n            const { state, composing } = view;\n            const { selection } = state;\n            const isSame = !selectionChanged && !docChanged;\n            if (composing || isSame) {\n                return;\n            }\n            this.createTooltip();\n            // support for CellSelections\n            const { ranges } = selection;\n            const from = Math.min(...ranges.map(range => range.$from.pos));\n            const to = Math.max(...ranges.map(range => range.$to.pos));\n            const shouldShow = (_a = this.shouldShow) === null || _a === void 0 ? void 0 : _a.call(this, {\n                editor: this.editor,\n                element: this.element,\n                view,\n                state,\n                oldState,\n                from,\n                to,\n            });\n            if (!shouldShow) {\n                this.hide();\n                return;\n            }\n            (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.setProps({\n                getReferenceClientRect: ((_c = this.tippyOptions) === null || _c === void 0 ? void 0 : _c.getReferenceClientRect)\n                    || (() => {\n                        if ((0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.isNodeSelection)(state.selection)) {\n                            let node = view.nodeDOM(from);\n                            if (node) {\n                                const nodeViewWrapper = node.dataset.nodeViewWrapper ? node : node.querySelector('[data-node-view-wrapper]');\n                                if (nodeViewWrapper) {\n                                    node = nodeViewWrapper.firstChild;\n                                }\n                                if (node) {\n                                    return node.getBoundingClientRect();\n                                }\n                            }\n                        }\n                        return (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.posToDOMRect)(view, from, to);\n                    }),\n            });\n            this.show();\n        };\n        this.editor = editor;\n        this.element = element;\n        this.view = view;\n        this.updateDelay = updateDelay;\n        if (shouldShow) {\n            this.shouldShow = shouldShow;\n        }\n        this.element.addEventListener('mousedown', this.mousedownHandler, { capture: true });\n        this.view.dom.addEventListener('dragstart', this.dragstartHandler);\n        this.editor.on('focus', this.focusHandler);\n        this.editor.on('blur', this.blurHandler);\n        this.tippyOptions = tippyOptions;\n        // Detaches menu content from its current parent\n        this.element.remove();\n        this.element.style.visibility = 'visible';\n    }\n    createTooltip() {\n        const { element: editorElement } = this.editor.options;\n        const editorIsAttached = !!editorElement.parentElement;\n        this.element.tabIndex = 0;\n        if (this.tippy || !editorIsAttached) {\n            return;\n        }\n        this.tippy = (0,tippy_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(editorElement, {\n            duration: 0,\n            getReferenceClientRect: null,\n            content: this.element,\n            interactive: true,\n            trigger: 'manual',\n            placement: 'top',\n            hideOnClick: 'toggle',\n            ...this.tippyOptions,\n        });\n        // maybe we have to hide tippy on its own blur event as well\n        if (this.tippy.popper.firstChild) {\n            this.tippy.popper.firstChild.addEventListener('blur', this.tippyBlurHandler);\n        }\n    }\n    update(view, oldState) {\n        const { state } = view;\n        const hasValidSelection = state.selection.from !== state.selection.to;\n        if (this.updateDelay > 0 && hasValidSelection) {\n            this.handleDebouncedUpdate(view, oldState);\n            return;\n        }\n        const selectionChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.selection.eq(view.state.selection));\n        const docChanged = !(oldState === null || oldState === void 0 ? void 0 : oldState.doc.eq(view.state.doc));\n        this.updateHandler(view, selectionChanged, docChanged, oldState);\n    }\n    show() {\n        var _a;\n        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.show();\n    }\n    hide() {\n        var _a;\n        (_a = this.tippy) === null || _a === void 0 ? void 0 : _a.hide();\n    }\n    destroy() {\n        var _a, _b;\n        if ((_a = this.tippy) === null || _a === void 0 ? void 0 : _a.popper.firstChild) {\n            this.tippy.popper.firstChild.removeEventListener('blur', this.tippyBlurHandler);\n        }\n        (_b = this.tippy) === null || _b === void 0 ? void 0 : _b.destroy();\n        this.element.removeEventListener('mousedown', this.mousedownHandler, { capture: true });\n        this.view.dom.removeEventListener('dragstart', this.dragstartHandler);\n        this.editor.off('focus', this.focusHandler);\n        this.editor.off('blur', this.blurHandler);\n    }\n}\nconst BubbleMenuPlugin = (options) => {\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        key: typeof options.pluginKey === 'string' ? new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(options.pluginKey) : options.pluginKey,\n        view: view => new BubbleMenuView({ view, ...options }),\n    });\n};\n\n/**\n * This extension allows you to create a bubble menu.\n * @see https://tiptap.dev/api/extensions/bubble-menu\n */\nconst BubbleMenu = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'bubbleMenu',\n    addOptions() {\n        return {\n            element: null,\n            tippyOptions: {},\n            pluginKey: 'bubbleMenu',\n            updateDelay: undefined,\n            shouldShow: null,\n        };\n    },\n    addProseMirrorPlugins() {\n        if (!this.options.element) {\n            return [];\n        }\n        return [\n            BubbleMenuPlugin({\n                pluginKey: this.options.pluginKey,\n                editor: this.editor,\n                element: this.options.element,\n                tippyOptions: this.options.tippyOptions,\n                updateDelay: this.options.updateDelay,\n                shouldShow: this.options.shouldShow,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-bubble-menu/dist/index.js\n");

/***/ })

};
;