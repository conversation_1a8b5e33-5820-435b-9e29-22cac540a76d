"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-dialog@1.1.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3_k55ntr4ew2exye3d53w7lmpcv4";
exports.ids = ["vendor-chunks/@radix-ui+react-dialog@1.1.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3_k55ntr4ew2exye3d53w7lmpcv4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3_k55ntr4ew2exye3d53w7lmpcv4/node_modules/@radix-ui/react-dialog/dist/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-dialog@1.1.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3_k55ntr4ew2exye3d53w7lmpcv4/node_modules/@radix-ui/react-dialog/dist/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger),\n/* harmony export */   Overlay: () => (/* binding */ Overlay),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   WarningProvider: () => (/* binding */ WarningProvider),\n/* harmony export */   createDialogScope: () => (/* binding */ createDialogScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@19.1.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@19.1.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@19.1.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@18.3.23_react@19.1.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23__@types_xmgzh4oi2u2zwauk6tbib2yqya/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_sw3ptm7yjoct5q27vnfsge5zbm/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3._a66dlbkifi6gmdcwljabhkflq4/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.4_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18._zei5wbs53j4eu27w4pgiy5j3iq/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18_ms6wff6atggl33zqudmswaq7oi/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.23_react@19.1.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@19.1.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@19.1.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Close,Content,Description,Dialog,DialogClose,DialogContent,DialogDescription,DialogOverlay,DialogPortal,DialogTitle,DialogTrigger,Overlay,Portal,Root,Title,Trigger,WarningProvider,createDialogScope auto */ // src/dialog.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar DIALOG_NAME = \"Dialog\";\nvar [createDialogContext, createDialogScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DIALOG_NAME);\nvar [DialogProvider, useDialogContext] = createDialogContext(DIALOG_NAME);\nvar Dialog = (props)=>{\n    const { __scopeDialog, children, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: DIALOG_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogProvider, {\n        scope: __scopeDialog,\n        triggerRef,\n        contentRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        titleId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        descriptionId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"Dialog.useCallback\": ()=>setOpen({\n                    \"Dialog.useCallback\": (prevOpen)=>!prevOpen\n                }[\"Dialog.useCallback\"])\n        }[\"Dialog.useCallback\"], [\n            setOpen\n        ]),\n        modal,\n        children\n    });\n};\nDialog.displayName = DIALOG_NAME;\nvar TRIGGER_NAME = \"DialogTrigger\";\nvar DialogTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.triggerRef);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DialogPortal\";\nvar [PortalProvider, usePortalContext] = createDialogContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar DialogPortal = (props)=>{\n    const { __scopeDialog, forceMount, children, container } = props;\n    const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeDialog,\n        forceMount,\n        children: react__WEBPACK_IMPORTED_MODULE_0__.Children.map(children, (child)=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n                present: forceMount || context.open,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                    asChild: true,\n                    container,\n                    children: child\n                })\n            }))\n    });\n};\nDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"DialogOverlay\";\nvar DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogOverlayImpl, {\n            ...overlayProps,\n            ref: forwardedRef\n        })\n    }) : null;\n});\nDialogOverlay.displayName = OVERLAY_NAME;\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_10__.createSlot)(\"DialogOverlay.RemoveScroll\");\nvar DialogOverlayImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return(// Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n    // ie. when `Overlay` and `Content` are siblings\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        as: Slot,\n        allowPinchZoom: true,\n        shards: [\n            context.contentRef\n        ],\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.div, {\n            \"data-state\": getState(context.open),\n            ...overlayProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"auto\",\n                ...overlayProps.style\n            }\n        })\n    }));\n});\nvar CONTENT_NAME = \"DialogContent\";\nvar DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nDialogContent.displayName = CONTENT_NAME;\nvar DialogContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, context.contentRef, contentRef);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DialogContentModal.useEffect\": ()=>{\n            const content = contentRef.current;\n            if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n        }\n    }[\"DialogContentModal.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: true,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            event.preventDefault();\n            context.triggerRef.current?.focus();\n        }),\n        onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (isRightClick) event.preventDefault();\n        }),\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault())\n    });\n});\nvar DialogContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DialogContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar DialogContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, contentRef);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_13__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_14__.FocusScope, {\n                asChild: true,\n                loop: true,\n                trapped: trapFocus,\n                onMountAutoFocus: onOpenAutoFocus,\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_15__.DismissableLayer, {\n                    role: \"dialog\",\n                    id: context.contentId,\n                    \"aria-describedby\": context.descriptionId,\n                    \"aria-labelledby\": context.titleId,\n                    \"data-state\": getState(context.open),\n                    ...contentProps,\n                    ref: composedRefs,\n                    onDismiss: ()=>context.onOpenChange(false)\n                })\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TitleWarning, {\n                        titleId: context.titleId\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DescriptionWarning, {\n                        contentRef,\n                        descriptionId: context.descriptionId\n                    })\n                ]\n            })\n        ]\n    });\n});\nvar TITLE_NAME = \"DialogTitle\";\nvar DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.h2, {\n        id: context.titleId,\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"DialogDescription\";\nvar DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.p, {\n        id: context.descriptionId,\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nDialogDescription.displayName = DESCRIPTION_NAME;\nvar CLOSE_NAME = \"DialogClose\";\nvar DialogClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nDialogClose.displayName = CLOSE_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar TITLE_WARNING_NAME = \"DialogTitleWarning\";\nvar [WarningProvider, useWarningContext] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContext)(TITLE_WARNING_NAME, {\n    contentName: CONTENT_NAME,\n    titleName: TITLE_NAME,\n    docsSlug: \"dialog\"\n});\nvar TitleWarning = ({ titleId })=>{\n    const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n    const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TitleWarning.useEffect\": ()=>{\n            if (titleId) {\n                const hasTitle = document.getElementById(titleId);\n                if (!hasTitle) console.error(MESSAGE);\n            }\n        }\n    }[\"TitleWarning.useEffect\"], [\n        MESSAGE,\n        titleId\n    ]);\n    return null;\n};\nvar DESCRIPTION_WARNING_NAME = \"DialogDescriptionWarning\";\nvar DescriptionWarning = ({ contentRef, descriptionId })=>{\n    const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n    const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DescriptionWarning.useEffect\": ()=>{\n            const describedById = contentRef.current?.getAttribute(\"aria-describedby\");\n            if (descriptionId && describedById) {\n                const hasDescription = document.getElementById(descriptionId);\n                if (!hasDescription) console.warn(MESSAGE);\n            }\n        }\n    }[\"DescriptionWarning.useEffect\"], [\n        MESSAGE,\n        contentRef,\n        descriptionId\n    ]);\n    return null;\n};\nvar Root = Dialog;\nvar Trigger = DialogTrigger;\nvar Portal = DialogPortal;\nvar Overlay = DialogOverlay;\nvar Content = DialogContent;\nvar Title = DialogTitle;\nvar Description = DialogDescription;\nvar Close = DialogClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@18.3_k55ntr4ew2exye3d53w7lmpcv4/node_modules/@radix-ui/react-dialog/dist/index.mjs\n");

/***/ })

};
;