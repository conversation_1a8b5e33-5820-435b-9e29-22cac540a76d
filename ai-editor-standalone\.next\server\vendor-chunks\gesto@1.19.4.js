"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gesto@1.19.4";
exports.ids = ["vendor-chunks/gesto@1.19.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/gesto@1.19.4/node_modules/gesto/dist/gesto.esm.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/gesto@1.19.4/node_modules/gesto/dist/gesto.esm.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Gesto)\n/* harmony export */ });\n/* harmony import */ var _scena_event_emitter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @scena/event-emitter */ \"(ssr)/./node_modules/.pnpm/@scena+event-emitter@1.0.5/node_modules/@scena/event-emitter/dist/event-emitter.esm.js\");\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2019 Daybrush\nname: gesto\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/gesto.git\nversion: 1.19.4\n*/\n\n\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nfunction getRad(pos1, pos2) {\n    var distX = pos2[0] - pos1[0];\n    var distY = pos2[1] - pos1[1];\n    var rad = Math.atan2(distY, distX);\n    return rad >= 0 ? rad : rad + Math.PI * 2;\n}\nfunction getRotatiion(touches) {\n    return getRad([\n        touches[0].clientX,\n        touches[0].clientY,\n    ], [\n        touches[1].clientX,\n        touches[1].clientY,\n    ]) / Math.PI * 180;\n}\nfunction isMultiTouch(e) {\n    return e.touches && e.touches.length >= 2;\n}\nfunction getEventClients(e) {\n    if (!e) {\n        return [];\n    }\n    if (e.touches) {\n        return getClients(e.touches);\n    }\n    else {\n        return [getClient(e)];\n    }\n}\nfunction isMouseEvent(e) {\n    return e && (e.type.indexOf(\"mouse\") > -1 || \"button\" in e);\n}\nfunction getPosition(clients, prevClients, startClients) {\n    var length = startClients.length;\n    var _a = getAverageClient(clients, length), clientX = _a.clientX, clientY = _a.clientY, originalClientX = _a.originalClientX, originalClientY = _a.originalClientY;\n    var _b = getAverageClient(prevClients, length), prevX = _b.clientX, prevY = _b.clientY;\n    var _c = getAverageClient(startClients, length), startX = _c.clientX, startY = _c.clientY;\n    var deltaX = clientX - prevX;\n    var deltaY = clientY - prevY;\n    var distX = clientX - startX;\n    var distY = clientY - startY;\n    return {\n        clientX: originalClientX,\n        clientY: originalClientY,\n        deltaX: deltaX,\n        deltaY: deltaY,\n        distX: distX,\n        distY: distY,\n    };\n}\nfunction getDist(clients) {\n    return Math.sqrt(Math.pow(clients[0].clientX - clients[1].clientX, 2)\n        + Math.pow(clients[0].clientY - clients[1].clientY, 2));\n}\nfunction getClients(touches) {\n    var length = Math.min(touches.length, 2);\n    var clients = [];\n    for (var i = 0; i < length; ++i) {\n        clients.push(getClient(touches[i]));\n    }\n    return clients;\n}\nfunction getClient(e) {\n    return {\n        clientX: e.clientX,\n        clientY: e.clientY,\n    };\n}\nfunction getAverageClient(clients, length) {\n    if (length === void 0) { length = clients.length; }\n    var sumClient = {\n        clientX: 0,\n        clientY: 0,\n        originalClientX: 0,\n        originalClientY: 0,\n    };\n    var minLength = Math.min(clients.length, length);\n    for (var i = 0; i < minLength; ++i) {\n        var client = clients[i];\n        sumClient.originalClientX += \"originalClientX\" in client ? client.originalClientX : client.clientX;\n        sumClient.originalClientY += \"originalClientY\" in client ? client.originalClientY : client.clientY;\n        sumClient.clientX += client.clientX;\n        sumClient.clientY += client.clientY;\n    }\n    if (!length) {\n        return sumClient;\n    }\n    return {\n        clientX: sumClient.clientX / length,\n        clientY: sumClient.clientY / length,\n        originalClientX: sumClient.originalClientX / length,\n        originalClientY: sumClient.originalClientY / length,\n    };\n}\n\nvar ClientStore = /*#__PURE__*/ (function () {\n    function ClientStore(clients) {\n        this.prevClients = [];\n        this.startClients = [];\n        this.movement = 0;\n        this.length = 0;\n        this.startClients = clients;\n        this.prevClients = clients;\n        this.length = clients.length;\n    }\n    ClientStore.prototype.getAngle = function (clients) {\n        if (clients === void 0) { clients = this.prevClients; }\n        return getRotatiion(clients);\n    };\n    ClientStore.prototype.getRotation = function (clients) {\n        if (clients === void 0) { clients = this.prevClients; }\n        return getRotatiion(clients) - getRotatiion(this.startClients);\n    };\n    ClientStore.prototype.getPosition = function (clients, isAdd) {\n        if (clients === void 0) { clients = this.prevClients; }\n        var position = getPosition(clients || this.prevClients, this.prevClients, this.startClients);\n        var deltaX = position.deltaX, deltaY = position.deltaY;\n        this.movement += Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n        this.prevClients = clients;\n        return position;\n    };\n    ClientStore.prototype.getPositions = function (clients) {\n        if (clients === void 0) { clients = this.prevClients; }\n        var prevClients = this.prevClients;\n        var startClients = this.startClients;\n        var minLength = Math.min(this.length, prevClients.length);\n        var positions = [];\n        for (var i = 0; i < minLength; ++i) {\n            positions[i] = getPosition([clients[i]], [prevClients[i]], [startClients[i]]);\n        }\n        return positions;\n    };\n    ClientStore.prototype.getMovement = function (clients) {\n        var movement = this.movement;\n        if (!clients) {\n            return movement;\n        }\n        var currentClient = getAverageClient(clients, this.length);\n        var prevClient = getAverageClient(this.prevClients, this.length);\n        var deltaX = currentClient.clientX - prevClient.clientX;\n        var deltaY = currentClient.clientY - prevClient.clientY;\n        return Math.sqrt(deltaX * deltaX + deltaY * deltaY) + movement;\n    };\n    ClientStore.prototype.getDistance = function (clients) {\n        if (clients === void 0) { clients = this.prevClients; }\n        return getDist(clients);\n    };\n    ClientStore.prototype.getScale = function (clients) {\n        if (clients === void 0) { clients = this.prevClients; }\n        return getDist(clients) / getDist(this.startClients);\n    };\n    ClientStore.prototype.move = function (deltaX, deltaY) {\n        this.startClients.forEach(function (client) {\n            client.clientX -= deltaX;\n            client.clientY -= deltaY;\n        });\n        this.prevClients.forEach(function (client) {\n            client.clientX -= deltaX;\n            client.clientY -= deltaY;\n        });\n    };\n    return ClientStore;\n}());\n\nvar INPUT_TAGNAMES = [\"textarea\", \"input\"];\n/**\n * You can set up drag, pinch events in any browser.\n */\nvar Gesto = /*#__PURE__*/ (function (_super) {\n    __extends(Gesto, _super);\n    /**\n     *\n     */\n    function Gesto(targets, options) {\n        if (options === void 0) { options = {}; }\n        var _this = _super.call(this) || this;\n        _this.options = {};\n        _this.flag = false;\n        _this.pinchFlag = false;\n        _this.data = {};\n        _this.isDrag = false;\n        _this.isPinch = false;\n        _this.clientStores = [];\n        _this.targets = [];\n        _this.prevTime = 0;\n        _this.doubleFlag = false;\n        _this._useMouse = false;\n        _this._useTouch = false;\n        _this._useDrag = false;\n        _this._dragFlag = false;\n        _this._isTrusted = false;\n        _this._isMouseEvent = false;\n        _this._isSecondaryButton = false;\n        _this._preventMouseEvent = false;\n        _this._prevInputEvent = null;\n        _this._isDragAPI = false;\n        _this._isIdle = true;\n        _this._preventMouseEventId = 0;\n        _this._window = window;\n        _this.onDragStart = function (e, isTrusted) {\n            if (isTrusted === void 0) { isTrusted = true; }\n            if (!_this.flag && e.cancelable === false) {\n                return;\n            }\n            var isDragAPI = e.type.indexOf(\"drag\") >= -1;\n            if (_this.flag && isDragAPI) {\n                return;\n            }\n            _this._isDragAPI = true;\n            var _a = _this.options, container = _a.container, pinchOutside = _a.pinchOutside, preventWheelClick = _a.preventWheelClick, preventRightClick = _a.preventRightClick, preventDefault = _a.preventDefault, checkInput = _a.checkInput, dragFocusedInput = _a.dragFocusedInput, preventClickEventOnDragStart = _a.preventClickEventOnDragStart, preventClickEventOnDrag = _a.preventClickEventOnDrag, preventClickEventByCondition = _a.preventClickEventByCondition;\n            var useTouch = _this._useTouch;\n            var isDragStart = !_this.flag;\n            _this._isSecondaryButton = e.which === 3 || e.button === 2;\n            if ((preventWheelClick && (e.which === 2 || e.button === 1))\n                || (preventRightClick && (e.which === 3 || e.button === 2))) {\n                _this.stop();\n                return false;\n            }\n            if (isDragStart) {\n                var activeElement = _this._window.document.activeElement;\n                var target = e.target;\n                if (target) {\n                    var tagName = target.tagName.toLowerCase();\n                    var hasInput = INPUT_TAGNAMES.indexOf(tagName) > -1;\n                    var hasContentEditable = target.isContentEditable;\n                    if (hasInput || hasContentEditable) {\n                        if (checkInput || (!dragFocusedInput && activeElement === target)) {\n                            // force false or already focused.\n                            return false;\n                        }\n                        // no focus\n                        if (activeElement && (activeElement === target\n                            || (hasContentEditable && activeElement.isContentEditable && activeElement.contains(target)))) {\n                            if (dragFocusedInput) {\n                                target.blur();\n                            }\n                            else {\n                                return false;\n                            }\n                        }\n                    }\n                    else if ((preventDefault || e.type === \"touchstart\") && activeElement) {\n                        var activeTagName = activeElement.tagName.toLowerCase();\n                        if (activeElement.isContentEditable || INPUT_TAGNAMES.indexOf(activeTagName) > -1) {\n                            activeElement.blur();\n                        }\n                    }\n                    if (preventClickEventOnDragStart || preventClickEventOnDrag || preventClickEventByCondition) {\n                        (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(_this._window, \"click\", _this._onClick, true);\n                    }\n                }\n                _this.clientStores = [new ClientStore(getEventClients(e))];\n                _this._isIdle = false;\n                _this.flag = true;\n                _this.isDrag = false;\n                _this._isTrusted = isTrusted;\n                _this._dragFlag = true;\n                _this._prevInputEvent = e;\n                _this.data = {};\n                _this.doubleFlag = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.now)() - _this.prevTime < 200;\n                _this._isMouseEvent = isMouseEvent(e);\n                if (!_this._isMouseEvent && _this._preventMouseEvent) {\n                    _this._allowMouseEvent();\n                }\n                var result = _this._preventMouseEvent || _this.emit(\"dragStart\", __assign(__assign({ data: _this.data, datas: _this.data, inputEvent: e, isMouseEvent: _this._isMouseEvent, isSecondaryButton: _this._isSecondaryButton, isTrusted: isTrusted, isDouble: _this.doubleFlag }, _this.getCurrentStore().getPosition()), { preventDefault: function () {\n                        e.preventDefault();\n                    }, preventDrag: function () {\n                        _this._dragFlag = false;\n                    } }));\n                if (result === false) {\n                    _this.stop();\n                }\n                if (_this._isMouseEvent && _this.flag && preventDefault) {\n                    e.preventDefault();\n                }\n            }\n            if (!_this.flag) {\n                return false;\n            }\n            var timer = 0;\n            if (isDragStart) {\n                _this._attchDragEvent();\n                // wait pinch\n                if (useTouch && pinchOutside) {\n                    timer = setTimeout(function () {\n                        (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(container, \"touchstart\", _this.onDragStart, {\n                            passive: false\n                        });\n                    });\n                }\n            }\n            else if (useTouch && pinchOutside) {\n                // pinch is occured\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"touchstart\", _this.onDragStart);\n            }\n            if (_this.flag && isMultiTouch(e)) {\n                clearTimeout(timer);\n                if (isDragStart && (e.touches.length !== e.changedTouches.length)) {\n                    return;\n                }\n                if (!_this.pinchFlag) {\n                    _this.onPinchStart(e);\n                }\n            }\n        };\n        _this.onDrag = function (e, isScroll) {\n            if (!_this.flag) {\n                return;\n            }\n            var preventDefault = _this.options.preventDefault;\n            if (!_this._isMouseEvent && preventDefault) {\n                e.preventDefault();\n            }\n            _this._prevInputEvent = e;\n            var clients = getEventClients(e);\n            var result = _this.moveClients(clients, e, false);\n            if (_this._dragFlag) {\n                if (_this.pinchFlag || result.deltaX || result.deltaY) {\n                    var dragResult = _this._preventMouseEvent || _this.emit(\"drag\", __assign(__assign({}, result), { isScroll: !!isScroll, inputEvent: e }));\n                    if (dragResult === false) {\n                        _this.stop();\n                        return;\n                    }\n                }\n                if (_this.pinchFlag) {\n                    _this.onPinch(e, clients);\n                }\n            }\n            _this.getCurrentStore().getPosition(clients, true);\n        };\n        _this.onDragEnd = function (e) {\n            if (!_this.flag) {\n                return;\n            }\n            var _a = _this.options, pinchOutside = _a.pinchOutside, container = _a.container, preventClickEventOnDrag = _a.preventClickEventOnDrag, preventClickEventOnDragStart = _a.preventClickEventOnDragStart, preventClickEventByCondition = _a.preventClickEventByCondition;\n            var isDrag = _this.isDrag;\n            if (preventClickEventOnDrag || preventClickEventOnDragStart || preventClickEventByCondition) {\n                requestAnimationFrame(function () {\n                    _this._allowClickEvent();\n                });\n            }\n            if (!preventClickEventByCondition && !preventClickEventOnDragStart && preventClickEventOnDrag && !isDrag) {\n                _this._allowClickEvent();\n            }\n            if (_this._useTouch && pinchOutside) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"touchstart\", _this.onDragStart);\n            }\n            if (_this.pinchFlag) {\n                _this.onPinchEnd(e);\n            }\n            var clients = (e === null || e === void 0 ? void 0 : e.touches) ? getEventClients(e) : [];\n            var clientsLength = clients.length;\n            if (clientsLength === 0 || !_this.options.keepDragging) {\n                _this.flag = false;\n            }\n            else {\n                _this._addStore(new ClientStore(clients));\n            }\n            var position = _this._getPosition();\n            var currentTime = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.now)();\n            var isDouble = !isDrag && _this.doubleFlag;\n            _this._prevInputEvent = null;\n            _this.prevTime = isDrag || isDouble ? 0 : currentTime;\n            if (!_this.flag) {\n                _this._dettachDragEvent();\n                _this._preventMouseEvent || _this.emit(\"dragEnd\", __assign({ data: _this.data, datas: _this.data, isDouble: isDouble, isDrag: isDrag, isClick: !isDrag, isMouseEvent: _this._isMouseEvent, isSecondaryButton: _this._isSecondaryButton, inputEvent: e, isTrusted: _this._isTrusted }, position));\n                _this.clientStores = [];\n                if (!_this._isMouseEvent) {\n                    _this._preventMouseEvent = true;\n                    // Prevent the problem of touch event and mouse event occurring simultaneously\n                    clearTimeout(_this._preventMouseEventId);\n                    _this._preventMouseEventId = setTimeout(function () {\n                        _this._preventMouseEvent = false;\n                    }, 200);\n                }\n                _this._isIdle = true;\n            }\n        };\n        _this.onBlur = function () {\n            _this.onDragEnd();\n        };\n        _this._allowClickEvent = function () {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(_this._window, \"click\", _this._onClick, true);\n        };\n        _this._onClick = function (e) {\n            _this._allowClickEvent();\n            _this._allowMouseEvent();\n            var preventClickEventByCondition = _this.options.preventClickEventByCondition;\n            if (preventClickEventByCondition === null || preventClickEventByCondition === void 0 ? void 0 : preventClickEventByCondition(e)) {\n                return;\n            }\n            e.stopPropagation();\n            e.preventDefault();\n        };\n        _this._onContextMenu = function (e) {\n            var options = _this.options;\n            if (!options.preventRightClick) {\n                e.preventDefault();\n            }\n            else {\n                _this.onDragEnd(e);\n            }\n        };\n        _this._passCallback = function () { };\n        var elements = [].concat(targets);\n        var firstTarget = elements[0];\n        _this._window = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isWindow)(firstTarget) ? firstTarget : (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getWindow)(firstTarget);\n        _this.options = __assign({ checkInput: false, container: firstTarget && !(\"document\" in firstTarget) ? (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getWindow)(firstTarget) : firstTarget, preventRightClick: true, preventWheelClick: true, preventClickEventOnDragStart: false, preventClickEventOnDrag: false, preventClickEventByCondition: null, preventDefault: true, checkWindowBlur: false, keepDragging: false, pinchThreshold: 0, events: [\"touch\", \"mouse\"] }, options);\n        var _a = _this.options, container = _a.container, events = _a.events, checkWindowBlur = _a.checkWindowBlur;\n        _this._useDrag = events.indexOf(\"drag\") > -1;\n        _this._useTouch = events.indexOf(\"touch\") > -1;\n        _this._useMouse = events.indexOf(\"mouse\") > -1;\n        _this.targets = elements;\n        if (_this._useDrag) {\n            elements.forEach(function (el) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(el, \"dragstart\", _this.onDragStart);\n            });\n        }\n        if (_this._useMouse) {\n            elements.forEach(function (el) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(el, \"mousedown\", _this.onDragStart);\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(el, \"mousemove\", _this._passCallback);\n            });\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(container, \"contextmenu\", _this._onContextMenu);\n        }\n        if (checkWindowBlur) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.getWindow)(), \"blur\", _this.onBlur);\n        }\n        if (_this._useTouch) {\n            var passive_1 = {\n                passive: false,\n            };\n            elements.forEach(function (el) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(el, \"touchstart\", _this.onDragStart, passive_1);\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(el, \"touchmove\", _this._passCallback, passive_1);\n            });\n        }\n        return _this;\n    }\n    /**\n     * Stop Gesto's drag events.\n     */\n    Gesto.prototype.stop = function () {\n        this.isDrag = false;\n        this.data = {};\n        this.clientStores = [];\n        this.pinchFlag = false;\n        this.doubleFlag = false;\n        this.prevTime = 0;\n        this.flag = false;\n        this._isIdle = true;\n        this._allowClickEvent();\n        this._dettachDragEvent();\n        this._isDragAPI = false;\n    };\n    /**\n     * The total moved distance\n     */\n    Gesto.prototype.getMovement = function (clients) {\n        return this.getCurrentStore().getMovement(clients) + this.clientStores.slice(1).reduce(function (prev, cur) {\n            return prev + cur.movement;\n        }, 0);\n    };\n    /**\n     * Whether to drag\n     */\n    Gesto.prototype.isDragging = function () {\n        return this.isDrag;\n    };\n    /**\n     * Whether the operation of gesto is finished and is in idle state\n     */\n    Gesto.prototype.isIdle = function () {\n        return this._isIdle;\n    };\n    /**\n     * Whether to start drag\n     */\n    Gesto.prototype.isFlag = function () {\n        return this.flag;\n    };\n    /**\n     * Whether to start pinch\n     */\n    Gesto.prototype.isPinchFlag = function () {\n        return this.pinchFlag;\n    };\n    /**\n     * Whether to start double click\n     */\n    Gesto.prototype.isDoubleFlag = function () {\n        return this.doubleFlag;\n    };\n    /**\n     * Whether to pinch\n     */\n    Gesto.prototype.isPinching = function () {\n        return this.isPinch;\n    };\n    /**\n     * If a scroll event occurs, it is corrected by the scroll distance.\n     */\n    Gesto.prototype.scrollBy = function (deltaX, deltaY, e, isCallDrag) {\n        if (isCallDrag === void 0) { isCallDrag = true; }\n        if (!this.flag) {\n            return;\n        }\n        this.clientStores[0].move(deltaX, deltaY);\n        isCallDrag && this.onDrag(e, true);\n    };\n    /**\n     * Create a virtual drag event.\n     */\n    Gesto.prototype.move = function (_a, inputEvent) {\n        var deltaX = _a[0], deltaY = _a[1];\n        var store = this.getCurrentStore();\n        var nextClients = store.prevClients;\n        return this.moveClients(nextClients.map(function (_a) {\n            var clientX = _a.clientX, clientY = _a.clientY;\n            return {\n                clientX: clientX + deltaX,\n                clientY: clientY + deltaY,\n                originalClientX: clientX,\n                originalClientY: clientY,\n            };\n        }), inputEvent, true);\n    };\n    /**\n     * The dragStart event is triggered by an external event.\n     */\n    Gesto.prototype.triggerDragStart = function (e) {\n        this.onDragStart(e, false);\n    };\n    /**\n     * Set the event data while dragging.\n     */\n    Gesto.prototype.setEventData = function (data) {\n        var currentData = this.data;\n        for (var name_1 in data) {\n            currentData[name_1] = data[name_1];\n        }\n        return this;\n    };\n    /**\n     * Set the event data while dragging.\n     * Use `setEventData`\n     * @deprecated\n     */\n    Gesto.prototype.setEventDatas = function (data) {\n        return this.setEventData(data);\n    };\n    /**\n     * Get the current event state while dragging.\n     */\n    Gesto.prototype.getCurrentEvent = function (inputEvent) {\n        if (inputEvent === void 0) { inputEvent = this._prevInputEvent; }\n        return __assign(__assign({ data: this.data, datas: this.data }, this._getPosition()), { movement: this.getMovement(), isDrag: this.isDrag, isPinch: this.isPinch, isScroll: false, inputEvent: inputEvent });\n    };\n    /**\n     * Get & Set the event data while dragging.\n     */\n    Gesto.prototype.getEventData = function () {\n        return this.data;\n    };\n    /**\n     * Get & Set the event data while dragging.\n     * Use getEventData method\n     * @depreacated\n     */\n    Gesto.prototype.getEventDatas = function () {\n        return this.data;\n    };\n    /**\n     * Unset Gesto\n     */\n    Gesto.prototype.unset = function () {\n        var _this = this;\n        var targets = this.targets;\n        var container = this.options.container;\n        this.off();\n        (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(this._window, \"blur\", this.onBlur);\n        if (this._useDrag) {\n            targets.forEach(function (el) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(el, \"dragstart\", _this.onDragStart);\n            });\n        }\n        if (this._useMouse) {\n            targets.forEach(function (target) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(target, \"mousedown\", _this.onDragStart);\n            });\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"contextmenu\", this._onContextMenu);\n        }\n        if (this._useTouch) {\n            targets.forEach(function (target) {\n                (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(target, \"touchstart\", _this.onDragStart);\n            });\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"touchstart\", this.onDragStart);\n        }\n        this._prevInputEvent = null;\n        this._allowClickEvent();\n        this._dettachDragEvent();\n    };\n    Gesto.prototype.onPinchStart = function (e) {\n        var _this = this;\n        var pinchThreshold = this.options.pinchThreshold;\n        if (this.isDrag && this.getMovement() > pinchThreshold) {\n            return;\n        }\n        var store = new ClientStore(getEventClients(e));\n        this.pinchFlag = true;\n        this._addStore(store);\n        var result = this.emit(\"pinchStart\", __assign(__assign({ data: this.data, datas: this.data, angle: store.getAngle(), touches: this.getCurrentStore().getPositions() }, store.getPosition()), { inputEvent: e, isTrusted: this._isTrusted, preventDefault: function () {\n                e.preventDefault();\n            }, preventDrag: function () {\n                _this._dragFlag = false;\n            } }));\n        if (result === false) {\n            this.pinchFlag = false;\n        }\n    };\n    Gesto.prototype.onPinch = function (e, clients) {\n        if (!this.flag || !this.pinchFlag || clients.length < 2) {\n            return;\n        }\n        var store = this.getCurrentStore();\n        this.isPinch = true;\n        this.emit(\"pinch\", __assign(__assign({ data: this.data, datas: this.data, movement: this.getMovement(clients), angle: store.getAngle(clients), rotation: store.getRotation(clients), touches: store.getPositions(clients), scale: store.getScale(clients), distance: store.getDistance(clients) }, store.getPosition(clients)), { inputEvent: e, isTrusted: this._isTrusted }));\n    };\n    Gesto.prototype.onPinchEnd = function (e) {\n        if (!this.pinchFlag) {\n            return;\n        }\n        var isPinch = this.isPinch;\n        this.isPinch = false;\n        this.pinchFlag = false;\n        var store = this.getCurrentStore();\n        this.emit(\"pinchEnd\", __assign(__assign({ data: this.data, datas: this.data, isPinch: isPinch, touches: store.getPositions() }, store.getPosition()), { inputEvent: e }));\n    };\n    Gesto.prototype.getCurrentStore = function () {\n        return this.clientStores[0];\n    };\n    Gesto.prototype.moveClients = function (clients, inputEvent, isAdd) {\n        var position = this._getPosition(clients, isAdd);\n        var isPrevDrag = this.isDrag;\n        if (position.deltaX || position.deltaY) {\n            this.isDrag = true;\n        }\n        var isFirstDrag = false;\n        if (!isPrevDrag && this.isDrag) {\n            isFirstDrag = true;\n        }\n        return __assign(__assign({ data: this.data, datas: this.data }, position), { movement: this.getMovement(clients), isDrag: this.isDrag, isPinch: this.isPinch, isScroll: false, isMouseEvent: this._isMouseEvent, isSecondaryButton: this._isSecondaryButton, inputEvent: inputEvent, isTrusted: this._isTrusted, isFirstDrag: isFirstDrag });\n    };\n    Gesto.prototype._addStore = function (store) {\n        this.clientStores.splice(0, 0, store);\n    };\n    Gesto.prototype._getPosition = function (clients, isAdd) {\n        var store = this.getCurrentStore();\n        var position = store.getPosition(clients, isAdd);\n        var _a = this.clientStores.slice(1).reduce(function (prev, cur) {\n            var storePosition = cur.getPosition();\n            prev.distX += storePosition.distX;\n            prev.distY += storePosition.distY;\n            return prev;\n        }, position), distX = _a.distX, distY = _a.distY;\n        return __assign(__assign({}, position), { distX: distX, distY: distY });\n    };\n    Gesto.prototype._attchDragEvent = function () {\n        var win = this._window;\n        var container = this.options.container;\n        var passive = {\n            passive: false\n        };\n        if (this._isDragAPI) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(container, \"dragover\", this.onDrag, passive);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(win, \"dragend\", this.onDragEnd);\n        }\n        if (this._useMouse) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(container, \"mousemove\", this.onDrag);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(win, \"mouseup\", this.onDragEnd);\n        }\n        if (this._useTouch) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(container, \"touchmove\", this.onDrag, passive);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(win, \"touchend\", this.onDragEnd, passive);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.addEvent)(win, \"touchcancel\", this.onDragEnd, passive);\n        }\n    };\n    Gesto.prototype._dettachDragEvent = function () {\n        var win = this._window;\n        var container = this.options.container;\n        if (this._isDragAPI) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"dragover\", this.onDrag);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(win, \"dragend\", this.onDragEnd);\n        }\n        if (this._useMouse) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"mousemove\", this.onDrag);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(win, \"mouseup\", this.onDragEnd);\n        }\n        if (this._useTouch) {\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"touchstart\", this.onDragStart);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(container, \"touchmove\", this.onDrag);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(win, \"touchend\", this.onDragEnd);\n            (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.removeEvent)(win, \"touchcancel\", this.onDragEnd);\n        }\n    };\n    Gesto.prototype._allowMouseEvent = function () {\n        this._preventMouseEvent = false;\n        clearTimeout(this._preventMouseEventId);\n    };\n    return Gesto;\n}(_scena_event_emitter__WEBPACK_IMPORTED_MODULE_1__[\"default\"]));\n\n\n//# sourceMappingURL=gesto.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZ2VzdG9AMS4xOS40L25vZGVfbW9kdWxlcy9nZXN0by9kaXN0L2dlc3RvLmVzbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2dEO0FBQ2tDOztBQUVsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGdCQUFnQixzQ0FBc0Msa0JBQWtCO0FBQ25GLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsT0FBTztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsWUFBWTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGVBQWU7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGVBQWU7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0M7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5REFBUTtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxvREFBRztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHFHQUFxRyxzTEFBc0wsNENBQTRDO0FBQ3ZVO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlEQUFRO0FBQ2hDO0FBQ0EseUJBQXlCO0FBQ3pCLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiw0REFBVztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3R0FBd0csYUFBYSxxQ0FBcUM7QUFDMUo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDREQUFXO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLG9EQUFHO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2RUFBNkUsdU5BQXVOO0FBQ3BTO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLDREQUFXO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseURBQVEsOEJBQThCLDBEQUFTO0FBQ3ZFLG1DQUFtQyw0RUFBNEUsMERBQVMsMFNBQTBTO0FBQ2xhO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLHlEQUFRO0FBQ3hCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IseURBQVE7QUFDeEIsZ0JBQWdCLHlEQUFRO0FBQ3hCLGFBQWE7QUFDYixZQUFZLHlEQUFRO0FBQ3BCO0FBQ0E7QUFDQSxZQUFZLHlEQUFRLENBQUMsMERBQVM7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLHlEQUFRO0FBQ3hCLGdCQUFnQix5REFBUTtBQUN4QixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQyxtQ0FBbUMsbUNBQW1DLDBCQUEwQixtSEFBbUg7QUFDbk47QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsNERBQVc7QUFDbkI7QUFDQTtBQUNBLGdCQUFnQiw0REFBVztBQUMzQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDREQUFXO0FBQzNCLGFBQWE7QUFDYixZQUFZLDREQUFXO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiw0REFBVztBQUMzQixhQUFhO0FBQ2IsWUFBWSw0REFBVztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLDRHQUE0RywwQkFBMEI7QUFDdk07QUFDQSxhQUFhO0FBQ2I7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MsMFBBQTBQLGlDQUFpQywyQ0FBMkM7QUFDclg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELG9GQUFvRiwwQkFBMEIsZUFBZTtBQUMvSztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLG1DQUFtQyxlQUFlLDhQQUE4UDtBQUNuVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsbUNBQW1DLGVBQWUsNEJBQTRCO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHlEQUFRO0FBQ3BCLFlBQVkseURBQVE7QUFDcEI7QUFDQTtBQUNBLFlBQVkseURBQVE7QUFDcEIsWUFBWSx5REFBUTtBQUNwQjtBQUNBO0FBQ0EsWUFBWSx5REFBUTtBQUNwQixZQUFZLHlEQUFRO0FBQ3BCLFlBQVkseURBQVE7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSw0REFBVztBQUN2QixZQUFZLDREQUFXO0FBQ3ZCO0FBQ0E7QUFDQSxZQUFZLDREQUFXO0FBQ3ZCLFlBQVksNERBQVc7QUFDdkI7QUFDQTtBQUNBLFlBQVksNERBQVc7QUFDdkIsWUFBWSw0REFBVztBQUN2QixZQUFZLDREQUFXO0FBQ3ZCLFlBQVksNERBQVc7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLENBQUMsNERBQVk7O0FBRWM7QUFDNUIiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxnZXN0b0AxLjE5LjRcXG5vZGVfbW9kdWxlc1xcZ2VzdG9cXGRpc3RcXGdlc3RvLmVzbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuQ29weXJpZ2h0IChjKSAyMDE5IERheWJydXNoXG5uYW1lOiBnZXN0b1xubGljZW5zZTogTUlUXG5hdXRob3I6IERheWJydXNoXG5yZXBvc2l0b3J5OiBnaXQraHR0cHM6Ly9naXRodWIuY29tL2RheWJydXNoL2dlc3RvLmdpdFxudmVyc2lvbjogMS4xOS40XG4qL1xuaW1wb3J0IEV2ZW50RW1pdHRlciBmcm9tICdAc2NlbmEvZXZlbnQtZW1pdHRlcic7XG5pbXBvcnQgeyByZW1vdmVFdmVudCwgYWRkRXZlbnQsIG5vdywgaXNXaW5kb3csIGdldFdpbmRvdyB9IGZyb20gJ0BkYXlicnVzaC91dGlscyc7XG5cbi8qISAqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKlxyXG5Db3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cclxuXHJcblBlcm1pc3Npb24gdG8gdXNlLCBjb3B5LCBtb2RpZnksIGFuZC9vciBkaXN0cmlidXRlIHRoaXMgc29mdHdhcmUgZm9yIGFueVxyXG5wdXJwb3NlIHdpdGggb3Igd2l0aG91dCBmZWUgaXMgaGVyZWJ5IGdyYW50ZWQuXHJcblxyXG5USEUgU09GVFdBUkUgSVMgUFJPVklERUQgXCJBUyBJU1wiIEFORCBUSEUgQVVUSE9SIERJU0NMQUlNUyBBTEwgV0FSUkFOVElFUyBXSVRIXHJcblJFR0FSRCBUTyBUSElTIFNPRlRXQVJFIElOQ0xVRElORyBBTEwgSU1QTElFRCBXQVJSQU5USUVTIE9GIE1FUkNIQU5UQUJJTElUWVxyXG5BTkQgRklUTkVTUy4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFIEFVVEhPUiBCRSBMSUFCTEUgRk9SIEFOWSBTUEVDSUFMLCBESVJFQ1QsXHJcbklORElSRUNULCBPUiBDT05TRVFVRU5USUFMIERBTUFHRVMgT1IgQU5ZIERBTUFHRVMgV0hBVFNPRVZFUiBSRVNVTFRJTkcgRlJPTVxyXG5MT1NTIE9GIFVTRSwgREFUQSBPUiBQUk9GSVRTLCBXSEVUSEVSIElOIEFOIEFDVElPTiBPRiBDT05UUkFDVCwgTkVHTElHRU5DRSBPUlxyXG5PVEhFUiBUT1JUSU9VUyBBQ1RJT04sIEFSSVNJTkcgT1VUIE9GIE9SIElOIENPTk5FQ1RJT04gV0lUSCBUSEUgVVNFIE9SXHJcblBFUkZPUk1BTkNFIE9GIFRISVMgU09GVFdBUkUuXHJcbioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqICovXHJcbi8qIGdsb2JhbCBSZWZsZWN0LCBQcm9taXNlICovXHJcblxyXG52YXIgZXh0ZW5kU3RhdGljcyA9IGZ1bmN0aW9uKGQsIGIpIHtcclxuICAgIGV4dGVuZFN0YXRpY3MgPSBPYmplY3Quc2V0UHJvdG90eXBlT2YgfHxcclxuICAgICAgICAoeyBfX3Byb3RvX186IFtdIH0gaW5zdGFuY2VvZiBBcnJheSAmJiBmdW5jdGlvbiAoZCwgYikgeyBkLl9fcHJvdG9fXyA9IGI7IH0pIHx8XHJcbiAgICAgICAgZnVuY3Rpb24gKGQsIGIpIHsgZm9yICh2YXIgcCBpbiBiKSBpZiAoYi5oYXNPd25Qcm9wZXJ0eShwKSkgZFtwXSA9IGJbcF07IH07XHJcbiAgICByZXR1cm4gZXh0ZW5kU3RhdGljcyhkLCBiKTtcclxufTtcclxuXHJcbmZ1bmN0aW9uIF9fZXh0ZW5kcyhkLCBiKSB7XHJcbiAgICBleHRlbmRTdGF0aWNzKGQsIGIpO1xyXG4gICAgZnVuY3Rpb24gX18oKSB7IHRoaXMuY29uc3RydWN0b3IgPSBkOyB9XHJcbiAgICBkLnByb3RvdHlwZSA9IGIgPT09IG51bGwgPyBPYmplY3QuY3JlYXRlKGIpIDogKF9fLnByb3RvdHlwZSA9IGIucHJvdG90eXBlLCBuZXcgX18oKSk7XHJcbn1cclxuXHJcbnZhciBfX2Fzc2lnbiA9IGZ1bmN0aW9uKCkge1xyXG4gICAgX19hc3NpZ24gPSBPYmplY3QuYXNzaWduIHx8IGZ1bmN0aW9uIF9fYXNzaWduKHQpIHtcclxuICAgICAgICBmb3IgKHZhciBzLCBpID0gMSwgbiA9IGFyZ3VtZW50cy5sZW5ndGg7IGkgPCBuOyBpKyspIHtcclxuICAgICAgICAgICAgcyA9IGFyZ3VtZW50c1tpXTtcclxuICAgICAgICAgICAgZm9yICh2YXIgcCBpbiBzKSBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHMsIHApKSB0W3BdID0gc1twXTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHQ7XHJcbiAgICB9O1xyXG4gICAgcmV0dXJuIF9fYXNzaWduLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XHJcbn07XG5cbmZ1bmN0aW9uIGdldFJhZChwb3MxLCBwb3MyKSB7XG4gICAgdmFyIGRpc3RYID0gcG9zMlswXSAtIHBvczFbMF07XG4gICAgdmFyIGRpc3RZID0gcG9zMlsxXSAtIHBvczFbMV07XG4gICAgdmFyIHJhZCA9IE1hdGguYXRhbjIoZGlzdFksIGRpc3RYKTtcbiAgICByZXR1cm4gcmFkID49IDAgPyByYWQgOiByYWQgKyBNYXRoLlBJICogMjtcbn1cbmZ1bmN0aW9uIGdldFJvdGF0aWlvbih0b3VjaGVzKSB7XG4gICAgcmV0dXJuIGdldFJhZChbXG4gICAgICAgIHRvdWNoZXNbMF0uY2xpZW50WCxcbiAgICAgICAgdG91Y2hlc1swXS5jbGllbnRZLFxuICAgIF0sIFtcbiAgICAgICAgdG91Y2hlc1sxXS5jbGllbnRYLFxuICAgICAgICB0b3VjaGVzWzFdLmNsaWVudFksXG4gICAgXSkgLyBNYXRoLlBJICogMTgwO1xufVxuZnVuY3Rpb24gaXNNdWx0aVRvdWNoKGUpIHtcbiAgICByZXR1cm4gZS50b3VjaGVzICYmIGUudG91Y2hlcy5sZW5ndGggPj0gMjtcbn1cbmZ1bmN0aW9uIGdldEV2ZW50Q2xpZW50cyhlKSB7XG4gICAgaWYgKCFlKSB7XG4gICAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gICAgaWYgKGUudG91Y2hlcykge1xuICAgICAgICByZXR1cm4gZ2V0Q2xpZW50cyhlLnRvdWNoZXMpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIFtnZXRDbGllbnQoZSldO1xuICAgIH1cbn1cbmZ1bmN0aW9uIGlzTW91c2VFdmVudChlKSB7XG4gICAgcmV0dXJuIGUgJiYgKGUudHlwZS5pbmRleE9mKFwibW91c2VcIikgPiAtMSB8fCBcImJ1dHRvblwiIGluIGUpO1xufVxuZnVuY3Rpb24gZ2V0UG9zaXRpb24oY2xpZW50cywgcHJldkNsaWVudHMsIHN0YXJ0Q2xpZW50cykge1xuICAgIHZhciBsZW5ndGggPSBzdGFydENsaWVudHMubGVuZ3RoO1xuICAgIHZhciBfYSA9IGdldEF2ZXJhZ2VDbGllbnQoY2xpZW50cywgbGVuZ3RoKSwgY2xpZW50WCA9IF9hLmNsaWVudFgsIGNsaWVudFkgPSBfYS5jbGllbnRZLCBvcmlnaW5hbENsaWVudFggPSBfYS5vcmlnaW5hbENsaWVudFgsIG9yaWdpbmFsQ2xpZW50WSA9IF9hLm9yaWdpbmFsQ2xpZW50WTtcbiAgICB2YXIgX2IgPSBnZXRBdmVyYWdlQ2xpZW50KHByZXZDbGllbnRzLCBsZW5ndGgpLCBwcmV2WCA9IF9iLmNsaWVudFgsIHByZXZZID0gX2IuY2xpZW50WTtcbiAgICB2YXIgX2MgPSBnZXRBdmVyYWdlQ2xpZW50KHN0YXJ0Q2xpZW50cywgbGVuZ3RoKSwgc3RhcnRYID0gX2MuY2xpZW50WCwgc3RhcnRZID0gX2MuY2xpZW50WTtcbiAgICB2YXIgZGVsdGFYID0gY2xpZW50WCAtIHByZXZYO1xuICAgIHZhciBkZWx0YVkgPSBjbGllbnRZIC0gcHJldlk7XG4gICAgdmFyIGRpc3RYID0gY2xpZW50WCAtIHN0YXJ0WDtcbiAgICB2YXIgZGlzdFkgPSBjbGllbnRZIC0gc3RhcnRZO1xuICAgIHJldHVybiB7XG4gICAgICAgIGNsaWVudFg6IG9yaWdpbmFsQ2xpZW50WCxcbiAgICAgICAgY2xpZW50WTogb3JpZ2luYWxDbGllbnRZLFxuICAgICAgICBkZWx0YVg6IGRlbHRhWCxcbiAgICAgICAgZGVsdGFZOiBkZWx0YVksXG4gICAgICAgIGRpc3RYOiBkaXN0WCxcbiAgICAgICAgZGlzdFk6IGRpc3RZLFxuICAgIH07XG59XG5mdW5jdGlvbiBnZXREaXN0KGNsaWVudHMpIHtcbiAgICByZXR1cm4gTWF0aC5zcXJ0KE1hdGgucG93KGNsaWVudHNbMF0uY2xpZW50WCAtIGNsaWVudHNbMV0uY2xpZW50WCwgMilcbiAgICAgICAgKyBNYXRoLnBvdyhjbGllbnRzWzBdLmNsaWVudFkgLSBjbGllbnRzWzFdLmNsaWVudFksIDIpKTtcbn1cbmZ1bmN0aW9uIGdldENsaWVudHModG91Y2hlcykge1xuICAgIHZhciBsZW5ndGggPSBNYXRoLm1pbih0b3VjaGVzLmxlbmd0aCwgMik7XG4gICAgdmFyIGNsaWVudHMgPSBbXTtcbiAgICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbmd0aDsgKytpKSB7XG4gICAgICAgIGNsaWVudHMucHVzaChnZXRDbGllbnQodG91Y2hlc1tpXSkpO1xuICAgIH1cbiAgICByZXR1cm4gY2xpZW50cztcbn1cbmZ1bmN0aW9uIGdldENsaWVudChlKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgY2xpZW50WDogZS5jbGllbnRYLFxuICAgICAgICBjbGllbnRZOiBlLmNsaWVudFksXG4gICAgfTtcbn1cbmZ1bmN0aW9uIGdldEF2ZXJhZ2VDbGllbnQoY2xpZW50cywgbGVuZ3RoKSB7XG4gICAgaWYgKGxlbmd0aCA9PT0gdm9pZCAwKSB7IGxlbmd0aCA9IGNsaWVudHMubGVuZ3RoOyB9XG4gICAgdmFyIHN1bUNsaWVudCA9IHtcbiAgICAgICAgY2xpZW50WDogMCxcbiAgICAgICAgY2xpZW50WTogMCxcbiAgICAgICAgb3JpZ2luYWxDbGllbnRYOiAwLFxuICAgICAgICBvcmlnaW5hbENsaWVudFk6IDAsXG4gICAgfTtcbiAgICB2YXIgbWluTGVuZ3RoID0gTWF0aC5taW4oY2xpZW50cy5sZW5ndGgsIGxlbmd0aCk7XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBtaW5MZW5ndGg7ICsraSkge1xuICAgICAgICB2YXIgY2xpZW50ID0gY2xpZW50c1tpXTtcbiAgICAgICAgc3VtQ2xpZW50Lm9yaWdpbmFsQ2xpZW50WCArPSBcIm9yaWdpbmFsQ2xpZW50WFwiIGluIGNsaWVudCA/IGNsaWVudC5vcmlnaW5hbENsaWVudFggOiBjbGllbnQuY2xpZW50WDtcbiAgICAgICAgc3VtQ2xpZW50Lm9yaWdpbmFsQ2xpZW50WSArPSBcIm9yaWdpbmFsQ2xpZW50WVwiIGluIGNsaWVudCA/IGNsaWVudC5vcmlnaW5hbENsaWVudFkgOiBjbGllbnQuY2xpZW50WTtcbiAgICAgICAgc3VtQ2xpZW50LmNsaWVudFggKz0gY2xpZW50LmNsaWVudFg7XG4gICAgICAgIHN1bUNsaWVudC5jbGllbnRZICs9IGNsaWVudC5jbGllbnRZO1xuICAgIH1cbiAgICBpZiAoIWxlbmd0aCkge1xuICAgICAgICByZXR1cm4gc3VtQ2xpZW50O1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICBjbGllbnRYOiBzdW1DbGllbnQuY2xpZW50WCAvIGxlbmd0aCxcbiAgICAgICAgY2xpZW50WTogc3VtQ2xpZW50LmNsaWVudFkgLyBsZW5ndGgsXG4gICAgICAgIG9yaWdpbmFsQ2xpZW50WDogc3VtQ2xpZW50Lm9yaWdpbmFsQ2xpZW50WCAvIGxlbmd0aCxcbiAgICAgICAgb3JpZ2luYWxDbGllbnRZOiBzdW1DbGllbnQub3JpZ2luYWxDbGllbnRZIC8gbGVuZ3RoLFxuICAgIH07XG59XG5cbnZhciBDbGllbnRTdG9yZSA9IC8qI19fUFVSRV9fKi8gKGZ1bmN0aW9uICgpIHtcbiAgICBmdW5jdGlvbiBDbGllbnRTdG9yZShjbGllbnRzKSB7XG4gICAgICAgIHRoaXMucHJldkNsaWVudHMgPSBbXTtcbiAgICAgICAgdGhpcy5zdGFydENsaWVudHMgPSBbXTtcbiAgICAgICAgdGhpcy5tb3ZlbWVudCA9IDA7XG4gICAgICAgIHRoaXMubGVuZ3RoID0gMDtcbiAgICAgICAgdGhpcy5zdGFydENsaWVudHMgPSBjbGllbnRzO1xuICAgICAgICB0aGlzLnByZXZDbGllbnRzID0gY2xpZW50cztcbiAgICAgICAgdGhpcy5sZW5ndGggPSBjbGllbnRzLmxlbmd0aDtcbiAgICB9XG4gICAgQ2xpZW50U3RvcmUucHJvdG90eXBlLmdldEFuZ2xlID0gZnVuY3Rpb24gKGNsaWVudHMpIHtcbiAgICAgICAgaWYgKGNsaWVudHMgPT09IHZvaWQgMCkgeyBjbGllbnRzID0gdGhpcy5wcmV2Q2xpZW50czsgfVxuICAgICAgICByZXR1cm4gZ2V0Um90YXRpaW9uKGNsaWVudHMpO1xuICAgIH07XG4gICAgQ2xpZW50U3RvcmUucHJvdG90eXBlLmdldFJvdGF0aW9uID0gZnVuY3Rpb24gKGNsaWVudHMpIHtcbiAgICAgICAgaWYgKGNsaWVudHMgPT09IHZvaWQgMCkgeyBjbGllbnRzID0gdGhpcy5wcmV2Q2xpZW50czsgfVxuICAgICAgICByZXR1cm4gZ2V0Um90YXRpaW9uKGNsaWVudHMpIC0gZ2V0Um90YXRpaW9uKHRoaXMuc3RhcnRDbGllbnRzKTtcbiAgICB9O1xuICAgIENsaWVudFN0b3JlLnByb3RvdHlwZS5nZXRQb3NpdGlvbiA9IGZ1bmN0aW9uIChjbGllbnRzLCBpc0FkZCkge1xuICAgICAgICBpZiAoY2xpZW50cyA9PT0gdm9pZCAwKSB7IGNsaWVudHMgPSB0aGlzLnByZXZDbGllbnRzOyB9XG4gICAgICAgIHZhciBwb3NpdGlvbiA9IGdldFBvc2l0aW9uKGNsaWVudHMgfHwgdGhpcy5wcmV2Q2xpZW50cywgdGhpcy5wcmV2Q2xpZW50cywgdGhpcy5zdGFydENsaWVudHMpO1xuICAgICAgICB2YXIgZGVsdGFYID0gcG9zaXRpb24uZGVsdGFYLCBkZWx0YVkgPSBwb3NpdGlvbi5kZWx0YVk7XG4gICAgICAgIHRoaXMubW92ZW1lbnQgKz0gTWF0aC5zcXJ0KGRlbHRhWCAqIGRlbHRhWCArIGRlbHRhWSAqIGRlbHRhWSk7XG4gICAgICAgIHRoaXMucHJldkNsaWVudHMgPSBjbGllbnRzO1xuICAgICAgICByZXR1cm4gcG9zaXRpb247XG4gICAgfTtcbiAgICBDbGllbnRTdG9yZS5wcm90b3R5cGUuZ2V0UG9zaXRpb25zID0gZnVuY3Rpb24gKGNsaWVudHMpIHtcbiAgICAgICAgaWYgKGNsaWVudHMgPT09IHZvaWQgMCkgeyBjbGllbnRzID0gdGhpcy5wcmV2Q2xpZW50czsgfVxuICAgICAgICB2YXIgcHJldkNsaWVudHMgPSB0aGlzLnByZXZDbGllbnRzO1xuICAgICAgICB2YXIgc3RhcnRDbGllbnRzID0gdGhpcy5zdGFydENsaWVudHM7XG4gICAgICAgIHZhciBtaW5MZW5ndGggPSBNYXRoLm1pbih0aGlzLmxlbmd0aCwgcHJldkNsaWVudHMubGVuZ3RoKTtcbiAgICAgICAgdmFyIHBvc2l0aW9ucyA9IFtdO1xuICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IG1pbkxlbmd0aDsgKytpKSB7XG4gICAgICAgICAgICBwb3NpdGlvbnNbaV0gPSBnZXRQb3NpdGlvbihbY2xpZW50c1tpXV0sIFtwcmV2Q2xpZW50c1tpXV0sIFtzdGFydENsaWVudHNbaV1dKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcG9zaXRpb25zO1xuICAgIH07XG4gICAgQ2xpZW50U3RvcmUucHJvdG90eXBlLmdldE1vdmVtZW50ID0gZnVuY3Rpb24gKGNsaWVudHMpIHtcbiAgICAgICAgdmFyIG1vdmVtZW50ID0gdGhpcy5tb3ZlbWVudDtcbiAgICAgICAgaWYgKCFjbGllbnRzKSB7XG4gICAgICAgICAgICByZXR1cm4gbW92ZW1lbnQ7XG4gICAgICAgIH1cbiAgICAgICAgdmFyIGN1cnJlbnRDbGllbnQgPSBnZXRBdmVyYWdlQ2xpZW50KGNsaWVudHMsIHRoaXMubGVuZ3RoKTtcbiAgICAgICAgdmFyIHByZXZDbGllbnQgPSBnZXRBdmVyYWdlQ2xpZW50KHRoaXMucHJldkNsaWVudHMsIHRoaXMubGVuZ3RoKTtcbiAgICAgICAgdmFyIGRlbHRhWCA9IGN1cnJlbnRDbGllbnQuY2xpZW50WCAtIHByZXZDbGllbnQuY2xpZW50WDtcbiAgICAgICAgdmFyIGRlbHRhWSA9IGN1cnJlbnRDbGllbnQuY2xpZW50WSAtIHByZXZDbGllbnQuY2xpZW50WTtcbiAgICAgICAgcmV0dXJuIE1hdGguc3FydChkZWx0YVggKiBkZWx0YVggKyBkZWx0YVkgKiBkZWx0YVkpICsgbW92ZW1lbnQ7XG4gICAgfTtcbiAgICBDbGllbnRTdG9yZS5wcm90b3R5cGUuZ2V0RGlzdGFuY2UgPSBmdW5jdGlvbiAoY2xpZW50cykge1xuICAgICAgICBpZiAoY2xpZW50cyA9PT0gdm9pZCAwKSB7IGNsaWVudHMgPSB0aGlzLnByZXZDbGllbnRzOyB9XG4gICAgICAgIHJldHVybiBnZXREaXN0KGNsaWVudHMpO1xuICAgIH07XG4gICAgQ2xpZW50U3RvcmUucHJvdG90eXBlLmdldFNjYWxlID0gZnVuY3Rpb24gKGNsaWVudHMpIHtcbiAgICAgICAgaWYgKGNsaWVudHMgPT09IHZvaWQgMCkgeyBjbGllbnRzID0gdGhpcy5wcmV2Q2xpZW50czsgfVxuICAgICAgICByZXR1cm4gZ2V0RGlzdChjbGllbnRzKSAvIGdldERpc3QodGhpcy5zdGFydENsaWVudHMpO1xuICAgIH07XG4gICAgQ2xpZW50U3RvcmUucHJvdG90eXBlLm1vdmUgPSBmdW5jdGlvbiAoZGVsdGFYLCBkZWx0YVkpIHtcbiAgICAgICAgdGhpcy5zdGFydENsaWVudHMuZm9yRWFjaChmdW5jdGlvbiAoY2xpZW50KSB7XG4gICAgICAgICAgICBjbGllbnQuY2xpZW50WCAtPSBkZWx0YVg7XG4gICAgICAgICAgICBjbGllbnQuY2xpZW50WSAtPSBkZWx0YVk7XG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLnByZXZDbGllbnRzLmZvckVhY2goZnVuY3Rpb24gKGNsaWVudCkge1xuICAgICAgICAgICAgY2xpZW50LmNsaWVudFggLT0gZGVsdGFYO1xuICAgICAgICAgICAgY2xpZW50LmNsaWVudFkgLT0gZGVsdGFZO1xuICAgICAgICB9KTtcbiAgICB9O1xuICAgIHJldHVybiBDbGllbnRTdG9yZTtcbn0oKSk7XG5cbnZhciBJTlBVVF9UQUdOQU1FUyA9IFtcInRleHRhcmVhXCIsIFwiaW5wdXRcIl07XG4vKipcbiAqIFlvdSBjYW4gc2V0IHVwIGRyYWcsIHBpbmNoIGV2ZW50cyBpbiBhbnkgYnJvd3Nlci5cbiAqL1xudmFyIEdlc3RvID0gLyojX19QVVJFX18qLyAoZnVuY3Rpb24gKF9zdXBlcikge1xuICAgIF9fZXh0ZW5kcyhHZXN0bywgX3N1cGVyKTtcbiAgICAvKipcbiAgICAgKlxuICAgICAqL1xuICAgIGZ1bmN0aW9uIEdlc3RvKHRhcmdldHMsIG9wdGlvbnMpIHtcbiAgICAgICAgaWYgKG9wdGlvbnMgPT09IHZvaWQgMCkgeyBvcHRpb25zID0ge307IH1cbiAgICAgICAgdmFyIF90aGlzID0gX3N1cGVyLmNhbGwodGhpcykgfHwgdGhpcztcbiAgICAgICAgX3RoaXMub3B0aW9ucyA9IHt9O1xuICAgICAgICBfdGhpcy5mbGFnID0gZmFsc2U7XG4gICAgICAgIF90aGlzLnBpbmNoRmxhZyA9IGZhbHNlO1xuICAgICAgICBfdGhpcy5kYXRhID0ge307XG4gICAgICAgIF90aGlzLmlzRHJhZyA9IGZhbHNlO1xuICAgICAgICBfdGhpcy5pc1BpbmNoID0gZmFsc2U7XG4gICAgICAgIF90aGlzLmNsaWVudFN0b3JlcyA9IFtdO1xuICAgICAgICBfdGhpcy50YXJnZXRzID0gW107XG4gICAgICAgIF90aGlzLnByZXZUaW1lID0gMDtcbiAgICAgICAgX3RoaXMuZG91YmxlRmxhZyA9IGZhbHNlO1xuICAgICAgICBfdGhpcy5fdXNlTW91c2UgPSBmYWxzZTtcbiAgICAgICAgX3RoaXMuX3VzZVRvdWNoID0gZmFsc2U7XG4gICAgICAgIF90aGlzLl91c2VEcmFnID0gZmFsc2U7XG4gICAgICAgIF90aGlzLl9kcmFnRmxhZyA9IGZhbHNlO1xuICAgICAgICBfdGhpcy5faXNUcnVzdGVkID0gZmFsc2U7XG4gICAgICAgIF90aGlzLl9pc01vdXNlRXZlbnQgPSBmYWxzZTtcbiAgICAgICAgX3RoaXMuX2lzU2Vjb25kYXJ5QnV0dG9uID0gZmFsc2U7XG4gICAgICAgIF90aGlzLl9wcmV2ZW50TW91c2VFdmVudCA9IGZhbHNlO1xuICAgICAgICBfdGhpcy5fcHJldklucHV0RXZlbnQgPSBudWxsO1xuICAgICAgICBfdGhpcy5faXNEcmFnQVBJID0gZmFsc2U7XG4gICAgICAgIF90aGlzLl9pc0lkbGUgPSB0cnVlO1xuICAgICAgICBfdGhpcy5fcHJldmVudE1vdXNlRXZlbnRJZCA9IDA7XG4gICAgICAgIF90aGlzLl93aW5kb3cgPSB3aW5kb3c7XG4gICAgICAgIF90aGlzLm9uRHJhZ1N0YXJ0ID0gZnVuY3Rpb24gKGUsIGlzVHJ1c3RlZCkge1xuICAgICAgICAgICAgaWYgKGlzVHJ1c3RlZCA9PT0gdm9pZCAwKSB7IGlzVHJ1c3RlZCA9IHRydWU7IH1cbiAgICAgICAgICAgIGlmICghX3RoaXMuZmxhZyAmJiBlLmNhbmNlbGFibGUgPT09IGZhbHNlKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdmFyIGlzRHJhZ0FQSSA9IGUudHlwZS5pbmRleE9mKFwiZHJhZ1wiKSA+PSAtMTtcbiAgICAgICAgICAgIGlmIChfdGhpcy5mbGFnICYmIGlzRHJhZ0FQSSkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF90aGlzLl9pc0RyYWdBUEkgPSB0cnVlO1xuICAgICAgICAgICAgdmFyIF9hID0gX3RoaXMub3B0aW9ucywgY29udGFpbmVyID0gX2EuY29udGFpbmVyLCBwaW5jaE91dHNpZGUgPSBfYS5waW5jaE91dHNpZGUsIHByZXZlbnRXaGVlbENsaWNrID0gX2EucHJldmVudFdoZWVsQ2xpY2ssIHByZXZlbnRSaWdodENsaWNrID0gX2EucHJldmVudFJpZ2h0Q2xpY2ssIHByZXZlbnREZWZhdWx0ID0gX2EucHJldmVudERlZmF1bHQsIGNoZWNrSW5wdXQgPSBfYS5jaGVja0lucHV0LCBkcmFnRm9jdXNlZElucHV0ID0gX2EuZHJhZ0ZvY3VzZWRJbnB1dCwgcHJldmVudENsaWNrRXZlbnRPbkRyYWdTdGFydCA9IF9hLnByZXZlbnRDbGlja0V2ZW50T25EcmFnU3RhcnQsIHByZXZlbnRDbGlja0V2ZW50T25EcmFnID0gX2EucHJldmVudENsaWNrRXZlbnRPbkRyYWcsIHByZXZlbnRDbGlja0V2ZW50QnlDb25kaXRpb24gPSBfYS5wcmV2ZW50Q2xpY2tFdmVudEJ5Q29uZGl0aW9uO1xuICAgICAgICAgICAgdmFyIHVzZVRvdWNoID0gX3RoaXMuX3VzZVRvdWNoO1xuICAgICAgICAgICAgdmFyIGlzRHJhZ1N0YXJ0ID0gIV90aGlzLmZsYWc7XG4gICAgICAgICAgICBfdGhpcy5faXNTZWNvbmRhcnlCdXR0b24gPSBlLndoaWNoID09PSAzIHx8IGUuYnV0dG9uID09PSAyO1xuICAgICAgICAgICAgaWYgKChwcmV2ZW50V2hlZWxDbGljayAmJiAoZS53aGljaCA9PT0gMiB8fCBlLmJ1dHRvbiA9PT0gMSkpXG4gICAgICAgICAgICAgICAgfHwgKHByZXZlbnRSaWdodENsaWNrICYmIChlLndoaWNoID09PSAzIHx8IGUuYnV0dG9uID09PSAyKSkpIHtcbiAgICAgICAgICAgICAgICBfdGhpcy5zdG9wKCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGlzRHJhZ1N0YXJ0KSB7XG4gICAgICAgICAgICAgICAgdmFyIGFjdGl2ZUVsZW1lbnQgPSBfdGhpcy5fd2luZG93LmRvY3VtZW50LmFjdGl2ZUVsZW1lbnQ7XG4gICAgICAgICAgICAgICAgdmFyIHRhcmdldCA9IGUudGFyZ2V0O1xuICAgICAgICAgICAgICAgIGlmICh0YXJnZXQpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIHRhZ05hbWUgPSB0YXJnZXQudGFnTmFtZS50b0xvd2VyQ2FzZSgpO1xuICAgICAgICAgICAgICAgICAgICB2YXIgaGFzSW5wdXQgPSBJTlBVVF9UQUdOQU1FUy5pbmRleE9mKHRhZ05hbWUpID4gLTE7XG4gICAgICAgICAgICAgICAgICAgIHZhciBoYXNDb250ZW50RWRpdGFibGUgPSB0YXJnZXQuaXNDb250ZW50RWRpdGFibGU7XG4gICAgICAgICAgICAgICAgICAgIGlmIChoYXNJbnB1dCB8fCBoYXNDb250ZW50RWRpdGFibGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjaGVja0lucHV0IHx8ICghZHJhZ0ZvY3VzZWRJbnB1dCAmJiBhY3RpdmVFbGVtZW50ID09PSB0YXJnZXQpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gZm9yY2UgZmFsc2Ugb3IgYWxyZWFkeSBmb2N1c2VkLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIG5vIGZvY3VzXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoYWN0aXZlRWxlbWVudCAmJiAoYWN0aXZlRWxlbWVudCA9PT0gdGFyZ2V0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfHwgKGhhc0NvbnRlbnRFZGl0YWJsZSAmJiBhY3RpdmVFbGVtZW50LmlzQ29udGVudEVkaXRhYmxlICYmIGFjdGl2ZUVsZW1lbnQuY29udGFpbnModGFyZ2V0KSkpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGRyYWdGb2N1c2VkSW5wdXQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0LmJsdXIoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAoKHByZXZlbnREZWZhdWx0IHx8IGUudHlwZSA9PT0gXCJ0b3VjaHN0YXJ0XCIpICYmIGFjdGl2ZUVsZW1lbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhciBhY3RpdmVUYWdOYW1lID0gYWN0aXZlRWxlbWVudC50YWdOYW1lLnRvTG93ZXJDYXNlKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoYWN0aXZlRWxlbWVudC5pc0NvbnRlbnRFZGl0YWJsZSB8fCBJTlBVVF9UQUdOQU1FUy5pbmRleE9mKGFjdGl2ZVRhZ05hbWUpID4gLTEpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVFbGVtZW50LmJsdXIoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAocHJldmVudENsaWNrRXZlbnRPbkRyYWdTdGFydCB8fCBwcmV2ZW50Q2xpY2tFdmVudE9uRHJhZyB8fCBwcmV2ZW50Q2xpY2tFdmVudEJ5Q29uZGl0aW9uKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhZGRFdmVudChfdGhpcy5fd2luZG93LCBcImNsaWNrXCIsIF90aGlzLl9vbkNsaWNrLCB0cnVlKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBfdGhpcy5jbGllbnRTdG9yZXMgPSBbbmV3IENsaWVudFN0b3JlKGdldEV2ZW50Q2xpZW50cyhlKSldO1xuICAgICAgICAgICAgICAgIF90aGlzLl9pc0lkbGUgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICBfdGhpcy5mbGFnID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICBfdGhpcy5pc0RyYWcgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICBfdGhpcy5faXNUcnVzdGVkID0gaXNUcnVzdGVkO1xuICAgICAgICAgICAgICAgIF90aGlzLl9kcmFnRmxhZyA9IHRydWU7XG4gICAgICAgICAgICAgICAgX3RoaXMuX3ByZXZJbnB1dEV2ZW50ID0gZTtcbiAgICAgICAgICAgICAgICBfdGhpcy5kYXRhID0ge307XG4gICAgICAgICAgICAgICAgX3RoaXMuZG91YmxlRmxhZyA9IG5vdygpIC0gX3RoaXMucHJldlRpbWUgPCAyMDA7XG4gICAgICAgICAgICAgICAgX3RoaXMuX2lzTW91c2VFdmVudCA9IGlzTW91c2VFdmVudChlKTtcbiAgICAgICAgICAgICAgICBpZiAoIV90aGlzLl9pc01vdXNlRXZlbnQgJiYgX3RoaXMuX3ByZXZlbnRNb3VzZUV2ZW50KSB7XG4gICAgICAgICAgICAgICAgICAgIF90aGlzLl9hbGxvd01vdXNlRXZlbnQoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdmFyIHJlc3VsdCA9IF90aGlzLl9wcmV2ZW50TW91c2VFdmVudCB8fCBfdGhpcy5lbWl0KFwiZHJhZ1N0YXJ0XCIsIF9fYXNzaWduKF9fYXNzaWduKHsgZGF0YTogX3RoaXMuZGF0YSwgZGF0YXM6IF90aGlzLmRhdGEsIGlucHV0RXZlbnQ6IGUsIGlzTW91c2VFdmVudDogX3RoaXMuX2lzTW91c2VFdmVudCwgaXNTZWNvbmRhcnlCdXR0b246IF90aGlzLl9pc1NlY29uZGFyeUJ1dHRvbiwgaXNUcnVzdGVkOiBpc1RydXN0ZWQsIGlzRG91YmxlOiBfdGhpcy5kb3VibGVGbGFnIH0sIF90aGlzLmdldEN1cnJlbnRTdG9yZSgpLmdldFBvc2l0aW9uKCkpLCB7IHByZXZlbnREZWZhdWx0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgICAgIH0sIHByZXZlbnREcmFnOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBfdGhpcy5fZHJhZ0ZsYWcgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgfSB9KSk7XG4gICAgICAgICAgICAgICAgaWYgKHJlc3VsdCA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgICAgICAgICAgX3RoaXMuc3RvcCgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoX3RoaXMuX2lzTW91c2VFdmVudCAmJiBfdGhpcy5mbGFnICYmIHByZXZlbnREZWZhdWx0KSB7XG4gICAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIV90aGlzLmZsYWcpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB2YXIgdGltZXIgPSAwO1xuICAgICAgICAgICAgaWYgKGlzRHJhZ1N0YXJ0KSB7XG4gICAgICAgICAgICAgICAgX3RoaXMuX2F0dGNoRHJhZ0V2ZW50KCk7XG4gICAgICAgICAgICAgICAgLy8gd2FpdCBwaW5jaFxuICAgICAgICAgICAgICAgIGlmICh1c2VUb3VjaCAmJiBwaW5jaE91dHNpZGUpIHtcbiAgICAgICAgICAgICAgICAgICAgdGltZXIgPSBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFkZEV2ZW50KGNvbnRhaW5lciwgXCJ0b3VjaHN0YXJ0XCIsIF90aGlzLm9uRHJhZ1N0YXJ0LCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFzc2l2ZTogZmFsc2VcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICh1c2VUb3VjaCAmJiBwaW5jaE91dHNpZGUpIHtcbiAgICAgICAgICAgICAgICAvLyBwaW5jaCBpcyBvY2N1cmVkXG4gICAgICAgICAgICAgICAgcmVtb3ZlRXZlbnQoY29udGFpbmVyLCBcInRvdWNoc3RhcnRcIiwgX3RoaXMub25EcmFnU3RhcnQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKF90aGlzLmZsYWcgJiYgaXNNdWx0aVRvdWNoKGUpKSB7XG4gICAgICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgICAgICAgICAgICAgICBpZiAoaXNEcmFnU3RhcnQgJiYgKGUudG91Y2hlcy5sZW5ndGggIT09IGUuY2hhbmdlZFRvdWNoZXMubGVuZ3RoKSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICghX3RoaXMucGluY2hGbGFnKSB7XG4gICAgICAgICAgICAgICAgICAgIF90aGlzLm9uUGluY2hTdGFydChlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIF90aGlzLm9uRHJhZyA9IGZ1bmN0aW9uIChlLCBpc1Njcm9sbCkge1xuICAgICAgICAgICAgaWYgKCFfdGhpcy5mbGFnKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdmFyIHByZXZlbnREZWZhdWx0ID0gX3RoaXMub3B0aW9ucy5wcmV2ZW50RGVmYXVsdDtcbiAgICAgICAgICAgIGlmICghX3RoaXMuX2lzTW91c2VFdmVudCAmJiBwcmV2ZW50RGVmYXVsdCkge1xuICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIF90aGlzLl9wcmV2SW5wdXRFdmVudCA9IGU7XG4gICAgICAgICAgICB2YXIgY2xpZW50cyA9IGdldEV2ZW50Q2xpZW50cyhlKTtcbiAgICAgICAgICAgIHZhciByZXN1bHQgPSBfdGhpcy5tb3ZlQ2xpZW50cyhjbGllbnRzLCBlLCBmYWxzZSk7XG4gICAgICAgICAgICBpZiAoX3RoaXMuX2RyYWdGbGFnKSB7XG4gICAgICAgICAgICAgICAgaWYgKF90aGlzLnBpbmNoRmxhZyB8fCByZXN1bHQuZGVsdGFYIHx8IHJlc3VsdC5kZWx0YVkpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIGRyYWdSZXN1bHQgPSBfdGhpcy5fcHJldmVudE1vdXNlRXZlbnQgfHwgX3RoaXMuZW1pdChcImRyYWdcIiwgX19hc3NpZ24oX19hc3NpZ24oe30sIHJlc3VsdCksIHsgaXNTY3JvbGw6ICEhaXNTY3JvbGwsIGlucHV0RXZlbnQ6IGUgfSkpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoZHJhZ1Jlc3VsdCA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzLnN0b3AoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoX3RoaXMucGluY2hGbGFnKSB7XG4gICAgICAgICAgICAgICAgICAgIF90aGlzLm9uUGluY2goZSwgY2xpZW50cyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgX3RoaXMuZ2V0Q3VycmVudFN0b3JlKCkuZ2V0UG9zaXRpb24oY2xpZW50cywgdHJ1ZSk7XG4gICAgICAgIH07XG4gICAgICAgIF90aGlzLm9uRHJhZ0VuZCA9IGZ1bmN0aW9uIChlKSB7XG4gICAgICAgICAgICBpZiAoIV90aGlzLmZsYWcpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB2YXIgX2EgPSBfdGhpcy5vcHRpb25zLCBwaW5jaE91dHNpZGUgPSBfYS5waW5jaE91dHNpZGUsIGNvbnRhaW5lciA9IF9hLmNvbnRhaW5lciwgcHJldmVudENsaWNrRXZlbnRPbkRyYWcgPSBfYS5wcmV2ZW50Q2xpY2tFdmVudE9uRHJhZywgcHJldmVudENsaWNrRXZlbnRPbkRyYWdTdGFydCA9IF9hLnByZXZlbnRDbGlja0V2ZW50T25EcmFnU3RhcnQsIHByZXZlbnRDbGlja0V2ZW50QnlDb25kaXRpb24gPSBfYS5wcmV2ZW50Q2xpY2tFdmVudEJ5Q29uZGl0aW9uO1xuICAgICAgICAgICAgdmFyIGlzRHJhZyA9IF90aGlzLmlzRHJhZztcbiAgICAgICAgICAgIGlmIChwcmV2ZW50Q2xpY2tFdmVudE9uRHJhZyB8fCBwcmV2ZW50Q2xpY2tFdmVudE9uRHJhZ1N0YXJ0IHx8IHByZXZlbnRDbGlja0V2ZW50QnlDb25kaXRpb24pIHtcbiAgICAgICAgICAgICAgICByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgICAgICAgICBfdGhpcy5fYWxsb3dDbGlja0V2ZW50KCk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIXByZXZlbnRDbGlja0V2ZW50QnlDb25kaXRpb24gJiYgIXByZXZlbnRDbGlja0V2ZW50T25EcmFnU3RhcnQgJiYgcHJldmVudENsaWNrRXZlbnRPbkRyYWcgJiYgIWlzRHJhZykge1xuICAgICAgICAgICAgICAgIF90aGlzLl9hbGxvd0NsaWNrRXZlbnQoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChfdGhpcy5fdXNlVG91Y2ggJiYgcGluY2hPdXRzaWRlKSB7XG4gICAgICAgICAgICAgICAgcmVtb3ZlRXZlbnQoY29udGFpbmVyLCBcInRvdWNoc3RhcnRcIiwgX3RoaXMub25EcmFnU3RhcnQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKF90aGlzLnBpbmNoRmxhZykge1xuICAgICAgICAgICAgICAgIF90aGlzLm9uUGluY2hFbmQoZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB2YXIgY2xpZW50cyA9IChlID09PSBudWxsIHx8IGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGUudG91Y2hlcykgPyBnZXRFdmVudENsaWVudHMoZSkgOiBbXTtcbiAgICAgICAgICAgIHZhciBjbGllbnRzTGVuZ3RoID0gY2xpZW50cy5sZW5ndGg7XG4gICAgICAgICAgICBpZiAoY2xpZW50c0xlbmd0aCA9PT0gMCB8fCAhX3RoaXMub3B0aW9ucy5rZWVwRHJhZ2dpbmcpIHtcbiAgICAgICAgICAgICAgICBfdGhpcy5mbGFnID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBfdGhpcy5fYWRkU3RvcmUobmV3IENsaWVudFN0b3JlKGNsaWVudHMpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHZhciBwb3NpdGlvbiA9IF90aGlzLl9nZXRQb3NpdGlvbigpO1xuICAgICAgICAgICAgdmFyIGN1cnJlbnRUaW1lID0gbm93KCk7XG4gICAgICAgICAgICB2YXIgaXNEb3VibGUgPSAhaXNEcmFnICYmIF90aGlzLmRvdWJsZUZsYWc7XG4gICAgICAgICAgICBfdGhpcy5fcHJldklucHV0RXZlbnQgPSBudWxsO1xuICAgICAgICAgICAgX3RoaXMucHJldlRpbWUgPSBpc0RyYWcgfHwgaXNEb3VibGUgPyAwIDogY3VycmVudFRpbWU7XG4gICAgICAgICAgICBpZiAoIV90aGlzLmZsYWcpIHtcbiAgICAgICAgICAgICAgICBfdGhpcy5fZGV0dGFjaERyYWdFdmVudCgpO1xuICAgICAgICAgICAgICAgIF90aGlzLl9wcmV2ZW50TW91c2VFdmVudCB8fCBfdGhpcy5lbWl0KFwiZHJhZ0VuZFwiLCBfX2Fzc2lnbih7IGRhdGE6IF90aGlzLmRhdGEsIGRhdGFzOiBfdGhpcy5kYXRhLCBpc0RvdWJsZTogaXNEb3VibGUsIGlzRHJhZzogaXNEcmFnLCBpc0NsaWNrOiAhaXNEcmFnLCBpc01vdXNlRXZlbnQ6IF90aGlzLl9pc01vdXNlRXZlbnQsIGlzU2Vjb25kYXJ5QnV0dG9uOiBfdGhpcy5faXNTZWNvbmRhcnlCdXR0b24sIGlucHV0RXZlbnQ6IGUsIGlzVHJ1c3RlZDogX3RoaXMuX2lzVHJ1c3RlZCB9LCBwb3NpdGlvbikpO1xuICAgICAgICAgICAgICAgIF90aGlzLmNsaWVudFN0b3JlcyA9IFtdO1xuICAgICAgICAgICAgICAgIGlmICghX3RoaXMuX2lzTW91c2VFdmVudCkge1xuICAgICAgICAgICAgICAgICAgICBfdGhpcy5fcHJldmVudE1vdXNlRXZlbnQgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICAvLyBQcmV2ZW50IHRoZSBwcm9ibGVtIG9mIHRvdWNoIGV2ZW50IGFuZCBtb3VzZSBldmVudCBvY2N1cnJpbmcgc2ltdWx0YW5lb3VzbHlcbiAgICAgICAgICAgICAgICAgICAgY2xlYXJUaW1lb3V0KF90aGlzLl9wcmV2ZW50TW91c2VFdmVudElkKTtcbiAgICAgICAgICAgICAgICAgICAgX3RoaXMuX3ByZXZlbnRNb3VzZUV2ZW50SWQgPSBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzLl9wcmV2ZW50TW91c2VFdmVudCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB9LCAyMDApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBfdGhpcy5faXNJZGxlID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgX3RoaXMub25CbHVyID0gZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgX3RoaXMub25EcmFnRW5kKCk7XG4gICAgICAgIH07XG4gICAgICAgIF90aGlzLl9hbGxvd0NsaWNrRXZlbnQgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICByZW1vdmVFdmVudChfdGhpcy5fd2luZG93LCBcImNsaWNrXCIsIF90aGlzLl9vbkNsaWNrLCB0cnVlKTtcbiAgICAgICAgfTtcbiAgICAgICAgX3RoaXMuX29uQ2xpY2sgPSBmdW5jdGlvbiAoZSkge1xuICAgICAgICAgICAgX3RoaXMuX2FsbG93Q2xpY2tFdmVudCgpO1xuICAgICAgICAgICAgX3RoaXMuX2FsbG93TW91c2VFdmVudCgpO1xuICAgICAgICAgICAgdmFyIHByZXZlbnRDbGlja0V2ZW50QnlDb25kaXRpb24gPSBfdGhpcy5vcHRpb25zLnByZXZlbnRDbGlja0V2ZW50QnlDb25kaXRpb247XG4gICAgICAgICAgICBpZiAocHJldmVudENsaWNrRXZlbnRCeUNvbmRpdGlvbiA9PT0gbnVsbCB8fCBwcmV2ZW50Q2xpY2tFdmVudEJ5Q29uZGl0aW9uID09PSB2b2lkIDAgPyB2b2lkIDAgOiBwcmV2ZW50Q2xpY2tFdmVudEJ5Q29uZGl0aW9uKGUpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgfTtcbiAgICAgICAgX3RoaXMuX29uQ29udGV4dE1lbnUgPSBmdW5jdGlvbiAoZSkge1xuICAgICAgICAgICAgdmFyIG9wdGlvbnMgPSBfdGhpcy5vcHRpb25zO1xuICAgICAgICAgICAgaWYgKCFvcHRpb25zLnByZXZlbnRSaWdodENsaWNrKSB7XG4gICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgX3RoaXMub25EcmFnRW5kKGUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICBfdGhpcy5fcGFzc0NhbGxiYWNrID0gZnVuY3Rpb24gKCkgeyB9O1xuICAgICAgICB2YXIgZWxlbWVudHMgPSBbXS5jb25jYXQodGFyZ2V0cyk7XG4gICAgICAgIHZhciBmaXJzdFRhcmdldCA9IGVsZW1lbnRzWzBdO1xuICAgICAgICBfdGhpcy5fd2luZG93ID0gaXNXaW5kb3coZmlyc3RUYXJnZXQpID8gZmlyc3RUYXJnZXQgOiBnZXRXaW5kb3coZmlyc3RUYXJnZXQpO1xuICAgICAgICBfdGhpcy5vcHRpb25zID0gX19hc3NpZ24oeyBjaGVja0lucHV0OiBmYWxzZSwgY29udGFpbmVyOiBmaXJzdFRhcmdldCAmJiAhKFwiZG9jdW1lbnRcIiBpbiBmaXJzdFRhcmdldCkgPyBnZXRXaW5kb3coZmlyc3RUYXJnZXQpIDogZmlyc3RUYXJnZXQsIHByZXZlbnRSaWdodENsaWNrOiB0cnVlLCBwcmV2ZW50V2hlZWxDbGljazogdHJ1ZSwgcHJldmVudENsaWNrRXZlbnRPbkRyYWdTdGFydDogZmFsc2UsIHByZXZlbnRDbGlja0V2ZW50T25EcmFnOiBmYWxzZSwgcHJldmVudENsaWNrRXZlbnRCeUNvbmRpdGlvbjogbnVsbCwgcHJldmVudERlZmF1bHQ6IHRydWUsIGNoZWNrV2luZG93Qmx1cjogZmFsc2UsIGtlZXBEcmFnZ2luZzogZmFsc2UsIHBpbmNoVGhyZXNob2xkOiAwLCBldmVudHM6IFtcInRvdWNoXCIsIFwibW91c2VcIl0gfSwgb3B0aW9ucyk7XG4gICAgICAgIHZhciBfYSA9IF90aGlzLm9wdGlvbnMsIGNvbnRhaW5lciA9IF9hLmNvbnRhaW5lciwgZXZlbnRzID0gX2EuZXZlbnRzLCBjaGVja1dpbmRvd0JsdXIgPSBfYS5jaGVja1dpbmRvd0JsdXI7XG4gICAgICAgIF90aGlzLl91c2VEcmFnID0gZXZlbnRzLmluZGV4T2YoXCJkcmFnXCIpID4gLTE7XG4gICAgICAgIF90aGlzLl91c2VUb3VjaCA9IGV2ZW50cy5pbmRleE9mKFwidG91Y2hcIikgPiAtMTtcbiAgICAgICAgX3RoaXMuX3VzZU1vdXNlID0gZXZlbnRzLmluZGV4T2YoXCJtb3VzZVwiKSA+IC0xO1xuICAgICAgICBfdGhpcy50YXJnZXRzID0gZWxlbWVudHM7XG4gICAgICAgIGlmIChfdGhpcy5fdXNlRHJhZykge1xuICAgICAgICAgICAgZWxlbWVudHMuZm9yRWFjaChmdW5jdGlvbiAoZWwpIHtcbiAgICAgICAgICAgICAgICBhZGRFdmVudChlbCwgXCJkcmFnc3RhcnRcIiwgX3RoaXMub25EcmFnU3RhcnQpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKF90aGlzLl91c2VNb3VzZSkge1xuICAgICAgICAgICAgZWxlbWVudHMuZm9yRWFjaChmdW5jdGlvbiAoZWwpIHtcbiAgICAgICAgICAgICAgICBhZGRFdmVudChlbCwgXCJtb3VzZWRvd25cIiwgX3RoaXMub25EcmFnU3RhcnQpO1xuICAgICAgICAgICAgICAgIGFkZEV2ZW50KGVsLCBcIm1vdXNlbW92ZVwiLCBfdGhpcy5fcGFzc0NhbGxiYWNrKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgYWRkRXZlbnQoY29udGFpbmVyLCBcImNvbnRleHRtZW51XCIsIF90aGlzLl9vbkNvbnRleHRNZW51KTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoY2hlY2tXaW5kb3dCbHVyKSB7XG4gICAgICAgICAgICBhZGRFdmVudChnZXRXaW5kb3coKSwgXCJibHVyXCIsIF90aGlzLm9uQmx1cik7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKF90aGlzLl91c2VUb3VjaCkge1xuICAgICAgICAgICAgdmFyIHBhc3NpdmVfMSA9IHtcbiAgICAgICAgICAgICAgICBwYXNzaXZlOiBmYWxzZSxcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBlbGVtZW50cy5mb3JFYWNoKGZ1bmN0aW9uIChlbCkge1xuICAgICAgICAgICAgICAgIGFkZEV2ZW50KGVsLCBcInRvdWNoc3RhcnRcIiwgX3RoaXMub25EcmFnU3RhcnQsIHBhc3NpdmVfMSk7XG4gICAgICAgICAgICAgICAgYWRkRXZlbnQoZWwsIFwidG91Y2htb3ZlXCIsIF90aGlzLl9wYXNzQ2FsbGJhY2ssIHBhc3NpdmVfMSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gX3RoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFN0b3AgR2VzdG8ncyBkcmFnIGV2ZW50cy5cbiAgICAgKi9cbiAgICBHZXN0by5wcm90b3R5cGUuc3RvcCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdGhpcy5pc0RyYWcgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5kYXRhID0ge307XG4gICAgICAgIHRoaXMuY2xpZW50U3RvcmVzID0gW107XG4gICAgICAgIHRoaXMucGluY2hGbGFnID0gZmFsc2U7XG4gICAgICAgIHRoaXMuZG91YmxlRmxhZyA9IGZhbHNlO1xuICAgICAgICB0aGlzLnByZXZUaW1lID0gMDtcbiAgICAgICAgdGhpcy5mbGFnID0gZmFsc2U7XG4gICAgICAgIHRoaXMuX2lzSWRsZSA9IHRydWU7XG4gICAgICAgIHRoaXMuX2FsbG93Q2xpY2tFdmVudCgpO1xuICAgICAgICB0aGlzLl9kZXR0YWNoRHJhZ0V2ZW50KCk7XG4gICAgICAgIHRoaXMuX2lzRHJhZ0FQSSA9IGZhbHNlO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogVGhlIHRvdGFsIG1vdmVkIGRpc3RhbmNlXG4gICAgICovXG4gICAgR2VzdG8ucHJvdG90eXBlLmdldE1vdmVtZW50ID0gZnVuY3Rpb24gKGNsaWVudHMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0Q3VycmVudFN0b3JlKCkuZ2V0TW92ZW1lbnQoY2xpZW50cykgKyB0aGlzLmNsaWVudFN0b3Jlcy5zbGljZSgxKS5yZWR1Y2UoZnVuY3Rpb24gKHByZXYsIGN1cikge1xuICAgICAgICAgICAgcmV0dXJuIHByZXYgKyBjdXIubW92ZW1lbnQ7XG4gICAgICAgIH0sIDApO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogV2hldGhlciB0byBkcmFnXG4gICAgICovXG4gICAgR2VzdG8ucHJvdG90eXBlLmlzRHJhZ2dpbmcgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmlzRHJhZztcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFdoZXRoZXIgdGhlIG9wZXJhdGlvbiBvZiBnZXN0byBpcyBmaW5pc2hlZCBhbmQgaXMgaW4gaWRsZSBzdGF0ZVxuICAgICAqL1xuICAgIEdlc3RvLnByb3RvdHlwZS5pc0lkbGUgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9pc0lkbGU7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBXaGV0aGVyIHRvIHN0YXJ0IGRyYWdcbiAgICAgKi9cbiAgICBHZXN0by5wcm90b3R5cGUuaXNGbGFnID0gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5mbGFnO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogV2hldGhlciB0byBzdGFydCBwaW5jaFxuICAgICAqL1xuICAgIEdlc3RvLnByb3RvdHlwZS5pc1BpbmNoRmxhZyA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucGluY2hGbGFnO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogV2hldGhlciB0byBzdGFydCBkb3VibGUgY2xpY2tcbiAgICAgKi9cbiAgICBHZXN0by5wcm90b3R5cGUuaXNEb3VibGVGbGFnID0gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5kb3VibGVGbGFnO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogV2hldGhlciB0byBwaW5jaFxuICAgICAqL1xuICAgIEdlc3RvLnByb3RvdHlwZS5pc1BpbmNoaW5nID0gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5pc1BpbmNoO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogSWYgYSBzY3JvbGwgZXZlbnQgb2NjdXJzLCBpdCBpcyBjb3JyZWN0ZWQgYnkgdGhlIHNjcm9sbCBkaXN0YW5jZS5cbiAgICAgKi9cbiAgICBHZXN0by5wcm90b3R5cGUuc2Nyb2xsQnkgPSBmdW5jdGlvbiAoZGVsdGFYLCBkZWx0YVksIGUsIGlzQ2FsbERyYWcpIHtcbiAgICAgICAgaWYgKGlzQ2FsbERyYWcgPT09IHZvaWQgMCkgeyBpc0NhbGxEcmFnID0gdHJ1ZTsgfVxuICAgICAgICBpZiAoIXRoaXMuZmxhZykge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuY2xpZW50U3RvcmVzWzBdLm1vdmUoZGVsdGFYLCBkZWx0YVkpO1xuICAgICAgICBpc0NhbGxEcmFnICYmIHRoaXMub25EcmFnKGUsIHRydWUpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogQ3JlYXRlIGEgdmlydHVhbCBkcmFnIGV2ZW50LlxuICAgICAqL1xuICAgIEdlc3RvLnByb3RvdHlwZS5tb3ZlID0gZnVuY3Rpb24gKF9hLCBpbnB1dEV2ZW50KSB7XG4gICAgICAgIHZhciBkZWx0YVggPSBfYVswXSwgZGVsdGFZID0gX2FbMV07XG4gICAgICAgIHZhciBzdG9yZSA9IHRoaXMuZ2V0Q3VycmVudFN0b3JlKCk7XG4gICAgICAgIHZhciBuZXh0Q2xpZW50cyA9IHN0b3JlLnByZXZDbGllbnRzO1xuICAgICAgICByZXR1cm4gdGhpcy5tb3ZlQ2xpZW50cyhuZXh0Q2xpZW50cy5tYXAoZnVuY3Rpb24gKF9hKSB7XG4gICAgICAgICAgICB2YXIgY2xpZW50WCA9IF9hLmNsaWVudFgsIGNsaWVudFkgPSBfYS5jbGllbnRZO1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBjbGllbnRYOiBjbGllbnRYICsgZGVsdGFYLFxuICAgICAgICAgICAgICAgIGNsaWVudFk6IGNsaWVudFkgKyBkZWx0YVksXG4gICAgICAgICAgICAgICAgb3JpZ2luYWxDbGllbnRYOiBjbGllbnRYLFxuICAgICAgICAgICAgICAgIG9yaWdpbmFsQ2xpZW50WTogY2xpZW50WSxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH0pLCBpbnB1dEV2ZW50LCB0cnVlKTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFRoZSBkcmFnU3RhcnQgZXZlbnQgaXMgdHJpZ2dlcmVkIGJ5IGFuIGV4dGVybmFsIGV2ZW50LlxuICAgICAqL1xuICAgIEdlc3RvLnByb3RvdHlwZS50cmlnZ2VyRHJhZ1N0YXJ0ID0gZnVuY3Rpb24gKGUpIHtcbiAgICAgICAgdGhpcy5vbkRyYWdTdGFydChlLCBmYWxzZSk7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBTZXQgdGhlIGV2ZW50IGRhdGEgd2hpbGUgZHJhZ2dpbmcuXG4gICAgICovXG4gICAgR2VzdG8ucHJvdG90eXBlLnNldEV2ZW50RGF0YSA9IGZ1bmN0aW9uIChkYXRhKSB7XG4gICAgICAgIHZhciBjdXJyZW50RGF0YSA9IHRoaXMuZGF0YTtcbiAgICAgICAgZm9yICh2YXIgbmFtZV8xIGluIGRhdGEpIHtcbiAgICAgICAgICAgIGN1cnJlbnREYXRhW25hbWVfMV0gPSBkYXRhW25hbWVfMV07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBTZXQgdGhlIGV2ZW50IGRhdGEgd2hpbGUgZHJhZ2dpbmcuXG4gICAgICogVXNlIGBzZXRFdmVudERhdGFgXG4gICAgICogQGRlcHJlY2F0ZWRcbiAgICAgKi9cbiAgICBHZXN0by5wcm90b3R5cGUuc2V0RXZlbnREYXRhcyA9IGZ1bmN0aW9uIChkYXRhKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnNldEV2ZW50RGF0YShkYXRhKTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIEdldCB0aGUgY3VycmVudCBldmVudCBzdGF0ZSB3aGlsZSBkcmFnZ2luZy5cbiAgICAgKi9cbiAgICBHZXN0by5wcm90b3R5cGUuZ2V0Q3VycmVudEV2ZW50ID0gZnVuY3Rpb24gKGlucHV0RXZlbnQpIHtcbiAgICAgICAgaWYgKGlucHV0RXZlbnQgPT09IHZvaWQgMCkgeyBpbnB1dEV2ZW50ID0gdGhpcy5fcHJldklucHV0RXZlbnQ7IH1cbiAgICAgICAgcmV0dXJuIF9fYXNzaWduKF9fYXNzaWduKHsgZGF0YTogdGhpcy5kYXRhLCBkYXRhczogdGhpcy5kYXRhIH0sIHRoaXMuX2dldFBvc2l0aW9uKCkpLCB7IG1vdmVtZW50OiB0aGlzLmdldE1vdmVtZW50KCksIGlzRHJhZzogdGhpcy5pc0RyYWcsIGlzUGluY2g6IHRoaXMuaXNQaW5jaCwgaXNTY3JvbGw6IGZhbHNlLCBpbnB1dEV2ZW50OiBpbnB1dEV2ZW50IH0pO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogR2V0ICYgU2V0IHRoZSBldmVudCBkYXRhIHdoaWxlIGRyYWdnaW5nLlxuICAgICAqL1xuICAgIEdlc3RvLnByb3RvdHlwZS5nZXRFdmVudERhdGEgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmRhdGE7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBHZXQgJiBTZXQgdGhlIGV2ZW50IGRhdGEgd2hpbGUgZHJhZ2dpbmcuXG4gICAgICogVXNlIGdldEV2ZW50RGF0YSBtZXRob2RcbiAgICAgKiBAZGVwcmVhY2F0ZWRcbiAgICAgKi9cbiAgICBHZXN0by5wcm90b3R5cGUuZ2V0RXZlbnREYXRhcyA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZGF0YTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFVuc2V0IEdlc3RvXG4gICAgICovXG4gICAgR2VzdG8ucHJvdG90eXBlLnVuc2V0ID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgX3RoaXMgPSB0aGlzO1xuICAgICAgICB2YXIgdGFyZ2V0cyA9IHRoaXMudGFyZ2V0cztcbiAgICAgICAgdmFyIGNvbnRhaW5lciA9IHRoaXMub3B0aW9ucy5jb250YWluZXI7XG4gICAgICAgIHRoaXMub2ZmKCk7XG4gICAgICAgIHJlbW92ZUV2ZW50KHRoaXMuX3dpbmRvdywgXCJibHVyXCIsIHRoaXMub25CbHVyKTtcbiAgICAgICAgaWYgKHRoaXMuX3VzZURyYWcpIHtcbiAgICAgICAgICAgIHRhcmdldHMuZm9yRWFjaChmdW5jdGlvbiAoZWwpIHtcbiAgICAgICAgICAgICAgICByZW1vdmVFdmVudChlbCwgXCJkcmFnc3RhcnRcIiwgX3RoaXMub25EcmFnU3RhcnQpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuX3VzZU1vdXNlKSB7XG4gICAgICAgICAgICB0YXJnZXRzLmZvckVhY2goZnVuY3Rpb24gKHRhcmdldCkge1xuICAgICAgICAgICAgICAgIHJlbW92ZUV2ZW50KHRhcmdldCwgXCJtb3VzZWRvd25cIiwgX3RoaXMub25EcmFnU3RhcnQpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZW1vdmVFdmVudChjb250YWluZXIsIFwiY29udGV4dG1lbnVcIiwgdGhpcy5fb25Db250ZXh0TWVudSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuX3VzZVRvdWNoKSB7XG4gICAgICAgICAgICB0YXJnZXRzLmZvckVhY2goZnVuY3Rpb24gKHRhcmdldCkge1xuICAgICAgICAgICAgICAgIHJlbW92ZUV2ZW50KHRhcmdldCwgXCJ0b3VjaHN0YXJ0XCIsIF90aGlzLm9uRHJhZ1N0YXJ0KTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmVtb3ZlRXZlbnQoY29udGFpbmVyLCBcInRvdWNoc3RhcnRcIiwgdGhpcy5vbkRyYWdTdGFydCk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5fcHJldklucHV0RXZlbnQgPSBudWxsO1xuICAgICAgICB0aGlzLl9hbGxvd0NsaWNrRXZlbnQoKTtcbiAgICAgICAgdGhpcy5fZGV0dGFjaERyYWdFdmVudCgpO1xuICAgIH07XG4gICAgR2VzdG8ucHJvdG90eXBlLm9uUGluY2hTdGFydCA9IGZ1bmN0aW9uIChlKSB7XG4gICAgICAgIHZhciBfdGhpcyA9IHRoaXM7XG4gICAgICAgIHZhciBwaW5jaFRocmVzaG9sZCA9IHRoaXMub3B0aW9ucy5waW5jaFRocmVzaG9sZDtcbiAgICAgICAgaWYgKHRoaXMuaXNEcmFnICYmIHRoaXMuZ2V0TW92ZW1lbnQoKSA+IHBpbmNoVGhyZXNob2xkKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdmFyIHN0b3JlID0gbmV3IENsaWVudFN0b3JlKGdldEV2ZW50Q2xpZW50cyhlKSk7XG4gICAgICAgIHRoaXMucGluY2hGbGFnID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5fYWRkU3RvcmUoc3RvcmUpO1xuICAgICAgICB2YXIgcmVzdWx0ID0gdGhpcy5lbWl0KFwicGluY2hTdGFydFwiLCBfX2Fzc2lnbihfX2Fzc2lnbih7IGRhdGE6IHRoaXMuZGF0YSwgZGF0YXM6IHRoaXMuZGF0YSwgYW5nbGU6IHN0b3JlLmdldEFuZ2xlKCksIHRvdWNoZXM6IHRoaXMuZ2V0Q3VycmVudFN0b3JlKCkuZ2V0UG9zaXRpb25zKCkgfSwgc3RvcmUuZ2V0UG9zaXRpb24oKSksIHsgaW5wdXRFdmVudDogZSwgaXNUcnVzdGVkOiB0aGlzLl9pc1RydXN0ZWQsIHByZXZlbnREZWZhdWx0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgfSwgcHJldmVudERyYWc6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgICAgICBfdGhpcy5fZHJhZ0ZsYWcgPSBmYWxzZTtcbiAgICAgICAgICAgIH0gfSkpO1xuICAgICAgICBpZiAocmVzdWx0ID09PSBmYWxzZSkge1xuICAgICAgICAgICAgdGhpcy5waW5jaEZsYWcgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgR2VzdG8ucHJvdG90eXBlLm9uUGluY2ggPSBmdW5jdGlvbiAoZSwgY2xpZW50cykge1xuICAgICAgICBpZiAoIXRoaXMuZmxhZyB8fCAhdGhpcy5waW5jaEZsYWcgfHwgY2xpZW50cy5sZW5ndGggPCAyKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdmFyIHN0b3JlID0gdGhpcy5nZXRDdXJyZW50U3RvcmUoKTtcbiAgICAgICAgdGhpcy5pc1BpbmNoID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5lbWl0KFwicGluY2hcIiwgX19hc3NpZ24oX19hc3NpZ24oeyBkYXRhOiB0aGlzLmRhdGEsIGRhdGFzOiB0aGlzLmRhdGEsIG1vdmVtZW50OiB0aGlzLmdldE1vdmVtZW50KGNsaWVudHMpLCBhbmdsZTogc3RvcmUuZ2V0QW5nbGUoY2xpZW50cyksIHJvdGF0aW9uOiBzdG9yZS5nZXRSb3RhdGlvbihjbGllbnRzKSwgdG91Y2hlczogc3RvcmUuZ2V0UG9zaXRpb25zKGNsaWVudHMpLCBzY2FsZTogc3RvcmUuZ2V0U2NhbGUoY2xpZW50cyksIGRpc3RhbmNlOiBzdG9yZS5nZXREaXN0YW5jZShjbGllbnRzKSB9LCBzdG9yZS5nZXRQb3NpdGlvbihjbGllbnRzKSksIHsgaW5wdXRFdmVudDogZSwgaXNUcnVzdGVkOiB0aGlzLl9pc1RydXN0ZWQgfSkpO1xuICAgIH07XG4gICAgR2VzdG8ucHJvdG90eXBlLm9uUGluY2hFbmQgPSBmdW5jdGlvbiAoZSkge1xuICAgICAgICBpZiAoIXRoaXMucGluY2hGbGFnKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdmFyIGlzUGluY2ggPSB0aGlzLmlzUGluY2g7XG4gICAgICAgIHRoaXMuaXNQaW5jaCA9IGZhbHNlO1xuICAgICAgICB0aGlzLnBpbmNoRmxhZyA9IGZhbHNlO1xuICAgICAgICB2YXIgc3RvcmUgPSB0aGlzLmdldEN1cnJlbnRTdG9yZSgpO1xuICAgICAgICB0aGlzLmVtaXQoXCJwaW5jaEVuZFwiLCBfX2Fzc2lnbihfX2Fzc2lnbih7IGRhdGE6IHRoaXMuZGF0YSwgZGF0YXM6IHRoaXMuZGF0YSwgaXNQaW5jaDogaXNQaW5jaCwgdG91Y2hlczogc3RvcmUuZ2V0UG9zaXRpb25zKCkgfSwgc3RvcmUuZ2V0UG9zaXRpb24oKSksIHsgaW5wdXRFdmVudDogZSB9KSk7XG4gICAgfTtcbiAgICBHZXN0by5wcm90b3R5cGUuZ2V0Q3VycmVudFN0b3JlID0gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnRTdG9yZXNbMF07XG4gICAgfTtcbiAgICBHZXN0by5wcm90b3R5cGUubW92ZUNsaWVudHMgPSBmdW5jdGlvbiAoY2xpZW50cywgaW5wdXRFdmVudCwgaXNBZGQpIHtcbiAgICAgICAgdmFyIHBvc2l0aW9uID0gdGhpcy5fZ2V0UG9zaXRpb24oY2xpZW50cywgaXNBZGQpO1xuICAgICAgICB2YXIgaXNQcmV2RHJhZyA9IHRoaXMuaXNEcmFnO1xuICAgICAgICBpZiAocG9zaXRpb24uZGVsdGFYIHx8IHBvc2l0aW9uLmRlbHRhWSkge1xuICAgICAgICAgICAgdGhpcy5pc0RyYWcgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIHZhciBpc0ZpcnN0RHJhZyA9IGZhbHNlO1xuICAgICAgICBpZiAoIWlzUHJldkRyYWcgJiYgdGhpcy5pc0RyYWcpIHtcbiAgICAgICAgICAgIGlzRmlyc3REcmFnID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gX19hc3NpZ24oX19hc3NpZ24oeyBkYXRhOiB0aGlzLmRhdGEsIGRhdGFzOiB0aGlzLmRhdGEgfSwgcG9zaXRpb24pLCB7IG1vdmVtZW50OiB0aGlzLmdldE1vdmVtZW50KGNsaWVudHMpLCBpc0RyYWc6IHRoaXMuaXNEcmFnLCBpc1BpbmNoOiB0aGlzLmlzUGluY2gsIGlzU2Nyb2xsOiBmYWxzZSwgaXNNb3VzZUV2ZW50OiB0aGlzLl9pc01vdXNlRXZlbnQsIGlzU2Vjb25kYXJ5QnV0dG9uOiB0aGlzLl9pc1NlY29uZGFyeUJ1dHRvbiwgaW5wdXRFdmVudDogaW5wdXRFdmVudCwgaXNUcnVzdGVkOiB0aGlzLl9pc1RydXN0ZWQsIGlzRmlyc3REcmFnOiBpc0ZpcnN0RHJhZyB9KTtcbiAgICB9O1xuICAgIEdlc3RvLnByb3RvdHlwZS5fYWRkU3RvcmUgPSBmdW5jdGlvbiAoc3RvcmUpIHtcbiAgICAgICAgdGhpcy5jbGllbnRTdG9yZXMuc3BsaWNlKDAsIDAsIHN0b3JlKTtcbiAgICB9O1xuICAgIEdlc3RvLnByb3RvdHlwZS5fZ2V0UG9zaXRpb24gPSBmdW5jdGlvbiAoY2xpZW50cywgaXNBZGQpIHtcbiAgICAgICAgdmFyIHN0b3JlID0gdGhpcy5nZXRDdXJyZW50U3RvcmUoKTtcbiAgICAgICAgdmFyIHBvc2l0aW9uID0gc3RvcmUuZ2V0UG9zaXRpb24oY2xpZW50cywgaXNBZGQpO1xuICAgICAgICB2YXIgX2EgPSB0aGlzLmNsaWVudFN0b3Jlcy5zbGljZSgxKS5yZWR1Y2UoZnVuY3Rpb24gKHByZXYsIGN1cikge1xuICAgICAgICAgICAgdmFyIHN0b3JlUG9zaXRpb24gPSBjdXIuZ2V0UG9zaXRpb24oKTtcbiAgICAgICAgICAgIHByZXYuZGlzdFggKz0gc3RvcmVQb3NpdGlvbi5kaXN0WDtcbiAgICAgICAgICAgIHByZXYuZGlzdFkgKz0gc3RvcmVQb3NpdGlvbi5kaXN0WTtcbiAgICAgICAgICAgIHJldHVybiBwcmV2O1xuICAgICAgICB9LCBwb3NpdGlvbiksIGRpc3RYID0gX2EuZGlzdFgsIGRpc3RZID0gX2EuZGlzdFk7XG4gICAgICAgIHJldHVybiBfX2Fzc2lnbihfX2Fzc2lnbih7fSwgcG9zaXRpb24pLCB7IGRpc3RYOiBkaXN0WCwgZGlzdFk6IGRpc3RZIH0pO1xuICAgIH07XG4gICAgR2VzdG8ucHJvdG90eXBlLl9hdHRjaERyYWdFdmVudCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdmFyIHdpbiA9IHRoaXMuX3dpbmRvdztcbiAgICAgICAgdmFyIGNvbnRhaW5lciA9IHRoaXMub3B0aW9ucy5jb250YWluZXI7XG4gICAgICAgIHZhciBwYXNzaXZlID0ge1xuICAgICAgICAgICAgcGFzc2l2ZTogZmFsc2VcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKHRoaXMuX2lzRHJhZ0FQSSkge1xuICAgICAgICAgICAgYWRkRXZlbnQoY29udGFpbmVyLCBcImRyYWdvdmVyXCIsIHRoaXMub25EcmFnLCBwYXNzaXZlKTtcbiAgICAgICAgICAgIGFkZEV2ZW50KHdpbiwgXCJkcmFnZW5kXCIsIHRoaXMub25EcmFnRW5kKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5fdXNlTW91c2UpIHtcbiAgICAgICAgICAgIGFkZEV2ZW50KGNvbnRhaW5lciwgXCJtb3VzZW1vdmVcIiwgdGhpcy5vbkRyYWcpO1xuICAgICAgICAgICAgYWRkRXZlbnQod2luLCBcIm1vdXNldXBcIiwgdGhpcy5vbkRyYWdFbmQpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLl91c2VUb3VjaCkge1xuICAgICAgICAgICAgYWRkRXZlbnQoY29udGFpbmVyLCBcInRvdWNobW92ZVwiLCB0aGlzLm9uRHJhZywgcGFzc2l2ZSk7XG4gICAgICAgICAgICBhZGRFdmVudCh3aW4sIFwidG91Y2hlbmRcIiwgdGhpcy5vbkRyYWdFbmQsIHBhc3NpdmUpO1xuICAgICAgICAgICAgYWRkRXZlbnQod2luLCBcInRvdWNoY2FuY2VsXCIsIHRoaXMub25EcmFnRW5kLCBwYXNzaXZlKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgR2VzdG8ucHJvdG90eXBlLl9kZXR0YWNoRHJhZ0V2ZW50ID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgd2luID0gdGhpcy5fd2luZG93O1xuICAgICAgICB2YXIgY29udGFpbmVyID0gdGhpcy5vcHRpb25zLmNvbnRhaW5lcjtcbiAgICAgICAgaWYgKHRoaXMuX2lzRHJhZ0FQSSkge1xuICAgICAgICAgICAgcmVtb3ZlRXZlbnQoY29udGFpbmVyLCBcImRyYWdvdmVyXCIsIHRoaXMub25EcmFnKTtcbiAgICAgICAgICAgIHJlbW92ZUV2ZW50KHdpbiwgXCJkcmFnZW5kXCIsIHRoaXMub25EcmFnRW5kKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5fdXNlTW91c2UpIHtcbiAgICAgICAgICAgIHJlbW92ZUV2ZW50KGNvbnRhaW5lciwgXCJtb3VzZW1vdmVcIiwgdGhpcy5vbkRyYWcpO1xuICAgICAgICAgICAgcmVtb3ZlRXZlbnQod2luLCBcIm1vdXNldXBcIiwgdGhpcy5vbkRyYWdFbmQpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLl91c2VUb3VjaCkge1xuICAgICAgICAgICAgcmVtb3ZlRXZlbnQoY29udGFpbmVyLCBcInRvdWNoc3RhcnRcIiwgdGhpcy5vbkRyYWdTdGFydCk7XG4gICAgICAgICAgICByZW1vdmVFdmVudChjb250YWluZXIsIFwidG91Y2htb3ZlXCIsIHRoaXMub25EcmFnKTtcbiAgICAgICAgICAgIHJlbW92ZUV2ZW50KHdpbiwgXCJ0b3VjaGVuZFwiLCB0aGlzLm9uRHJhZ0VuZCk7XG4gICAgICAgICAgICByZW1vdmVFdmVudCh3aW4sIFwidG91Y2hjYW5jZWxcIiwgdGhpcy5vbkRyYWdFbmQpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBHZXN0by5wcm90b3R5cGUuX2FsbG93TW91c2VFdmVudCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdGhpcy5fcHJldmVudE1vdXNlRXZlbnQgPSBmYWxzZTtcbiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuX3ByZXZlbnRNb3VzZUV2ZW50SWQpO1xuICAgIH07XG4gICAgcmV0dXJuIEdlc3RvO1xufShFdmVudEVtaXR0ZXIpKTtcblxuZXhwb3J0IHsgR2VzdG8gYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2VzdG8uZXNtLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/gesto@1.19.4/node_modules/gesto/dist/gesto.esm.js\n");

/***/ })

};
;