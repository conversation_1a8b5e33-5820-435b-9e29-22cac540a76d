"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-hard-break/dist/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-hard-break/dist/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HardBreak: () => (/* binding */ HardBreak),\n/* harmony export */   \"default\": () => (/* binding */ HardBreak)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * This extension allows you to insert hard breaks.\n * @see https://www.tiptap.dev/api/nodes/hard-break\n */\nconst HardBreak = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'hardBreak',\n    addOptions() {\n        return {\n            keepMarks: true,\n            HTMLAttributes: {},\n        };\n    },\n    inline: true,\n    group: 'inline',\n    selectable: false,\n    linebreakReplacement: true,\n    parseHTML() {\n        return [\n            { tag: 'br' },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['br', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes)];\n    },\n    renderText() {\n        return '\\n';\n    },\n    addCommands() {\n        return {\n            setHardBreak: () => ({ commands, chain, state, editor, }) => {\n                return commands.first([\n                    () => commands.exitCode(),\n                    () => commands.command(() => {\n                        const { selection, storedMarks } = state;\n                        if (selection.$from.parent.type.spec.isolating) {\n                            return false;\n                        }\n                        const { keepMarks } = this.options;\n                        const { splittableMarks } = editor.extensionManager;\n                        const marks = storedMarks\n                            || (selection.$to.parentOffset && selection.$from.marks());\n                        return chain()\n                            .insertContent({ type: this.name })\n                            .command(({ tr, dispatch }) => {\n                            if (dispatch && marks && keepMarks) {\n                                const filteredMarks = marks\n                                    .filter(mark => splittableMarks.includes(mark.type.name));\n                                tr.ensureMarks(filteredMarks);\n                            }\n                            return true;\n                        })\n                            .run();\n                    }),\n                ]);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-Enter': () => this.editor.commands.setHardBreak(),\n            'Shift-Enter': () => this.editor.commands.setHardBreak(),\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-hard-break/dist/index.js\n");

/***/ })

};
;