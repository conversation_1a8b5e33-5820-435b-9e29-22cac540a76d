"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-highlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-highlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-highlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-highlight/dist/index.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-highlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-highlight/dist/index.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Highlight: () => (/* binding */ Highlight),\n/* harmony export */   \"default\": () => (/* binding */ Highlight),\n/* harmony export */   inputRegex: () => (/* binding */ inputRegex),\n/* harmony export */   pasteRegex: () => (/* binding */ pasteRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * Matches a highlight to a ==highlight== on input.\n */\nconst inputRegex = /(?:^|\\s)(==(?!\\s+==)((?:[^=]+))==(?!\\s+==))$/;\n/**\n * Matches a highlight to a ==highlight== on paste.\n */\nconst pasteRegex = /(?:^|\\s)(==(?!\\s+==)((?:[^=]+))==(?!\\s+==))/g;\n/**\n * This extension allows you to highlight text.\n * @see https://www.tiptap.dev/api/marks/highlight\n */\nconst Highlight = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Mark.create({\n    name: 'highlight',\n    addOptions() {\n        return {\n            multicolor: false,\n            HTMLAttributes: {},\n        };\n    },\n    addAttributes() {\n        if (!this.options.multicolor) {\n            return {};\n        }\n        return {\n            color: {\n                default: null,\n                parseHTML: element => element.getAttribute('data-color') || element.style.backgroundColor,\n                renderHTML: attributes => {\n                    if (!attributes.color) {\n                        return {};\n                    }\n                    return {\n                        'data-color': attributes.color,\n                        style: `background-color: ${attributes.color}; color: inherit`,\n                    };\n                },\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'mark',\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['mark', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setHighlight: attributes => ({ commands }) => {\n                return commands.setMark(this.name, attributes);\n            },\n            toggleHighlight: attributes => ({ commands }) => {\n                return commands.toggleMark(this.name, attributes);\n            },\n            unsetHighlight: () => ({ commands }) => {\n                return commands.unsetMark(this.name);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-Shift-h': () => this.editor.commands.toggleHighlight(),\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markInputRule)({\n                find: inputRegex,\n                type: this.type,\n            }),\n        ];\n    },\n    addPasteRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markPasteRule)({\n                find: pasteRegex,\n                type: this.type,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-highlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-highlight/dist/index.js\n");

/***/ })

};
;