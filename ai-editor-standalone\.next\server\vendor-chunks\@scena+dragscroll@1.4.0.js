"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@scena+dragscroll@1.4.0";
exports.ids = ["vendor-chunks/@scena+dragscroll@1.4.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@scena+dragscroll@1.4.0/node_modules/@scena/dragscroll/dist/dragscroll.esm.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@scena+dragscroll@1.4.0/node_modules/@scena/dragscroll/dist/dragscroll.esm.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _scena_event_emitter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @scena/event-emitter */ \"(ssr)/./node_modules/.pnpm/@scena+event-emitter@1.0.5/node_modules/@scena/event-emitter/dist/event-emitter.esm.js\");\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2019 Daybrush\nname: @scena/dragscroll\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/dragscroll.git\nversion: 1.4.0\n*/\n\n\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise */\n\nvar extendStatics = function (d, b) {\n  extendStatics = Object.setPrototypeOf || {\n    __proto__: []\n  } instanceof Array && function (d, b) {\n    d.__proto__ = b;\n  } || function (d, b) {\n    for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n  };\n  return extendStatics(d, b);\n};\nfunction __extends(d, b) {\n  extendStatics(d, b);\n  function __() {\n    this.constructor = d;\n  }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\n\nfunction getDefaultScrollPosition(e) {\n  var container = e.container;\n  if (container === document.body) {\n    return [container.scrollLeft || document.documentElement.scrollLeft, container.scrollTop || document.documentElement.scrollTop];\n  }\n  return [container.scrollLeft, container.scrollTop];\n}\nfunction checkDefaultScrollEvent(container, callback) {\n  container.addEventListener(\"scroll\", callback);\n  return function () {\n    container.removeEventListener(\"scroll\", callback);\n  };\n}\nfunction getContainerElement(container) {\n  if (!container) {\n    return null;\n  } else if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isString)(container)) {\n    return document.querySelector(container);\n  }\n  if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isFunction)(container)) {\n    return container();\n  } else if (container instanceof Element) {\n    return container;\n  } else if (\"current\" in container) {\n    return container.current;\n  } else if (\"value\" in container) {\n    return container.value;\n  }\n}\n/**\n * @sort 1\n */\nvar DragScroll = /*#__PURE__*/function (_super) {\n  __extends(DragScroll, _super);\n  function DragScroll() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this._startRect = null;\n    _this._startPos = [];\n    _this._prevTime = 0;\n    _this._timer = 0;\n    _this._prevScrollPos = [0, 0];\n    _this._isWait = false;\n    _this._flag = false;\n    _this._currentOptions = null;\n    _this._lock = false;\n    _this._unregister = null;\n    _this._onScroll = function () {\n      var options = _this._currentOptions;\n      if (_this._lock || !options) {\n        return;\n      }\n      _this.emit(\"scrollDrag\", {\n        next: function (inputEvent) {\n          _this.checkScroll({\n            container: options.container,\n            inputEvent: inputEvent\n          });\n        }\n      });\n    };\n    return _this;\n  }\n  /**\n   */\n  var __proto = DragScroll.prototype;\n  __proto.dragStart = function (e, options) {\n    var container = getContainerElement(options.container);\n    if (!container) {\n      this._flag = false;\n      return;\n    }\n    var top = 0;\n    var left = 0;\n    var width = 0;\n    var height = 0;\n    if (container === document.body) {\n      width = window.innerWidth;\n      height = window.innerHeight;\n    } else {\n      var rect = container.getBoundingClientRect();\n      top = rect.top;\n      left = rect.left;\n      width = rect.width;\n      height = rect.height;\n    }\n    this._flag = true;\n    this._startPos = [e.clientX, e.clientY];\n    this._startRect = {\n      top: top,\n      left: left,\n      width: width,\n      height: height\n    };\n    this._prevScrollPos = this._getScrollPosition([0, 0], options);\n    this._currentOptions = options;\n    this._registerScrollEvent(options);\n  };\n  __proto.drag = function (e, options) {\n    clearTimeout(this._timer);\n    if (!this._flag) {\n      return;\n    }\n    var clientX = e.clientX,\n      clientY = e.clientY;\n    var _a = options.threshold,\n      threshold = _a === void 0 ? 0 : _a;\n    var _b = this,\n      _startRect = _b._startRect,\n      _startPos = _b._startPos;\n    this._currentOptions = options;\n    var direction = [0, 0];\n    if (_startRect.top > clientY - threshold) {\n      if (_startPos[1] > _startRect.top || clientY < _startPos[1]) {\n        direction[1] = -1;\n      }\n    } else if (_startRect.top + _startRect.height < clientY + threshold) {\n      if (_startPos[1] < _startRect.top + _startRect.height || clientY > _startPos[1]) {\n        direction[1] = 1;\n      }\n    }\n    if (_startRect.left > clientX - threshold) {\n      if (_startPos[0] > _startRect.left || clientX < _startPos[0]) {\n        direction[0] = -1;\n      }\n    } else if (_startRect.left + _startRect.width < clientX + threshold) {\n      if (_startPos[0] < _startRect.left + _startRect.width || clientX > _startPos[0]) {\n        direction[0] = 1;\n      }\n    }\n    if (!direction[0] && !direction[1]) {\n      return false;\n    }\n    return this._continueDrag(__assign(__assign({}, options), {\n      direction: direction,\n      inputEvent: e,\n      isDrag: true\n    }));\n  };\n  /**\n   */\n  __proto.checkScroll = function (options) {\n    var _this = this;\n    if (this._isWait) {\n      return false;\n    }\n    var _a = options.prevScrollPos,\n      prevScrollPos = _a === void 0 ? this._prevScrollPos : _a,\n      direction = options.direction,\n      _b = options.throttleTime,\n      throttleTime = _b === void 0 ? 0 : _b,\n      inputEvent = options.inputEvent,\n      isDrag = options.isDrag;\n    var nextScrollPos = this._getScrollPosition(direction || [0, 0], options);\n    var offsetX = nextScrollPos[0] - prevScrollPos[0];\n    var offsetY = nextScrollPos[1] - prevScrollPos[1];\n    var nextDirection = direction || [offsetX ? Math.abs(offsetX) / offsetX : 0, offsetY ? Math.abs(offsetY) / offsetY : 0];\n    this._prevScrollPos = nextScrollPos;\n    this._lock = false;\n    if (!offsetX && !offsetY) {\n      return false;\n    }\n    /**\n     * @event DragScroll#move\n     */\n    this.emit(\"move\", {\n      offsetX: nextDirection[0] ? offsetX : 0,\n      offsetY: nextDirection[1] ? offsetY : 0,\n      inputEvent: inputEvent\n    });\n    if (throttleTime && isDrag) {\n      clearTimeout(this._timer);\n      this._timer = window.setTimeout(function () {\n        _this._continueDrag(options);\n      }, throttleTime);\n    }\n    return true;\n  };\n  /**\n   *\n   */\n  __proto.dragEnd = function () {\n    this._flag = false;\n    this._lock = false;\n    clearTimeout(this._timer);\n    this._unregisterScrollEvent();\n  };\n  __proto._getScrollPosition = function (direction, options) {\n    var container = options.container,\n      _a = options.getScrollPosition,\n      getScrollPosition = _a === void 0 ? getDefaultScrollPosition : _a;\n    return getScrollPosition({\n      container: getContainerElement(container),\n      direction: direction\n    });\n  };\n  __proto._continueDrag = function (options) {\n    var _this = this;\n    var _a;\n    var container = options.container,\n      direction = options.direction,\n      throttleTime = options.throttleTime,\n      useScroll = options.useScroll,\n      isDrag = options.isDrag,\n      inputEvent = options.inputEvent;\n    if (!this._flag || isDrag && this._isWait) {\n      return;\n    }\n    var nowTime = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.now)();\n    var distTime = Math.max(throttleTime + this._prevTime - nowTime, 0);\n    if (distTime > 0) {\n      clearTimeout(this._timer);\n      this._timer = window.setTimeout(function () {\n        _this._continueDrag(options);\n      }, distTime);\n      return false;\n    }\n    this._prevTime = nowTime;\n    var prevScrollPos = this._getScrollPosition(direction, options);\n    this._prevScrollPos = prevScrollPos;\n    if (isDrag) {\n      this._isWait = true;\n    }\n    // unregister native scroll event\n    if (!useScroll) {\n      this._lock = true;\n    }\n    var param = {\n      container: getContainerElement(container),\n      direction: direction,\n      inputEvent: inputEvent\n    };\n    (_a = options.requestScroll) === null || _a === void 0 ? void 0 : _a.call(options, param);\n    /**\n     * @event DragScroll#scroll\n     */\n    this.emit(\"scroll\", param);\n    this._isWait = false;\n    return useScroll || this.checkScroll(__assign(__assign({}, options), {\n      prevScrollPos: prevScrollPos,\n      direction: direction,\n      inputEvent: inputEvent\n    }));\n  };\n  __proto._registerScrollEvent = function (options) {\n    this._unregisterScrollEvent();\n    var checkScrollEvent = options.checkScrollEvent;\n    if (!checkScrollEvent) {\n      return;\n    }\n    var callback = checkScrollEvent === true ? checkDefaultScrollEvent : checkScrollEvent;\n    var container = getContainerElement(options.container);\n    if (checkScrollEvent === true && (container === document.body || container === document.documentElement)) {\n      this._unregister = checkDefaultScrollEvent(window, this._onScroll);\n    } else {\n      this._unregister = callback(container, this._onScroll);\n    }\n  };\n  __proto._unregisterScrollEvent = function () {\n    var _a;\n    (_a = this._unregister) === null || _a === void 0 ? void 0 : _a.call(this);\n    this._unregister = null;\n  };\n  return DragScroll;\n}(_scena_event_emitter__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DragScroll);\n//# sourceMappingURL=dragscroll.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@scena+dragscroll@1.4.0/node_modules/@scena/dragscroll/dist/dragscroll.esm.js\n");

/***/ })

};
;