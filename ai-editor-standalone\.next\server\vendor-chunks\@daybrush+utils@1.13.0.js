"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@daybrush+utils@1.13.0";
exports.ids = ["vendor-chunks/@daybrush+utils@1.13.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $: () => (/* binding */ $),\n/* harmony export */   ANIMATION: () => (/* binding */ ANIMATION),\n/* harmony export */   ARRAY: () => (/* binding */ ARRAY),\n/* harmony export */   COLOR_MODELS: () => (/* binding */ COLOR_MODELS),\n/* harmony export */   DEFAULT_UNIT_PRESETS: () => (/* binding */ DEFAULT_UNIT_PRESETS),\n/* harmony export */   FILTER: () => (/* binding */ FILTER),\n/* harmony export */   FUNCTION: () => (/* binding */ FUNCTION),\n/* harmony export */   HSL: () => (/* binding */ HSL),\n/* harmony export */   HSLA: () => (/* binding */ HSLA),\n/* harmony export */   IS_WINDOW: () => (/* binding */ IS_WINDOW),\n/* harmony export */   KEYFRAMES: () => (/* binding */ KEYFRAMES),\n/* harmony export */   NUMBER: () => (/* binding */ NUMBER),\n/* harmony export */   OBJECT: () => (/* binding */ OBJECT),\n/* harmony export */   OPEN_CLOSED_CHARACTERS: () => (/* binding */ OPEN_CLOSED_CHARACTERS),\n/* harmony export */   PROPERTY: () => (/* binding */ PROPERTY),\n/* harmony export */   REVERSE_TINY_NUM: () => (/* binding */ REVERSE_TINY_NUM),\n/* harmony export */   RGB: () => (/* binding */ RGB),\n/* harmony export */   RGBA: () => (/* binding */ RGBA),\n/* harmony export */   STRING: () => (/* binding */ STRING),\n/* harmony export */   TINY_NUM: () => (/* binding */ TINY_NUM),\n/* harmony export */   TRANSFORM: () => (/* binding */ TRANSFORM),\n/* harmony export */   UNDEFINED: () => (/* binding */ UNDEFINED),\n/* harmony export */   addClass: () => (/* binding */ addClass),\n/* harmony export */   addEvent: () => (/* binding */ addEvent),\n/* harmony export */   average: () => (/* binding */ average),\n/* harmony export */   between: () => (/* binding */ between),\n/* harmony export */   calculateBoundSize: () => (/* binding */ calculateBoundSize),\n/* harmony export */   camelize: () => (/* binding */ camelize),\n/* harmony export */   cancelAnimationFrame: () => (/* binding */ cancelAnimationFrame),\n/* harmony export */   checkBoundSize: () => (/* binding */ checkBoundSize),\n/* harmony export */   convertUnitSize: () => (/* binding */ convertUnitSize),\n/* harmony export */   counter: () => (/* binding */ counter),\n/* harmony export */   cutHex: () => (/* binding */ cutHex),\n/* harmony export */   decamelize: () => (/* binding */ decamelize),\n/* harmony export */   deepFlat: () => (/* binding */ deepFlat),\n/* harmony export */   document: () => (/* binding */ doc),\n/* harmony export */   dot: () => (/* binding */ dot),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   findIndex: () => (/* binding */ findIndex),\n/* harmony export */   findLast: () => (/* binding */ findLast),\n/* harmony export */   findLastIndex: () => (/* binding */ findLastIndex),\n/* harmony export */   flat: () => (/* binding */ flat),\n/* harmony export */   fromCSS: () => (/* binding */ fromCSS),\n/* harmony export */   getCenterPoint: () => (/* binding */ getCenterPoint),\n/* harmony export */   getCrossBrowserProperty: () => (/* binding */ getCrossBrowserProperty),\n/* harmony export */   getDist: () => (/* binding */ getDist),\n/* harmony export */   getDocument: () => (/* binding */ getDocument),\n/* harmony export */   getDocumentBody: () => (/* binding */ getDocumentBody),\n/* harmony export */   getDocumentElement: () => (/* binding */ getDocumentElement),\n/* harmony export */   getEntries: () => (/* binding */ getEntries),\n/* harmony export */   getKeys: () => (/* binding */ getKeys),\n/* harmony export */   getRad: () => (/* binding */ getRad),\n/* harmony export */   getShapeDirection: () => (/* binding */ getShapeDirection),\n/* harmony export */   getValues: () => (/* binding */ getValues),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   hasClass: () => (/* binding */ hasClass),\n/* harmony export */   hexToRGBA: () => (/* binding */ hexToRGBA),\n/* harmony export */   hslToRGBA: () => (/* binding */ hslToRGBA),\n/* harmony export */   isArray: () => (/* binding */ isArray),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   isUndefined: () => (/* binding */ isUndefined),\n/* harmony export */   isWindow: () => (/* binding */ isWindow),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   pushSet: () => (/* binding */ pushSet),\n/* harmony export */   removeClass: () => (/* binding */ removeClass),\n/* harmony export */   removeEvent: () => (/* binding */ removeEvent),\n/* harmony export */   replaceOnce: () => (/* binding */ replaceOnce),\n/* harmony export */   requestAnimationFrame: () => (/* binding */ requestAnimationFrame),\n/* harmony export */   sortOrders: () => (/* binding */ sortOrders),\n/* harmony export */   splitBracket: () => (/* binding */ splitBracket),\n/* harmony export */   splitComma: () => (/* binding */ splitComma),\n/* harmony export */   splitSpace: () => (/* binding */ splitSpace),\n/* harmony export */   splitText: () => (/* binding */ splitText),\n/* harmony export */   splitUnit: () => (/* binding */ splitUnit),\n/* harmony export */   stringToRGBA: () => (/* binding */ stringToRGBA),\n/* harmony export */   sum: () => (/* binding */ sum),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   throttleArray: () => (/* binding */ throttleArray),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   toFullHex: () => (/* binding */ toFullHex)\n/* harmony export */ });\n/*\nCopyright (c) 2018 Daybrush\n@name: @daybrush/utils\nlicense: MIT\nauthor: Daybrush\nrepository: https://github.com/daybrush/utils\n@version 1.13.0\n*/\n/**\n* @namespace\n* @name Consts\n*/\n/**\n* get string \"rgb\"\n* @memberof Color\n* @example\nimport {RGB} from \"@daybrush/utils\";\n\nconsole.log(RGB); // \"rgb\"\n*/\nvar RGB = \"rgb\";\n/**\n* get string \"rgba\"\n* @memberof Color\n* @example\nimport {RGBA} from \"@daybrush/utils\";\n\nconsole.log(RGBA); // \"rgba\"\n*/\nvar RGBA = \"rgba\";\n/**\n* get string \"hsl\"\n* @memberof Color\n* @example\nimport {HSL} from \"@daybrush/utils\";\n\nconsole.log(HSL); // \"hsl\"\n*/\nvar HSL = \"hsl\";\n/**\n* get string \"hsla\"\n* @memberof Color\n* @example\nimport {HSLA} from \"@daybrush/utils\";\n\nconsole.log(HSLA); // \"hsla\"\n*/\nvar HSLA = \"hsla\";\n/**\n* gets an array of color models.\n* @memberof Color\n* @example\nimport {COLOR_MODELS} from \"@daybrush/utils\";\n\nconsole.log(COLOR_MODELS); // [\"rgb\", \"rgba\", \"hsl\", \"hsla\"];\n*/\nvar COLOR_MODELS = [RGB, RGBA, HSL, HSLA];\n/**\n* get string \"function\"\n* @memberof Consts\n* @example\nimport {FUNCTION} from \"@daybrush/utils\";\n\nconsole.log(FUNCTION); // \"function\"\n*/\nvar FUNCTION = \"function\";\n/**\n* get string \"property\"\n* @memberof Consts\n* @example\nimport {PROPERTY} from \"@daybrush/utils\";\n\nconsole.log(PROPERTY); // \"property\"\n*/\nvar PROPERTY = \"property\";\n/**\n* get string \"array\"\n* @memberof Consts\n* @example\nimport {ARRAY} from \"@daybrush/utils\";\n\nconsole.log(ARRAY); // \"array\"\n*/\nvar ARRAY = \"array\";\n/**\n* get string \"object\"\n* @memberof Consts\n* @example\nimport {OBJECT} from \"@daybrush/utils\";\n\nconsole.log(OBJECT); // \"object\"\n*/\nvar OBJECT = \"object\";\n/**\n* get string \"string\"\n* @memberof Consts\n* @example\nimport {STRING} from \"@daybrush/utils\";\n\nconsole.log(STRING); // \"string\"\n*/\nvar STRING = \"string\";\n/**\n* get string \"number\"\n* @memberof Consts\n* @example\nimport {NUMBER} from \"@daybrush/utils\";\n\nconsole.log(NUMBER); // \"number\"\n*/\nvar NUMBER = \"number\";\n/**\n* get string \"undefined\"\n* @memberof Consts\n* @example\nimport {UNDEFINED} from \"@daybrush/utils\";\n\nconsole.log(UNDEFINED); // \"undefined\"\n*/\nvar UNDEFINED = \"undefined\";\n/**\n* Check whether the environment is window or node.js.\n* @memberof Consts\n* @example\nimport {IS_WINDOW} from \"@daybrush/utils\";\n\nconsole.log(IS_WINDOW); // false in node.js\nconsole.log(IS_WINDOW); // true in browser\n*/\nvar IS_WINDOW = typeof window !== UNDEFINED;\n/**\n* Check whether the environment is window or node.js.\n* @memberof Consts\n* @name document\n* @example\nimport {IS_WINDOW} from \"@daybrush/utils\";\n\nconsole.log(IS_WINDOW); // false in node.js\nconsole.log(IS_WINDOW); // true in browser\n*/\nvar doc = typeof document !== UNDEFINED && document; // FIXME: this type maybe false\nvar prefixes = [\"webkit\", \"ms\", \"moz\", \"o\"];\n/**\n * @namespace CrossBrowser\n */\n/**\n* Get a CSS property with a vendor prefix that supports cross browser.\n* @function\n* @param {string} property - A CSS property\n* @return {string} CSS property with cross-browser vendor prefix\n* @memberof CrossBrowser\n* @example\nimport {getCrossBrowserProperty} from \"@daybrush/utils\";\n\nconsole.log(getCrossBrowserProperty(\"transform\")); // \"transform\", \"-ms-transform\", \"-webkit-transform\"\nconsole.log(getCrossBrowserProperty(\"filter\")); // \"filter\", \"-webkit-filter\"\n*/\nvar getCrossBrowserProperty = /*#__PURE__*/function (property) {\n  if (!doc) {\n    return \"\";\n  }\n  var styles = (doc.body || doc.documentElement).style;\n  var length = prefixes.length;\n  if (typeof styles[property] !== UNDEFINED) {\n    return property;\n  }\n  for (var i = 0; i < length; ++i) {\n    var name = \"-\" + prefixes[i] + \"-\" + property;\n    if (typeof styles[name] !== UNDEFINED) {\n      return name;\n    }\n  }\n  return \"\";\n};\n/**\n* get string \"transfrom\" with the vendor prefix.\n* @memberof CrossBrowser\n* @example\nimport {TRANSFORM} from \"@daybrush/utils\";\n\nconsole.log(TRANSFORM); // \"transform\", \"-ms-transform\", \"-webkit-transform\"\n*/\nvar TRANSFORM = /*#__PURE__*/getCrossBrowserProperty(\"transform\");\n/**\n* get string \"filter\" with the vendor prefix.\n* @memberof CrossBrowser\n* @example\nimport {FILTER} from \"@daybrush/utils\";\n\nconsole.log(FILTER); // \"filter\", \"-ms-filter\", \"-webkit-filter\"\n*/\nvar FILTER = /*#__PURE__*/getCrossBrowserProperty(\"filter\");\n/**\n* get string \"animation\" with the vendor prefix.\n* @memberof CrossBrowser\n* @example\nimport {ANIMATION} from \"@daybrush/utils\";\n\nconsole.log(ANIMATION); // \"animation\", \"-ms-animation\", \"-webkit-animation\"\n*/\nvar ANIMATION = /*#__PURE__*/getCrossBrowserProperty(\"animation\");\n/**\n* get string \"keyframes\" with the vendor prefix.\n* @memberof CrossBrowser\n* @example\nimport {KEYFRAMES} from \"@daybrush/utils\";\n\nconsole.log(KEYFRAMES); // \"keyframes\", \"-ms-keyframes\", \"-webkit-keyframes\"\n*/\nvar KEYFRAMES = /*#__PURE__*/ANIMATION.replace(\"animation\", \"keyframes\");\nvar OPEN_CLOSED_CHARACTERS = [{\n  open: \"(\",\n  close: \")\"\n}, {\n  open: \"\\\"\",\n  close: \"\\\"\"\n}, {\n  open: \"'\",\n  close: \"'\"\n}, {\n  open: \"\\\\\\\"\",\n  close: \"\\\\\\\"\"\n}, {\n  open: \"\\\\'\",\n  close: \"\\\\'\"\n}];\nvar TINY_NUM = 0.0000001;\nvar REVERSE_TINY_NUM = 1 / TINY_NUM;\nvar DEFAULT_UNIT_PRESETS = {\n  \"cm\": function (pos) {\n    return pos * 96 / 2.54;\n  },\n  \"mm\": function (pos) {\n    return pos * 96 / 254;\n  },\n  \"in\": function (pos) {\n    return pos * 96;\n  },\n  \"pt\": function (pos) {\n    return pos * 96 / 72;\n  },\n  \"pc\": function (pos) {\n    return pos * 96 / 6;\n  },\n  \"%\": function (pos, size) {\n    return pos * size / 100;\n  },\n  \"vw\": function (pos, size) {\n    if (size === void 0) {\n      size = window.innerWidth;\n    }\n    return pos / 100 * size;\n  },\n  \"vh\": function (pos, size) {\n    if (size === void 0) {\n      size = window.innerHeight;\n    }\n    return pos / 100 * size;\n  },\n  \"vmax\": function (pos, size) {\n    if (size === void 0) {\n      size = Math.max(window.innerWidth, window.innerHeight);\n    }\n    return pos / 100 * size;\n  },\n  \"vmin\": function (pos, size) {\n    if (size === void 0) {\n      size = Math.min(window.innerWidth, window.innerHeight);\n    }\n    return pos / 100 * size;\n  }\n};\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++) for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++) r[k] = a[j];\n  return r;\n}\n\n/**\n* @namespace\n* @name Utils\n*/\n/**\n * Returns the inner product of two numbers(`a1`, `a2`) by two criteria(`b1`, `b2`).\n * @memberof Utils\n * @param - The first number\n * @param - The second number\n * @param - The first number to base on the inner product\n * @param - The second number to base on the inner product\n * @return - Returns the inner product\nimport { dot } from \"@daybrush/utils\";\n\nconsole.log(dot(0, 15, 2, 3)); // 6\nconsole.log(dot(5, 15, 2, 3)); // 9\nconsole.log(dot(5, 15, 1, 1)); // 10\n */\nfunction dot(a1, a2, b1, b2) {\n  return (a1 * b2 + a2 * b1) / (b1 + b2);\n}\n/**\n* Check the type that the value is undefined.\n* @memberof Utils\n* @param {string} value - Value to check the type\n* @return {boolean} true if the type is correct, false otherwise\n* @example\nimport {isUndefined} from \"@daybrush/utils\";\n\nconsole.log(isUndefined(undefined)); // true\nconsole.log(isUndefined(\"\")); // false\nconsole.log(isUndefined(1)); // false\nconsole.log(isUndefined(null)); // false\n*/\nfunction isUndefined(value) {\n  return typeof value === UNDEFINED;\n}\n/**\n* Check the type that the value is object.\n* @memberof Utils\n* @param {string} value - Value to check the type\n* @return {} true if the type is correct, false otherwise\n* @example\nimport {isObject} from \"@daybrush/utils\";\n\nconsole.log(isObject({})); // true\nconsole.log(isObject(undefined)); // false\nconsole.log(isObject(\"\")); // false\nconsole.log(isObject(null)); // false\n*/\nfunction isObject(value) {\n  return value && typeof value === OBJECT;\n}\n/**\n* Check the type that the value is isArray.\n* @memberof Utils\n* @param {string} value - Value to check the type\n* @return {} true if the type is correct, false otherwise\n* @example\nimport {isArray} from \"@daybrush/utils\";\n\nconsole.log(isArray([])); // true\nconsole.log(isArray({})); // false\nconsole.log(isArray(undefined)); // false\nconsole.log(isArray(null)); // false\n*/\nfunction isArray(value) {\n  return Array.isArray(value);\n}\n/**\n* Check the type that the value is string.\n* @memberof Utils\n* @param {string} value - Value to check the type\n* @return {} true if the type is correct, false otherwise\n* @example\nimport {isString} from \"@daybrush/utils\";\n\nconsole.log(isString(\"1234\")); // true\nconsole.log(isString(undefined)); // false\nconsole.log(isString(1)); // false\nconsole.log(isString(null)); // false\n*/\nfunction isString(value) {\n  return typeof value === STRING;\n}\nfunction isNumber(value) {\n  return typeof value === NUMBER;\n}\n/**\n* Check the type that the value is function.\n* @memberof Utils\n* @param {string} value - Value to check the type\n* @return {} true if the type is correct, false otherwise\n* @example\nimport {isFunction} from \"@daybrush/utils\";\n\nconsole.log(isFunction(function a() {})); // true\nconsole.log(isFunction(() => {})); // true\nconsole.log(isFunction(\"1234\")); // false\nconsole.log(isFunction(1)); // false\nconsole.log(isFunction(null)); // false\n*/\nfunction isFunction(value) {\n  return typeof value === FUNCTION;\n}\nfunction isEqualSeparator(character, separator) {\n  var isCharacterSpace = character === \"\" || character == \" \";\n  var isSeparatorSpace = separator === \"\" || separator == \" \";\n  return isSeparatorSpace && isCharacterSpace || character === separator;\n}\nfunction findOpen(openCharacter, texts, index, length, openCloseCharacters) {\n  var isIgnore = findIgnore(openCharacter, texts, index);\n  if (!isIgnore) {\n    return findClose(openCharacter, texts, index + 1, length, openCloseCharacters);\n  }\n  return index;\n}\nfunction findIgnore(character, texts, index) {\n  if (!character.ignore) {\n    return null;\n  }\n  var otherText = texts.slice(Math.max(index - 3, 0), index + 3).join(\"\");\n  return new RegExp(character.ignore).exec(otherText);\n}\nfunction findClose(closeCharacter, texts, index, length, openCloseCharacters) {\n  var _loop_1 = function (i) {\n    var character = texts[i].trim();\n    if (character === closeCharacter.close && !findIgnore(closeCharacter, texts, i)) {\n      return {\n        value: i\n      };\n    }\n    var nextIndex = i;\n    // re open\n    var openCharacter = find(openCloseCharacters, function (_a) {\n      var open = _a.open;\n      return open === character;\n    });\n    if (openCharacter) {\n      nextIndex = findOpen(openCharacter, texts, i, length, openCloseCharacters);\n    }\n    if (nextIndex === -1) {\n      return out_i_1 = i, \"break\";\n    }\n    i = nextIndex;\n    out_i_1 = i;\n  };\n  var out_i_1;\n  for (var i = index; i < length; ++i) {\n    var state_1 = _loop_1(i);\n    i = out_i_1;\n    if (typeof state_1 === \"object\") return state_1.value;\n    if (state_1 === \"break\") break;\n  }\n  return -1;\n}\nfunction splitText(text, splitOptions) {\n  var _a = isString(splitOptions) ? {\n      separator: splitOptions\n    } : splitOptions,\n    _b = _a.separator,\n    separator = _b === void 0 ? \",\" : _b,\n    isSeparateFirst = _a.isSeparateFirst,\n    isSeparateOnlyOpenClose = _a.isSeparateOnlyOpenClose,\n    _c = _a.isSeparateOpenClose,\n    isSeparateOpenClose = _c === void 0 ? isSeparateOnlyOpenClose : _c,\n    _d = _a.openCloseCharacters,\n    openCloseCharacters = _d === void 0 ? OPEN_CLOSED_CHARACTERS : _d;\n  var openClosedText = openCloseCharacters.map(function (_a) {\n    var open = _a.open,\n      close = _a.close;\n    if (open === close) {\n      return open;\n    }\n    return open + \"|\" + close;\n  }).join(\"|\");\n  var regexText = \"(\\\\s*\" + separator + \"\\\\s*|\" + openClosedText + \"|\\\\s+)\";\n  var regex = new RegExp(regexText, \"g\");\n  var texts = text.split(regex).filter(function (chr) {\n    return chr && chr !== \"undefined\";\n  });\n  var length = texts.length;\n  var values = [];\n  var tempValues = [];\n  function resetTemp() {\n    if (tempValues.length) {\n      values.push(tempValues.join(\"\"));\n      tempValues = [];\n      return true;\n    }\n    return false;\n  }\n  var _loop_2 = function (i) {\n    var character = texts[i].trim();\n    var nextIndex = i;\n    var openCharacter = find(openCloseCharacters, function (_a) {\n      var open = _a.open;\n      return open === character;\n    });\n    var closeCharacter = find(openCloseCharacters, function (_a) {\n      var close = _a.close;\n      return close === character;\n    });\n    if (openCharacter) {\n      nextIndex = findOpen(openCharacter, texts, i, length, openCloseCharacters);\n      if (nextIndex !== -1 && isSeparateOpenClose) {\n        if (resetTemp() && isSeparateFirst) {\n          return out_i_2 = i, \"break\";\n        }\n        values.push(texts.slice(i, nextIndex + 1).join(\"\"));\n        i = nextIndex;\n        if (isSeparateFirst) {\n          return out_i_2 = i, \"break\";\n        }\n        return out_i_2 = i, \"continue\";\n      }\n    } else if (closeCharacter && !findIgnore(closeCharacter, texts, i)) {\n      var nextOpenCloseCharacters = __spreadArrays(openCloseCharacters);\n      nextOpenCloseCharacters.splice(openCloseCharacters.indexOf(closeCharacter), 1);\n      return {\n        value: splitText(text, {\n          separator: separator,\n          isSeparateFirst: isSeparateFirst,\n          isSeparateOnlyOpenClose: isSeparateOnlyOpenClose,\n          isSeparateOpenClose: isSeparateOpenClose,\n          openCloseCharacters: nextOpenCloseCharacters\n        })\n      };\n    } else if (isEqualSeparator(character, separator) && !isSeparateOnlyOpenClose) {\n      resetTemp();\n      if (isSeparateFirst) {\n        return out_i_2 = i, \"break\";\n      }\n      return out_i_2 = i, \"continue\";\n    }\n    if (nextIndex === -1) {\n      nextIndex = length - 1;\n    }\n    tempValues.push(texts.slice(i, nextIndex + 1).join(\"\"));\n    i = nextIndex;\n    out_i_2 = i;\n  };\n  var out_i_2;\n  for (var i = 0; i < length; ++i) {\n    var state_2 = _loop_2(i);\n    i = out_i_2;\n    if (typeof state_2 === \"object\") return state_2.value;\n    if (state_2 === \"break\") break;\n  }\n  if (tempValues.length) {\n    values.push(tempValues.join(\"\"));\n  }\n  return values;\n}\n/**\n* divide text by space.\n* @memberof Utils\n* @param {string} text - text to divide\n* @return {Array} divided texts\n* @example\nimport {spliceSpace} from \"@daybrush/utils\";\n\nconsole.log(splitSpace(\"a b c d e f g\"));\n// [\"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\"]\nconsole.log(splitSpace(\"'a,b' c 'd,e' f g\"));\n// [\"'a,b'\", \"c\", \"'d,e'\", \"f\", \"g\"]\n*/\nfunction splitSpace(text) {\n  // divide comma(space)\n  return splitText(text, \"\");\n}\n/**\n* divide text by comma.\n* @memberof Utils\n* @param {string} text - text to divide\n* @return {Array} divided texts\n* @example\nimport {splitComma} from \"@daybrush/utils\";\n\nconsole.log(splitComma(\"a,b,c,d,e,f,g\"));\n// [\"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\"]\nconsole.log(splitComma(\"'a,b',c,'d,e',f,g\"));\n// [\"'a,b'\", \"c\", \"'d,e'\", \"f\", \"g\"]\n*/\nfunction splitComma(text) {\n  // divide comma(,)\n  // \"[^\"]*\"|'[^']*'\n  return splitText(text, \",\");\n}\n/**\n* divide text by bracket \"(\", \")\".\n* @memberof Utils\n* @param {string} text - text to divide\n* @return {object} divided texts\n* @example\nimport {splitBracket} from \"@daybrush/utils\";\n\nconsole.log(splitBracket(\"a(1, 2)\"));\n// {prefix: \"a\", value: \"1, 2\", suffix: \"\"}\nconsole.log(splitBracket(\"a(1, 2)b\"));\n// {prefix: \"a\", value: \"1, 2\", suffix: \"b\"}\n*/\nfunction splitBracket(text) {\n  var matches = /([^(]*)\\(([\\s\\S]*)\\)([\\s\\S]*)/g.exec(text);\n  if (!matches || matches.length < 4) {\n    return {};\n  } else {\n    return {\n      prefix: matches[1],\n      value: matches[2],\n      suffix: matches[3]\n    };\n  }\n}\n/**\n* divide text by number and unit.\n* @memberof Utils\n* @param {string} text - text to divide\n* @return {} divided texts\n* @example\nimport {splitUnit} from \"@daybrush/utils\";\n\nconsole.log(splitUnit(\"10px\"));\n// {prefix: \"\", value: 10, unit: \"px\"}\nconsole.log(splitUnit(\"-10px\"));\n// {prefix: \"\", value: -10, unit: \"px\"}\nconsole.log(splitUnit(\"a10%\"));\n// {prefix: \"a\", value: 10, unit: \"%\"}\n*/\nfunction splitUnit(text) {\n  var matches = /^([^\\d|e|\\-|\\+]*)((?:\\d|\\.|-|e-|e\\+)+)(\\S*)$/g.exec(text);\n  if (!matches) {\n    return {\n      prefix: \"\",\n      unit: \"\",\n      value: NaN\n    };\n  }\n  var prefix = matches[1];\n  var value = matches[2];\n  var unit = matches[3];\n  return {\n    prefix: prefix,\n    unit: unit,\n    value: parseFloat(value)\n  };\n}\n/**\n* transform strings to camel-case\n* @memberof Utils\n* @param {String} text - string\n* @return {String} camel-case string\n* @example\nimport {camelize} from \"@daybrush/utils\";\n\nconsole.log(camelize(\"transform-origin\")); // transformOrigin\nconsole.log(camelize(\"abcd_efg\")); // abcdEfg\nconsole.log(camelize(\"abcd efg\")); // abcdEfg\n*/\nfunction camelize(str) {\n  return str.replace(/[\\s-_]+([^\\s-_])/g, function (all, letter) {\n    return letter.toUpperCase();\n  });\n}\n/**\n* transform a camelized string into a lowercased string.\n* @memberof Utils\n* @param {string} text - a camel-cased string\n* @param {string} [separator=\"-\"] - a separator\n* @return {string}  a lowercased string\n* @example\nimport {decamelize} from \"@daybrush/utils\";\n\nconsole.log(decamelize(\"transformOrigin\")); // transform-origin\nconsole.log(decamelize(\"abcdEfg\", \"_\")); // abcd_efg\n*/\nfunction decamelize(str, separator) {\n  if (separator === void 0) {\n    separator = \"-\";\n  }\n  return str.replace(/([a-z])([A-Z])/g, function (all, letter, letter2) {\n    return \"\" + letter + separator + letter2.toLowerCase();\n  });\n}\n/**\n* transforms something in an array into an array.\n* @memberof Utils\n* @param - Array form\n* @return an array\n* @example\nimport {toArray} from \"@daybrush/utils\";\n\nconst arr1 = toArray(document.querySelectorAll(\".a\")); // Element[]\nconst arr2 = toArray(document.querySelectorAll<HTMLElement>(\".a\")); // HTMLElement[]\n*/\nfunction toArray(value) {\n  return [].slice.call(value);\n}\n/**\n* Date.now() method\n* @memberof CrossBrowser\n* @return {number} milliseconds\n* @example\nimport {now} from \"@daybrush/utils\";\n\nconsole.log(now()); // 12121324241(milliseconds)\n*/\nfunction now() {\n  return Date.now ? Date.now() : new Date().getTime();\n}\n/**\n* Returns the index of the first element in the array that satisfies the provided testing function.\n* @function\n* @memberof CrossBrowser\n* @param - The array `findIndex` was called upon.\n* @param - A function to execute on each value in the array until the function returns true, indicating that the satisfying element was found.\n* @param - Returns defaultIndex if not found by the function.\n* @example\nimport { findIndex } from \"@daybrush/utils\";\n\nfindIndex([{a: 1}, {a: 2}, {a: 3}, {a: 4}], ({ a }) => a === 2); // 1\n*/\nfunction findIndex(arr, callback, defaultIndex) {\n  if (defaultIndex === void 0) {\n    defaultIndex = -1;\n  }\n  var length = arr.length;\n  for (var i = 0; i < length; ++i) {\n    if (callback(arr[i], i, arr)) {\n      return i;\n    }\n  }\n  return defaultIndex;\n}\n/**\n* Returns the reverse direction index of the first element in the array that satisfies the provided testing function.\n* @function\n* @memberof CrossBrowser\n* @param - The array `findLastIndex` was called upon.\n* @param - A function to execute on each value in the array until the function returns true, indicating that the satisfying element was found.\n* @param - Returns defaultIndex if not found by the function.\n* @example\nimport { findLastIndex } from \"@daybrush/utils\";\n\nfindLastIndex([{a: 1}, {a: 2}, {a: 3}, {a: 4}], ({ a }) => a === 2); // 1\n*/\nfunction findLastIndex(arr, callback, defaultIndex) {\n  if (defaultIndex === void 0) {\n    defaultIndex = -1;\n  }\n  var length = arr.length;\n  for (var i = length - 1; i >= 0; --i) {\n    if (callback(arr[i], i, arr)) {\n      return i;\n    }\n  }\n  return defaultIndex;\n}\n/**\n* Returns the value of the reverse direction element in the array that satisfies the provided testing function.\n* @function\n* @memberof CrossBrowser\n* @param - The array `findLast` was called upon.\n* @param - A function to execute on each value in the array,\n* @param - Returns defalutValue if not found by the function.\n* @example\nimport { find } from \"@daybrush/utils\";\n\nfind([{a: 1}, {a: 2}, {a: 3}, {a: 4}], ({ a }) => a === 2); // {a: 2}\n*/\nfunction findLast(arr, callback, defalutValue) {\n  var index = findLastIndex(arr, callback);\n  return index > -1 ? arr[index] : defalutValue;\n}\n/**\n* Returns the value of the first element in the array that satisfies the provided testing function.\n* @function\n* @memberof CrossBrowser\n* @param - The array `find` was called upon.\n* @param - A function to execute on each value in the array,\n* @param - Returns defalutValue if not found by the function.\n* @example\nimport { find } from \"@daybrush/utils\";\n\nfind([{a: 1}, {a: 2}, {a: 3}, {a: 4}], ({ a }) => a === 2); // {a: 2}\n*/\nfunction find(arr, callback, defalutValue) {\n  var index = findIndex(arr, callback);\n  return index > -1 ? arr[index] : defalutValue;\n}\n/**\n* window.requestAnimationFrame() method with cross browser.\n* @function\n* @memberof CrossBrowser\n* @param {FrameRequestCallback} callback - The function to call when it's time to update your animation for the next repaint.\n* @return {number} id\n* @example\nimport {requestAnimationFrame} from \"@daybrush/utils\";\n\nrequestAnimationFrame((timestamp) => {\n  console.log(timestamp);\n});\n*/\nvar requestAnimationFrame = /*#__PURE__*/function () {\n  var firstTime = now();\n  var raf = IS_WINDOW && (window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.msRequestAnimationFrame);\n  return raf ? raf.bind(window) : function (callback) {\n    var currTime = now();\n    var id = setTimeout(function () {\n      callback(currTime - firstTime);\n    }, 1000 / 60);\n    return id;\n  };\n}();\n/**\n* window.cancelAnimationFrame() method with cross browser.\n* @function\n* @memberof CrossBrowser\n* @param {number} handle - the id obtained through requestAnimationFrame method\n* @return {void}\n* @example\nimport { requestAnimationFrame, cancelAnimationFrame } from \"@daybrush/utils\";\n\nconst id = requestAnimationFrame((timestamp) => {\n  console.log(timestamp);\n});\n\ncancelAnimationFrame(id);\n*/\nvar cancelAnimationFrame = /*#__PURE__*/function () {\n  var caf = IS_WINDOW && (window.cancelAnimationFrame || window.webkitCancelAnimationFrame || window.mozCancelAnimationFrame || window.msCancelAnimationFrame);\n  return caf ? caf.bind(window) : function (handle) {\n    clearTimeout(handle);\n  };\n}();\n/**\n* @function\n* @memberof Utils\n*/\nfunction getKeys(obj) {\n  return Object.keys(obj);\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction getValues(obj) {\n  var keys = getKeys(obj);\n  return keys.map(function (key) {\n    return obj[key];\n  });\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction getEntries(obj) {\n  var keys = getKeys(obj);\n  return keys.map(function (key) {\n    return [key, obj[key]];\n  });\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction sortOrders(keys, orders) {\n  if (orders === void 0) {\n    orders = [];\n  }\n  keys.sort(function (a, b) {\n    var index1 = orders.indexOf(a);\n    var index2 = orders.indexOf(b);\n    if (index2 === -1 && index1 === -1) {\n      return 0;\n    }\n    if (index1 === -1) {\n      return 1;\n    }\n    if (index2 === -1) {\n      return -1;\n    }\n    return index1 - index2;\n  });\n}\n/**\n* convert unit size to px size\n* @function\n* @memberof Utils\n*/\nfunction convertUnitSize(pos, size) {\n  var _a = splitUnit(pos),\n    value = _a.value,\n    unit = _a.unit;\n  if (isObject(size)) {\n    var sizeFunction = size[unit];\n    if (sizeFunction) {\n      if (isFunction(sizeFunction)) {\n        return sizeFunction(value);\n      } else if (DEFAULT_UNIT_PRESETS[unit]) {\n        return DEFAULT_UNIT_PRESETS[unit](value, sizeFunction);\n      }\n    }\n  } else if (unit === \"%\") {\n    return value * size / 100;\n  }\n  if (DEFAULT_UNIT_PRESETS[unit]) {\n    return DEFAULT_UNIT_PRESETS[unit](value);\n  }\n  return value;\n}\n/**\n* calculate between min, max\n* @function\n* @memberof Utils\n*/\nfunction between(value, min, max) {\n  return Math.max(min, Math.min(value, max));\n}\nfunction checkBoundSize(targetSize, compareSize, isMax, ratio) {\n  if (ratio === void 0) {\n    ratio = targetSize[0] / targetSize[1];\n  }\n  return [[throttle(compareSize[0], TINY_NUM), throttle(compareSize[0] / ratio, TINY_NUM)], [throttle(compareSize[1] * ratio, TINY_NUM), throttle(compareSize[1], TINY_NUM)]].filter(function (size) {\n    return size.every(function (value, i) {\n      var defaultSize = compareSize[i];\n      var throttledSize = throttle(defaultSize, TINY_NUM);\n      return isMax ? value <= defaultSize || value <= throttledSize : value >= defaultSize || value >= throttledSize;\n    });\n  })[0] || targetSize;\n}\n/**\n* calculate bound size\n* @function\n* @memberof Utils\n*/\nfunction calculateBoundSize(size, minSize, maxSize, keepRatio) {\n  if (!keepRatio) {\n    return size.map(function (value, i) {\n      return between(value, minSize[i], maxSize[i]);\n    });\n  }\n  var width = size[0],\n    height = size[1];\n  var ratio = keepRatio === true ? width / height : keepRatio;\n  // width : height = minWidth : minHeight;\n  var _a = checkBoundSize(size, minSize, false, ratio),\n    minWidth = _a[0],\n    minHeight = _a[1];\n  var _b = checkBoundSize(size, maxSize, true, ratio),\n    maxWidth = _b[0],\n    maxHeight = _b[1];\n  if (width < minWidth || height < minHeight) {\n    width = minWidth;\n    height = minHeight;\n  } else if (width > maxWidth || height > maxHeight) {\n    width = maxWidth;\n    height = maxHeight;\n  }\n  return [width, height];\n}\n/**\n* Add all the numbers.\n* @function\n* @memberof Utils\n*/\nfunction sum(nums) {\n  var length = nums.length;\n  var total = 0;\n  for (var i = length - 1; i >= 0; --i) {\n    total += nums[i];\n  }\n  return total;\n}\n/**\n* Average all numbers.\n* @function\n* @memberof Utils\n*/\nfunction average(nums) {\n  var length = nums.length;\n  var total = 0;\n  for (var i = length - 1; i >= 0; --i) {\n    total += nums[i];\n  }\n  return length ? total / length : 0;\n}\n/**\n* Get the angle of two points. (0 <= rad < 359)\n* @function\n* @memberof Utils\n*/\nfunction getRad(pos1, pos2) {\n  var distX = pos2[0] - pos1[0];\n  var distY = pos2[1] - pos1[1];\n  var rad = Math.atan2(distY, distX);\n  return rad >= 0 ? rad : rad + Math.PI * 2;\n}\n/**\n* Get the average point of all points.\n* @function\n* @memberof Utils\n*/\nfunction getCenterPoint(points) {\n  return [0, 1].map(function (i) {\n    return average(points.map(function (pos) {\n      return pos[i];\n    }));\n  });\n}\n/**\n* Gets the direction of the shape.\n* @function\n* @memberof Utils\n*/\nfunction getShapeDirection(points) {\n  var center = getCenterPoint(points);\n  var pos1Rad = getRad(center, points[0]);\n  var pos2Rad = getRad(center, points[1]);\n  return pos1Rad < pos2Rad && pos2Rad - pos1Rad < Math.PI || pos1Rad > pos2Rad && pos2Rad - pos1Rad < -Math.PI ? 1 : -1;\n}\n/**\n* Get the distance between two points.\n* @function\n* @memberof Utils\n*/\nfunction getDist(a, b) {\n  return Math.sqrt(Math.pow((b ? b[0] : 0) - a[0], 2) + Math.pow((b ? b[1] : 0) - a[1], 2));\n}\n/**\n* throttle number depending on the unit.\n* @function\n* @memberof Utils\n*/\nfunction throttle(num, unit) {\n  if (!unit) {\n    return num;\n  }\n  var reverseUnit = 1 / unit;\n  return Math.round(num / unit) / reverseUnit;\n}\n/**\n* throttle number array depending on the unit.\n* @function\n* @memberof Utils\n*/\nfunction throttleArray(nums, unit) {\n  nums.forEach(function (_, i) {\n    nums[i] = throttle(nums[i], unit);\n  });\n  return nums;\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction counter(num) {\n  var nums = [];\n  for (var i = 0; i < num; ++i) {\n    nums.push(i);\n  }\n  return nums;\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction replaceOnce(text, fromText, toText) {\n  var isOnce = false;\n  return text.replace(fromText, function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (isOnce) {\n      return args[0];\n    }\n    isOnce = true;\n    return isString(toText) ? toText : toText.apply(void 0, args);\n  });\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction flat(arr) {\n  return arr.reduce(function (prev, cur) {\n    return prev.concat(cur);\n  }, []);\n}\n/**\n* @function\n* @memberof Utils\n*/\nfunction deepFlat(arr) {\n  return arr.reduce(function (prev, cur) {\n    if (isArray(cur)) {\n      prev.push.apply(prev, deepFlat(cur));\n    } else {\n      prev.push(cur);\n    }\n    return prev;\n  }, []);\n}\n/**\n * @function\n * @memberof Utils\n */\nfunction pushSet(elements, element) {\n  if (elements.indexOf(element) === -1) {\n    elements.push(element);\n  }\n}\n\n/**\n* @namespace\n* @name Color\n*/\n/**\n* Remove the # from the hex color.\n* @memberof Color\n* @param {} hex - hex color\n* @return {} hex color\n* @example\nimport {cutHex} from \"@daybrush/utils\";\n\nconsole.log(cutHex(\"#000000\")) // \"000000\"\n*/\nfunction cutHex(hex) {\n  return hex.replace(\"#\", \"\");\n}\n/**\n* convert hex color to rgb color.\n* @memberof Color\n* @param {} hex - hex color\n* @return {} rgb color\n* @example\nimport {hexToRGBA} from \"@daybrush/utils\";\n\nconsole.log(hexToRGBA(\"#00000005\"));\n// [0, 0, 0, 1]\nconsole.log(hexToRGBA(\"#201045\"));\n// [32, 16, 69, 1]\n*/\nfunction hexToRGBA(hex) {\n  var h = cutHex(hex);\n  var r = parseInt(h.substring(0, 2), 16);\n  var g = parseInt(h.substring(2, 4), 16);\n  var b = parseInt(h.substring(4, 6), 16);\n  var a = parseInt(h.substring(6, 8), 16) / 255;\n  if (isNaN(a)) {\n    a = 1;\n  }\n  return [r, g, b, a];\n}\n/**\n* convert 3(or 4)-digit hex color to 6(or 8)-digit hex color.\n* @memberof Color\n* @param {} hex - 3(or 4)-digit hex color\n* @return {} 6(or 8)-digit hex color\n* @example\nimport {toFullHex} from \"@daybrush/utils\";\n\nconsole.log(toFullHex(\"#123\")); // \"#112233\"\nconsole.log(toFullHex(\"#123a\")); // \"#112233aa\"\n*/\nfunction toFullHex(h) {\n  var r = h.charAt(1);\n  var g = h.charAt(2);\n  var b = h.charAt(3);\n  var a = h.charAt(4);\n  var arr = [\"#\", r, r, g, g, b, b, a, a];\n  return arr.join(\"\");\n}\n/**\n* convert hsl color to rgba color.\n* @memberof Color\n* @param {} hsl - hsl color(hue: 0 ~ 360, saturation: 0 ~ 1, lightness: 0 ~ 1, alpha: 0 ~ 1)\n* @return {} rgba color\n* @example\nimport {hslToRGBA} from \"@daybrush/utils\";\n\nconsole.log(hslToRGBA([150, 0.5, 0.4]));\n// [51, 153, 102, 1]\n*/\nfunction hslToRGBA(hsl) {\n  var _a;\n  var h = hsl[0];\n  var s = hsl[1];\n  var l = hsl[2];\n  if (h < 0) {\n    h += Math.floor((Math.abs(h) + 360) / 360) * 360;\n  }\n  h %= 360;\n  var c = (1 - Math.abs(2 * l - 1)) * s;\n  var x = c * (1 - Math.abs(h / 60 % 2 - 1));\n  var m = l - c / 2;\n  var rgb;\n  if (h < 60) {\n    rgb = [c, x, 0];\n  } else if (h < 120) {\n    rgb = [x, c, 0];\n  } else if (h < 180) {\n    rgb = [0, c, x];\n  } else if (h < 240) {\n    rgb = [0, x, c];\n  } else if (h < 300) {\n    rgb = [x, 0, c];\n  } else if (h < 360) {\n    rgb = [c, 0, x];\n  } else {\n    rgb = [0, 0, 0];\n  }\n  return [Math.round((rgb[0] + m) * 255), Math.round((rgb[1] + m) * 255), Math.round((rgb[2] + m) * 255), (_a = hsl[3]) !== null && _a !== void 0 ? _a : 1];\n}\n/**\n* convert string to rgba color.\n* @memberof Color\n* @param {} - 3-hex(#000), 4-hex(#0000) 6-hex(#000000), 8-hex(#00000000) or RGB(A), or HSL(A)\n* @return {} rgba color\n* @example\nimport {stringToRGBA} from \"@daybrush/utils\";\n\nconsole.log(stringToRGBA(\"#000000\")); // [0, 0, 0, 1]\nconsole.log(stringToRGBA(\"rgb(100, 100, 100)\")); // [100, 100, 100, 1]\nconsole.log(stringToRGBA(\"hsl(150, 0.5, 0.4)\")); // [51, 153, 102, 1]\n*/\nfunction stringToRGBA(color) {\n  if (color.charAt(0) === \"#\") {\n    if (color.length === 4 || color.length === 5) {\n      return hexToRGBA(toFullHex(color));\n    } else {\n      return hexToRGBA(color);\n    }\n  } else if (color.indexOf(\"(\") !== -1) {\n    // in bracket.\n    var _a = splitBracket(color),\n      prefix = _a.prefix,\n      value = _a.value;\n    if (!prefix || !value) {\n      return undefined;\n    }\n    var arr = splitComma(value);\n    var colorArr = [0, 0, 0, 1];\n    var length = arr.length;\n    switch (prefix) {\n      case RGB:\n      case RGBA:\n        for (var i = 0; i < length; ++i) {\n          colorArr[i] = parseFloat(arr[i]);\n        }\n        return colorArr;\n      case HSL:\n      case HSLA:\n        for (var i = 0; i < length; ++i) {\n          if (arr[i].indexOf(\"%\") !== -1) {\n            colorArr[i] = parseFloat(arr[i]) / 100;\n          } else {\n            colorArr[i] = parseFloat(arr[i]);\n          }\n        }\n        // hsl, hsla to rgba\n        return hslToRGBA(colorArr);\n    }\n  }\n  return undefined;\n}\n\n/**\n * Returns all element descendants of node that\n * match selectors.\n */\n/**\n * Checks if the specified class value exists in the element's class attribute.\n * @memberof DOM\n * @param - A DOMString containing one or more selectors to match\n * @param - If multi is true, a DOMString containing one or more selectors to match against.\n * @example\nimport {$} from \"@daybrush/utils\";\n\nconsole.log($(\"div\")); // div element\nconsole.log($(\"div\", true)); // [div, div] elements\n*/\nfunction $(selectors, multi) {\n  if (!doc) {\n    return multi ? [] : null;\n  }\n  return multi ? doc.querySelectorAll(selectors) : doc.querySelector(selectors);\n}\n/**\n* Checks if the specified class value exists in the element's class attribute.\n* @memberof DOM\n* @param element - target\n* @param className - the class name to search\n* @return {boolean} return false if the class is not found.\n* @example\nimport {hasClass} from \"@daybrush/utils\";\n\nconsole.log(hasClass(element, \"start\")); // true or false\n*/\nfunction hasClass(element, className) {\n  if (element.classList) {\n    return element.classList.contains(className);\n  }\n  return !!element.className.match(new RegExp(\"(\\\\s|^)\" + className + \"(\\\\s|$)\"));\n}\n/**\n* Add the specified class value. If these classe already exist in the element's class attribute they are ignored.\n* @memberof DOM\n* @param element - target\n* @param className - the class name to add\n* @example\nimport {addClass} from \"@daybrush/utils\";\n\naddClass(element, \"start\");\n*/\nfunction addClass(element, className) {\n  if (element.classList) {\n    element.classList.add(className);\n  } else {\n    element.className += \" \" + className;\n  }\n}\n/**\n* Removes the specified class value.\n* @memberof DOM\n* @param element - target\n* @param className - the class name to remove\n* @example\nimport {removeClass} from \"@daybrush/utils\";\n\nremoveClass(element, \"start\");\n*/\nfunction removeClass(element, className) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else {\n    var reg = new RegExp(\"(\\\\s|^)\" + className + \"(\\\\s|$)\");\n    element.className = element.className.replace(reg, \" \");\n  }\n}\n/**\n* Gets the CSS properties from the element.\n* @memberof DOM\n* @param elements - elements\n* @param properites - the CSS properties\n* @return returns CSS properties and values.\n* @example\nimport {fromCSS} from \"@daybrush/utils\";\n\nconsole.log(fromCSS(element, [\"left\", \"opacity\", \"top\"])); // {\"left\": \"10px\", \"opacity\": 1, \"top\": \"10px\"}\n*/\nfunction fromCSS(elements, properties) {\n  if (!elements || !properties || !properties.length) {\n    return {};\n  }\n  var element;\n  if (elements instanceof Element) {\n    element = elements;\n  } else if (elements.length) {\n    element = elements[0];\n  } else {\n    return {};\n  }\n  var cssObject = {};\n  var styles = getWindow(element).getComputedStyle(element);\n  var length = properties.length;\n  for (var i = 0; i < length; ++i) {\n    cssObject[properties[i]] = styles[properties[i]];\n  }\n  return cssObject;\n}\n/**\n* Sets up a function that will be called whenever the specified event is delivered to the target\n* @memberof DOM\n* @param - event target\n* @param - A case-sensitive string representing the event type to listen for.\n* @param - The object which receives a notification (an object that implements the Event interface) when an event of the specified type occurs\n* @param - An options object that specifies characteristics about the event listener.\n* @example\nimport {addEvent} from \"@daybrush/utils\";\n\naddEvent(el, \"click\", e => {\n  console.log(e);\n});\n*/\nfunction addEvent(el, type, listener, options) {\n  el.addEventListener(type, listener, options);\n}\n/**\n* removes from the EventTarget an event listener previously registered with EventTarget.addEventListener()\n* @memberof DOM\n* @param - event target\n* @param - A case-sensitive string representing the event type to listen for.\n* @param - The EventListener function of the event handler to remove from the event target.\n* @param - An options object that specifies characteristics about the event listener.\n* @example\nimport {addEvent, removeEvent} from \"@daybrush/utils\";\nconst listener = e => {\n  console.log(e);\n};\naddEvent(el, \"click\", listener);\nremoveEvent(el, \"click\", listener);\n*/\nfunction removeEvent(el, type, listener, options) {\n  el.removeEventListener(type, listener, options);\n}\nfunction getDocument(el) {\n  return (el === null || el === void 0 ? void 0 : el.ownerDocument) || doc;\n}\nfunction getDocumentElement(el) {\n  return getDocument(el).documentElement;\n}\nfunction getDocumentBody(el) {\n  return getDocument(el).body;\n}\nfunction getWindow(el) {\n  var _a;\n  return ((_a = el === null || el === void 0 ? void 0 : el.ownerDocument) === null || _a === void 0 ? void 0 : _a.defaultView) || window;\n}\nfunction isWindow(val) {\n  return val && \"postMessage\" in val && \"blur\" in val && \"self\" in val;\n}\nfunction isNode(el) {\n  return isObject(el) && el.nodeName && el.nodeType && \"ownerDocument\" in el;\n}\n\n\n//# sourceMappingURL=utils.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGRheWJydXNoK3V0aWxzQDEuMTMuMC9ub2RlX21vZHVsZXMvQGRheWJydXNoL3V0aWxzL2Rpc3QvdXRpbHMuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxLQUFLOztBQUViLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLE1BQU07O0FBRWQsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsS0FBSzs7QUFFYixrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxNQUFNOztBQUVkLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLGNBQWM7O0FBRXRCLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLFVBQVU7O0FBRWxCLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLFVBQVU7O0FBRWxCLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLE9BQU87O0FBRWYsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsUUFBUTs7QUFFaEIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsUUFBUTs7QUFFaEIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsUUFBUTs7QUFFaEIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsV0FBVzs7QUFFbkIsd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsV0FBVzs7QUFFbkIsd0JBQXdCO0FBQ3hCLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsV0FBVzs7QUFFbkIsd0JBQXdCO0FBQ3hCLHdCQUF3QjtBQUN4QjtBQUNBLHFEQUFxRDtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsUUFBUTtBQUNsQixXQUFXLFFBQVE7QUFDbkI7QUFDQTtBQUNBLFFBQVEseUJBQXlCOztBQUVqQyxtREFBbUQ7QUFDbkQsZ0RBQWdEO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFlBQVk7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsV0FBVzs7QUFFbkIsd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsUUFBUTs7QUFFaEIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsV0FBVzs7QUFFbkIsd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsV0FBVzs7QUFFbkIsd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsUUFBUTtBQUN4RCx1Q0FBdUMsUUFBUSxzREFBc0QsUUFBUTtBQUM3RztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsTUFBTTs7QUFFZiwrQkFBK0I7QUFDL0IsK0JBQStCO0FBQy9CLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsUUFBUTtBQUNsQixXQUFXLFNBQVM7QUFDcEI7QUFDQSxRQUFRLGFBQWE7O0FBRXJCLHFDQUFxQztBQUNyQyw4QkFBOEI7QUFDOUIsNkJBQTZCO0FBQzdCLGdDQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsUUFBUTtBQUNsQixhQUFhO0FBQ2I7QUFDQSxRQUFRLFVBQVU7O0FBRWxCLHVCQUF1QixJQUFJO0FBQzNCLGtDQUFrQztBQUNsQywyQkFBMkI7QUFDM0IsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxRQUFRO0FBQ2xCLGFBQWE7QUFDYjtBQUNBLFFBQVEsU0FBUzs7QUFFakIsMEJBQTBCO0FBQzFCLHNCQUFzQixJQUFJO0FBQzFCLGlDQUFpQztBQUNqQyw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLFFBQVE7QUFDbEIsYUFBYTtBQUNiO0FBQ0EsUUFBUSxVQUFVOztBQUVsQiwrQkFBK0I7QUFDL0Isa0NBQWtDO0FBQ2xDLDBCQUEwQjtBQUMxQiw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLFFBQVE7QUFDbEIsYUFBYTtBQUNiO0FBQ0EsUUFBUSxZQUFZOztBQUVwQixzQ0FBc0MsSUFBSTtBQUMxQywrQkFBK0IsSUFBSTtBQUNuQyxpQ0FBaUM7QUFDakMsNEJBQTRCO0FBQzVCLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsWUFBWTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsWUFBWTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsUUFBUTtBQUNsQixXQUFXLE9BQU87QUFDbEI7QUFDQSxRQUFRLGFBQWE7O0FBRXJCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsUUFBUTtBQUNsQixXQUFXLE9BQU87QUFDbEI7QUFDQSxRQUFRLFlBQVk7O0FBRXBCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxRQUFRO0FBQ2xCLFdBQVcsUUFBUTtBQUNuQjtBQUNBLFFBQVEsY0FBYzs7QUFFdEI7QUFDQSxJQUFJO0FBQ0o7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsUUFBUTtBQUNsQixhQUFhO0FBQ2I7QUFDQSxRQUFRLFdBQVc7O0FBRW5CO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxRQUFRO0FBQ2xCLFdBQVcsUUFBUTtBQUNuQjtBQUNBLFFBQVEsVUFBVTs7QUFFbEIsMkNBQTJDO0FBQzNDLG1DQUFtQztBQUNuQyxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLFFBQVE7QUFDbEIsVUFBVSxRQUFRO0FBQ2xCLFdBQVcsU0FBUztBQUNwQjtBQUNBLFFBQVEsWUFBWTs7QUFFcEIsNENBQTRDO0FBQzVDLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsU0FBUzs7QUFFakIsdURBQXVEO0FBQ3ZELG9FQUFvRTtBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLFFBQVEsS0FBSzs7QUFFYixvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxZQUFZOztBQUVyQixZQUFZLEtBQUssR0FBRyxLQUFLLEdBQUcsS0FBSyxHQUFHLEtBQUssTUFBTSxHQUFHLGVBQWU7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFlBQVk7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsZ0JBQWdCOztBQUV6QixnQkFBZ0IsS0FBSyxHQUFHLEtBQUssR0FBRyxLQUFLLEdBQUcsS0FBSyxNQUFNLEdBQUcsZUFBZTtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsUUFBUTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxPQUFPOztBQUVoQixPQUFPLEtBQUssR0FBRyxLQUFLLEdBQUcsS0FBSyxHQUFHLEtBQUssTUFBTSxHQUFHLGVBQWUsSUFBSTtBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsT0FBTzs7QUFFaEIsT0FBTyxLQUFLLEdBQUcsS0FBSyxHQUFHLEtBQUssR0FBRyxLQUFLLE1BQU0sR0FBRyxlQUFlLElBQUk7QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxzQkFBc0I7QUFDaEMsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsUUFBUSx1QkFBdUI7O0FBRS9CO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxRQUFRO0FBQ2xCLFdBQVc7QUFDWDtBQUNBLFNBQVMsOENBQThDOztBQUV2RDtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLFFBQVE7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLFFBQVE7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFNBQVM7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLHVCQUF1QjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osYUFBYTtBQUNiO0FBQ0EsUUFBUSxRQUFROztBQUVoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGFBQWE7QUFDYjtBQUNBLFFBQVEsV0FBVzs7QUFFbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osYUFBYTtBQUNiO0FBQ0EsUUFBUSxXQUFXOztBQUVuQixnQ0FBZ0M7QUFDaEMsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhO0FBQ2I7QUFDQSxRQUFRLFdBQVc7O0FBRW5CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhO0FBQ2I7QUFDQSxRQUFRLGNBQWM7O0FBRXRCLHNDQUFzQztBQUN0QyxpREFBaUQ7QUFDakQsaURBQWlEO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixZQUFZO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsWUFBWTtBQUNwQztBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsR0FBRzs7QUFFWCx1QkFBdUI7QUFDdkIsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFFBQVEsVUFBVTs7QUFFbEIseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxVQUFVOztBQUVsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsYUFBYTs7QUFFckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxTQUFTOztBQUVqQiwyREFBMkQsSUFBSTtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsWUFBWTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLFVBQVU7O0FBRWxCO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx1QkFBdUI7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTg4QjtBQUM5OEIiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAZGF5YnJ1c2grdXRpbHNAMS4xMy4wXFxub2RlX21vZHVsZXNcXEBkYXlicnVzaFxcdXRpbHNcXGRpc3RcXHV0aWxzLmVzbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuQ29weXJpZ2h0IChjKSAyMDE4IERheWJydXNoXG5AbmFtZTogQGRheWJydXNoL3V0aWxzXG5saWNlbnNlOiBNSVRcbmF1dGhvcjogRGF5YnJ1c2hcbnJlcG9zaXRvcnk6IGh0dHBzOi8vZ2l0aHViLmNvbS9kYXlicnVzaC91dGlsc1xuQHZlcnNpb24gMS4xMy4wXG4qL1xuLyoqXG4qIEBuYW1lc3BhY2VcbiogQG5hbWUgQ29uc3RzXG4qL1xuLyoqXG4qIGdldCBzdHJpbmcgXCJyZ2JcIlxuKiBAbWVtYmVyb2YgQ29sb3JcbiogQGV4YW1wbGVcbmltcG9ydCB7UkdCfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKFJHQik7IC8vIFwicmdiXCJcbiovXG52YXIgUkdCID0gXCJyZ2JcIjtcbi8qKlxuKiBnZXQgc3RyaW5nIFwicmdiYVwiXG4qIEBtZW1iZXJvZiBDb2xvclxuKiBAZXhhbXBsZVxuaW1wb3J0IHtSR0JBfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKFJHQkEpOyAvLyBcInJnYmFcIlxuKi9cbnZhciBSR0JBID0gXCJyZ2JhXCI7XG4vKipcbiogZ2V0IHN0cmluZyBcImhzbFwiXG4qIEBtZW1iZXJvZiBDb2xvclxuKiBAZXhhbXBsZVxuaW1wb3J0IHtIU0x9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coSFNMKTsgLy8gXCJoc2xcIlxuKi9cbnZhciBIU0wgPSBcImhzbFwiO1xuLyoqXG4qIGdldCBzdHJpbmcgXCJoc2xhXCJcbiogQG1lbWJlcm9mIENvbG9yXG4qIEBleGFtcGxlXG5pbXBvcnQge0hTTEF9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coSFNMQSk7IC8vIFwiaHNsYVwiXG4qL1xudmFyIEhTTEEgPSBcImhzbGFcIjtcbi8qKlxuKiBnZXRzIGFuIGFycmF5IG9mIGNvbG9yIG1vZGVscy5cbiogQG1lbWJlcm9mIENvbG9yXG4qIEBleGFtcGxlXG5pbXBvcnQge0NPTE9SX01PREVMU30gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5jb25zb2xlLmxvZyhDT0xPUl9NT0RFTFMpOyAvLyBbXCJyZ2JcIiwgXCJyZ2JhXCIsIFwiaHNsXCIsIFwiaHNsYVwiXTtcbiovXG52YXIgQ09MT1JfTU9ERUxTID0gW1JHQiwgUkdCQSwgSFNMLCBIU0xBXTtcbi8qKlxuKiBnZXQgc3RyaW5nIFwiZnVuY3Rpb25cIlxuKiBAbWVtYmVyb2YgQ29uc3RzXG4qIEBleGFtcGxlXG5pbXBvcnQge0ZVTkNUSU9OfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKEZVTkNUSU9OKTsgLy8gXCJmdW5jdGlvblwiXG4qL1xudmFyIEZVTkNUSU9OID0gXCJmdW5jdGlvblwiO1xuLyoqXG4qIGdldCBzdHJpbmcgXCJwcm9wZXJ0eVwiXG4qIEBtZW1iZXJvZiBDb25zdHNcbiogQGV4YW1wbGVcbmltcG9ydCB7UFJPUEVSVFl9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coUFJPUEVSVFkpOyAvLyBcInByb3BlcnR5XCJcbiovXG52YXIgUFJPUEVSVFkgPSBcInByb3BlcnR5XCI7XG4vKipcbiogZ2V0IHN0cmluZyBcImFycmF5XCJcbiogQG1lbWJlcm9mIENvbnN0c1xuKiBAZXhhbXBsZVxuaW1wb3J0IHtBUlJBWX0gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5jb25zb2xlLmxvZyhBUlJBWSk7IC8vIFwiYXJyYXlcIlxuKi9cbnZhciBBUlJBWSA9IFwiYXJyYXlcIjtcbi8qKlxuKiBnZXQgc3RyaW5nIFwib2JqZWN0XCJcbiogQG1lbWJlcm9mIENvbnN0c1xuKiBAZXhhbXBsZVxuaW1wb3J0IHtPQkpFQ1R9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coT0JKRUNUKTsgLy8gXCJvYmplY3RcIlxuKi9cbnZhciBPQkpFQ1QgPSBcIm9iamVjdFwiO1xuLyoqXG4qIGdldCBzdHJpbmcgXCJzdHJpbmdcIlxuKiBAbWVtYmVyb2YgQ29uc3RzXG4qIEBleGFtcGxlXG5pbXBvcnQge1NUUklOR30gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5jb25zb2xlLmxvZyhTVFJJTkcpOyAvLyBcInN0cmluZ1wiXG4qL1xudmFyIFNUUklORyA9IFwic3RyaW5nXCI7XG4vKipcbiogZ2V0IHN0cmluZyBcIm51bWJlclwiXG4qIEBtZW1iZXJvZiBDb25zdHNcbiogQGV4YW1wbGVcbmltcG9ydCB7TlVNQkVSfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKE5VTUJFUik7IC8vIFwibnVtYmVyXCJcbiovXG52YXIgTlVNQkVSID0gXCJudW1iZXJcIjtcbi8qKlxuKiBnZXQgc3RyaW5nIFwidW5kZWZpbmVkXCJcbiogQG1lbWJlcm9mIENvbnN0c1xuKiBAZXhhbXBsZVxuaW1wb3J0IHtVTkRFRklORUR9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coVU5ERUZJTkVEKTsgLy8gXCJ1bmRlZmluZWRcIlxuKi9cbnZhciBVTkRFRklORUQgPSBcInVuZGVmaW5lZFwiO1xuLyoqXG4qIENoZWNrIHdoZXRoZXIgdGhlIGVudmlyb25tZW50IGlzIHdpbmRvdyBvciBub2RlLmpzLlxuKiBAbWVtYmVyb2YgQ29uc3RzXG4qIEBleGFtcGxlXG5pbXBvcnQge0lTX1dJTkRPV30gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5jb25zb2xlLmxvZyhJU19XSU5ET1cpOyAvLyBmYWxzZSBpbiBub2RlLmpzXG5jb25zb2xlLmxvZyhJU19XSU5ET1cpOyAvLyB0cnVlIGluIGJyb3dzZXJcbiovXG52YXIgSVNfV0lORE9XID0gdHlwZW9mIHdpbmRvdyAhPT0gVU5ERUZJTkVEO1xuLyoqXG4qIENoZWNrIHdoZXRoZXIgdGhlIGVudmlyb25tZW50IGlzIHdpbmRvdyBvciBub2RlLmpzLlxuKiBAbWVtYmVyb2YgQ29uc3RzXG4qIEBuYW1lIGRvY3VtZW50XG4qIEBleGFtcGxlXG5pbXBvcnQge0lTX1dJTkRPV30gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5jb25zb2xlLmxvZyhJU19XSU5ET1cpOyAvLyBmYWxzZSBpbiBub2RlLmpzXG5jb25zb2xlLmxvZyhJU19XSU5ET1cpOyAvLyB0cnVlIGluIGJyb3dzZXJcbiovXG52YXIgZG9jID0gdHlwZW9mIGRvY3VtZW50ICE9PSBVTkRFRklORUQgJiYgZG9jdW1lbnQ7IC8vIEZJWE1FOiB0aGlzIHR5cGUgbWF5YmUgZmFsc2VcbnZhciBwcmVmaXhlcyA9IFtcIndlYmtpdFwiLCBcIm1zXCIsIFwibW96XCIsIFwib1wiXTtcbi8qKlxuICogQG5hbWVzcGFjZSBDcm9zc0Jyb3dzZXJcbiAqL1xuLyoqXG4qIEdldCBhIENTUyBwcm9wZXJ0eSB3aXRoIGEgdmVuZG9yIHByZWZpeCB0aGF0IHN1cHBvcnRzIGNyb3NzIGJyb3dzZXIuXG4qIEBmdW5jdGlvblxuKiBAcGFyYW0ge3N0cmluZ30gcHJvcGVydHkgLSBBIENTUyBwcm9wZXJ0eVxuKiBAcmV0dXJuIHtzdHJpbmd9IENTUyBwcm9wZXJ0eSB3aXRoIGNyb3NzLWJyb3dzZXIgdmVuZG9yIHByZWZpeFxuKiBAbWVtYmVyb2YgQ3Jvc3NCcm93c2VyXG4qIEBleGFtcGxlXG5pbXBvcnQge2dldENyb3NzQnJvd3NlclByb3BlcnR5fSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKGdldENyb3NzQnJvd3NlclByb3BlcnR5KFwidHJhbnNmb3JtXCIpKTsgLy8gXCJ0cmFuc2Zvcm1cIiwgXCItbXMtdHJhbnNmb3JtXCIsIFwiLXdlYmtpdC10cmFuc2Zvcm1cIlxuY29uc29sZS5sb2coZ2V0Q3Jvc3NCcm93c2VyUHJvcGVydHkoXCJmaWx0ZXJcIikpOyAvLyBcImZpbHRlclwiLCBcIi13ZWJraXQtZmlsdGVyXCJcbiovXG52YXIgZ2V0Q3Jvc3NCcm93c2VyUHJvcGVydHkgPSAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKHByb3BlcnR5KSB7XG4gIGlmICghZG9jKSB7XG4gICAgcmV0dXJuIFwiXCI7XG4gIH1cbiAgdmFyIHN0eWxlcyA9IChkb2MuYm9keSB8fCBkb2MuZG9jdW1lbnRFbGVtZW50KS5zdHlsZTtcbiAgdmFyIGxlbmd0aCA9IHByZWZpeGVzLmxlbmd0aDtcbiAgaWYgKHR5cGVvZiBzdHlsZXNbcHJvcGVydHldICE9PSBVTkRFRklORUQpIHtcbiAgICByZXR1cm4gcHJvcGVydHk7XG4gIH1cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBsZW5ndGg7ICsraSkge1xuICAgIHZhciBuYW1lID0gXCItXCIgKyBwcmVmaXhlc1tpXSArIFwiLVwiICsgcHJvcGVydHk7XG4gICAgaWYgKHR5cGVvZiBzdHlsZXNbbmFtZV0gIT09IFVOREVGSU5FRCkge1xuICAgICAgcmV0dXJuIG5hbWU7XG4gICAgfVxuICB9XG4gIHJldHVybiBcIlwiO1xufTtcbi8qKlxuKiBnZXQgc3RyaW5nIFwidHJhbnNmcm9tXCIgd2l0aCB0aGUgdmVuZG9yIHByZWZpeC5cbiogQG1lbWJlcm9mIENyb3NzQnJvd3NlclxuKiBAZXhhbXBsZVxuaW1wb3J0IHtUUkFOU0ZPUk19IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coVFJBTlNGT1JNKTsgLy8gXCJ0cmFuc2Zvcm1cIiwgXCItbXMtdHJhbnNmb3JtXCIsIFwiLXdlYmtpdC10cmFuc2Zvcm1cIlxuKi9cbnZhciBUUkFOU0ZPUk0gPSAvKiNfX1BVUkVfXyovZ2V0Q3Jvc3NCcm93c2VyUHJvcGVydHkoXCJ0cmFuc2Zvcm1cIik7XG4vKipcbiogZ2V0IHN0cmluZyBcImZpbHRlclwiIHdpdGggdGhlIHZlbmRvciBwcmVmaXguXG4qIEBtZW1iZXJvZiBDcm9zc0Jyb3dzZXJcbiogQGV4YW1wbGVcbmltcG9ydCB7RklMVEVSfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKEZJTFRFUik7IC8vIFwiZmlsdGVyXCIsIFwiLW1zLWZpbHRlclwiLCBcIi13ZWJraXQtZmlsdGVyXCJcbiovXG52YXIgRklMVEVSID0gLyojX19QVVJFX18qL2dldENyb3NzQnJvd3NlclByb3BlcnR5KFwiZmlsdGVyXCIpO1xuLyoqXG4qIGdldCBzdHJpbmcgXCJhbmltYXRpb25cIiB3aXRoIHRoZSB2ZW5kb3IgcHJlZml4LlxuKiBAbWVtYmVyb2YgQ3Jvc3NCcm93c2VyXG4qIEBleGFtcGxlXG5pbXBvcnQge0FOSU1BVElPTn0gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5jb25zb2xlLmxvZyhBTklNQVRJT04pOyAvLyBcImFuaW1hdGlvblwiLCBcIi1tcy1hbmltYXRpb25cIiwgXCItd2Via2l0LWFuaW1hdGlvblwiXG4qL1xudmFyIEFOSU1BVElPTiA9IC8qI19fUFVSRV9fKi9nZXRDcm9zc0Jyb3dzZXJQcm9wZXJ0eShcImFuaW1hdGlvblwiKTtcbi8qKlxuKiBnZXQgc3RyaW5nIFwia2V5ZnJhbWVzXCIgd2l0aCB0aGUgdmVuZG9yIHByZWZpeC5cbiogQG1lbWJlcm9mIENyb3NzQnJvd3NlclxuKiBAZXhhbXBsZVxuaW1wb3J0IHtLRVlGUkFNRVN9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coS0VZRlJBTUVTKTsgLy8gXCJrZXlmcmFtZXNcIiwgXCItbXMta2V5ZnJhbWVzXCIsIFwiLXdlYmtpdC1rZXlmcmFtZXNcIlxuKi9cbnZhciBLRVlGUkFNRVMgPSAvKiNfX1BVUkVfXyovQU5JTUFUSU9OLnJlcGxhY2UoXCJhbmltYXRpb25cIiwgXCJrZXlmcmFtZXNcIik7XG52YXIgT1BFTl9DTE9TRURfQ0hBUkFDVEVSUyA9IFt7XG4gIG9wZW46IFwiKFwiLFxuICBjbG9zZTogXCIpXCJcbn0sIHtcbiAgb3BlbjogXCJcXFwiXCIsXG4gIGNsb3NlOiBcIlxcXCJcIlxufSwge1xuICBvcGVuOiBcIidcIixcbiAgY2xvc2U6IFwiJ1wiXG59LCB7XG4gIG9wZW46IFwiXFxcXFxcXCJcIixcbiAgY2xvc2U6IFwiXFxcXFxcXCJcIlxufSwge1xuICBvcGVuOiBcIlxcXFwnXCIsXG4gIGNsb3NlOiBcIlxcXFwnXCJcbn1dO1xudmFyIFRJTllfTlVNID0gMC4wMDAwMDAxO1xudmFyIFJFVkVSU0VfVElOWV9OVU0gPSAxIC8gVElOWV9OVU07XG52YXIgREVGQVVMVF9VTklUX1BSRVNFVFMgPSB7XG4gIFwiY21cIjogZnVuY3Rpb24gKHBvcykge1xuICAgIHJldHVybiBwb3MgKiA5NiAvIDIuNTQ7XG4gIH0sXG4gIFwibW1cIjogZnVuY3Rpb24gKHBvcykge1xuICAgIHJldHVybiBwb3MgKiA5NiAvIDI1NDtcbiAgfSxcbiAgXCJpblwiOiBmdW5jdGlvbiAocG9zKSB7XG4gICAgcmV0dXJuIHBvcyAqIDk2O1xuICB9LFxuICBcInB0XCI6IGZ1bmN0aW9uIChwb3MpIHtcbiAgICByZXR1cm4gcG9zICogOTYgLyA3MjtcbiAgfSxcbiAgXCJwY1wiOiBmdW5jdGlvbiAocG9zKSB7XG4gICAgcmV0dXJuIHBvcyAqIDk2IC8gNjtcbiAgfSxcbiAgXCIlXCI6IGZ1bmN0aW9uIChwb3MsIHNpemUpIHtcbiAgICByZXR1cm4gcG9zICogc2l6ZSAvIDEwMDtcbiAgfSxcbiAgXCJ2d1wiOiBmdW5jdGlvbiAocG9zLCBzaXplKSB7XG4gICAgaWYgKHNpemUgPT09IHZvaWQgMCkge1xuICAgICAgc2l6ZSA9IHdpbmRvdy5pbm5lcldpZHRoO1xuICAgIH1cbiAgICByZXR1cm4gcG9zIC8gMTAwICogc2l6ZTtcbiAgfSxcbiAgXCJ2aFwiOiBmdW5jdGlvbiAocG9zLCBzaXplKSB7XG4gICAgaWYgKHNpemUgPT09IHZvaWQgMCkge1xuICAgICAgc2l6ZSA9IHdpbmRvdy5pbm5lckhlaWdodDtcbiAgICB9XG4gICAgcmV0dXJuIHBvcyAvIDEwMCAqIHNpemU7XG4gIH0sXG4gIFwidm1heFwiOiBmdW5jdGlvbiAocG9zLCBzaXplKSB7XG4gICAgaWYgKHNpemUgPT09IHZvaWQgMCkge1xuICAgICAgc2l6ZSA9IE1hdGgubWF4KHdpbmRvdy5pbm5lcldpZHRoLCB3aW5kb3cuaW5uZXJIZWlnaHQpO1xuICAgIH1cbiAgICByZXR1cm4gcG9zIC8gMTAwICogc2l6ZTtcbiAgfSxcbiAgXCJ2bWluXCI6IGZ1bmN0aW9uIChwb3MsIHNpemUpIHtcbiAgICBpZiAoc2l6ZSA9PT0gdm9pZCAwKSB7XG4gICAgICBzaXplID0gTWF0aC5taW4od2luZG93LmlubmVyV2lkdGgsIHdpbmRvdy5pbm5lckhlaWdodCk7XG4gICAgfVxuICAgIHJldHVybiBwb3MgLyAxMDAgKiBzaXplO1xuICB9XG59O1xuXG4vKiEgKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKipcclxuQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uXHJcblxyXG5QZXJtaXNzaW9uIHRvIHVzZSwgY29weSwgbW9kaWZ5LCBhbmQvb3IgZGlzdHJpYnV0ZSB0aGlzIHNvZnR3YXJlIGZvciBhbnlcclxucHVycG9zZSB3aXRoIG9yIHdpdGhvdXQgZmVlIGlzIGhlcmVieSBncmFudGVkLlxyXG5cclxuVEhFIFNPRlRXQVJFIElTIFBST1ZJREVEIFwiQVMgSVNcIiBBTkQgVEhFIEFVVEhPUiBESVNDTEFJTVMgQUxMIFdBUlJBTlRJRVMgV0lUSFxyXG5SRUdBUkQgVE8gVEhJUyBTT0ZUV0FSRSBJTkNMVURJTkcgQUxMIElNUExJRUQgV0FSUkFOVElFUyBPRiBNRVJDSEFOVEFCSUxJVFlcclxuQU5EIEZJVE5FU1MuIElOIE5PIEVWRU5UIFNIQUxMIFRIRSBBVVRIT1IgQkUgTElBQkxFIEZPUiBBTlkgU1BFQ0lBTCwgRElSRUNULFxyXG5JTkRJUkVDVCwgT1IgQ09OU0VRVUVOVElBTCBEQU1BR0VTIE9SIEFOWSBEQU1BR0VTIFdIQVRTT0VWRVIgUkVTVUxUSU5HIEZST01cclxuTE9TUyBPRiBVU0UsIERBVEEgT1IgUFJPRklUUywgV0hFVEhFUiBJTiBBTiBBQ1RJT04gT0YgQ09OVFJBQ1QsIE5FR0xJR0VOQ0UgT1JcclxuT1RIRVIgVE9SVElPVVMgQUNUSU9OLCBBUklTSU5HIE9VVCBPRiBPUiBJTiBDT05ORUNUSU9OIFdJVEggVEhFIFVTRSBPUlxyXG5QRVJGT1JNQU5DRSBPRiBUSElTIFNPRlRXQVJFLlxyXG4qKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKiAqL1xuZnVuY3Rpb24gX19zcHJlYWRBcnJheXMoKSB7XG4gIGZvciAodmFyIHMgPSAwLCBpID0gMCwgaWwgPSBhcmd1bWVudHMubGVuZ3RoOyBpIDwgaWw7IGkrKykgcyArPSBhcmd1bWVudHNbaV0ubGVuZ3RoO1xuICBmb3IgKHZhciByID0gQXJyYXkocyksIGsgPSAwLCBpID0gMDsgaSA8IGlsOyBpKyspIGZvciAodmFyIGEgPSBhcmd1bWVudHNbaV0sIGogPSAwLCBqbCA9IGEubGVuZ3RoOyBqIDwgamw7IGorKywgaysrKSByW2tdID0gYVtqXTtcbiAgcmV0dXJuIHI7XG59XG5cbi8qKlxuKiBAbmFtZXNwYWNlXG4qIEBuYW1lIFV0aWxzXG4qL1xuLyoqXG4gKiBSZXR1cm5zIHRoZSBpbm5lciBwcm9kdWN0IG9mIHR3byBudW1iZXJzKGBhMWAsIGBhMmApIGJ5IHR3byBjcml0ZXJpYShgYjFgLCBgYjJgKS5cbiAqIEBtZW1iZXJvZiBVdGlsc1xuICogQHBhcmFtIC0gVGhlIGZpcnN0IG51bWJlclxuICogQHBhcmFtIC0gVGhlIHNlY29uZCBudW1iZXJcbiAqIEBwYXJhbSAtIFRoZSBmaXJzdCBudW1iZXIgdG8gYmFzZSBvbiB0aGUgaW5uZXIgcHJvZHVjdFxuICogQHBhcmFtIC0gVGhlIHNlY29uZCBudW1iZXIgdG8gYmFzZSBvbiB0aGUgaW5uZXIgcHJvZHVjdFxuICogQHJldHVybiAtIFJldHVybnMgdGhlIGlubmVyIHByb2R1Y3RcbmltcG9ydCB7IGRvdCB9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coZG90KDAsIDE1LCAyLCAzKSk7IC8vIDZcbmNvbnNvbGUubG9nKGRvdCg1LCAxNSwgMiwgMykpOyAvLyA5XG5jb25zb2xlLmxvZyhkb3QoNSwgMTUsIDEsIDEpKTsgLy8gMTBcbiAqL1xuZnVuY3Rpb24gZG90KGExLCBhMiwgYjEsIGIyKSB7XG4gIHJldHVybiAoYTEgKiBiMiArIGEyICogYjEpIC8gKGIxICsgYjIpO1xufVxuLyoqXG4qIENoZWNrIHRoZSB0eXBlIHRoYXQgdGhlIHZhbHVlIGlzIHVuZGVmaW5lZC5cbiogQG1lbWJlcm9mIFV0aWxzXG4qIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZSAtIFZhbHVlIHRvIGNoZWNrIHRoZSB0eXBlXG4qIEByZXR1cm4ge2Jvb2xlYW59IHRydWUgaWYgdGhlIHR5cGUgaXMgY29ycmVjdCwgZmFsc2Ugb3RoZXJ3aXNlXG4qIEBleGFtcGxlXG5pbXBvcnQge2lzVW5kZWZpbmVkfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKGlzVW5kZWZpbmVkKHVuZGVmaW5lZCkpOyAvLyB0cnVlXG5jb25zb2xlLmxvZyhpc1VuZGVmaW5lZChcIlwiKSk7IC8vIGZhbHNlXG5jb25zb2xlLmxvZyhpc1VuZGVmaW5lZCgxKSk7IC8vIGZhbHNlXG5jb25zb2xlLmxvZyhpc1VuZGVmaW5lZChudWxsKSk7IC8vIGZhbHNlXG4qL1xuZnVuY3Rpb24gaXNVbmRlZmluZWQodmFsdWUpIHtcbiAgcmV0dXJuIHR5cGVvZiB2YWx1ZSA9PT0gVU5ERUZJTkVEO1xufVxuLyoqXG4qIENoZWNrIHRoZSB0eXBlIHRoYXQgdGhlIHZhbHVlIGlzIG9iamVjdC5cbiogQG1lbWJlcm9mIFV0aWxzXG4qIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZSAtIFZhbHVlIHRvIGNoZWNrIHRoZSB0eXBlXG4qIEByZXR1cm4ge30gdHJ1ZSBpZiB0aGUgdHlwZSBpcyBjb3JyZWN0LCBmYWxzZSBvdGhlcndpc2VcbiogQGV4YW1wbGVcbmltcG9ydCB7aXNPYmplY3R9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coaXNPYmplY3Qoe30pKTsgLy8gdHJ1ZVxuY29uc29sZS5sb2coaXNPYmplY3QodW5kZWZpbmVkKSk7IC8vIGZhbHNlXG5jb25zb2xlLmxvZyhpc09iamVjdChcIlwiKSk7IC8vIGZhbHNlXG5jb25zb2xlLmxvZyhpc09iamVjdChudWxsKSk7IC8vIGZhbHNlXG4qL1xuZnVuY3Rpb24gaXNPYmplY3QodmFsdWUpIHtcbiAgcmV0dXJuIHZhbHVlICYmIHR5cGVvZiB2YWx1ZSA9PT0gT0JKRUNUO1xufVxuLyoqXG4qIENoZWNrIHRoZSB0eXBlIHRoYXQgdGhlIHZhbHVlIGlzIGlzQXJyYXkuXG4qIEBtZW1iZXJvZiBVdGlsc1xuKiBAcGFyYW0ge3N0cmluZ30gdmFsdWUgLSBWYWx1ZSB0byBjaGVjayB0aGUgdHlwZVxuKiBAcmV0dXJuIHt9IHRydWUgaWYgdGhlIHR5cGUgaXMgY29ycmVjdCwgZmFsc2Ugb3RoZXJ3aXNlXG4qIEBleGFtcGxlXG5pbXBvcnQge2lzQXJyYXl9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coaXNBcnJheShbXSkpOyAvLyB0cnVlXG5jb25zb2xlLmxvZyhpc0FycmF5KHt9KSk7IC8vIGZhbHNlXG5jb25zb2xlLmxvZyhpc0FycmF5KHVuZGVmaW5lZCkpOyAvLyBmYWxzZVxuY29uc29sZS5sb2coaXNBcnJheShudWxsKSk7IC8vIGZhbHNlXG4qL1xuZnVuY3Rpb24gaXNBcnJheSh2YWx1ZSkge1xuICByZXR1cm4gQXJyYXkuaXNBcnJheSh2YWx1ZSk7XG59XG4vKipcbiogQ2hlY2sgdGhlIHR5cGUgdGhhdCB0aGUgdmFsdWUgaXMgc3RyaW5nLlxuKiBAbWVtYmVyb2YgVXRpbHNcbiogQHBhcmFtIHtzdHJpbmd9IHZhbHVlIC0gVmFsdWUgdG8gY2hlY2sgdGhlIHR5cGVcbiogQHJldHVybiB7fSB0cnVlIGlmIHRoZSB0eXBlIGlzIGNvcnJlY3QsIGZhbHNlIG90aGVyd2lzZVxuKiBAZXhhbXBsZVxuaW1wb3J0IHtpc1N0cmluZ30gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5jb25zb2xlLmxvZyhpc1N0cmluZyhcIjEyMzRcIikpOyAvLyB0cnVlXG5jb25zb2xlLmxvZyhpc1N0cmluZyh1bmRlZmluZWQpKTsgLy8gZmFsc2VcbmNvbnNvbGUubG9nKGlzU3RyaW5nKDEpKTsgLy8gZmFsc2VcbmNvbnNvbGUubG9nKGlzU3RyaW5nKG51bGwpKTsgLy8gZmFsc2VcbiovXG5mdW5jdGlvbiBpc1N0cmluZyh2YWx1ZSkge1xuICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSBTVFJJTkc7XG59XG5mdW5jdGlvbiBpc051bWJlcih2YWx1ZSkge1xuICByZXR1cm4gdHlwZW9mIHZhbHVlID09PSBOVU1CRVI7XG59XG4vKipcbiogQ2hlY2sgdGhlIHR5cGUgdGhhdCB0aGUgdmFsdWUgaXMgZnVuY3Rpb24uXG4qIEBtZW1iZXJvZiBVdGlsc1xuKiBAcGFyYW0ge3N0cmluZ30gdmFsdWUgLSBWYWx1ZSB0byBjaGVjayB0aGUgdHlwZVxuKiBAcmV0dXJuIHt9IHRydWUgaWYgdGhlIHR5cGUgaXMgY29ycmVjdCwgZmFsc2Ugb3RoZXJ3aXNlXG4qIEBleGFtcGxlXG5pbXBvcnQge2lzRnVuY3Rpb259IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coaXNGdW5jdGlvbihmdW5jdGlvbiBhKCkge30pKTsgLy8gdHJ1ZVxuY29uc29sZS5sb2coaXNGdW5jdGlvbigoKSA9PiB7fSkpOyAvLyB0cnVlXG5jb25zb2xlLmxvZyhpc0Z1bmN0aW9uKFwiMTIzNFwiKSk7IC8vIGZhbHNlXG5jb25zb2xlLmxvZyhpc0Z1bmN0aW9uKDEpKTsgLy8gZmFsc2VcbmNvbnNvbGUubG9nKGlzRnVuY3Rpb24obnVsbCkpOyAvLyBmYWxzZVxuKi9cbmZ1bmN0aW9uIGlzRnVuY3Rpb24odmFsdWUpIHtcbiAgcmV0dXJuIHR5cGVvZiB2YWx1ZSA9PT0gRlVOQ1RJT047XG59XG5mdW5jdGlvbiBpc0VxdWFsU2VwYXJhdG9yKGNoYXJhY3Rlciwgc2VwYXJhdG9yKSB7XG4gIHZhciBpc0NoYXJhY3RlclNwYWNlID0gY2hhcmFjdGVyID09PSBcIlwiIHx8IGNoYXJhY3RlciA9PSBcIiBcIjtcbiAgdmFyIGlzU2VwYXJhdG9yU3BhY2UgPSBzZXBhcmF0b3IgPT09IFwiXCIgfHwgc2VwYXJhdG9yID09IFwiIFwiO1xuICByZXR1cm4gaXNTZXBhcmF0b3JTcGFjZSAmJiBpc0NoYXJhY3RlclNwYWNlIHx8IGNoYXJhY3RlciA9PT0gc2VwYXJhdG9yO1xufVxuZnVuY3Rpb24gZmluZE9wZW4ob3BlbkNoYXJhY3RlciwgdGV4dHMsIGluZGV4LCBsZW5ndGgsIG9wZW5DbG9zZUNoYXJhY3RlcnMpIHtcbiAgdmFyIGlzSWdub3JlID0gZmluZElnbm9yZShvcGVuQ2hhcmFjdGVyLCB0ZXh0cywgaW5kZXgpO1xuICBpZiAoIWlzSWdub3JlKSB7XG4gICAgcmV0dXJuIGZpbmRDbG9zZShvcGVuQ2hhcmFjdGVyLCB0ZXh0cywgaW5kZXggKyAxLCBsZW5ndGgsIG9wZW5DbG9zZUNoYXJhY3RlcnMpO1xuICB9XG4gIHJldHVybiBpbmRleDtcbn1cbmZ1bmN0aW9uIGZpbmRJZ25vcmUoY2hhcmFjdGVyLCB0ZXh0cywgaW5kZXgpIHtcbiAgaWYgKCFjaGFyYWN0ZXIuaWdub3JlKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgdmFyIG90aGVyVGV4dCA9IHRleHRzLnNsaWNlKE1hdGgubWF4KGluZGV4IC0gMywgMCksIGluZGV4ICsgMykuam9pbihcIlwiKTtcbiAgcmV0dXJuIG5ldyBSZWdFeHAoY2hhcmFjdGVyLmlnbm9yZSkuZXhlYyhvdGhlclRleHQpO1xufVxuZnVuY3Rpb24gZmluZENsb3NlKGNsb3NlQ2hhcmFjdGVyLCB0ZXh0cywgaW5kZXgsIGxlbmd0aCwgb3BlbkNsb3NlQ2hhcmFjdGVycykge1xuICB2YXIgX2xvb3BfMSA9IGZ1bmN0aW9uIChpKSB7XG4gICAgdmFyIGNoYXJhY3RlciA9IHRleHRzW2ldLnRyaW0oKTtcbiAgICBpZiAoY2hhcmFjdGVyID09PSBjbG9zZUNoYXJhY3Rlci5jbG9zZSAmJiAhZmluZElnbm9yZShjbG9zZUNoYXJhY3RlciwgdGV4dHMsIGkpKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICB2YWx1ZTogaVxuICAgICAgfTtcbiAgICB9XG4gICAgdmFyIG5leHRJbmRleCA9IGk7XG4gICAgLy8gcmUgb3BlblxuICAgIHZhciBvcGVuQ2hhcmFjdGVyID0gZmluZChvcGVuQ2xvc2VDaGFyYWN0ZXJzLCBmdW5jdGlvbiAoX2EpIHtcbiAgICAgIHZhciBvcGVuID0gX2Eub3BlbjtcbiAgICAgIHJldHVybiBvcGVuID09PSBjaGFyYWN0ZXI7XG4gICAgfSk7XG4gICAgaWYgKG9wZW5DaGFyYWN0ZXIpIHtcbiAgICAgIG5leHRJbmRleCA9IGZpbmRPcGVuKG9wZW5DaGFyYWN0ZXIsIHRleHRzLCBpLCBsZW5ndGgsIG9wZW5DbG9zZUNoYXJhY3RlcnMpO1xuICAgIH1cbiAgICBpZiAobmV4dEluZGV4ID09PSAtMSkge1xuICAgICAgcmV0dXJuIG91dF9pXzEgPSBpLCBcImJyZWFrXCI7XG4gICAgfVxuICAgIGkgPSBuZXh0SW5kZXg7XG4gICAgb3V0X2lfMSA9IGk7XG4gIH07XG4gIHZhciBvdXRfaV8xO1xuICBmb3IgKHZhciBpID0gaW5kZXg7IGkgPCBsZW5ndGg7ICsraSkge1xuICAgIHZhciBzdGF0ZV8xID0gX2xvb3BfMShpKTtcbiAgICBpID0gb3V0X2lfMTtcbiAgICBpZiAodHlwZW9mIHN0YXRlXzEgPT09IFwib2JqZWN0XCIpIHJldHVybiBzdGF0ZV8xLnZhbHVlO1xuICAgIGlmIChzdGF0ZV8xID09PSBcImJyZWFrXCIpIGJyZWFrO1xuICB9XG4gIHJldHVybiAtMTtcbn1cbmZ1bmN0aW9uIHNwbGl0VGV4dCh0ZXh0LCBzcGxpdE9wdGlvbnMpIHtcbiAgdmFyIF9hID0gaXNTdHJpbmcoc3BsaXRPcHRpb25zKSA/IHtcbiAgICAgIHNlcGFyYXRvcjogc3BsaXRPcHRpb25zXG4gICAgfSA6IHNwbGl0T3B0aW9ucyxcbiAgICBfYiA9IF9hLnNlcGFyYXRvcixcbiAgICBzZXBhcmF0b3IgPSBfYiA9PT0gdm9pZCAwID8gXCIsXCIgOiBfYixcbiAgICBpc1NlcGFyYXRlRmlyc3QgPSBfYS5pc1NlcGFyYXRlRmlyc3QsXG4gICAgaXNTZXBhcmF0ZU9ubHlPcGVuQ2xvc2UgPSBfYS5pc1NlcGFyYXRlT25seU9wZW5DbG9zZSxcbiAgICBfYyA9IF9hLmlzU2VwYXJhdGVPcGVuQ2xvc2UsXG4gICAgaXNTZXBhcmF0ZU9wZW5DbG9zZSA9IF9jID09PSB2b2lkIDAgPyBpc1NlcGFyYXRlT25seU9wZW5DbG9zZSA6IF9jLFxuICAgIF9kID0gX2Eub3BlbkNsb3NlQ2hhcmFjdGVycyxcbiAgICBvcGVuQ2xvc2VDaGFyYWN0ZXJzID0gX2QgPT09IHZvaWQgMCA/IE9QRU5fQ0xPU0VEX0NIQVJBQ1RFUlMgOiBfZDtcbiAgdmFyIG9wZW5DbG9zZWRUZXh0ID0gb3BlbkNsb3NlQ2hhcmFjdGVycy5tYXAoZnVuY3Rpb24gKF9hKSB7XG4gICAgdmFyIG9wZW4gPSBfYS5vcGVuLFxuICAgICAgY2xvc2UgPSBfYS5jbG9zZTtcbiAgICBpZiAob3BlbiA9PT0gY2xvc2UpIHtcbiAgICAgIHJldHVybiBvcGVuO1xuICAgIH1cbiAgICByZXR1cm4gb3BlbiArIFwifFwiICsgY2xvc2U7XG4gIH0pLmpvaW4oXCJ8XCIpO1xuICB2YXIgcmVnZXhUZXh0ID0gXCIoXFxcXHMqXCIgKyBzZXBhcmF0b3IgKyBcIlxcXFxzKnxcIiArIG9wZW5DbG9zZWRUZXh0ICsgXCJ8XFxcXHMrKVwiO1xuICB2YXIgcmVnZXggPSBuZXcgUmVnRXhwKHJlZ2V4VGV4dCwgXCJnXCIpO1xuICB2YXIgdGV4dHMgPSB0ZXh0LnNwbGl0KHJlZ2V4KS5maWx0ZXIoZnVuY3Rpb24gKGNocikge1xuICAgIHJldHVybiBjaHIgJiYgY2hyICE9PSBcInVuZGVmaW5lZFwiO1xuICB9KTtcbiAgdmFyIGxlbmd0aCA9IHRleHRzLmxlbmd0aDtcbiAgdmFyIHZhbHVlcyA9IFtdO1xuICB2YXIgdGVtcFZhbHVlcyA9IFtdO1xuICBmdW5jdGlvbiByZXNldFRlbXAoKSB7XG4gICAgaWYgKHRlbXBWYWx1ZXMubGVuZ3RoKSB7XG4gICAgICB2YWx1ZXMucHVzaCh0ZW1wVmFsdWVzLmpvaW4oXCJcIikpO1xuICAgICAgdGVtcFZhbHVlcyA9IFtdO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICB2YXIgX2xvb3BfMiA9IGZ1bmN0aW9uIChpKSB7XG4gICAgdmFyIGNoYXJhY3RlciA9IHRleHRzW2ldLnRyaW0oKTtcbiAgICB2YXIgbmV4dEluZGV4ID0gaTtcbiAgICB2YXIgb3BlbkNoYXJhY3RlciA9IGZpbmQob3BlbkNsb3NlQ2hhcmFjdGVycywgZnVuY3Rpb24gKF9hKSB7XG4gICAgICB2YXIgb3BlbiA9IF9hLm9wZW47XG4gICAgICByZXR1cm4gb3BlbiA9PT0gY2hhcmFjdGVyO1xuICAgIH0pO1xuICAgIHZhciBjbG9zZUNoYXJhY3RlciA9IGZpbmQob3BlbkNsb3NlQ2hhcmFjdGVycywgZnVuY3Rpb24gKF9hKSB7XG4gICAgICB2YXIgY2xvc2UgPSBfYS5jbG9zZTtcbiAgICAgIHJldHVybiBjbG9zZSA9PT0gY2hhcmFjdGVyO1xuICAgIH0pO1xuICAgIGlmIChvcGVuQ2hhcmFjdGVyKSB7XG4gICAgICBuZXh0SW5kZXggPSBmaW5kT3BlbihvcGVuQ2hhcmFjdGVyLCB0ZXh0cywgaSwgbGVuZ3RoLCBvcGVuQ2xvc2VDaGFyYWN0ZXJzKTtcbiAgICAgIGlmIChuZXh0SW5kZXggIT09IC0xICYmIGlzU2VwYXJhdGVPcGVuQ2xvc2UpIHtcbiAgICAgICAgaWYgKHJlc2V0VGVtcCgpICYmIGlzU2VwYXJhdGVGaXJzdCkge1xuICAgICAgICAgIHJldHVybiBvdXRfaV8yID0gaSwgXCJicmVha1wiO1xuICAgICAgICB9XG4gICAgICAgIHZhbHVlcy5wdXNoKHRleHRzLnNsaWNlKGksIG5leHRJbmRleCArIDEpLmpvaW4oXCJcIikpO1xuICAgICAgICBpID0gbmV4dEluZGV4O1xuICAgICAgICBpZiAoaXNTZXBhcmF0ZUZpcnN0KSB7XG4gICAgICAgICAgcmV0dXJuIG91dF9pXzIgPSBpLCBcImJyZWFrXCI7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG91dF9pXzIgPSBpLCBcImNvbnRpbnVlXCI7XG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChjbG9zZUNoYXJhY3RlciAmJiAhZmluZElnbm9yZShjbG9zZUNoYXJhY3RlciwgdGV4dHMsIGkpKSB7XG4gICAgICB2YXIgbmV4dE9wZW5DbG9zZUNoYXJhY3RlcnMgPSBfX3NwcmVhZEFycmF5cyhvcGVuQ2xvc2VDaGFyYWN0ZXJzKTtcbiAgICAgIG5leHRPcGVuQ2xvc2VDaGFyYWN0ZXJzLnNwbGljZShvcGVuQ2xvc2VDaGFyYWN0ZXJzLmluZGV4T2YoY2xvc2VDaGFyYWN0ZXIpLCAxKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHZhbHVlOiBzcGxpdFRleHQodGV4dCwge1xuICAgICAgICAgIHNlcGFyYXRvcjogc2VwYXJhdG9yLFxuICAgICAgICAgIGlzU2VwYXJhdGVGaXJzdDogaXNTZXBhcmF0ZUZpcnN0LFxuICAgICAgICAgIGlzU2VwYXJhdGVPbmx5T3BlbkNsb3NlOiBpc1NlcGFyYXRlT25seU9wZW5DbG9zZSxcbiAgICAgICAgICBpc1NlcGFyYXRlT3BlbkNsb3NlOiBpc1NlcGFyYXRlT3BlbkNsb3NlLFxuICAgICAgICAgIG9wZW5DbG9zZUNoYXJhY3RlcnM6IG5leHRPcGVuQ2xvc2VDaGFyYWN0ZXJzXG4gICAgICAgIH0pXG4gICAgICB9O1xuICAgIH0gZWxzZSBpZiAoaXNFcXVhbFNlcGFyYXRvcihjaGFyYWN0ZXIsIHNlcGFyYXRvcikgJiYgIWlzU2VwYXJhdGVPbmx5T3BlbkNsb3NlKSB7XG4gICAgICByZXNldFRlbXAoKTtcbiAgICAgIGlmIChpc1NlcGFyYXRlRmlyc3QpIHtcbiAgICAgICAgcmV0dXJuIG91dF9pXzIgPSBpLCBcImJyZWFrXCI7XG4gICAgICB9XG4gICAgICByZXR1cm4gb3V0X2lfMiA9IGksIFwiY29udGludWVcIjtcbiAgICB9XG4gICAgaWYgKG5leHRJbmRleCA9PT0gLTEpIHtcbiAgICAgIG5leHRJbmRleCA9IGxlbmd0aCAtIDE7XG4gICAgfVxuICAgIHRlbXBWYWx1ZXMucHVzaCh0ZXh0cy5zbGljZShpLCBuZXh0SW5kZXggKyAxKS5qb2luKFwiXCIpKTtcbiAgICBpID0gbmV4dEluZGV4O1xuICAgIG91dF9pXzIgPSBpO1xuICB9O1xuICB2YXIgb3V0X2lfMjtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBsZW5ndGg7ICsraSkge1xuICAgIHZhciBzdGF0ZV8yID0gX2xvb3BfMihpKTtcbiAgICBpID0gb3V0X2lfMjtcbiAgICBpZiAodHlwZW9mIHN0YXRlXzIgPT09IFwib2JqZWN0XCIpIHJldHVybiBzdGF0ZV8yLnZhbHVlO1xuICAgIGlmIChzdGF0ZV8yID09PSBcImJyZWFrXCIpIGJyZWFrO1xuICB9XG4gIGlmICh0ZW1wVmFsdWVzLmxlbmd0aCkge1xuICAgIHZhbHVlcy5wdXNoKHRlbXBWYWx1ZXMuam9pbihcIlwiKSk7XG4gIH1cbiAgcmV0dXJuIHZhbHVlcztcbn1cbi8qKlxuKiBkaXZpZGUgdGV4dCBieSBzcGFjZS5cbiogQG1lbWJlcm9mIFV0aWxzXG4qIEBwYXJhbSB7c3RyaW5nfSB0ZXh0IC0gdGV4dCB0byBkaXZpZGVcbiogQHJldHVybiB7QXJyYXl9IGRpdmlkZWQgdGV4dHNcbiogQGV4YW1wbGVcbmltcG9ydCB7c3BsaWNlU3BhY2V9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coc3BsaXRTcGFjZShcImEgYiBjIGQgZSBmIGdcIikpO1xuLy8gW1wiYVwiLCBcImJcIiwgXCJjXCIsIFwiZFwiLCBcImVcIiwgXCJmXCIsIFwiZ1wiXVxuY29uc29sZS5sb2coc3BsaXRTcGFjZShcIidhLGInIGMgJ2QsZScgZiBnXCIpKTtcbi8vIFtcIidhLGInXCIsIFwiY1wiLCBcIidkLGUnXCIsIFwiZlwiLCBcImdcIl1cbiovXG5mdW5jdGlvbiBzcGxpdFNwYWNlKHRleHQpIHtcbiAgLy8gZGl2aWRlIGNvbW1hKHNwYWNlKVxuICByZXR1cm4gc3BsaXRUZXh0KHRleHQsIFwiXCIpO1xufVxuLyoqXG4qIGRpdmlkZSB0ZXh0IGJ5IGNvbW1hLlxuKiBAbWVtYmVyb2YgVXRpbHNcbiogQHBhcmFtIHtzdHJpbmd9IHRleHQgLSB0ZXh0IHRvIGRpdmlkZVxuKiBAcmV0dXJuIHtBcnJheX0gZGl2aWRlZCB0ZXh0c1xuKiBAZXhhbXBsZVxuaW1wb3J0IHtzcGxpdENvbW1hfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKHNwbGl0Q29tbWEoXCJhLGIsYyxkLGUsZixnXCIpKTtcbi8vIFtcImFcIiwgXCJiXCIsIFwiY1wiLCBcImRcIiwgXCJlXCIsIFwiZlwiLCBcImdcIl1cbmNvbnNvbGUubG9nKHNwbGl0Q29tbWEoXCInYSxiJyxjLCdkLGUnLGYsZ1wiKSk7XG4vLyBbXCInYSxiJ1wiLCBcImNcIiwgXCInZCxlJ1wiLCBcImZcIiwgXCJnXCJdXG4qL1xuZnVuY3Rpb24gc3BsaXRDb21tYSh0ZXh0KSB7XG4gIC8vIGRpdmlkZSBjb21tYSgsKVxuICAvLyBcIlteXCJdKlwifCdbXiddKidcbiAgcmV0dXJuIHNwbGl0VGV4dCh0ZXh0LCBcIixcIik7XG59XG4vKipcbiogZGl2aWRlIHRleHQgYnkgYnJhY2tldCBcIihcIiwgXCIpXCIuXG4qIEBtZW1iZXJvZiBVdGlsc1xuKiBAcGFyYW0ge3N0cmluZ30gdGV4dCAtIHRleHQgdG8gZGl2aWRlXG4qIEByZXR1cm4ge29iamVjdH0gZGl2aWRlZCB0ZXh0c1xuKiBAZXhhbXBsZVxuaW1wb3J0IHtzcGxpdEJyYWNrZXR9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coc3BsaXRCcmFja2V0KFwiYSgxLCAyKVwiKSk7XG4vLyB7cHJlZml4OiBcImFcIiwgdmFsdWU6IFwiMSwgMlwiLCBzdWZmaXg6IFwiXCJ9XG5jb25zb2xlLmxvZyhzcGxpdEJyYWNrZXQoXCJhKDEsIDIpYlwiKSk7XG4vLyB7cHJlZml4OiBcImFcIiwgdmFsdWU6IFwiMSwgMlwiLCBzdWZmaXg6IFwiYlwifVxuKi9cbmZ1bmN0aW9uIHNwbGl0QnJhY2tldCh0ZXh0KSB7XG4gIHZhciBtYXRjaGVzID0gLyhbXihdKilcXCgoW1xcc1xcU10qKVxcKShbXFxzXFxTXSopL2cuZXhlYyh0ZXh0KTtcbiAgaWYgKCFtYXRjaGVzIHx8IG1hdGNoZXMubGVuZ3RoIDwgNCkge1xuICAgIHJldHVybiB7fTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4ge1xuICAgICAgcHJlZml4OiBtYXRjaGVzWzFdLFxuICAgICAgdmFsdWU6IG1hdGNoZXNbMl0sXG4gICAgICBzdWZmaXg6IG1hdGNoZXNbM11cbiAgICB9O1xuICB9XG59XG4vKipcbiogZGl2aWRlIHRleHQgYnkgbnVtYmVyIGFuZCB1bml0LlxuKiBAbWVtYmVyb2YgVXRpbHNcbiogQHBhcmFtIHtzdHJpbmd9IHRleHQgLSB0ZXh0IHRvIGRpdmlkZVxuKiBAcmV0dXJuIHt9IGRpdmlkZWQgdGV4dHNcbiogQGV4YW1wbGVcbmltcG9ydCB7c3BsaXRVbml0fSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKHNwbGl0VW5pdChcIjEwcHhcIikpO1xuLy8ge3ByZWZpeDogXCJcIiwgdmFsdWU6IDEwLCB1bml0OiBcInB4XCJ9XG5jb25zb2xlLmxvZyhzcGxpdFVuaXQoXCItMTBweFwiKSk7XG4vLyB7cHJlZml4OiBcIlwiLCB2YWx1ZTogLTEwLCB1bml0OiBcInB4XCJ9XG5jb25zb2xlLmxvZyhzcGxpdFVuaXQoXCJhMTAlXCIpKTtcbi8vIHtwcmVmaXg6IFwiYVwiLCB2YWx1ZTogMTAsIHVuaXQ6IFwiJVwifVxuKi9cbmZ1bmN0aW9uIHNwbGl0VW5pdCh0ZXh0KSB7XG4gIHZhciBtYXRjaGVzID0gL14oW15cXGR8ZXxcXC18XFwrXSopKCg/OlxcZHxcXC58LXxlLXxlXFwrKSspKFxcUyopJC9nLmV4ZWModGV4dCk7XG4gIGlmICghbWF0Y2hlcykge1xuICAgIHJldHVybiB7XG4gICAgICBwcmVmaXg6IFwiXCIsXG4gICAgICB1bml0OiBcIlwiLFxuICAgICAgdmFsdWU6IE5hTlxuICAgIH07XG4gIH1cbiAgdmFyIHByZWZpeCA9IG1hdGNoZXNbMV07XG4gIHZhciB2YWx1ZSA9IG1hdGNoZXNbMl07XG4gIHZhciB1bml0ID0gbWF0Y2hlc1szXTtcbiAgcmV0dXJuIHtcbiAgICBwcmVmaXg6IHByZWZpeCxcbiAgICB1bml0OiB1bml0LFxuICAgIHZhbHVlOiBwYXJzZUZsb2F0KHZhbHVlKVxuICB9O1xufVxuLyoqXG4qIHRyYW5zZm9ybSBzdHJpbmdzIHRvIGNhbWVsLWNhc2VcbiogQG1lbWJlcm9mIFV0aWxzXG4qIEBwYXJhbSB7U3RyaW5nfSB0ZXh0IC0gc3RyaW5nXG4qIEByZXR1cm4ge1N0cmluZ30gY2FtZWwtY2FzZSBzdHJpbmdcbiogQGV4YW1wbGVcbmltcG9ydCB7Y2FtZWxpemV9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coY2FtZWxpemUoXCJ0cmFuc2Zvcm0tb3JpZ2luXCIpKTsgLy8gdHJhbnNmb3JtT3JpZ2luXG5jb25zb2xlLmxvZyhjYW1lbGl6ZShcImFiY2RfZWZnXCIpKTsgLy8gYWJjZEVmZ1xuY29uc29sZS5sb2coY2FtZWxpemUoXCJhYmNkIGVmZ1wiKSk7IC8vIGFiY2RFZmdcbiovXG5mdW5jdGlvbiBjYW1lbGl6ZShzdHIpIHtcbiAgcmV0dXJuIHN0ci5yZXBsYWNlKC9bXFxzLV9dKyhbXlxccy1fXSkvZywgZnVuY3Rpb24gKGFsbCwgbGV0dGVyKSB7XG4gICAgcmV0dXJuIGxldHRlci50b1VwcGVyQ2FzZSgpO1xuICB9KTtcbn1cbi8qKlxuKiB0cmFuc2Zvcm0gYSBjYW1lbGl6ZWQgc3RyaW5nIGludG8gYSBsb3dlcmNhc2VkIHN0cmluZy5cbiogQG1lbWJlcm9mIFV0aWxzXG4qIEBwYXJhbSB7c3RyaW5nfSB0ZXh0IC0gYSBjYW1lbC1jYXNlZCBzdHJpbmdcbiogQHBhcmFtIHtzdHJpbmd9IFtzZXBhcmF0b3I9XCItXCJdIC0gYSBzZXBhcmF0b3JcbiogQHJldHVybiB7c3RyaW5nfSAgYSBsb3dlcmNhc2VkIHN0cmluZ1xuKiBAZXhhbXBsZVxuaW1wb3J0IHtkZWNhbWVsaXplfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKGRlY2FtZWxpemUoXCJ0cmFuc2Zvcm1PcmlnaW5cIikpOyAvLyB0cmFuc2Zvcm0tb3JpZ2luXG5jb25zb2xlLmxvZyhkZWNhbWVsaXplKFwiYWJjZEVmZ1wiLCBcIl9cIikpOyAvLyBhYmNkX2VmZ1xuKi9cbmZ1bmN0aW9uIGRlY2FtZWxpemUoc3RyLCBzZXBhcmF0b3IpIHtcbiAgaWYgKHNlcGFyYXRvciA9PT0gdm9pZCAwKSB7XG4gICAgc2VwYXJhdG9yID0gXCItXCI7XG4gIH1cbiAgcmV0dXJuIHN0ci5yZXBsYWNlKC8oW2Etel0pKFtBLVpdKS9nLCBmdW5jdGlvbiAoYWxsLCBsZXR0ZXIsIGxldHRlcjIpIHtcbiAgICByZXR1cm4gXCJcIiArIGxldHRlciArIHNlcGFyYXRvciArIGxldHRlcjIudG9Mb3dlckNhc2UoKTtcbiAgfSk7XG59XG4vKipcbiogdHJhbnNmb3JtcyBzb21ldGhpbmcgaW4gYW4gYXJyYXkgaW50byBhbiBhcnJheS5cbiogQG1lbWJlcm9mIFV0aWxzXG4qIEBwYXJhbSAtIEFycmF5IGZvcm1cbiogQHJldHVybiBhbiBhcnJheVxuKiBAZXhhbXBsZVxuaW1wb3J0IHt0b0FycmF5fSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnN0IGFycjEgPSB0b0FycmF5KGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoXCIuYVwiKSk7IC8vIEVsZW1lbnRbXVxuY29uc3QgYXJyMiA9IHRvQXJyYXkoZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbDxIVE1MRWxlbWVudD4oXCIuYVwiKSk7IC8vIEhUTUxFbGVtZW50W11cbiovXG5mdW5jdGlvbiB0b0FycmF5KHZhbHVlKSB7XG4gIHJldHVybiBbXS5zbGljZS5jYWxsKHZhbHVlKTtcbn1cbi8qKlxuKiBEYXRlLm5vdygpIG1ldGhvZFxuKiBAbWVtYmVyb2YgQ3Jvc3NCcm93c2VyXG4qIEByZXR1cm4ge251bWJlcn0gbWlsbGlzZWNvbmRzXG4qIEBleGFtcGxlXG5pbXBvcnQge25vd30gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5jb25zb2xlLmxvZyhub3coKSk7IC8vIDEyMTIxMzI0MjQxKG1pbGxpc2Vjb25kcylcbiovXG5mdW5jdGlvbiBub3coKSB7XG4gIHJldHVybiBEYXRlLm5vdyA/IERhdGUubm93KCkgOiBuZXcgRGF0ZSgpLmdldFRpbWUoKTtcbn1cbi8qKlxuKiBSZXR1cm5zIHRoZSBpbmRleCBvZiB0aGUgZmlyc3QgZWxlbWVudCBpbiB0aGUgYXJyYXkgdGhhdCBzYXRpc2ZpZXMgdGhlIHByb3ZpZGVkIHRlc3RpbmcgZnVuY3Rpb24uXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgQ3Jvc3NCcm93c2VyXG4qIEBwYXJhbSAtIFRoZSBhcnJheSBgZmluZEluZGV4YCB3YXMgY2FsbGVkIHVwb24uXG4qIEBwYXJhbSAtIEEgZnVuY3Rpb24gdG8gZXhlY3V0ZSBvbiBlYWNoIHZhbHVlIGluIHRoZSBhcnJheSB1bnRpbCB0aGUgZnVuY3Rpb24gcmV0dXJucyB0cnVlLCBpbmRpY2F0aW5nIHRoYXQgdGhlIHNhdGlzZnlpbmcgZWxlbWVudCB3YXMgZm91bmQuXG4qIEBwYXJhbSAtIFJldHVybnMgZGVmYXVsdEluZGV4IGlmIG5vdCBmb3VuZCBieSB0aGUgZnVuY3Rpb24uXG4qIEBleGFtcGxlXG5pbXBvcnQgeyBmaW5kSW5kZXggfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmZpbmRJbmRleChbe2E6IDF9LCB7YTogMn0sIHthOiAzfSwge2E6IDR9XSwgKHsgYSB9KSA9PiBhID09PSAyKTsgLy8gMVxuKi9cbmZ1bmN0aW9uIGZpbmRJbmRleChhcnIsIGNhbGxiYWNrLCBkZWZhdWx0SW5kZXgpIHtcbiAgaWYgKGRlZmF1bHRJbmRleCA9PT0gdm9pZCAwKSB7XG4gICAgZGVmYXVsdEluZGV4ID0gLTE7XG4gIH1cbiAgdmFyIGxlbmd0aCA9IGFyci5sZW5ndGg7XG4gIGZvciAodmFyIGkgPSAwOyBpIDwgbGVuZ3RoOyArK2kpIHtcbiAgICBpZiAoY2FsbGJhY2soYXJyW2ldLCBpLCBhcnIpKSB7XG4gICAgICByZXR1cm4gaTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGRlZmF1bHRJbmRleDtcbn1cbi8qKlxuKiBSZXR1cm5zIHRoZSByZXZlcnNlIGRpcmVjdGlvbiBpbmRleCBvZiB0aGUgZmlyc3QgZWxlbWVudCBpbiB0aGUgYXJyYXkgdGhhdCBzYXRpc2ZpZXMgdGhlIHByb3ZpZGVkIHRlc3RpbmcgZnVuY3Rpb24uXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgQ3Jvc3NCcm93c2VyXG4qIEBwYXJhbSAtIFRoZSBhcnJheSBgZmluZExhc3RJbmRleGAgd2FzIGNhbGxlZCB1cG9uLlxuKiBAcGFyYW0gLSBBIGZ1bmN0aW9uIHRvIGV4ZWN1dGUgb24gZWFjaCB2YWx1ZSBpbiB0aGUgYXJyYXkgdW50aWwgdGhlIGZ1bmN0aW9uIHJldHVybnMgdHJ1ZSwgaW5kaWNhdGluZyB0aGF0IHRoZSBzYXRpc2Z5aW5nIGVsZW1lbnQgd2FzIGZvdW5kLlxuKiBAcGFyYW0gLSBSZXR1cm5zIGRlZmF1bHRJbmRleCBpZiBub3QgZm91bmQgYnkgdGhlIGZ1bmN0aW9uLlxuKiBAZXhhbXBsZVxuaW1wb3J0IHsgZmluZExhc3RJbmRleCB9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuZmluZExhc3RJbmRleChbe2E6IDF9LCB7YTogMn0sIHthOiAzfSwge2E6IDR9XSwgKHsgYSB9KSA9PiBhID09PSAyKTsgLy8gMVxuKi9cbmZ1bmN0aW9uIGZpbmRMYXN0SW5kZXgoYXJyLCBjYWxsYmFjaywgZGVmYXVsdEluZGV4KSB7XG4gIGlmIChkZWZhdWx0SW5kZXggPT09IHZvaWQgMCkge1xuICAgIGRlZmF1bHRJbmRleCA9IC0xO1xuICB9XG4gIHZhciBsZW5ndGggPSBhcnIubGVuZ3RoO1xuICBmb3IgKHZhciBpID0gbGVuZ3RoIC0gMTsgaSA+PSAwOyAtLWkpIHtcbiAgICBpZiAoY2FsbGJhY2soYXJyW2ldLCBpLCBhcnIpKSB7XG4gICAgICByZXR1cm4gaTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGRlZmF1bHRJbmRleDtcbn1cbi8qKlxuKiBSZXR1cm5zIHRoZSB2YWx1ZSBvZiB0aGUgcmV2ZXJzZSBkaXJlY3Rpb24gZWxlbWVudCBpbiB0aGUgYXJyYXkgdGhhdCBzYXRpc2ZpZXMgdGhlIHByb3ZpZGVkIHRlc3RpbmcgZnVuY3Rpb24uXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgQ3Jvc3NCcm93c2VyXG4qIEBwYXJhbSAtIFRoZSBhcnJheSBgZmluZExhc3RgIHdhcyBjYWxsZWQgdXBvbi5cbiogQHBhcmFtIC0gQSBmdW5jdGlvbiB0byBleGVjdXRlIG9uIGVhY2ggdmFsdWUgaW4gdGhlIGFycmF5LFxuKiBAcGFyYW0gLSBSZXR1cm5zIGRlZmFsdXRWYWx1ZSBpZiBub3QgZm91bmQgYnkgdGhlIGZ1bmN0aW9uLlxuKiBAZXhhbXBsZVxuaW1wb3J0IHsgZmluZCB9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuZmluZChbe2E6IDF9LCB7YTogMn0sIHthOiAzfSwge2E6IDR9XSwgKHsgYSB9KSA9PiBhID09PSAyKTsgLy8ge2E6IDJ9XG4qL1xuZnVuY3Rpb24gZmluZExhc3QoYXJyLCBjYWxsYmFjaywgZGVmYWx1dFZhbHVlKSB7XG4gIHZhciBpbmRleCA9IGZpbmRMYXN0SW5kZXgoYXJyLCBjYWxsYmFjayk7XG4gIHJldHVybiBpbmRleCA+IC0xID8gYXJyW2luZGV4XSA6IGRlZmFsdXRWYWx1ZTtcbn1cbi8qKlxuKiBSZXR1cm5zIHRoZSB2YWx1ZSBvZiB0aGUgZmlyc3QgZWxlbWVudCBpbiB0aGUgYXJyYXkgdGhhdCBzYXRpc2ZpZXMgdGhlIHByb3ZpZGVkIHRlc3RpbmcgZnVuY3Rpb24uXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgQ3Jvc3NCcm93c2VyXG4qIEBwYXJhbSAtIFRoZSBhcnJheSBgZmluZGAgd2FzIGNhbGxlZCB1cG9uLlxuKiBAcGFyYW0gLSBBIGZ1bmN0aW9uIHRvIGV4ZWN1dGUgb24gZWFjaCB2YWx1ZSBpbiB0aGUgYXJyYXksXG4qIEBwYXJhbSAtIFJldHVybnMgZGVmYWx1dFZhbHVlIGlmIG5vdCBmb3VuZCBieSB0aGUgZnVuY3Rpb24uXG4qIEBleGFtcGxlXG5pbXBvcnQgeyBmaW5kIH0gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5maW5kKFt7YTogMX0sIHthOiAyfSwge2E6IDN9LCB7YTogNH1dLCAoeyBhIH0pID0+IGEgPT09IDIpOyAvLyB7YTogMn1cbiovXG5mdW5jdGlvbiBmaW5kKGFyciwgY2FsbGJhY2ssIGRlZmFsdXRWYWx1ZSkge1xuICB2YXIgaW5kZXggPSBmaW5kSW5kZXgoYXJyLCBjYWxsYmFjayk7XG4gIHJldHVybiBpbmRleCA+IC0xID8gYXJyW2luZGV4XSA6IGRlZmFsdXRWYWx1ZTtcbn1cbi8qKlxuKiB3aW5kb3cucmVxdWVzdEFuaW1hdGlvbkZyYW1lKCkgbWV0aG9kIHdpdGggY3Jvc3MgYnJvd3Nlci5cbiogQGZ1bmN0aW9uXG4qIEBtZW1iZXJvZiBDcm9zc0Jyb3dzZXJcbiogQHBhcmFtIHtGcmFtZVJlcXVlc3RDYWxsYmFja30gY2FsbGJhY2sgLSBUaGUgZnVuY3Rpb24gdG8gY2FsbCB3aGVuIGl0J3MgdGltZSB0byB1cGRhdGUgeW91ciBhbmltYXRpb24gZm9yIHRoZSBuZXh0IHJlcGFpbnQuXG4qIEByZXR1cm4ge251bWJlcn0gaWRcbiogQGV4YW1wbGVcbmltcG9ydCB7cmVxdWVzdEFuaW1hdGlvbkZyYW1lfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbnJlcXVlc3RBbmltYXRpb25GcmFtZSgodGltZXN0YW1wKSA9PiB7XG4gIGNvbnNvbGUubG9nKHRpbWVzdGFtcCk7XG59KTtcbiovXG52YXIgcmVxdWVzdEFuaW1hdGlvbkZyYW1lID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHtcbiAgdmFyIGZpcnN0VGltZSA9IG5vdygpO1xuICB2YXIgcmFmID0gSVNfV0lORE9XICYmICh3aW5kb3cucmVxdWVzdEFuaW1hdGlvbkZyYW1lIHx8IHdpbmRvdy53ZWJraXRSZXF1ZXN0QW5pbWF0aW9uRnJhbWUgfHwgd2luZG93Lm1velJlcXVlc3RBbmltYXRpb25GcmFtZSB8fCB3aW5kb3cubXNSZXF1ZXN0QW5pbWF0aW9uRnJhbWUpO1xuICByZXR1cm4gcmFmID8gcmFmLmJpbmQod2luZG93KSA6IGZ1bmN0aW9uIChjYWxsYmFjaykge1xuICAgIHZhciBjdXJyVGltZSA9IG5vdygpO1xuICAgIHZhciBpZCA9IHNldFRpbWVvdXQoZnVuY3Rpb24gKCkge1xuICAgICAgY2FsbGJhY2soY3VyclRpbWUgLSBmaXJzdFRpbWUpO1xuICAgIH0sIDEwMDAgLyA2MCk7XG4gICAgcmV0dXJuIGlkO1xuICB9O1xufSgpO1xuLyoqXG4qIHdpbmRvdy5jYW5jZWxBbmltYXRpb25GcmFtZSgpIG1ldGhvZCB3aXRoIGNyb3NzIGJyb3dzZXIuXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgQ3Jvc3NCcm93c2VyXG4qIEBwYXJhbSB7bnVtYmVyfSBoYW5kbGUgLSB0aGUgaWQgb2J0YWluZWQgdGhyb3VnaCByZXF1ZXN0QW5pbWF0aW9uRnJhbWUgbWV0aG9kXG4qIEByZXR1cm4ge3ZvaWR9XG4qIEBleGFtcGxlXG5pbXBvcnQgeyByZXF1ZXN0QW5pbWF0aW9uRnJhbWUsIGNhbmNlbEFuaW1hdGlvbkZyYW1lIH0gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5jb25zdCBpZCA9IHJlcXVlc3RBbmltYXRpb25GcmFtZSgodGltZXN0YW1wKSA9PiB7XG4gIGNvbnNvbGUubG9nKHRpbWVzdGFtcCk7XG59KTtcblxuY2FuY2VsQW5pbWF0aW9uRnJhbWUoaWQpO1xuKi9cbnZhciBjYW5jZWxBbmltYXRpb25GcmFtZSA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7XG4gIHZhciBjYWYgPSBJU19XSU5ET1cgJiYgKHdpbmRvdy5jYW5jZWxBbmltYXRpb25GcmFtZSB8fCB3aW5kb3cud2Via2l0Q2FuY2VsQW5pbWF0aW9uRnJhbWUgfHwgd2luZG93Lm1vekNhbmNlbEFuaW1hdGlvbkZyYW1lIHx8IHdpbmRvdy5tc0NhbmNlbEFuaW1hdGlvbkZyYW1lKTtcbiAgcmV0dXJuIGNhZiA/IGNhZi5iaW5kKHdpbmRvdykgOiBmdW5jdGlvbiAoaGFuZGxlKSB7XG4gICAgY2xlYXJUaW1lb3V0KGhhbmRsZSk7XG4gIH07XG59KCk7XG4vKipcbiogQGZ1bmN0aW9uXG4qIEBtZW1iZXJvZiBVdGlsc1xuKi9cbmZ1bmN0aW9uIGdldEtleXMob2JqKSB7XG4gIHJldHVybiBPYmplY3Qua2V5cyhvYmopO1xufVxuLyoqXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgVXRpbHNcbiovXG5mdW5jdGlvbiBnZXRWYWx1ZXMob2JqKSB7XG4gIHZhciBrZXlzID0gZ2V0S2V5cyhvYmopO1xuICByZXR1cm4ga2V5cy5tYXAoZnVuY3Rpb24gKGtleSkge1xuICAgIHJldHVybiBvYmpba2V5XTtcbiAgfSk7XG59XG4vKipcbiogQGZ1bmN0aW9uXG4qIEBtZW1iZXJvZiBVdGlsc1xuKi9cbmZ1bmN0aW9uIGdldEVudHJpZXMob2JqKSB7XG4gIHZhciBrZXlzID0gZ2V0S2V5cyhvYmopO1xuICByZXR1cm4ga2V5cy5tYXAoZnVuY3Rpb24gKGtleSkge1xuICAgIHJldHVybiBba2V5LCBvYmpba2V5XV07XG4gIH0pO1xufVxuLyoqXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgVXRpbHNcbiovXG5mdW5jdGlvbiBzb3J0T3JkZXJzKGtleXMsIG9yZGVycykge1xuICBpZiAob3JkZXJzID09PSB2b2lkIDApIHtcbiAgICBvcmRlcnMgPSBbXTtcbiAgfVxuICBrZXlzLnNvcnQoZnVuY3Rpb24gKGEsIGIpIHtcbiAgICB2YXIgaW5kZXgxID0gb3JkZXJzLmluZGV4T2YoYSk7XG4gICAgdmFyIGluZGV4MiA9IG9yZGVycy5pbmRleE9mKGIpO1xuICAgIGlmIChpbmRleDIgPT09IC0xICYmIGluZGV4MSA9PT0gLTEpIHtcbiAgICAgIHJldHVybiAwO1xuICAgIH1cbiAgICBpZiAoaW5kZXgxID09PSAtMSkge1xuICAgICAgcmV0dXJuIDE7XG4gICAgfVxuICAgIGlmIChpbmRleDIgPT09IC0xKSB7XG4gICAgICByZXR1cm4gLTE7XG4gICAgfVxuICAgIHJldHVybiBpbmRleDEgLSBpbmRleDI7XG4gIH0pO1xufVxuLyoqXG4qIGNvbnZlcnQgdW5pdCBzaXplIHRvIHB4IHNpemVcbiogQGZ1bmN0aW9uXG4qIEBtZW1iZXJvZiBVdGlsc1xuKi9cbmZ1bmN0aW9uIGNvbnZlcnRVbml0U2l6ZShwb3MsIHNpemUpIHtcbiAgdmFyIF9hID0gc3BsaXRVbml0KHBvcyksXG4gICAgdmFsdWUgPSBfYS52YWx1ZSxcbiAgICB1bml0ID0gX2EudW5pdDtcbiAgaWYgKGlzT2JqZWN0KHNpemUpKSB7XG4gICAgdmFyIHNpemVGdW5jdGlvbiA9IHNpemVbdW5pdF07XG4gICAgaWYgKHNpemVGdW5jdGlvbikge1xuICAgICAgaWYgKGlzRnVuY3Rpb24oc2l6ZUZ1bmN0aW9uKSkge1xuICAgICAgICByZXR1cm4gc2l6ZUZ1bmN0aW9uKHZhbHVlKTtcbiAgICAgIH0gZWxzZSBpZiAoREVGQVVMVF9VTklUX1BSRVNFVFNbdW5pdF0pIHtcbiAgICAgICAgcmV0dXJuIERFRkFVTFRfVU5JVF9QUkVTRVRTW3VuaXRdKHZhbHVlLCBzaXplRnVuY3Rpb24pO1xuICAgICAgfVxuICAgIH1cbiAgfSBlbHNlIGlmICh1bml0ID09PSBcIiVcIikge1xuICAgIHJldHVybiB2YWx1ZSAqIHNpemUgLyAxMDA7XG4gIH1cbiAgaWYgKERFRkFVTFRfVU5JVF9QUkVTRVRTW3VuaXRdKSB7XG4gICAgcmV0dXJuIERFRkFVTFRfVU5JVF9QUkVTRVRTW3VuaXRdKHZhbHVlKTtcbiAgfVxuICByZXR1cm4gdmFsdWU7XG59XG4vKipcbiogY2FsY3VsYXRlIGJldHdlZW4gbWluLCBtYXhcbiogQGZ1bmN0aW9uXG4qIEBtZW1iZXJvZiBVdGlsc1xuKi9cbmZ1bmN0aW9uIGJldHdlZW4odmFsdWUsIG1pbiwgbWF4KSB7XG4gIHJldHVybiBNYXRoLm1heChtaW4sIE1hdGgubWluKHZhbHVlLCBtYXgpKTtcbn1cbmZ1bmN0aW9uIGNoZWNrQm91bmRTaXplKHRhcmdldFNpemUsIGNvbXBhcmVTaXplLCBpc01heCwgcmF0aW8pIHtcbiAgaWYgKHJhdGlvID09PSB2b2lkIDApIHtcbiAgICByYXRpbyA9IHRhcmdldFNpemVbMF0gLyB0YXJnZXRTaXplWzFdO1xuICB9XG4gIHJldHVybiBbW3Rocm90dGxlKGNvbXBhcmVTaXplWzBdLCBUSU5ZX05VTSksIHRocm90dGxlKGNvbXBhcmVTaXplWzBdIC8gcmF0aW8sIFRJTllfTlVNKV0sIFt0aHJvdHRsZShjb21wYXJlU2l6ZVsxXSAqIHJhdGlvLCBUSU5ZX05VTSksIHRocm90dGxlKGNvbXBhcmVTaXplWzFdLCBUSU5ZX05VTSldXS5maWx0ZXIoZnVuY3Rpb24gKHNpemUpIHtcbiAgICByZXR1cm4gc2l6ZS5ldmVyeShmdW5jdGlvbiAodmFsdWUsIGkpIHtcbiAgICAgIHZhciBkZWZhdWx0U2l6ZSA9IGNvbXBhcmVTaXplW2ldO1xuICAgICAgdmFyIHRocm90dGxlZFNpemUgPSB0aHJvdHRsZShkZWZhdWx0U2l6ZSwgVElOWV9OVU0pO1xuICAgICAgcmV0dXJuIGlzTWF4ID8gdmFsdWUgPD0gZGVmYXVsdFNpemUgfHwgdmFsdWUgPD0gdGhyb3R0bGVkU2l6ZSA6IHZhbHVlID49IGRlZmF1bHRTaXplIHx8IHZhbHVlID49IHRocm90dGxlZFNpemU7XG4gICAgfSk7XG4gIH0pWzBdIHx8IHRhcmdldFNpemU7XG59XG4vKipcbiogY2FsY3VsYXRlIGJvdW5kIHNpemVcbiogQGZ1bmN0aW9uXG4qIEBtZW1iZXJvZiBVdGlsc1xuKi9cbmZ1bmN0aW9uIGNhbGN1bGF0ZUJvdW5kU2l6ZShzaXplLCBtaW5TaXplLCBtYXhTaXplLCBrZWVwUmF0aW8pIHtcbiAgaWYgKCFrZWVwUmF0aW8pIHtcbiAgICByZXR1cm4gc2l6ZS5tYXAoZnVuY3Rpb24gKHZhbHVlLCBpKSB7XG4gICAgICByZXR1cm4gYmV0d2Vlbih2YWx1ZSwgbWluU2l6ZVtpXSwgbWF4U2l6ZVtpXSk7XG4gICAgfSk7XG4gIH1cbiAgdmFyIHdpZHRoID0gc2l6ZVswXSxcbiAgICBoZWlnaHQgPSBzaXplWzFdO1xuICB2YXIgcmF0aW8gPSBrZWVwUmF0aW8gPT09IHRydWUgPyB3aWR0aCAvIGhlaWdodCA6IGtlZXBSYXRpbztcbiAgLy8gd2lkdGggOiBoZWlnaHQgPSBtaW5XaWR0aCA6IG1pbkhlaWdodDtcbiAgdmFyIF9hID0gY2hlY2tCb3VuZFNpemUoc2l6ZSwgbWluU2l6ZSwgZmFsc2UsIHJhdGlvKSxcbiAgICBtaW5XaWR0aCA9IF9hWzBdLFxuICAgIG1pbkhlaWdodCA9IF9hWzFdO1xuICB2YXIgX2IgPSBjaGVja0JvdW5kU2l6ZShzaXplLCBtYXhTaXplLCB0cnVlLCByYXRpbyksXG4gICAgbWF4V2lkdGggPSBfYlswXSxcbiAgICBtYXhIZWlnaHQgPSBfYlsxXTtcbiAgaWYgKHdpZHRoIDwgbWluV2lkdGggfHwgaGVpZ2h0IDwgbWluSGVpZ2h0KSB7XG4gICAgd2lkdGggPSBtaW5XaWR0aDtcbiAgICBoZWlnaHQgPSBtaW5IZWlnaHQ7XG4gIH0gZWxzZSBpZiAod2lkdGggPiBtYXhXaWR0aCB8fCBoZWlnaHQgPiBtYXhIZWlnaHQpIHtcbiAgICB3aWR0aCA9IG1heFdpZHRoO1xuICAgIGhlaWdodCA9IG1heEhlaWdodDtcbiAgfVxuICByZXR1cm4gW3dpZHRoLCBoZWlnaHRdO1xufVxuLyoqXG4qIEFkZCBhbGwgdGhlIG51bWJlcnMuXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgVXRpbHNcbiovXG5mdW5jdGlvbiBzdW0obnVtcykge1xuICB2YXIgbGVuZ3RoID0gbnVtcy5sZW5ndGg7XG4gIHZhciB0b3RhbCA9IDA7XG4gIGZvciAodmFyIGkgPSBsZW5ndGggLSAxOyBpID49IDA7IC0taSkge1xuICAgIHRvdGFsICs9IG51bXNbaV07XG4gIH1cbiAgcmV0dXJuIHRvdGFsO1xufVxuLyoqXG4qIEF2ZXJhZ2UgYWxsIG51bWJlcnMuXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgVXRpbHNcbiovXG5mdW5jdGlvbiBhdmVyYWdlKG51bXMpIHtcbiAgdmFyIGxlbmd0aCA9IG51bXMubGVuZ3RoO1xuICB2YXIgdG90YWwgPSAwO1xuICBmb3IgKHZhciBpID0gbGVuZ3RoIC0gMTsgaSA+PSAwOyAtLWkpIHtcbiAgICB0b3RhbCArPSBudW1zW2ldO1xuICB9XG4gIHJldHVybiBsZW5ndGggPyB0b3RhbCAvIGxlbmd0aCA6IDA7XG59XG4vKipcbiogR2V0IHRoZSBhbmdsZSBvZiB0d28gcG9pbnRzLiAoMCA8PSByYWQgPCAzNTkpXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgVXRpbHNcbiovXG5mdW5jdGlvbiBnZXRSYWQocG9zMSwgcG9zMikge1xuICB2YXIgZGlzdFggPSBwb3MyWzBdIC0gcG9zMVswXTtcbiAgdmFyIGRpc3RZID0gcG9zMlsxXSAtIHBvczFbMV07XG4gIHZhciByYWQgPSBNYXRoLmF0YW4yKGRpc3RZLCBkaXN0WCk7XG4gIHJldHVybiByYWQgPj0gMCA/IHJhZCA6IHJhZCArIE1hdGguUEkgKiAyO1xufVxuLyoqXG4qIEdldCB0aGUgYXZlcmFnZSBwb2ludCBvZiBhbGwgcG9pbnRzLlxuKiBAZnVuY3Rpb25cbiogQG1lbWJlcm9mIFV0aWxzXG4qL1xuZnVuY3Rpb24gZ2V0Q2VudGVyUG9pbnQocG9pbnRzKSB7XG4gIHJldHVybiBbMCwgMV0ubWFwKGZ1bmN0aW9uIChpKSB7XG4gICAgcmV0dXJuIGF2ZXJhZ2UocG9pbnRzLm1hcChmdW5jdGlvbiAocG9zKSB7XG4gICAgICByZXR1cm4gcG9zW2ldO1xuICAgIH0pKTtcbiAgfSk7XG59XG4vKipcbiogR2V0cyB0aGUgZGlyZWN0aW9uIG9mIHRoZSBzaGFwZS5cbiogQGZ1bmN0aW9uXG4qIEBtZW1iZXJvZiBVdGlsc1xuKi9cbmZ1bmN0aW9uIGdldFNoYXBlRGlyZWN0aW9uKHBvaW50cykge1xuICB2YXIgY2VudGVyID0gZ2V0Q2VudGVyUG9pbnQocG9pbnRzKTtcbiAgdmFyIHBvczFSYWQgPSBnZXRSYWQoY2VudGVyLCBwb2ludHNbMF0pO1xuICB2YXIgcG9zMlJhZCA9IGdldFJhZChjZW50ZXIsIHBvaW50c1sxXSk7XG4gIHJldHVybiBwb3MxUmFkIDwgcG9zMlJhZCAmJiBwb3MyUmFkIC0gcG9zMVJhZCA8IE1hdGguUEkgfHwgcG9zMVJhZCA+IHBvczJSYWQgJiYgcG9zMlJhZCAtIHBvczFSYWQgPCAtTWF0aC5QSSA/IDEgOiAtMTtcbn1cbi8qKlxuKiBHZXQgdGhlIGRpc3RhbmNlIGJldHdlZW4gdHdvIHBvaW50cy5cbiogQGZ1bmN0aW9uXG4qIEBtZW1iZXJvZiBVdGlsc1xuKi9cbmZ1bmN0aW9uIGdldERpc3QoYSwgYikge1xuICByZXR1cm4gTWF0aC5zcXJ0KE1hdGgucG93KChiID8gYlswXSA6IDApIC0gYVswXSwgMikgKyBNYXRoLnBvdygoYiA/IGJbMV0gOiAwKSAtIGFbMV0sIDIpKTtcbn1cbi8qKlxuKiB0aHJvdHRsZSBudW1iZXIgZGVwZW5kaW5nIG9uIHRoZSB1bml0LlxuKiBAZnVuY3Rpb25cbiogQG1lbWJlcm9mIFV0aWxzXG4qL1xuZnVuY3Rpb24gdGhyb3R0bGUobnVtLCB1bml0KSB7XG4gIGlmICghdW5pdCkge1xuICAgIHJldHVybiBudW07XG4gIH1cbiAgdmFyIHJldmVyc2VVbml0ID0gMSAvIHVuaXQ7XG4gIHJldHVybiBNYXRoLnJvdW5kKG51bSAvIHVuaXQpIC8gcmV2ZXJzZVVuaXQ7XG59XG4vKipcbiogdGhyb3R0bGUgbnVtYmVyIGFycmF5IGRlcGVuZGluZyBvbiB0aGUgdW5pdC5cbiogQGZ1bmN0aW9uXG4qIEBtZW1iZXJvZiBVdGlsc1xuKi9cbmZ1bmN0aW9uIHRocm90dGxlQXJyYXkobnVtcywgdW5pdCkge1xuICBudW1zLmZvckVhY2goZnVuY3Rpb24gKF8sIGkpIHtcbiAgICBudW1zW2ldID0gdGhyb3R0bGUobnVtc1tpXSwgdW5pdCk7XG4gIH0pO1xuICByZXR1cm4gbnVtcztcbn1cbi8qKlxuKiBAZnVuY3Rpb25cbiogQG1lbWJlcm9mIFV0aWxzXG4qL1xuZnVuY3Rpb24gY291bnRlcihudW0pIHtcbiAgdmFyIG51bXMgPSBbXTtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBudW07ICsraSkge1xuICAgIG51bXMucHVzaChpKTtcbiAgfVxuICByZXR1cm4gbnVtcztcbn1cbi8qKlxuKiBAZnVuY3Rpb25cbiogQG1lbWJlcm9mIFV0aWxzXG4qL1xuZnVuY3Rpb24gcmVwbGFjZU9uY2UodGV4dCwgZnJvbVRleHQsIHRvVGV4dCkge1xuICB2YXIgaXNPbmNlID0gZmFsc2U7XG4gIHJldHVybiB0ZXh0LnJlcGxhY2UoZnJvbVRleHQsIGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgYXJncyA9IFtdO1xuICAgIGZvciAodmFyIF9pID0gMDsgX2kgPCBhcmd1bWVudHMubGVuZ3RoOyBfaSsrKSB7XG4gICAgICBhcmdzW19pXSA9IGFyZ3VtZW50c1tfaV07XG4gICAgfVxuICAgIGlmIChpc09uY2UpIHtcbiAgICAgIHJldHVybiBhcmdzWzBdO1xuICAgIH1cbiAgICBpc09uY2UgPSB0cnVlO1xuICAgIHJldHVybiBpc1N0cmluZyh0b1RleHQpID8gdG9UZXh0IDogdG9UZXh0LmFwcGx5KHZvaWQgMCwgYXJncyk7XG4gIH0pO1xufVxuLyoqXG4qIEBmdW5jdGlvblxuKiBAbWVtYmVyb2YgVXRpbHNcbiovXG5mdW5jdGlvbiBmbGF0KGFycikge1xuICByZXR1cm4gYXJyLnJlZHVjZShmdW5jdGlvbiAocHJldiwgY3VyKSB7XG4gICAgcmV0dXJuIHByZXYuY29uY2F0KGN1cik7XG4gIH0sIFtdKTtcbn1cbi8qKlxuKiBAZnVuY3Rpb25cbiogQG1lbWJlcm9mIFV0aWxzXG4qL1xuZnVuY3Rpb24gZGVlcEZsYXQoYXJyKSB7XG4gIHJldHVybiBhcnIucmVkdWNlKGZ1bmN0aW9uIChwcmV2LCBjdXIpIHtcbiAgICBpZiAoaXNBcnJheShjdXIpKSB7XG4gICAgICBwcmV2LnB1c2guYXBwbHkocHJldiwgZGVlcEZsYXQoY3VyKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHByZXYucHVzaChjdXIpO1xuICAgIH1cbiAgICByZXR1cm4gcHJldjtcbiAgfSwgW10pO1xufVxuLyoqXG4gKiBAZnVuY3Rpb25cbiAqIEBtZW1iZXJvZiBVdGlsc1xuICovXG5mdW5jdGlvbiBwdXNoU2V0KGVsZW1lbnRzLCBlbGVtZW50KSB7XG4gIGlmIChlbGVtZW50cy5pbmRleE9mKGVsZW1lbnQpID09PSAtMSkge1xuICAgIGVsZW1lbnRzLnB1c2goZWxlbWVudCk7XG4gIH1cbn1cblxuLyoqXG4qIEBuYW1lc3BhY2VcbiogQG5hbWUgQ29sb3JcbiovXG4vKipcbiogUmVtb3ZlIHRoZSAjIGZyb20gdGhlIGhleCBjb2xvci5cbiogQG1lbWJlcm9mIENvbG9yXG4qIEBwYXJhbSB7fSBoZXggLSBoZXggY29sb3JcbiogQHJldHVybiB7fSBoZXggY29sb3JcbiogQGV4YW1wbGVcbmltcG9ydCB7Y3V0SGV4fSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKGN1dEhleChcIiMwMDAwMDBcIikpIC8vIFwiMDAwMDAwXCJcbiovXG5mdW5jdGlvbiBjdXRIZXgoaGV4KSB7XG4gIHJldHVybiBoZXgucmVwbGFjZShcIiNcIiwgXCJcIik7XG59XG4vKipcbiogY29udmVydCBoZXggY29sb3IgdG8gcmdiIGNvbG9yLlxuKiBAbWVtYmVyb2YgQ29sb3JcbiogQHBhcmFtIHt9IGhleCAtIGhleCBjb2xvclxuKiBAcmV0dXJuIHt9IHJnYiBjb2xvclxuKiBAZXhhbXBsZVxuaW1wb3J0IHtoZXhUb1JHQkF9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coaGV4VG9SR0JBKFwiIzAwMDAwMDA1XCIpKTtcbi8vIFswLCAwLCAwLCAxXVxuY29uc29sZS5sb2coaGV4VG9SR0JBKFwiIzIwMTA0NVwiKSk7XG4vLyBbMzIsIDE2LCA2OSwgMV1cbiovXG5mdW5jdGlvbiBoZXhUb1JHQkEoaGV4KSB7XG4gIHZhciBoID0gY3V0SGV4KGhleCk7XG4gIHZhciByID0gcGFyc2VJbnQoaC5zdWJzdHJpbmcoMCwgMiksIDE2KTtcbiAgdmFyIGcgPSBwYXJzZUludChoLnN1YnN0cmluZygyLCA0KSwgMTYpO1xuICB2YXIgYiA9IHBhcnNlSW50KGguc3Vic3RyaW5nKDQsIDYpLCAxNik7XG4gIHZhciBhID0gcGFyc2VJbnQoaC5zdWJzdHJpbmcoNiwgOCksIDE2KSAvIDI1NTtcbiAgaWYgKGlzTmFOKGEpKSB7XG4gICAgYSA9IDE7XG4gIH1cbiAgcmV0dXJuIFtyLCBnLCBiLCBhXTtcbn1cbi8qKlxuKiBjb252ZXJ0IDMob3IgNCktZGlnaXQgaGV4IGNvbG9yIHRvIDYob3IgOCktZGlnaXQgaGV4IGNvbG9yLlxuKiBAbWVtYmVyb2YgQ29sb3JcbiogQHBhcmFtIHt9IGhleCAtIDMob3IgNCktZGlnaXQgaGV4IGNvbG9yXG4qIEByZXR1cm4ge30gNihvciA4KS1kaWdpdCBoZXggY29sb3JcbiogQGV4YW1wbGVcbmltcG9ydCB7dG9GdWxsSGV4fSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKHRvRnVsbEhleChcIiMxMjNcIikpOyAvLyBcIiMxMTIyMzNcIlxuY29uc29sZS5sb2codG9GdWxsSGV4KFwiIzEyM2FcIikpOyAvLyBcIiMxMTIyMzNhYVwiXG4qL1xuZnVuY3Rpb24gdG9GdWxsSGV4KGgpIHtcbiAgdmFyIHIgPSBoLmNoYXJBdCgxKTtcbiAgdmFyIGcgPSBoLmNoYXJBdCgyKTtcbiAgdmFyIGIgPSBoLmNoYXJBdCgzKTtcbiAgdmFyIGEgPSBoLmNoYXJBdCg0KTtcbiAgdmFyIGFyciA9IFtcIiNcIiwgciwgciwgZywgZywgYiwgYiwgYSwgYV07XG4gIHJldHVybiBhcnIuam9pbihcIlwiKTtcbn1cbi8qKlxuKiBjb252ZXJ0IGhzbCBjb2xvciB0byByZ2JhIGNvbG9yLlxuKiBAbWVtYmVyb2YgQ29sb3JcbiogQHBhcmFtIHt9IGhzbCAtIGhzbCBjb2xvcihodWU6IDAgfiAzNjAsIHNhdHVyYXRpb246IDAgfiAxLCBsaWdodG5lc3M6IDAgfiAxLCBhbHBoYTogMCB+IDEpXG4qIEByZXR1cm4ge30gcmdiYSBjb2xvclxuKiBAZXhhbXBsZVxuaW1wb3J0IHtoc2xUb1JHQkF9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coaHNsVG9SR0JBKFsxNTAsIDAuNSwgMC40XSkpO1xuLy8gWzUxLCAxNTMsIDEwMiwgMV1cbiovXG5mdW5jdGlvbiBoc2xUb1JHQkEoaHNsKSB7XG4gIHZhciBfYTtcbiAgdmFyIGggPSBoc2xbMF07XG4gIHZhciBzID0gaHNsWzFdO1xuICB2YXIgbCA9IGhzbFsyXTtcbiAgaWYgKGggPCAwKSB7XG4gICAgaCArPSBNYXRoLmZsb29yKChNYXRoLmFicyhoKSArIDM2MCkgLyAzNjApICogMzYwO1xuICB9XG4gIGggJT0gMzYwO1xuICB2YXIgYyA9ICgxIC0gTWF0aC5hYnMoMiAqIGwgLSAxKSkgKiBzO1xuICB2YXIgeCA9IGMgKiAoMSAtIE1hdGguYWJzKGggLyA2MCAlIDIgLSAxKSk7XG4gIHZhciBtID0gbCAtIGMgLyAyO1xuICB2YXIgcmdiO1xuICBpZiAoaCA8IDYwKSB7XG4gICAgcmdiID0gW2MsIHgsIDBdO1xuICB9IGVsc2UgaWYgKGggPCAxMjApIHtcbiAgICByZ2IgPSBbeCwgYywgMF07XG4gIH0gZWxzZSBpZiAoaCA8IDE4MCkge1xuICAgIHJnYiA9IFswLCBjLCB4XTtcbiAgfSBlbHNlIGlmIChoIDwgMjQwKSB7XG4gICAgcmdiID0gWzAsIHgsIGNdO1xuICB9IGVsc2UgaWYgKGggPCAzMDApIHtcbiAgICByZ2IgPSBbeCwgMCwgY107XG4gIH0gZWxzZSBpZiAoaCA8IDM2MCkge1xuICAgIHJnYiA9IFtjLCAwLCB4XTtcbiAgfSBlbHNlIHtcbiAgICByZ2IgPSBbMCwgMCwgMF07XG4gIH1cbiAgcmV0dXJuIFtNYXRoLnJvdW5kKChyZ2JbMF0gKyBtKSAqIDI1NSksIE1hdGgucm91bmQoKHJnYlsxXSArIG0pICogMjU1KSwgTWF0aC5yb3VuZCgocmdiWzJdICsgbSkgKiAyNTUpLCAoX2EgPSBoc2xbM10pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IDFdO1xufVxuLyoqXG4qIGNvbnZlcnQgc3RyaW5nIHRvIHJnYmEgY29sb3IuXG4qIEBtZW1iZXJvZiBDb2xvclxuKiBAcGFyYW0ge30gLSAzLWhleCgjMDAwKSwgNC1oZXgoIzAwMDApIDYtaGV4KCMwMDAwMDApLCA4LWhleCgjMDAwMDAwMDApIG9yIFJHQihBKSwgb3IgSFNMKEEpXG4qIEByZXR1cm4ge30gcmdiYSBjb2xvclxuKiBAZXhhbXBsZVxuaW1wb3J0IHtzdHJpbmdUb1JHQkF9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuY29uc29sZS5sb2coc3RyaW5nVG9SR0JBKFwiIzAwMDAwMFwiKSk7IC8vIFswLCAwLCAwLCAxXVxuY29uc29sZS5sb2coc3RyaW5nVG9SR0JBKFwicmdiKDEwMCwgMTAwLCAxMDApXCIpKTsgLy8gWzEwMCwgMTAwLCAxMDAsIDFdXG5jb25zb2xlLmxvZyhzdHJpbmdUb1JHQkEoXCJoc2woMTUwLCAwLjUsIDAuNClcIikpOyAvLyBbNTEsIDE1MywgMTAyLCAxXVxuKi9cbmZ1bmN0aW9uIHN0cmluZ1RvUkdCQShjb2xvcikge1xuICBpZiAoY29sb3IuY2hhckF0KDApID09PSBcIiNcIikge1xuICAgIGlmIChjb2xvci5sZW5ndGggPT09IDQgfHwgY29sb3IubGVuZ3RoID09PSA1KSB7XG4gICAgICByZXR1cm4gaGV4VG9SR0JBKHRvRnVsbEhleChjb2xvcikpO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gaGV4VG9SR0JBKGNvbG9yKTtcbiAgICB9XG4gIH0gZWxzZSBpZiAoY29sb3IuaW5kZXhPZihcIihcIikgIT09IC0xKSB7XG4gICAgLy8gaW4gYnJhY2tldC5cbiAgICB2YXIgX2EgPSBzcGxpdEJyYWNrZXQoY29sb3IpLFxuICAgICAgcHJlZml4ID0gX2EucHJlZml4LFxuICAgICAgdmFsdWUgPSBfYS52YWx1ZTtcbiAgICBpZiAoIXByZWZpeCB8fCAhdmFsdWUpIHtcbiAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIHZhciBhcnIgPSBzcGxpdENvbW1hKHZhbHVlKTtcbiAgICB2YXIgY29sb3JBcnIgPSBbMCwgMCwgMCwgMV07XG4gICAgdmFyIGxlbmd0aCA9IGFyci5sZW5ndGg7XG4gICAgc3dpdGNoIChwcmVmaXgpIHtcbiAgICAgIGNhc2UgUkdCOlxuICAgICAgY2FzZSBSR0JBOlxuICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbmd0aDsgKytpKSB7XG4gICAgICAgICAgY29sb3JBcnJbaV0gPSBwYXJzZUZsb2F0KGFycltpXSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGNvbG9yQXJyO1xuICAgICAgY2FzZSBIU0w6XG4gICAgICBjYXNlIEhTTEE6XG4gICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbGVuZ3RoOyArK2kpIHtcbiAgICAgICAgICBpZiAoYXJyW2ldLmluZGV4T2YoXCIlXCIpICE9PSAtMSkge1xuICAgICAgICAgICAgY29sb3JBcnJbaV0gPSBwYXJzZUZsb2F0KGFycltpXSkgLyAxMDA7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbG9yQXJyW2ldID0gcGFyc2VGbG9hdChhcnJbaV0pO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAvLyBoc2wsIGhzbGEgdG8gcmdiYVxuICAgICAgICByZXR1cm4gaHNsVG9SR0JBKGNvbG9yQXJyKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHVuZGVmaW5lZDtcbn1cblxuLyoqXG4gKiBSZXR1cm5zIGFsbCBlbGVtZW50IGRlc2NlbmRhbnRzIG9mIG5vZGUgdGhhdFxuICogbWF0Y2ggc2VsZWN0b3JzLlxuICovXG4vKipcbiAqIENoZWNrcyBpZiB0aGUgc3BlY2lmaWVkIGNsYXNzIHZhbHVlIGV4aXN0cyBpbiB0aGUgZWxlbWVudCdzIGNsYXNzIGF0dHJpYnV0ZS5cbiAqIEBtZW1iZXJvZiBET01cbiAqIEBwYXJhbSAtIEEgRE9NU3RyaW5nIGNvbnRhaW5pbmcgb25lIG9yIG1vcmUgc2VsZWN0b3JzIHRvIG1hdGNoXG4gKiBAcGFyYW0gLSBJZiBtdWx0aSBpcyB0cnVlLCBhIERPTVN0cmluZyBjb250YWluaW5nIG9uZSBvciBtb3JlIHNlbGVjdG9ycyB0byBtYXRjaCBhZ2FpbnN0LlxuICogQGV4YW1wbGVcbmltcG9ydCB7JH0gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5jb25zb2xlLmxvZygkKFwiZGl2XCIpKTsgLy8gZGl2IGVsZW1lbnRcbmNvbnNvbGUubG9nKCQoXCJkaXZcIiwgdHJ1ZSkpOyAvLyBbZGl2LCBkaXZdIGVsZW1lbnRzXG4qL1xuZnVuY3Rpb24gJChzZWxlY3RvcnMsIG11bHRpKSB7XG4gIGlmICghZG9jKSB7XG4gICAgcmV0dXJuIG11bHRpID8gW10gOiBudWxsO1xuICB9XG4gIHJldHVybiBtdWx0aSA/IGRvYy5xdWVyeVNlbGVjdG9yQWxsKHNlbGVjdG9ycykgOiBkb2MucXVlcnlTZWxlY3RvcihzZWxlY3RvcnMpO1xufVxuLyoqXG4qIENoZWNrcyBpZiB0aGUgc3BlY2lmaWVkIGNsYXNzIHZhbHVlIGV4aXN0cyBpbiB0aGUgZWxlbWVudCdzIGNsYXNzIGF0dHJpYnV0ZS5cbiogQG1lbWJlcm9mIERPTVxuKiBAcGFyYW0gZWxlbWVudCAtIHRhcmdldFxuKiBAcGFyYW0gY2xhc3NOYW1lIC0gdGhlIGNsYXNzIG5hbWUgdG8gc2VhcmNoXG4qIEByZXR1cm4ge2Jvb2xlYW59IHJldHVybiBmYWxzZSBpZiB0aGUgY2xhc3MgaXMgbm90IGZvdW5kLlxuKiBAZXhhbXBsZVxuaW1wb3J0IHtoYXNDbGFzc30gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5jb25zb2xlLmxvZyhoYXNDbGFzcyhlbGVtZW50LCBcInN0YXJ0XCIpKTsgLy8gdHJ1ZSBvciBmYWxzZVxuKi9cbmZ1bmN0aW9uIGhhc0NsYXNzKGVsZW1lbnQsIGNsYXNzTmFtZSkge1xuICBpZiAoZWxlbWVudC5jbGFzc0xpc3QpIHtcbiAgICByZXR1cm4gZWxlbWVudC5jbGFzc0xpc3QuY29udGFpbnMoY2xhc3NOYW1lKTtcbiAgfVxuICByZXR1cm4gISFlbGVtZW50LmNsYXNzTmFtZS5tYXRjaChuZXcgUmVnRXhwKFwiKFxcXFxzfF4pXCIgKyBjbGFzc05hbWUgKyBcIihcXFxcc3wkKVwiKSk7XG59XG4vKipcbiogQWRkIHRoZSBzcGVjaWZpZWQgY2xhc3MgdmFsdWUuIElmIHRoZXNlIGNsYXNzZSBhbHJlYWR5IGV4aXN0IGluIHRoZSBlbGVtZW50J3MgY2xhc3MgYXR0cmlidXRlIHRoZXkgYXJlIGlnbm9yZWQuXG4qIEBtZW1iZXJvZiBET01cbiogQHBhcmFtIGVsZW1lbnQgLSB0YXJnZXRcbiogQHBhcmFtIGNsYXNzTmFtZSAtIHRoZSBjbGFzcyBuYW1lIHRvIGFkZFxuKiBAZXhhbXBsZVxuaW1wb3J0IHthZGRDbGFzc30gZnJvbSBcIkBkYXlicnVzaC91dGlsc1wiO1xuXG5hZGRDbGFzcyhlbGVtZW50LCBcInN0YXJ0XCIpO1xuKi9cbmZ1bmN0aW9uIGFkZENsYXNzKGVsZW1lbnQsIGNsYXNzTmFtZSkge1xuICBpZiAoZWxlbWVudC5jbGFzc0xpc3QpIHtcbiAgICBlbGVtZW50LmNsYXNzTGlzdC5hZGQoY2xhc3NOYW1lKTtcbiAgfSBlbHNlIHtcbiAgICBlbGVtZW50LmNsYXNzTmFtZSArPSBcIiBcIiArIGNsYXNzTmFtZTtcbiAgfVxufVxuLyoqXG4qIFJlbW92ZXMgdGhlIHNwZWNpZmllZCBjbGFzcyB2YWx1ZS5cbiogQG1lbWJlcm9mIERPTVxuKiBAcGFyYW0gZWxlbWVudCAtIHRhcmdldFxuKiBAcGFyYW0gY2xhc3NOYW1lIC0gdGhlIGNsYXNzIG5hbWUgdG8gcmVtb3ZlXG4qIEBleGFtcGxlXG5pbXBvcnQge3JlbW92ZUNsYXNzfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbnJlbW92ZUNsYXNzKGVsZW1lbnQsIFwic3RhcnRcIik7XG4qL1xuZnVuY3Rpb24gcmVtb3ZlQ2xhc3MoZWxlbWVudCwgY2xhc3NOYW1lKSB7XG4gIGlmIChlbGVtZW50LmNsYXNzTGlzdCkge1xuICAgIGVsZW1lbnQuY2xhc3NMaXN0LnJlbW92ZShjbGFzc05hbWUpO1xuICB9IGVsc2Uge1xuICAgIHZhciByZWcgPSBuZXcgUmVnRXhwKFwiKFxcXFxzfF4pXCIgKyBjbGFzc05hbWUgKyBcIihcXFxcc3wkKVwiKTtcbiAgICBlbGVtZW50LmNsYXNzTmFtZSA9IGVsZW1lbnQuY2xhc3NOYW1lLnJlcGxhY2UocmVnLCBcIiBcIik7XG4gIH1cbn1cbi8qKlxuKiBHZXRzIHRoZSBDU1MgcHJvcGVydGllcyBmcm9tIHRoZSBlbGVtZW50LlxuKiBAbWVtYmVyb2YgRE9NXG4qIEBwYXJhbSBlbGVtZW50cyAtIGVsZW1lbnRzXG4qIEBwYXJhbSBwcm9wZXJpdGVzIC0gdGhlIENTUyBwcm9wZXJ0aWVzXG4qIEByZXR1cm4gcmV0dXJucyBDU1MgcHJvcGVydGllcyBhbmQgdmFsdWVzLlxuKiBAZXhhbXBsZVxuaW1wb3J0IHtmcm9tQ1NTfSBmcm9tIFwiQGRheWJydXNoL3V0aWxzXCI7XG5cbmNvbnNvbGUubG9nKGZyb21DU1MoZWxlbWVudCwgW1wibGVmdFwiLCBcIm9wYWNpdHlcIiwgXCJ0b3BcIl0pKTsgLy8ge1wibGVmdFwiOiBcIjEwcHhcIiwgXCJvcGFjaXR5XCI6IDEsIFwidG9wXCI6IFwiMTBweFwifVxuKi9cbmZ1bmN0aW9uIGZyb21DU1MoZWxlbWVudHMsIHByb3BlcnRpZXMpIHtcbiAgaWYgKCFlbGVtZW50cyB8fCAhcHJvcGVydGllcyB8fCAhcHJvcGVydGllcy5sZW5ndGgpIHtcbiAgICByZXR1cm4ge307XG4gIH1cbiAgdmFyIGVsZW1lbnQ7XG4gIGlmIChlbGVtZW50cyBpbnN0YW5jZW9mIEVsZW1lbnQpIHtcbiAgICBlbGVtZW50ID0gZWxlbWVudHM7XG4gIH0gZWxzZSBpZiAoZWxlbWVudHMubGVuZ3RoKSB7XG4gICAgZWxlbWVudCA9IGVsZW1lbnRzWzBdO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiB7fTtcbiAgfVxuICB2YXIgY3NzT2JqZWN0ID0ge307XG4gIHZhciBzdHlsZXMgPSBnZXRXaW5kb3coZWxlbWVudCkuZ2V0Q29tcHV0ZWRTdHlsZShlbGVtZW50KTtcbiAgdmFyIGxlbmd0aCA9IHByb3BlcnRpZXMubGVuZ3RoO1xuICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbmd0aDsgKytpKSB7XG4gICAgY3NzT2JqZWN0W3Byb3BlcnRpZXNbaV1dID0gc3R5bGVzW3Byb3BlcnRpZXNbaV1dO1xuICB9XG4gIHJldHVybiBjc3NPYmplY3Q7XG59XG4vKipcbiogU2V0cyB1cCBhIGZ1bmN0aW9uIHRoYXQgd2lsbCBiZSBjYWxsZWQgd2hlbmV2ZXIgdGhlIHNwZWNpZmllZCBldmVudCBpcyBkZWxpdmVyZWQgdG8gdGhlIHRhcmdldFxuKiBAbWVtYmVyb2YgRE9NXG4qIEBwYXJhbSAtIGV2ZW50IHRhcmdldFxuKiBAcGFyYW0gLSBBIGNhc2Utc2Vuc2l0aXZlIHN0cmluZyByZXByZXNlbnRpbmcgdGhlIGV2ZW50IHR5cGUgdG8gbGlzdGVuIGZvci5cbiogQHBhcmFtIC0gVGhlIG9iamVjdCB3aGljaCByZWNlaXZlcyBhIG5vdGlmaWNhdGlvbiAoYW4gb2JqZWN0IHRoYXQgaW1wbGVtZW50cyB0aGUgRXZlbnQgaW50ZXJmYWNlKSB3aGVuIGFuIGV2ZW50IG9mIHRoZSBzcGVjaWZpZWQgdHlwZSBvY2N1cnNcbiogQHBhcmFtIC0gQW4gb3B0aW9ucyBvYmplY3QgdGhhdCBzcGVjaWZpZXMgY2hhcmFjdGVyaXN0aWNzIGFib3V0IHRoZSBldmVudCBsaXN0ZW5lci5cbiogQGV4YW1wbGVcbmltcG9ydCB7YWRkRXZlbnR9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcblxuYWRkRXZlbnQoZWwsIFwiY2xpY2tcIiwgZSA9PiB7XG4gIGNvbnNvbGUubG9nKGUpO1xufSk7XG4qL1xuZnVuY3Rpb24gYWRkRXZlbnQoZWwsIHR5cGUsIGxpc3RlbmVyLCBvcHRpb25zKSB7XG4gIGVsLmFkZEV2ZW50TGlzdGVuZXIodHlwZSwgbGlzdGVuZXIsIG9wdGlvbnMpO1xufVxuLyoqXG4qIHJlbW92ZXMgZnJvbSB0aGUgRXZlbnRUYXJnZXQgYW4gZXZlbnQgbGlzdGVuZXIgcHJldmlvdXNseSByZWdpc3RlcmVkIHdpdGggRXZlbnRUYXJnZXQuYWRkRXZlbnRMaXN0ZW5lcigpXG4qIEBtZW1iZXJvZiBET01cbiogQHBhcmFtIC0gZXZlbnQgdGFyZ2V0XG4qIEBwYXJhbSAtIEEgY2FzZS1zZW5zaXRpdmUgc3RyaW5nIHJlcHJlc2VudGluZyB0aGUgZXZlbnQgdHlwZSB0byBsaXN0ZW4gZm9yLlxuKiBAcGFyYW0gLSBUaGUgRXZlbnRMaXN0ZW5lciBmdW5jdGlvbiBvZiB0aGUgZXZlbnQgaGFuZGxlciB0byByZW1vdmUgZnJvbSB0aGUgZXZlbnQgdGFyZ2V0LlxuKiBAcGFyYW0gLSBBbiBvcHRpb25zIG9iamVjdCB0aGF0IHNwZWNpZmllcyBjaGFyYWN0ZXJpc3RpY3MgYWJvdXQgdGhlIGV2ZW50IGxpc3RlbmVyLlxuKiBAZXhhbXBsZVxuaW1wb3J0IHthZGRFdmVudCwgcmVtb3ZlRXZlbnR9IGZyb20gXCJAZGF5YnJ1c2gvdXRpbHNcIjtcbmNvbnN0IGxpc3RlbmVyID0gZSA9PiB7XG4gIGNvbnNvbGUubG9nKGUpO1xufTtcbmFkZEV2ZW50KGVsLCBcImNsaWNrXCIsIGxpc3RlbmVyKTtcbnJlbW92ZUV2ZW50KGVsLCBcImNsaWNrXCIsIGxpc3RlbmVyKTtcbiovXG5mdW5jdGlvbiByZW1vdmVFdmVudChlbCwgdHlwZSwgbGlzdGVuZXIsIG9wdGlvbnMpIHtcbiAgZWwucmVtb3ZlRXZlbnRMaXN0ZW5lcih0eXBlLCBsaXN0ZW5lciwgb3B0aW9ucyk7XG59XG5mdW5jdGlvbiBnZXREb2N1bWVudChlbCkge1xuICByZXR1cm4gKGVsID09PSBudWxsIHx8IGVsID09PSB2b2lkIDAgPyB2b2lkIDAgOiBlbC5vd25lckRvY3VtZW50KSB8fCBkb2M7XG59XG5mdW5jdGlvbiBnZXREb2N1bWVudEVsZW1lbnQoZWwpIHtcbiAgcmV0dXJuIGdldERvY3VtZW50KGVsKS5kb2N1bWVudEVsZW1lbnQ7XG59XG5mdW5jdGlvbiBnZXREb2N1bWVudEJvZHkoZWwpIHtcbiAgcmV0dXJuIGdldERvY3VtZW50KGVsKS5ib2R5O1xufVxuZnVuY3Rpb24gZ2V0V2luZG93KGVsKSB7XG4gIHZhciBfYTtcbiAgcmV0dXJuICgoX2EgPSBlbCA9PT0gbnVsbCB8fCBlbCA9PT0gdm9pZCAwID8gdm9pZCAwIDogZWwub3duZXJEb2N1bWVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmRlZmF1bHRWaWV3KSB8fCB3aW5kb3c7XG59XG5mdW5jdGlvbiBpc1dpbmRvdyh2YWwpIHtcbiAgcmV0dXJuIHZhbCAmJiBcInBvc3RNZXNzYWdlXCIgaW4gdmFsICYmIFwiYmx1clwiIGluIHZhbCAmJiBcInNlbGZcIiBpbiB2YWw7XG59XG5mdW5jdGlvbiBpc05vZGUoZWwpIHtcbiAgcmV0dXJuIGlzT2JqZWN0KGVsKSAmJiBlbC5ub2RlTmFtZSAmJiBlbC5ub2RlVHlwZSAmJiBcIm93bmVyRG9jdW1lbnRcIiBpbiBlbDtcbn1cblxuZXhwb3J0IHsgUkdCLCBSR0JBLCBIU0wsIEhTTEEsIENPTE9SX01PREVMUywgRlVOQ1RJT04sIFBST1BFUlRZLCBBUlJBWSwgT0JKRUNULCBTVFJJTkcsIE5VTUJFUiwgVU5ERUZJTkVELCBJU19XSU5ET1csIGRvYyBhcyBkb2N1bWVudCwgZ2V0Q3Jvc3NCcm93c2VyUHJvcGVydHksIFRSQU5TRk9STSwgRklMVEVSLCBBTklNQVRJT04sIEtFWUZSQU1FUywgT1BFTl9DTE9TRURfQ0hBUkFDVEVSUywgVElOWV9OVU0sIFJFVkVSU0VfVElOWV9OVU0sIERFRkFVTFRfVU5JVF9QUkVTRVRTLCBjdXRIZXgsIGhleFRvUkdCQSwgdG9GdWxsSGV4LCBoc2xUb1JHQkEsIHN0cmluZ1RvUkdCQSwgZG90LCBpc1VuZGVmaW5lZCwgaXNPYmplY3QsIGlzQXJyYXksIGlzU3RyaW5nLCBpc051bWJlciwgaXNGdW5jdGlvbiwgc3BsaXRUZXh0LCBzcGxpdFNwYWNlLCBzcGxpdENvbW1hLCBzcGxpdEJyYWNrZXQsIHNwbGl0VW5pdCwgY2FtZWxpemUsIGRlY2FtZWxpemUsIHRvQXJyYXksIG5vdywgZmluZEluZGV4LCBmaW5kTGFzdEluZGV4LCBmaW5kTGFzdCwgZmluZCwgcmVxdWVzdEFuaW1hdGlvbkZyYW1lLCBjYW5jZWxBbmltYXRpb25GcmFtZSwgZ2V0S2V5cywgZ2V0VmFsdWVzLCBnZXRFbnRyaWVzLCBzb3J0T3JkZXJzLCBjb252ZXJ0VW5pdFNpemUsIGJldHdlZW4sIGNoZWNrQm91bmRTaXplLCBjYWxjdWxhdGVCb3VuZFNpemUsIHN1bSwgYXZlcmFnZSwgZ2V0UmFkLCBnZXRDZW50ZXJQb2ludCwgZ2V0U2hhcGVEaXJlY3Rpb24sIGdldERpc3QsIHRocm90dGxlLCB0aHJvdHRsZUFycmF5LCBjb3VudGVyLCByZXBsYWNlT25jZSwgZmxhdCwgZGVlcEZsYXQsIHB1c2hTZXQsICQsIGhhc0NsYXNzLCBhZGRDbGFzcywgcmVtb3ZlQ2xhc3MsIGZyb21DU1MsIGFkZEV2ZW50LCByZW1vdmVFdmVudCwgZ2V0RG9jdW1lbnQsIGdldERvY3VtZW50RWxlbWVudCwgZ2V0RG9jdW1lbnRCb2R5LCBnZXRXaW5kb3csIGlzV2luZG93LCBpc05vZGUgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWxzLmVzbS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js\n");

/***/ })

};
;