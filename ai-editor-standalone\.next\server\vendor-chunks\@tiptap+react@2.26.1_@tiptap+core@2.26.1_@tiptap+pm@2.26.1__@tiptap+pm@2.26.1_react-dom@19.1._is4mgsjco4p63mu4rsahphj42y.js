"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+react@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1_react-dom@19.1._is4mgsjco4p63mu4rsahphj42y";
exports.ids = ["vendor-chunks/@tiptap+react@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1_react-dom@19.1._is4mgsjco4p63mu4rsahphj42y"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+react@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1_react-dom@19.1._is4mgsjco4p63mu4rsahphj42y/node_modules/@tiptap/react/dist/index.js":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+react@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1_react-dom@19.1._is4mgsjco4p63mu4rsahphj42y/node_modules/@tiptap/react/dist/index.js ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BubbleMenu: () => (/* binding */ BubbleMenu),\n/* harmony export */   CommandManager: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.CommandManager),\n/* harmony export */   Editor: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Editor),\n/* harmony export */   EditorConsumer: () => (/* binding */ EditorConsumer),\n/* harmony export */   EditorContent: () => (/* binding */ EditorContent),\n/* harmony export */   EditorContext: () => (/* binding */ EditorContext),\n/* harmony export */   EditorProvider: () => (/* binding */ EditorProvider),\n/* harmony export */   Extension: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Extension),\n/* harmony export */   FloatingMenu: () => (/* binding */ FloatingMenu),\n/* harmony export */   InputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.InputRule),\n/* harmony export */   Mark: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Mark),\n/* harmony export */   Node: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Node),\n/* harmony export */   NodePos: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.NodePos),\n/* harmony export */   NodeView: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.NodeView),\n/* harmony export */   NodeViewContent: () => (/* binding */ NodeViewContent),\n/* harmony export */   NodeViewWrapper: () => (/* binding */ NodeViewWrapper),\n/* harmony export */   PasteRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.PasteRule),\n/* harmony export */   PureEditorContent: () => (/* binding */ PureEditorContent),\n/* harmony export */   ReactNodeView: () => (/* binding */ ReactNodeView),\n/* harmony export */   ReactNodeViewContext: () => (/* binding */ ReactNodeViewContext),\n/* harmony export */   ReactNodeViewRenderer: () => (/* binding */ ReactNodeViewRenderer),\n/* harmony export */   ReactRenderer: () => (/* binding */ ReactRenderer),\n/* harmony export */   Tracker: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Tracker),\n/* harmony export */   callOrReturn: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.callOrReturn),\n/* harmony export */   canInsertNode: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.canInsertNode),\n/* harmony export */   combineTransactionSteps: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.combineTransactionSteps),\n/* harmony export */   createChainableState: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.createChainableState),\n/* harmony export */   createDocument: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.createDocument),\n/* harmony export */   createNodeFromContent: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.createNodeFromContent),\n/* harmony export */   createStyleTag: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.createStyleTag),\n/* harmony export */   defaultBlockAt: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.defaultBlockAt),\n/* harmony export */   deleteProps: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.deleteProps),\n/* harmony export */   elementFromString: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.elementFromString),\n/* harmony export */   escapeForRegEx: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.escapeForRegEx),\n/* harmony export */   extensions: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.extensions),\n/* harmony export */   findChildren: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findChildren),\n/* harmony export */   findChildrenInRange: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findChildrenInRange),\n/* harmony export */   findDuplicates: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findDuplicates),\n/* harmony export */   findParentNode: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findParentNode),\n/* harmony export */   findParentNodeClosestToPos: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.findParentNodeClosestToPos),\n/* harmony export */   fromString: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.fromString),\n/* harmony export */   generateHTML: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.generateHTML),\n/* harmony export */   generateJSON: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.generateJSON),\n/* harmony export */   generateText: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.generateText),\n/* harmony export */   getAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getAttributes),\n/* harmony export */   getAttributesFromExtensions: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getAttributesFromExtensions),\n/* harmony export */   getChangedRanges: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getChangedRanges),\n/* harmony export */   getDebugJSON: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getDebugJSON),\n/* harmony export */   getExtensionField: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getExtensionField),\n/* harmony export */   getHTMLFromFragment: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getHTMLFromFragment),\n/* harmony export */   getMarkAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getMarkAttributes),\n/* harmony export */   getMarkRange: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getMarkRange),\n/* harmony export */   getMarkType: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getMarkType),\n/* harmony export */   getMarksBetween: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getMarksBetween),\n/* harmony export */   getNodeAtPosition: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getNodeAtPosition),\n/* harmony export */   getNodeAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getNodeAttributes),\n/* harmony export */   getNodeType: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getNodeType),\n/* harmony export */   getRenderedAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getRenderedAttributes),\n/* harmony export */   getSchema: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getSchema),\n/* harmony export */   getSchemaByResolvedExtensions: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getSchemaByResolvedExtensions),\n/* harmony export */   getSchemaTypeByName: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getSchemaTypeByName),\n/* harmony export */   getSchemaTypeNameByName: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getSchemaTypeNameByName),\n/* harmony export */   getSplittedAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getSplittedAttributes),\n/* harmony export */   getText: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getText),\n/* harmony export */   getTextBetween: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getTextBetween),\n/* harmony export */   getTextContentFromNodes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getTextContentFromNodes),\n/* harmony export */   getTextSerializersFromSchema: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getTextSerializersFromSchema),\n/* harmony export */   injectExtensionAttributesToParseRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.injectExtensionAttributesToParseRule),\n/* harmony export */   inputRulesPlugin: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.inputRulesPlugin),\n/* harmony export */   isActive: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isActive),\n/* harmony export */   isAtEndOfNode: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isAtEndOfNode),\n/* harmony export */   isAtStartOfNode: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isAtStartOfNode),\n/* harmony export */   isEmptyObject: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isEmptyObject),\n/* harmony export */   isExtensionRulesEnabled: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isExtensionRulesEnabled),\n/* harmony export */   isFunction: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isFunction),\n/* harmony export */   isList: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isList),\n/* harmony export */   isMacOS: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isMacOS),\n/* harmony export */   isMarkActive: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isMarkActive),\n/* harmony export */   isNodeActive: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isNodeActive),\n/* harmony export */   isNodeEmpty: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isNodeEmpty),\n/* harmony export */   isNodeSelection: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isNodeSelection),\n/* harmony export */   isNumber: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isNumber),\n/* harmony export */   isPlainObject: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isPlainObject),\n/* harmony export */   isRegExp: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isRegExp),\n/* harmony export */   isString: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isString),\n/* harmony export */   isTextSelection: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isTextSelection),\n/* harmony export */   isiOS: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isiOS),\n/* harmony export */   markInputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.markInputRule),\n/* harmony export */   markPasteRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.markPasteRule),\n/* harmony export */   mergeAttributes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.mergeAttributes),\n/* harmony export */   mergeDeep: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.mergeDeep),\n/* harmony export */   minMax: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.minMax),\n/* harmony export */   nodeInputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.nodeInputRule),\n/* harmony export */   nodePasteRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.nodePasteRule),\n/* harmony export */   objectIncludes: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.objectIncludes),\n/* harmony export */   pasteRulesPlugin: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.pasteRulesPlugin),\n/* harmony export */   posToDOMRect: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.posToDOMRect),\n/* harmony export */   removeDuplicates: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.removeDuplicates),\n/* harmony export */   resolveFocusPosition: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.resolveFocusPosition),\n/* harmony export */   rewriteUnknownContent: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.rewriteUnknownContent),\n/* harmony export */   selectionToInsertionEnd: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.selectionToInsertionEnd),\n/* harmony export */   splitExtensions: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.splitExtensions),\n/* harmony export */   textInputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.textInputRule),\n/* harmony export */   textPasteRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.textPasteRule),\n/* harmony export */   textblockTypeInputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.textblockTypeInputRule),\n/* harmony export */   useCurrentEditor: () => (/* binding */ useCurrentEditor),\n/* harmony export */   useEditor: () => (/* binding */ useEditor),\n/* harmony export */   useEditorState: () => (/* binding */ useEditorState),\n/* harmony export */   useReactNodeView: () => (/* binding */ useReactNodeView),\n/* harmony export */   wrappingInputRule: () => (/* reexport safe */ _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.wrappingInputRule)\n/* harmony export */ });\n/* harmony import */ var _tiptap_extension_bubble_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/extension-bubble-menu */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-bubble-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-bubble-menu/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_extension_floating_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tiptap/extension-floating-menu */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-floating-menu@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-floating-menu/dist/index.js\");\n\n\n\n\n\n\n\nfunction getDefaultExportFromCjs (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\nvar shim = {exports: {}};\n\nvar useSyncExternalStoreShim_production_min = {};\n\n/**\n * @license React\n * use-sync-external-store-shim.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredUseSyncExternalStoreShim_production_min;\n\nfunction requireUseSyncExternalStoreShim_production_min () {\n\tif (hasRequiredUseSyncExternalStoreShim_production_min) return useSyncExternalStoreShim_production_min;\n\thasRequiredUseSyncExternalStoreShim_production_min = 1;\nvar e=react__WEBPACK_IMPORTED_MODULE_0__;function h(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var k=\"function\"===typeof Object.is?Object.is:h,l=e.useState,m=e.useEffect,n=e.useLayoutEffect,p=e.useDebugValue;function q(a,b){var d=b(),f=l({inst:{value:d,getSnapshot:b}}),c=f[0].inst,g=f[1];n(function(){c.value=d;c.getSnapshot=b;r(c)&&g({inst:c});},[a,d,b]);m(function(){r(c)&&g({inst:c});return a(function(){r(c)&&g({inst:c});})},[a]);p(d);return d}\n\tfunction r(a){var b=a.getSnapshot;a=a.value;try{var d=b();return !k(a,d)}catch(f){return !0}}function t(a,b){return b()}var u=\"undefined\"===typeof window||\"undefined\"===typeof window.document||\"undefined\"===typeof window.document.createElement?t:q;useSyncExternalStoreShim_production_min.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:u;\n\treturn useSyncExternalStoreShim_production_min;\n}\n\nvar useSyncExternalStoreShim_development = {};\n\n/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredUseSyncExternalStoreShim_development;\n\nfunction requireUseSyncExternalStoreShim_development () {\n\tif (hasRequiredUseSyncExternalStoreShim_development) return useSyncExternalStoreShim_development;\n\thasRequiredUseSyncExternalStoreShim_development = 1;\n\n\tif (true) {\n\t  (function() {\n\n\t/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\tif (\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n\t    'function'\n\t) {\n\t  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n\t}\n\t          var React$1 = react__WEBPACK_IMPORTED_MODULE_0__;\n\n\tvar ReactSharedInternals = React$1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\n\tfunction error(format) {\n\t  {\n\t    {\n\t      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n\t        args[_key2 - 1] = arguments[_key2];\n\t      }\n\n\t      printWarning('error', format, args);\n\t    }\n\t  }\n\t}\n\n\tfunction printWarning(level, format, args) {\n\t  // When changing this logic, you might want to also\n\t  // update consoleWithStackDev.www.js as well.\n\t  {\n\t    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n\t    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n\t    if (stack !== '') {\n\t      format += '%s';\n\t      args = args.concat([stack]);\n\t    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n\t    var argsWithFormat = args.map(function (item) {\n\t      return String(item);\n\t    }); // Careful: RN currently depends on this prefix\n\n\t    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n\t    // breaks IE9: https://github.com/facebook/react/issues/13610\n\t    // eslint-disable-next-line react-internal/no-production-logging\n\n\t    Function.prototype.apply.call(console[level], console, argsWithFormat);\n\t  }\n\t}\n\n\t/**\n\t * inlined Object.is polyfill to avoid requiring consumers ship their own\n\t * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n\t */\n\tfunction is(x, y) {\n\t  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n\t  ;\n\t}\n\n\tvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n\t// dispatch for CommonJS interop named imports.\n\n\tvar useState = React$1.useState,\n\t    useEffect = React$1.useEffect,\n\t    useLayoutEffect = React$1.useLayoutEffect,\n\t    useDebugValue = React$1.useDebugValue;\n\tvar didWarnOld18Alpha = false;\n\tvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n\t// because of a very particular set of implementation details and assumptions\n\t// -- change any one of them and it will break. The most important assumption\n\t// is that updates are always synchronous, because concurrent rendering is\n\t// only available in versions of React that also have a built-in\n\t// useSyncExternalStore API. And we only use this shim when the built-in API\n\t// does not exist.\n\t//\n\t// Do not assume that the clever hacks used by this hook also work in general.\n\t// The point of this shim is to replace the need for hacks by other libraries.\n\n\tfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n\t// React do not expose a way to check if we're hydrating. So users of the shim\n\t// will need to track that themselves and return the correct value\n\t// from `getSnapshot`.\n\tgetServerSnapshot) {\n\t  {\n\t    if (!didWarnOld18Alpha) {\n\t      if (React$1.startTransition !== undefined) {\n\t        didWarnOld18Alpha = true;\n\n\t        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n\t      }\n\t    }\n\t  } // Read the current snapshot from the store on every render. Again, this\n\t  // breaks the rules of React, and only works here because of specific\n\t  // implementation details, most importantly that updates are\n\t  // always synchronous.\n\n\n\t  var value = getSnapshot();\n\n\t  {\n\t    if (!didWarnUncachedGetSnapshot) {\n\t      var cachedValue = getSnapshot();\n\n\t      if (!objectIs(value, cachedValue)) {\n\t        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n\t        didWarnUncachedGetSnapshot = true;\n\t      }\n\t    }\n\t  } // Because updates are synchronous, we don't queue them. Instead we force a\n\t  // re-render whenever the subscribed state changes by updating an some\n\t  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n\t  // the current value.\n\t  //\n\t  // Because we don't actually use the state returned by the useState hook, we\n\t  // can save a bit of memory by storing other stuff in that slot.\n\t  //\n\t  // To implement the early bailout, we need to track some things on a mutable\n\t  // object. Usually, we would put that in a useRef hook, but we can stash it in\n\t  // our useState hook instead.\n\t  //\n\t  // To force a re-render, we call forceUpdate({inst}). That works because the\n\t  // new object always fails an equality check.\n\n\n\t  var _useState = useState({\n\t    inst: {\n\t      value: value,\n\t      getSnapshot: getSnapshot\n\t    }\n\t  }),\n\t      inst = _useState[0].inst,\n\t      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n\t  // in the layout phase so we can access it during the tearing check that\n\t  // happens on subscribe.\n\n\n\t  useLayoutEffect(function () {\n\t    inst.value = value;\n\t    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n\t    // commit phase if there was an interleaved mutation. In concurrent mode\n\t    // this can happen all the time, but even in synchronous mode, an earlier\n\t    // effect may have mutated the store.\n\n\t    if (checkIfSnapshotChanged(inst)) {\n\t      // Force a re-render.\n\t      forceUpdate({\n\t        inst: inst\n\t      });\n\t    }\n\t  }, [subscribe, value, getSnapshot]);\n\t  useEffect(function () {\n\t    // Check for changes right before subscribing. Subsequent changes will be\n\t    // detected in the subscription handler.\n\t    if (checkIfSnapshotChanged(inst)) {\n\t      // Force a re-render.\n\t      forceUpdate({\n\t        inst: inst\n\t      });\n\t    }\n\n\t    var handleStoreChange = function () {\n\t      // TODO: Because there is no cross-renderer API for batching updates, it's\n\t      // up to the consumer of this library to wrap their subscription event\n\t      // with unstable_batchedUpdates. Should we try to detect when this isn't\n\t      // the case and print a warning in development?\n\t      // The store changed. Check if the snapshot changed since the last time we\n\t      // read from the store.\n\t      if (checkIfSnapshotChanged(inst)) {\n\t        // Force a re-render.\n\t        forceUpdate({\n\t          inst: inst\n\t        });\n\t      }\n\t    }; // Subscribe to the store and return a clean-up function.\n\n\n\t    return subscribe(handleStoreChange);\n\t  }, [subscribe]);\n\t  useDebugValue(value);\n\t  return value;\n\t}\n\n\tfunction checkIfSnapshotChanged(inst) {\n\t  var latestGetSnapshot = inst.getSnapshot;\n\t  var prevValue = inst.value;\n\n\t  try {\n\t    var nextValue = latestGetSnapshot();\n\t    return !objectIs(prevValue, nextValue);\n\t  } catch (error) {\n\t    return true;\n\t  }\n\t}\n\n\tfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n\t  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n\t  // React do not expose a way to check if we're hydrating. So users of the shim\n\t  // will need to track that themselves and return the correct value\n\t  // from `getSnapshot`.\n\t  return getSnapshot();\n\t}\n\n\tvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\n\tvar isServerEnvironment = !canUseDOM;\n\n\tvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\n\tvar useSyncExternalStore$2 = React$1.useSyncExternalStore !== undefined ? React$1.useSyncExternalStore : shim;\n\n\tuseSyncExternalStoreShim_development.useSyncExternalStore = useSyncExternalStore$2;\n\t          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\tif (\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n\t    'function'\n\t) {\n\t  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n\t}\n\t        \n\t  })();\n\t}\n\treturn useSyncExternalStoreShim_development;\n}\n\nif (false) {} else {\n  shim.exports = requireUseSyncExternalStoreShim_development();\n}\n\nvar shimExports = shim.exports;\n\nconst mergeRefs = (...refs) => {\n    return (node) => {\n        refs.forEach(ref => {\n            if (typeof ref === 'function') {\n                ref(node);\n            }\n            else if (ref) {\n                ref.current = node;\n            }\n        });\n    };\n};\n/**\n * This component renders all of the editor's node views.\n */\nconst Portals = ({ contentComponent, }) => {\n    // For performance reasons, we render the node view portals on state changes only\n    const renderers = shimExports.useSyncExternalStore(contentComponent.subscribe, contentComponent.getSnapshot, contentComponent.getServerSnapshot);\n    // This allows us to directly render the portals without any additional wrapper\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, Object.values(renderers)));\n};\nfunction getInstance() {\n    const subscribers = new Set();\n    let renderers = {};\n    return {\n        /**\n         * Subscribe to the editor instance's changes.\n         */\n        subscribe(callback) {\n            subscribers.add(callback);\n            return () => {\n                subscribers.delete(callback);\n            };\n        },\n        getSnapshot() {\n            return renderers;\n        },\n        getServerSnapshot() {\n            return renderers;\n        },\n        /**\n         * Adds a new NodeView Renderer to the editor.\n         */\n        setRenderer(id, renderer) {\n            renderers = {\n                ...renderers,\n                [id]: react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(renderer.reactElement, renderer.element, id),\n            };\n            subscribers.forEach(subscriber => subscriber());\n        },\n        /**\n         * Removes a NodeView Renderer from the editor.\n         */\n        removeRenderer(id) {\n            const nextRenderers = { ...renderers };\n            delete nextRenderers[id];\n            renderers = nextRenderers;\n            subscribers.forEach(subscriber => subscriber());\n        },\n    };\n}\nclass PureEditorContent extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    constructor(props) {\n        var _a;\n        super(props);\n        this.editorContentRef = react__WEBPACK_IMPORTED_MODULE_0__.createRef();\n        this.initialized = false;\n        this.state = {\n            hasContentComponentInitialized: Boolean((_a = props.editor) === null || _a === void 0 ? void 0 : _a.contentComponent),\n        };\n    }\n    componentDidMount() {\n        this.init();\n    }\n    componentDidUpdate() {\n        this.init();\n    }\n    init() {\n        const editor = this.props.editor;\n        if (editor && !editor.isDestroyed && editor.options.element) {\n            if (editor.contentComponent) {\n                return;\n            }\n            const element = this.editorContentRef.current;\n            element.append(...editor.options.element.childNodes);\n            editor.setOptions({\n                element,\n            });\n            editor.contentComponent = getInstance();\n            // Has the content component been initialized?\n            if (!this.state.hasContentComponentInitialized) {\n                // Subscribe to the content component\n                this.unsubscribeToContentComponent = editor.contentComponent.subscribe(() => {\n                    this.setState(prevState => {\n                        if (!prevState.hasContentComponentInitialized) {\n                            return {\n                                hasContentComponentInitialized: true,\n                            };\n                        }\n                        return prevState;\n                    });\n                    // Unsubscribe to previous content component\n                    if (this.unsubscribeToContentComponent) {\n                        this.unsubscribeToContentComponent();\n                    }\n                });\n            }\n            editor.createNodeViews();\n            this.initialized = true;\n        }\n    }\n    componentWillUnmount() {\n        const editor = this.props.editor;\n        if (!editor) {\n            return;\n        }\n        this.initialized = false;\n        if (!editor.isDestroyed) {\n            editor.view.setProps({\n                nodeViews: {},\n            });\n        }\n        if (this.unsubscribeToContentComponent) {\n            this.unsubscribeToContentComponent();\n        }\n        editor.contentComponent = null;\n        if (!editor.options.element.firstChild) {\n            return;\n        }\n        const newElement = document.createElement('div');\n        newElement.append(...editor.options.element.childNodes);\n        editor.setOptions({\n            element: newElement,\n        });\n    }\n    render() {\n        const { editor, innerRef, ...rest } = this.props;\n        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null,\n            react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: mergeRefs(innerRef, this.editorContentRef), ...rest }),\n            (editor === null || editor === void 0 ? void 0 : editor.contentComponent) && react__WEBPACK_IMPORTED_MODULE_0__.createElement(Portals, { contentComponent: editor.contentComponent })));\n    }\n}\n// EditorContent should be re-created whenever the Editor instance changes\nconst EditorContentWithKey = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n    const key = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n        return Math.floor(Math.random() * 0xffffffff).toString();\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.editor]);\n    // Can't use JSX here because it conflicts with the type definition of Vue's JSX, so use createElement\n    return react__WEBPACK_IMPORTED_MODULE_0__.createElement(PureEditorContent, {\n        key,\n        innerRef: ref,\n        ...props,\n    });\n});\nconst EditorContent = react__WEBPACK_IMPORTED_MODULE_0__.memo(EditorContentWithKey);\n\nvar react = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n    if ((a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      for (i of a.entries())\n        if (!equal(i[1], b.get(i[0]))) return false;\n      return true;\n    }\n\n    if ((a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      return true;\n    }\n\n    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (key === '_owner' && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        continue;\n      }\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n\nvar deepEqual = /*@__PURE__*/getDefaultExportFromCjs(react);\n\nvar withSelector = {exports: {}};\n\nvar withSelector_production_min = {};\n\n/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredWithSelector_production_min;\n\nfunction requireWithSelector_production_min () {\n\tif (hasRequiredWithSelector_production_min) return withSelector_production_min;\n\thasRequiredWithSelector_production_min = 1;\nvar h=react__WEBPACK_IMPORTED_MODULE_0__,n=shimExports;function p(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var q=\"function\"===typeof Object.is?Object.is:p,r=n.useSyncExternalStore,t=h.useRef,u=h.useEffect,v=h.useMemo,w=h.useDebugValue;\n\twithSelector_production_min.useSyncExternalStoreWithSelector=function(a,b,e,l,g){var c=t(null);if(null===c.current){var f={hasValue:!1,value:null};c.current=f;}else f=c.current;c=v(function(){function a(a){if(!c){c=!0;d=a;a=l(a);if(void 0!==g&&f.hasValue){var b=f.value;if(g(b,a))return k=b}return k=a}b=k;if(q(d,a))return b;var e=l(a);if(void 0!==g&&g(b,e))return b;d=a;return k=e}var c=!1,d,k,m=void 0===e?null:e;return [function(){return a(b())},null===m?void 0:function(){return a(m())}]},[b,e,l,g]);var d=r(a,c[0],c[1]);\n\tu(function(){f.hasValue=!0;f.value=d;},[d]);w(d);return d};\n\treturn withSelector_production_min;\n}\n\nvar withSelector_development = {};\n\n/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredWithSelector_development;\n\nfunction requireWithSelector_development () {\n\tif (hasRequiredWithSelector_development) return withSelector_development;\n\thasRequiredWithSelector_development = 1;\n\n\tif (true) {\n\t  (function() {\n\n\t/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\tif (\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n\t    'function'\n\t) {\n\t  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n\t}\n\t          var React$1 = react__WEBPACK_IMPORTED_MODULE_0__;\n\tvar shim = shimExports;\n\n\t/**\n\t * inlined Object.is polyfill to avoid requiring consumers ship their own\n\t * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n\t */\n\tfunction is(x, y) {\n\t  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n\t  ;\n\t}\n\n\tvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n\tvar useSyncExternalStore = shim.useSyncExternalStore;\n\n\t// for CommonJS interop.\n\n\tvar useRef = React$1.useRef,\n\t    useEffect = React$1.useEffect,\n\t    useMemo = React$1.useMemo,\n\t    useDebugValue = React$1.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.\n\n\tfunction useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n\t  // Use this to track the rendered snapshot.\n\t  var instRef = useRef(null);\n\t  var inst;\n\n\t  if (instRef.current === null) {\n\t    inst = {\n\t      hasValue: false,\n\t      value: null\n\t    };\n\t    instRef.current = inst;\n\t  } else {\n\t    inst = instRef.current;\n\t  }\n\n\t  var _useMemo = useMemo(function () {\n\t    // Track the memoized state using closure variables that are local to this\n\t    // memoized instance of a getSnapshot function. Intentionally not using a\n\t    // useRef hook, because that state would be shared across all concurrent\n\t    // copies of the hook/component.\n\t    var hasMemo = false;\n\t    var memoizedSnapshot;\n\t    var memoizedSelection;\n\n\t    var memoizedSelector = function (nextSnapshot) {\n\t      if (!hasMemo) {\n\t        // The first time the hook is called, there is no memoized result.\n\t        hasMemo = true;\n\t        memoizedSnapshot = nextSnapshot;\n\n\t        var _nextSelection = selector(nextSnapshot);\n\n\t        if (isEqual !== undefined) {\n\t          // Even if the selector has changed, the currently rendered selection\n\t          // may be equal to the new selection. We should attempt to reuse the\n\t          // current value if possible, to preserve downstream memoizations.\n\t          if (inst.hasValue) {\n\t            var currentSelection = inst.value;\n\n\t            if (isEqual(currentSelection, _nextSelection)) {\n\t              memoizedSelection = currentSelection;\n\t              return currentSelection;\n\t            }\n\t          }\n\t        }\n\n\t        memoizedSelection = _nextSelection;\n\t        return _nextSelection;\n\t      } // We may be able to reuse the previous invocation's result.\n\n\n\t      // We may be able to reuse the previous invocation's result.\n\t      var prevSnapshot = memoizedSnapshot;\n\t      var prevSelection = memoizedSelection;\n\n\t      if (objectIs(prevSnapshot, nextSnapshot)) {\n\t        // The snapshot is the same as last time. Reuse the previous selection.\n\t        return prevSelection;\n\t      } // The snapshot has changed, so we need to compute a new selection.\n\n\n\t      // The snapshot has changed, so we need to compute a new selection.\n\t      var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data\n\t      // has changed. If it hasn't, return the previous selection. That signals\n\t      // to React that the selections are conceptually equal, and we can bail\n\t      // out of rendering.\n\n\t      // If a custom isEqual function is provided, use that to check if the data\n\t      // has changed. If it hasn't, return the previous selection. That signals\n\t      // to React that the selections are conceptually equal, and we can bail\n\t      // out of rendering.\n\t      if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {\n\t        return prevSelection;\n\t      }\n\n\t      memoizedSnapshot = nextSnapshot;\n\t      memoizedSelection = nextSelection;\n\t      return nextSelection;\n\t    }; // Assigning this to a constant so that Flow knows it can't change.\n\n\n\t    // Assigning this to a constant so that Flow knows it can't change.\n\t    var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;\n\n\t    var getSnapshotWithSelector = function () {\n\t      return memoizedSelector(getSnapshot());\n\t    };\n\n\t    var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function () {\n\t      return memoizedSelector(maybeGetServerSnapshot());\n\t    };\n\t    return [getSnapshotWithSelector, getServerSnapshotWithSelector];\n\t  }, [getSnapshot, getServerSnapshot, selector, isEqual]),\n\t      getSelection = _useMemo[0],\n\t      getServerSelection = _useMemo[1];\n\n\t  var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);\n\t  useEffect(function () {\n\t    inst.hasValue = true;\n\t    inst.value = value;\n\t  }, [value]);\n\t  useDebugValue(value);\n\t  return value;\n\t}\n\n\twithSelector_development.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;\n\t          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n\tif (\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n\t  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n\t    'function'\n\t) {\n\t  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n\t}\n\t        \n\t  })();\n\t}\n\treturn withSelector_development;\n}\n\nif (false) {} else {\n  withSelector.exports = requireWithSelector_development();\n}\n\nvar withSelectorExports = withSelector.exports;\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n/**\n * To synchronize the editor instance with the component state,\n * we need to create a separate instance that is not affected by the component re-renders.\n */\nclass EditorStateManager {\n    constructor(initialEditor) {\n        this.transactionNumber = 0;\n        this.lastTransactionNumber = 0;\n        this.subscribers = new Set();\n        this.editor = initialEditor;\n        this.lastSnapshot = { editor: initialEditor, transactionNumber: 0 };\n        this.getSnapshot = this.getSnapshot.bind(this);\n        this.getServerSnapshot = this.getServerSnapshot.bind(this);\n        this.watch = this.watch.bind(this);\n        this.subscribe = this.subscribe.bind(this);\n    }\n    /**\n     * Get the current editor instance.\n     */\n    getSnapshot() {\n        if (this.transactionNumber === this.lastTransactionNumber) {\n            return this.lastSnapshot;\n        }\n        this.lastTransactionNumber = this.transactionNumber;\n        this.lastSnapshot = { editor: this.editor, transactionNumber: this.transactionNumber };\n        return this.lastSnapshot;\n    }\n    /**\n     * Always disable the editor on the server-side.\n     */\n    getServerSnapshot() {\n        return { editor: null, transactionNumber: 0 };\n    }\n    /**\n     * Subscribe to the editor instance's changes.\n     */\n    subscribe(callback) {\n        this.subscribers.add(callback);\n        return () => {\n            this.subscribers.delete(callback);\n        };\n    }\n    /**\n     * Watch the editor instance for changes.\n     */\n    watch(nextEditor) {\n        this.editor = nextEditor;\n        if (this.editor) {\n            /**\n             * This will force a re-render when the editor state changes.\n             * This is to support things like `editor.can().toggleBold()` in components that `useEditor`.\n             * This could be more efficient, but it's a good trade-off for now.\n             */\n            const fn = () => {\n                this.transactionNumber += 1;\n                this.subscribers.forEach(callback => callback());\n            };\n            const currentEditor = this.editor;\n            currentEditor.on('transaction', fn);\n            return () => {\n                currentEditor.off('transaction', fn);\n            };\n        }\n        return undefined;\n    }\n}\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nfunction useEditorState(options) {\n    var _a;\n    const [editorStateManager] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new EditorStateManager(options.editor));\n    // Using the `useSyncExternalStore` hook to sync the editor instance with the component state\n    const selectedState = withSelectorExports.useSyncExternalStoreWithSelector(editorStateManager.subscribe, editorStateManager.getSnapshot, editorStateManager.getServerSnapshot, options.selector, (_a = options.equalityFn) !== null && _a !== void 0 ? _a : deepEqual);\n    useIsomorphicLayoutEffect(() => {\n        return editorStateManager.watch(options.editor);\n    }, [options.editor, editorStateManager]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(selectedState);\n    return selectedState;\n}\n\nconst isDev = \"development\" !== 'production';\nconst isSSR = typeof window === 'undefined';\nconst isNext = isSSR || Boolean(typeof window !== 'undefined' && window.next);\n/**\n * This class handles the creation, destruction, and re-creation of the editor instance.\n */\nclass EditorInstanceManager {\n    constructor(options) {\n        /**\n         * The current editor instance.\n         */\n        this.editor = null;\n        /**\n         * The subscriptions to notify when the editor instance\n         * has been created or destroyed.\n         */\n        this.subscriptions = new Set();\n        /**\n         * Whether the editor has been mounted.\n         */\n        this.isComponentMounted = false;\n        /**\n         * The most recent dependencies array.\n         */\n        this.previousDeps = null;\n        /**\n         * The unique instance ID. This is used to identify the editor instance. And will be re-generated for each new instance.\n         */\n        this.instanceId = '';\n        this.options = options;\n        this.subscriptions = new Set();\n        this.setEditor(this.getInitialEditor());\n        this.scheduleDestroy();\n        this.getEditor = this.getEditor.bind(this);\n        this.getServerSnapshot = this.getServerSnapshot.bind(this);\n        this.subscribe = this.subscribe.bind(this);\n        this.refreshEditorInstance = this.refreshEditorInstance.bind(this);\n        this.scheduleDestroy = this.scheduleDestroy.bind(this);\n        this.onRender = this.onRender.bind(this);\n        this.createEditor = this.createEditor.bind(this);\n    }\n    setEditor(editor) {\n        this.editor = editor;\n        this.instanceId = Math.random().toString(36).slice(2, 9);\n        // Notify all subscribers that the editor instance has been created\n        this.subscriptions.forEach(cb => cb());\n    }\n    getInitialEditor() {\n        if (this.options.current.immediatelyRender === undefined) {\n            if (isSSR || isNext) {\n                // TODO in the next major release, we should throw an error here\n                if (isDev) {\n                    /**\n                     * Throw an error in development, to make sure the developer is aware that tiptap cannot be SSR'd\n                     * and that they need to set `immediatelyRender` to `false` to avoid hydration mismatches.\n                     */\n                    console.warn('Tiptap Error: SSR has been detected, please set `immediatelyRender` explicitly to `false` to avoid hydration mismatches.');\n                }\n                // Best faith effort in production, run the code in the legacy mode to avoid hydration mismatches and errors in production\n                return null;\n            }\n            // Default to immediately rendering when client-side rendering\n            return this.createEditor();\n        }\n        if (this.options.current.immediatelyRender && isSSR && isDev) {\n            // Warn in development, to make sure the developer is aware that tiptap cannot be SSR'd, set `immediatelyRender` to `false` to avoid hydration mismatches.\n            throw new Error('Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.');\n        }\n        if (this.options.current.immediatelyRender) {\n            return this.createEditor();\n        }\n        return null;\n    }\n    /**\n     * Create a new editor instance. And attach event listeners.\n     */\n    createEditor() {\n        const optionsToApply = {\n            ...this.options.current,\n            // Always call the most recent version of the callback function by default\n            onBeforeCreate: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onBeforeCreate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onBlur: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onBlur) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onCreate: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onCreate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onDestroy: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onDestroy) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onFocus: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onFocus) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onSelectionUpdate: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onSelectionUpdate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onTransaction: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onTransaction) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onUpdate: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onUpdate) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onContentError: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onContentError) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onDrop: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onDrop) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n            onPaste: (...args) => { var _a, _b; return (_b = (_a = this.options.current).onPaste) === null || _b === void 0 ? void 0 : _b.call(_a, ...args); },\n        };\n        const editor = new _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Editor(optionsToApply);\n        // no need to keep track of the event listeners, they will be removed when the editor is destroyed\n        return editor;\n    }\n    /**\n     * Get the current editor instance.\n     */\n    getEditor() {\n        return this.editor;\n    }\n    /**\n     * Always disable the editor on the server-side.\n     */\n    getServerSnapshot() {\n        return null;\n    }\n    /**\n     * Subscribe to the editor instance's changes.\n     */\n    subscribe(onStoreChange) {\n        this.subscriptions.add(onStoreChange);\n        return () => {\n            this.subscriptions.delete(onStoreChange);\n        };\n    }\n    static compareOptions(a, b) {\n        return Object.keys(a).every(key => {\n            if (['onCreate', 'onBeforeCreate', 'onDestroy', 'onUpdate', 'onTransaction', 'onFocus', 'onBlur', 'onSelectionUpdate', 'onContentError', 'onDrop', 'onPaste'].includes(key)) {\n                // we don't want to compare callbacks, they are always different and only registered once\n                return true;\n            }\n            // We often encourage putting extensions inlined in the options object, so we will do a slightly deeper comparison here\n            if (key === 'extensions' && a.extensions && b.extensions) {\n                if (a.extensions.length !== b.extensions.length) {\n                    return false;\n                }\n                return a.extensions.every((extension, index) => {\n                    var _a;\n                    if (extension !== ((_a = b.extensions) === null || _a === void 0 ? void 0 : _a[index])) {\n                        return false;\n                    }\n                    return true;\n                });\n            }\n            if (a[key] !== b[key]) {\n                // if any of the options have changed, we should update the editor options\n                return false;\n            }\n            return true;\n        });\n    }\n    /**\n     * On each render, we will create, update, or destroy the editor instance.\n     * @param deps The dependencies to watch for changes\n     * @returns A cleanup function\n     */\n    onRender(deps) {\n        // The returned callback will run on each render\n        return () => {\n            this.isComponentMounted = true;\n            // Cleanup any scheduled destructions, since we are currently rendering\n            clearTimeout(this.scheduledDestructionTimeout);\n            if (this.editor && !this.editor.isDestroyed && deps.length === 0) {\n                // if the editor does exist & deps are empty, we don't need to re-initialize the editor generally\n                if (!EditorInstanceManager.compareOptions(this.options.current, this.editor.options)) {\n                    // But, the options are different, so we need to update the editor options\n                    // Still, this is faster than re-creating the editor\n                    this.editor.setOptions({\n                        ...this.options.current,\n                        editable: this.editor.isEditable,\n                    });\n                }\n            }\n            else {\n                // When the editor:\n                // - does not yet exist\n                // - is destroyed\n                // - the deps array changes\n                // We need to destroy the editor instance and re-initialize it\n                this.refreshEditorInstance(deps);\n            }\n            return () => {\n                this.isComponentMounted = false;\n                this.scheduleDestroy();\n            };\n        };\n    }\n    /**\n     * Recreate the editor instance if the dependencies have changed.\n     */\n    refreshEditorInstance(deps) {\n        if (this.editor && !this.editor.isDestroyed) {\n            // Editor instance already exists\n            if (this.previousDeps === null) {\n                // If lastDeps has not yet been initialized, reuse the current editor instance\n                this.previousDeps = deps;\n                return;\n            }\n            const depsAreEqual = this.previousDeps.length === deps.length\n                && this.previousDeps.every((dep, index) => dep === deps[index]);\n            if (depsAreEqual) {\n                // deps exist and are equal, no need to recreate\n                return;\n            }\n        }\n        if (this.editor && !this.editor.isDestroyed) {\n            // Destroy the editor instance if it exists\n            this.editor.destroy();\n        }\n        this.setEditor(this.createEditor());\n        // Update the lastDeps to the current deps\n        this.previousDeps = deps;\n    }\n    /**\n     * Schedule the destruction of the editor instance.\n     * This will only destroy the editor if it was not mounted on the next tick.\n     * This is to avoid destroying the editor instance when it's actually still mounted.\n     */\n    scheduleDestroy() {\n        const currentInstanceId = this.instanceId;\n        const currentEditor = this.editor;\n        // Wait two ticks to see if the component is still mounted\n        this.scheduledDestructionTimeout = setTimeout(() => {\n            if (this.isComponentMounted && this.instanceId === currentInstanceId) {\n                // If still mounted on the following tick, with the same instanceId, do not destroy the editor\n                if (currentEditor) {\n                    // just re-apply options as they might have changed\n                    currentEditor.setOptions(this.options.current);\n                }\n                return;\n            }\n            if (currentEditor && !currentEditor.isDestroyed) {\n                currentEditor.destroy();\n                if (this.instanceId === currentInstanceId) {\n                    this.setEditor(null);\n                }\n            }\n            // This allows the effect to run again between ticks\n            // which may save us from having to re-create the editor\n        }, 1);\n    }\n}\nfunction useEditor(options = {}, deps = []) {\n    const mostRecentOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(options);\n    mostRecentOptions.current = options;\n    const [instanceManager] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new EditorInstanceManager(mostRecentOptions));\n    const editor = shimExports.useSyncExternalStore(instanceManager.subscribe, instanceManager.getEditor, instanceManager.getServerSnapshot);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(editor);\n    // This effect will handle creating/updating the editor instance\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(instanceManager.onRender(deps));\n    // The default behavior is to re-render on each transaction\n    // This is legacy behavior that will be removed in future versions\n    useEditorState({\n        editor,\n        selector: ({ transactionNumber }) => {\n            if (options.shouldRerenderOnTransaction === false) {\n                // This will prevent the editor from re-rendering on each transaction\n                return null;\n            }\n            // This will avoid re-rendering on the first transaction when `immediatelyRender` is set to `true`\n            if (options.immediatelyRender && transactionNumber === 0) {\n                return 0;\n            }\n            return transactionNumber + 1;\n        },\n    });\n    return editor;\n}\n\nconst EditorContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    editor: null,\n});\nconst EditorConsumer = EditorContext.Consumer;\n/**\n * A hook to get the current editor instance.\n */\nconst useCurrentEditor = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(EditorContext);\n/**\n * This is the provider component for the editor.\n * It allows the editor to be accessible across the entire component tree\n * with `useCurrentEditor`.\n */\nfunction EditorProvider({ children, slotAfter, slotBefore, editorContainerProps = {}, ...editorOptions }) {\n    const editor = useEditor(editorOptions);\n    if (!editor) {\n        return null;\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(EditorContext.Provider, { value: { editor } },\n        slotBefore,\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(EditorConsumer, null, ({ editor: currentEditor }) => (react__WEBPACK_IMPORTED_MODULE_0__.createElement(EditorContent, { editor: currentEditor, ...editorContainerProps }))),\n        children,\n        slotAfter));\n}\n\nconst BubbleMenu = (props) => {\n    const [element, setElement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const { editor: currentEditor } = useCurrentEditor();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        var _a;\n        if (!element) {\n            return;\n        }\n        if (((_a = props.editor) === null || _a === void 0 ? void 0 : _a.isDestroyed) || (currentEditor === null || currentEditor === void 0 ? void 0 : currentEditor.isDestroyed)) {\n            return;\n        }\n        const { pluginKey = 'bubbleMenu', editor, tippyOptions = {}, updateDelay, shouldShow = null, } = props;\n        const menuEditor = editor || currentEditor;\n        if (!menuEditor) {\n            console.warn('BubbleMenu component is not rendered inside of an editor component or does not have editor prop.');\n            return;\n        }\n        const plugin = (0,_tiptap_extension_bubble_menu__WEBPACK_IMPORTED_MODULE_3__.BubbleMenuPlugin)({\n            updateDelay,\n            editor: menuEditor,\n            element,\n            pluginKey,\n            shouldShow,\n            tippyOptions,\n        });\n        menuEditor.registerPlugin(plugin);\n        return () => { menuEditor.unregisterPlugin(pluginKey); };\n    }, [props.editor, currentEditor, element]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setElement, className: props.className, style: { visibility: 'hidden' } }, props.children));\n};\n\nconst FloatingMenu = (props) => {\n    const [element, setElement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const { editor: currentEditor } = useCurrentEditor();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        var _a;\n        if (!element) {\n            return;\n        }\n        if (((_a = props.editor) === null || _a === void 0 ? void 0 : _a.isDestroyed) || (currentEditor === null || currentEditor === void 0 ? void 0 : currentEditor.isDestroyed)) {\n            return;\n        }\n        const { pluginKey = 'floatingMenu', editor, tippyOptions = {}, shouldShow = null, } = props;\n        const menuEditor = editor || currentEditor;\n        if (!menuEditor) {\n            console.warn('FloatingMenu component is not rendered inside of an editor component or does not have editor prop.');\n            return;\n        }\n        const plugin = (0,_tiptap_extension_floating_menu__WEBPACK_IMPORTED_MODULE_4__.FloatingMenuPlugin)({\n            pluginKey,\n            editor: menuEditor,\n            element,\n            tippyOptions,\n            shouldShow,\n        });\n        menuEditor.registerPlugin(plugin);\n        return () => { menuEditor.unregisterPlugin(pluginKey); };\n    }, [\n        props.editor,\n        currentEditor,\n        element,\n    ]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: setElement, className: props.className, style: { visibility: 'hidden' } }, props.children));\n};\n\nconst ReactNodeViewContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    onDragStart: undefined,\n});\nconst useReactNodeView = () => (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ReactNodeViewContext);\n\nconst NodeViewContent = props => {\n    const Tag = props.as || 'div';\n    const { nodeViewContentRef } = useReactNodeView();\n    return (\n    // @ts-ignore\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(Tag, { ...props, ref: nodeViewContentRef, \"data-node-view-content\": \"\", style: {\n            whiteSpace: 'pre-wrap',\n            ...props.style,\n        } }));\n};\n\nconst NodeViewWrapper = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, ref) => {\n    const { onDragStart } = useReactNodeView();\n    const Tag = props.as || 'div';\n    return (\n    // @ts-ignore\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(Tag, { ...props, ref: ref, \"data-node-view-wrapper\": \"\", onDragStart: onDragStart, style: {\n            whiteSpace: 'normal',\n            ...props.style,\n        } }));\n});\n\n/**\n * Check if a component is a class component.\n * @param Component\n * @returns {boolean}\n */\nfunction isClassComponent(Component) {\n    return !!(typeof Component === 'function'\n        && Component.prototype\n        && Component.prototype.isReactComponent);\n}\n/**\n * Check if a component is a forward ref component.\n * @param Component\n * @returns {boolean}\n */\nfunction isForwardRefComponent(Component) {\n    return !!(typeof Component === 'object'\n        && Component.$$typeof\n        && (Component.$$typeof.toString() === 'Symbol(react.forward_ref)'\n            || Component.$$typeof.description === 'react.forward_ref'));\n}\n/**\n * Check if a component is a memoized component.\n * @param Component\n * @returns {boolean}\n */\nfunction isMemoComponent(Component) {\n    return !!(typeof Component === 'object'\n        && Component.$$typeof\n        && (Component.$$typeof.toString() === 'Symbol(react.memo)' || Component.$$typeof.description === 'react.memo'));\n}\n/**\n * Check if a component can safely receive a ref prop.\n * This includes class components, forwardRef components, and memoized components\n * that wrap forwardRef or class components.\n * @param Component\n * @returns {boolean}\n */\nfunction canReceiveRef(Component) {\n    // Check if it's a class component\n    if (isClassComponent(Component)) {\n        return true;\n    }\n    // Check if it's a forwardRef component\n    if (isForwardRefComponent(Component)) {\n        return true;\n    }\n    // Check if it's a memoized component\n    if (isMemoComponent(Component)) {\n        // For memoized components, check the wrapped component\n        const wrappedComponent = Component.type;\n        if (wrappedComponent) {\n            return isClassComponent(wrappedComponent) || isForwardRefComponent(wrappedComponent);\n        }\n    }\n    return false;\n}\n/**\n * Check if we're running React 19+ by detecting if function components support ref props\n * @returns {boolean}\n */\nfunction isReact19Plus() {\n    // React 19 is detected by checking React version if available\n    // In practice, we'll use a more conservative approach and assume React 18 behavior\n    // unless we can definitively detect React 19\n    try {\n        // @ts-ignore\n        if (react__WEBPACK_IMPORTED_MODULE_0__.version) {\n            const majorVersion = parseInt(react__WEBPACK_IMPORTED_MODULE_0__.version.split('.')[0], 10);\n            return majorVersion >= 19;\n        }\n    }\n    catch {\n        // Fallback to React 18 behavior if we can't determine version\n    }\n    return false;\n}\n/**\n * The ReactRenderer class. It's responsible for rendering React components inside the editor.\n * @example\n * new ReactRenderer(MyComponent, {\n *   editor,\n *   props: {\n *     foo: 'bar',\n *   },\n *   as: 'span',\n * })\n*/\nclass ReactRenderer {\n    /**\n     * Immediately creates element and renders the provided React component.\n     */\n    constructor(component, { editor, props = {}, as = 'div', className = '', }) {\n        this.ref = null;\n        this.id = Math.floor(Math.random() * 0xFFFFFFFF).toString();\n        this.component = component;\n        this.editor = editor;\n        this.props = props;\n        this.element = document.createElement(as);\n        this.element.classList.add('react-renderer');\n        if (className) {\n            this.element.classList.add(...className.split(' '));\n        }\n        // If the editor is already initialized, we will need to\n        // synchronously render the component to ensure it renders\n        // together with Prosemirror's rendering.\n        if (this.editor.isInitialized) {\n            (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => {\n                this.render();\n            });\n        }\n        else {\n            queueMicrotask(() => {\n                this.render();\n            });\n        }\n    }\n    /**\n     * Render the React component.\n     */\n    render() {\n        var _a;\n        const Component = this.component;\n        const props = this.props;\n        const editor = this.editor;\n        // Handle ref forwarding with React 18/19 compatibility\n        const isReact19 = isReact19Plus();\n        const componentCanReceiveRef = canReceiveRef(Component);\n        const elementProps = { ...props };\n        // Always remove ref if the component cannot receive it (unless React 19+)\n        if (elementProps.ref && !(isReact19 || componentCanReceiveRef)) {\n            delete elementProps.ref;\n        }\n        // Only assign our own ref if allowed\n        if (!elementProps.ref && (isReact19 || componentCanReceiveRef)) {\n            // @ts-ignore - Setting ref prop for compatible components\n            elementProps.ref = (ref) => {\n                this.ref = ref;\n            };\n        }\n        this.reactElement = react__WEBPACK_IMPORTED_MODULE_0__.createElement(Component, { ...elementProps });\n        (_a = editor === null || editor === void 0 ? void 0 : editor.contentComponent) === null || _a === void 0 ? void 0 : _a.setRenderer(this.id, this);\n    }\n    /**\n     * Re-renders the React component with new props.\n     */\n    updateProps(props = {}) {\n        this.props = {\n            ...this.props,\n            ...props,\n        };\n        this.render();\n    }\n    /**\n     * Destroy the React component.\n     */\n    destroy() {\n        var _a;\n        const editor = this.editor;\n        (_a = editor === null || editor === void 0 ? void 0 : editor.contentComponent) === null || _a === void 0 ? void 0 : _a.removeRenderer(this.id);\n    }\n    /**\n     * Update the attributes of the element that holds the React component.\n     */\n    updateAttributes(attributes) {\n        Object.keys(attributes).forEach(key => {\n            this.element.setAttribute(key, attributes[key]);\n        });\n    }\n}\n\nclass ReactNodeView extends _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.NodeView {\n    constructor(component, props, options) {\n        super(component, props, options);\n        if (!this.node.isLeaf) {\n            if (this.options.contentDOMElementTag) {\n                this.contentDOMElement = document.createElement(this.options.contentDOMElementTag);\n            }\n            else {\n                this.contentDOMElement = document.createElement(this.node.isInline ? 'span' : 'div');\n            }\n            this.contentDOMElement.dataset.nodeViewContentReact = '';\n            this.contentDOMElement.dataset.nodeViewWrapper = '';\n            // For some reason the whiteSpace prop is not inherited properly in Chrome and Safari\n            // With this fix it seems to work fine\n            // See: https://github.com/ueberdosis/tiptap/issues/1197\n            this.contentDOMElement.style.whiteSpace = 'inherit';\n            const contentTarget = this.dom.querySelector('[data-node-view-content]');\n            if (!contentTarget) {\n                return;\n            }\n            contentTarget.appendChild(this.contentDOMElement);\n        }\n    }\n    /**\n     * Setup the React component.\n     * Called on initialization.\n     */\n    mount() {\n        const props = {\n            editor: this.editor,\n            node: this.node,\n            decorations: this.decorations,\n            innerDecorations: this.innerDecorations,\n            view: this.view,\n            selected: false,\n            extension: this.extension,\n            HTMLAttributes: this.HTMLAttributes,\n            getPos: () => this.getPos(),\n            updateAttributes: (attributes = {}) => this.updateAttributes(attributes),\n            deleteNode: () => this.deleteNode(),\n            ref: (0,react__WEBPACK_IMPORTED_MODULE_0__.createRef)(),\n        };\n        if (!this.component.displayName) {\n            const capitalizeFirstChar = (string) => {\n                return string.charAt(0).toUpperCase() + string.substring(1);\n            };\n            this.component.displayName = capitalizeFirstChar(this.extension.name);\n        }\n        const onDragStart = this.onDragStart.bind(this);\n        const nodeViewContentRef = element => {\n            if (element && this.contentDOMElement && element.firstChild !== this.contentDOMElement) {\n                // remove the nodeViewWrapper attribute from the element\n                if (element.hasAttribute('data-node-view-wrapper')) {\n                    element.removeAttribute('data-node-view-wrapper');\n                }\n                element.appendChild(this.contentDOMElement);\n            }\n        };\n        const context = { onDragStart, nodeViewContentRef };\n        const Component = this.component;\n        // For performance reasons, we memoize the provider component\n        // And all of the things it requires are declared outside of the component, so it doesn't need to re-render\n        const ReactNodeViewProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(componentProps => {\n            return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(ReactNodeViewContext.Provider, { value: context }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Component, componentProps)));\n        });\n        ReactNodeViewProvider.displayName = 'ReactNodeView';\n        let as = this.node.isInline ? 'span' : 'div';\n        if (this.options.as) {\n            as = this.options.as;\n        }\n        const { className = '' } = this.options;\n        this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this);\n        this.renderer = new ReactRenderer(ReactNodeViewProvider, {\n            editor: this.editor,\n            props,\n            as,\n            className: `node-${this.node.type.name} ${className}`.trim(),\n        });\n        this.editor.on('selectionUpdate', this.handleSelectionUpdate);\n        this.updateElementAttributes();\n    }\n    /**\n     * Return the DOM element.\n     * This is the element that will be used to display the node view.\n     */\n    get dom() {\n        var _a;\n        if (this.renderer.element.firstElementChild\n            && !((_a = this.renderer.element.firstElementChild) === null || _a === void 0 ? void 0 : _a.hasAttribute('data-node-view-wrapper'))) {\n            throw Error('Please use the NodeViewWrapper component for your node view.');\n        }\n        return this.renderer.element;\n    }\n    /**\n     * Return the content DOM element.\n     * This is the element that will be used to display the rich-text content of the node.\n     */\n    get contentDOM() {\n        if (this.node.isLeaf) {\n            return null;\n        }\n        return this.contentDOMElement;\n    }\n    /**\n     * On editor selection update, check if the node is selected.\n     * If it is, call `selectNode`, otherwise call `deselectNode`.\n     */\n    handleSelectionUpdate() {\n        const { from, to } = this.editor.state.selection;\n        const pos = this.getPos();\n        if (typeof pos !== 'number') {\n            return;\n        }\n        if (from <= pos && to >= pos + this.node.nodeSize) {\n            if (this.renderer.props.selected) {\n                return;\n            }\n            this.selectNode();\n        }\n        else {\n            if (!this.renderer.props.selected) {\n                return;\n            }\n            this.deselectNode();\n        }\n    }\n    /**\n     * On update, update the React component.\n     * To prevent unnecessary updates, the `update` option can be used.\n     */\n    update(node, decorations, innerDecorations) {\n        const rerenderComponent = (props) => {\n            this.renderer.updateProps(props);\n            if (typeof this.options.attrs === 'function') {\n                this.updateElementAttributes();\n            }\n        };\n        if (node.type !== this.node.type) {\n            return false;\n        }\n        if (typeof this.options.update === 'function') {\n            const oldNode = this.node;\n            const oldDecorations = this.decorations;\n            const oldInnerDecorations = this.innerDecorations;\n            this.node = node;\n            this.decorations = decorations;\n            this.innerDecorations = innerDecorations;\n            return this.options.update({\n                oldNode,\n                oldDecorations,\n                newNode: node,\n                newDecorations: decorations,\n                oldInnerDecorations,\n                innerDecorations,\n                updateProps: () => rerenderComponent({ node, decorations, innerDecorations }),\n            });\n        }\n        if (node === this.node\n            && this.decorations === decorations\n            && this.innerDecorations === innerDecorations) {\n            return true;\n        }\n        this.node = node;\n        this.decorations = decorations;\n        this.innerDecorations = innerDecorations;\n        rerenderComponent({ node, decorations, innerDecorations });\n        return true;\n    }\n    /**\n     * Select the node.\n     * Add the `selected` prop and the `ProseMirror-selectednode` class.\n     */\n    selectNode() {\n        this.renderer.updateProps({\n            selected: true,\n        });\n        this.renderer.element.classList.add('ProseMirror-selectednode');\n    }\n    /**\n     * Deselect the node.\n     * Remove the `selected` prop and the `ProseMirror-selectednode` class.\n     */\n    deselectNode() {\n        this.renderer.updateProps({\n            selected: false,\n        });\n        this.renderer.element.classList.remove('ProseMirror-selectednode');\n    }\n    /**\n     * Destroy the React component instance.\n     */\n    destroy() {\n        this.renderer.destroy();\n        this.editor.off('selectionUpdate', this.handleSelectionUpdate);\n        this.contentDOMElement = null;\n    }\n    /**\n     * Update the attributes of the top-level element that holds the React component.\n     * Applying the attributes defined in the `attrs` option.\n     */\n    updateElementAttributes() {\n        if (this.options.attrs) {\n            let attrsObj = {};\n            if (typeof this.options.attrs === 'function') {\n                const extensionAttributes = this.editor.extensionManager.attributes;\n                const HTMLAttributes = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.getRenderedAttributes)(this.node, extensionAttributes);\n                attrsObj = this.options.attrs({ node: this.node, HTMLAttributes });\n            }\n            else {\n                attrsObj = this.options.attrs;\n            }\n            this.renderer.updateAttributes(attrsObj);\n        }\n    }\n}\n/**\n * Create a React node view renderer.\n */\nfunction ReactNodeViewRenderer(component, options) {\n    return props => {\n        // try to get the parent component\n        // this is important for vue devtools to show the component hierarchy correctly\n        // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n        if (!props.editor.contentComponent) {\n            return {};\n        }\n        return new ReactNodeView(component, props, options);\n    };\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+react@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1_react-dom@19.1._is4mgsjco4p63mu4rsahphj42y/node_modules/@tiptap/react/dist/index.js\n");

/***/ })

};
;