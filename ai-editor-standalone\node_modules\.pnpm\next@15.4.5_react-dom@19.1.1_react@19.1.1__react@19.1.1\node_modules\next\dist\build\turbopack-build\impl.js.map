{"version": 3, "sources": ["../../../src/build/turbopack-build/impl.ts"], "sourcesContent": ["import path from 'path'\nimport { validateTurboNextConfig } from '../../lib/turbopack-warning'\nimport {\n  formatIssue,\n  getTurbopackJsConfig,\n  isPersistentCachingEnabled,\n  isRelevantWarning,\n} from '../../shared/lib/turbopack/utils'\nimport { NextBuildContext } from '../build-context'\nimport { createDefineEnv, loadBindings } from '../swc'\nimport {\n  rawEntrypointsToEntrypoints,\n  handleRouteType,\n} from '../handle-entrypoints'\nimport { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport { promises as fs } from 'fs'\nimport { PHASE_PRODUCTION_BUILD } from '../../shared/lib/constants'\nimport loadConfig from '../../server/config'\nimport { hasCustomExportOutput } from '../../export/utils'\nimport { Telemetry } from '../../telemetry/storage'\nimport { setGlobal } from '../../trace'\nimport { isCI } from '../../server/ci-info'\nimport { backgroundLogCompilationEvents } from '../../shared/lib/turbopack/compilation-events'\nimport { getSupportedBrowsers } from '../utils'\nimport { normalizePath } from '../../lib/normalize-path'\n\nexport async function turbopackBuild(): Promise<{\n  duration: number\n  buildTraceContext: undefined\n  shutdownPromise: Promise<void>\n}> {\n  await validateTurboNextConfig({\n    dir: NextBuildContext.dir!,\n    isDev: false,\n  })\n\n  const config = NextBuildContext.config!\n  const dir = NextBuildContext.dir!\n  const distDir = NextBuildContext.distDir!\n  const buildId = NextBuildContext.buildId!\n  const encryptionKey = NextBuildContext.encryptionKey!\n  const previewProps = NextBuildContext.previewProps!\n  const hasRewrites = NextBuildContext.hasRewrites!\n  const rewrites = NextBuildContext.rewrites!\n  const appDirOnly = NextBuildContext.appDirOnly!\n  const noMangling = NextBuildContext.noMangling!\n  const currentNodeJsVersion = process.versions.node\n\n  const startTime = process.hrtime()\n  const bindings = await loadBindings(config?.experimental?.useWasmBinary)\n  const dev = false\n\n  const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n\n  const persistentCaching = isPersistentCachingEnabled(config)\n  const rootPath = config.turbopack?.root || config.outputFileTracingRoot || dir\n  const project = await bindings.turbo.createProject(\n    {\n      rootPath: config.turbopack?.root || config.outputFileTracingRoot || dir,\n      projectPath: normalizePath(path.relative(rootPath, dir) || '.'),\n      distDir,\n      nextConfig: config,\n      jsConfig: await getTurbopackJsConfig(dir, config),\n      watch: {\n        enable: false,\n      },\n      dev,\n      env: process.env as Record<string, string>,\n      defineEnv: createDefineEnv({\n        isTurbopack: true,\n        clientRouterFilters: NextBuildContext.clientRouterFilters!,\n        config,\n        dev,\n        distDir,\n        projectPath: dir,\n        fetchCacheKeyPrefix: config.experimental.fetchCacheKeyPrefix,\n        hasRewrites,\n        // Implemented separately in Turbopack, doesn't have to be passed here.\n        middlewareMatchers: undefined,\n        rewrites,\n      }),\n      buildId,\n      encryptionKey,\n      previewProps,\n      browserslistQuery: supportedBrowsers.join(', '),\n      noMangling,\n      currentNodeJsVersion,\n    },\n    {\n      persistentCaching,\n      memoryLimit: config.experimental?.turbopackMemoryLimit,\n      dependencyTracking: persistentCaching,\n      isCi: isCI,\n    }\n  )\n  try {\n    backgroundLogCompilationEvents(project)\n\n    // Write an empty file in a known location to signal this was built with Turbopack\n    await fs.writeFile(path.join(distDir, 'turbopack'), '')\n\n    await fs.mkdir(path.join(distDir, 'server'), { recursive: true })\n    await fs.mkdir(path.join(distDir, 'static', buildId), {\n      recursive: true,\n    })\n    await fs.writeFile(\n      path.join(distDir, 'package.json'),\n      JSON.stringify(\n        {\n          type: 'commonjs',\n        },\n        null,\n        2\n      )\n    )\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const entrypoints = await project.writeAllEntrypointsToDisk(appDirOnly)\n\n    const manifestLoader = new TurbopackManifestLoader({\n      buildId,\n      distDir,\n      encryptionKey,\n    })\n\n    const topLevelErrors = []\n    const topLevelWarnings = []\n    for (const issue of entrypoints.issues) {\n      if (issue.severity === 'error' || issue.severity === 'fatal') {\n        topLevelErrors.push(formatIssue(issue))\n      } else if (isRelevantWarning(issue)) {\n        topLevelWarnings.push(formatIssue(issue))\n      }\n    }\n\n    if (topLevelWarnings.length > 0) {\n      console.warn(\n        `Turbopack build encountered ${\n          topLevelWarnings.length\n        } warnings:\\n${topLevelWarnings.join('\\n')}`\n      )\n    }\n\n    if (topLevelErrors.length > 0) {\n      throw new Error(\n        `Turbopack build failed with ${\n          topLevelErrors.length\n        } errors:\\n${topLevelErrors.join('\\n')}`\n      )\n    }\n\n    const currentEntrypoints = await rawEntrypointsToEntrypoints(entrypoints)\n\n    const promises: Promise<any>[] = []\n\n    if (!appDirOnly) {\n      for (const [page, route] of currentEntrypoints.page) {\n        promises.push(\n          handleRouteType({\n            page,\n            route,\n            manifestLoader,\n          })\n        )\n      }\n    }\n\n    for (const [page, route] of currentEntrypoints.app) {\n      promises.push(\n        handleRouteType({\n          page,\n          route,\n          manifestLoader,\n        })\n      )\n    }\n\n    await Promise.all(promises)\n\n    await Promise.all([\n      manifestLoader.loadBuildManifest('_app'),\n      manifestLoader.loadPagesManifest('_app'),\n      manifestLoader.loadFontManifest('_app'),\n      manifestLoader.loadPagesManifest('_document'),\n      manifestLoader.loadBuildManifest('_error'),\n      manifestLoader.loadPagesManifest('_error'),\n      manifestLoader.loadFontManifest('_error'),\n      entrypoints.instrumentation &&\n        manifestLoader.loadMiddlewareManifest(\n          'instrumentation',\n          'instrumentation'\n        ),\n      entrypoints.middleware &&\n        (await manifestLoader.loadMiddlewareManifest(\n          'middleware',\n          'middleware'\n        )),\n    ])\n\n    await manifestLoader.writeManifests({\n      devRewrites: undefined,\n      productionRewrites: rewrites,\n      entrypoints: currentEntrypoints,\n    })\n\n    const shutdownPromise = project.shutdown()\n\n    const time = process.hrtime(startTime)\n    return {\n      duration: time[0] + time[1] / 1e9,\n      buildTraceContext: undefined,\n      shutdownPromise,\n    }\n  } catch (err) {\n    await project.shutdown()\n    throw err\n  }\n}\n\nlet shutdownPromise: Promise<void> | undefined\nexport async function workerMain(workerData: {\n  buildContext: typeof NextBuildContext\n}): Promise<Awaited<ReturnType<typeof turbopackBuild>>> {\n  // setup new build context from the serialized data passed from the parent\n  Object.assign(NextBuildContext, workerData.buildContext)\n\n  /// load the config because it's not serializable\n  NextBuildContext.config = await loadConfig(\n    PHASE_PRODUCTION_BUILD,\n    NextBuildContext.dir!,\n    { debugPrerender: NextBuildContext.debugPrerender }\n  )\n\n  // Matches handling in build/index.ts\n  // https://github.com/vercel/next.js/blob/84f347fc86f4efc4ec9f13615c215e4b9fb6f8f0/packages/next/src/build/index.ts#L815-L818\n  // Ensures the `config.distDir` option is matched.\n  if (hasCustomExportOutput(NextBuildContext.config)) {\n    NextBuildContext.config.distDir = '.next'\n  }\n\n  // Clone the telemetry for worker\n  const telemetry = new Telemetry({\n    distDir: NextBuildContext.config.distDir,\n  })\n  setGlobal('telemetry', telemetry)\n\n  const result = await turbopackBuild()\n  shutdownPromise = result.shutdownPromise\n  return result\n}\n\nexport async function waitForShutdown(): Promise<void> {\n  if (shutdownPromise) {\n    await shutdownPromise\n  }\n}\n"], "names": ["turbopackBuild", "waitForShutdown", "worker<PERSON>ain", "config", "validateTurboNextConfig", "dir", "NextBuildContext", "isDev", "distDir", "buildId", "<PERSON><PERSON><PERSON>", "previewProps", "hasRewrites", "rewrites", "appDirOnly", "noMangling", "currentNodeJsVersion", "process", "versions", "node", "startTime", "hrtime", "bindings", "loadBindings", "experimental", "useWasmBinary", "dev", "supportedBrowsers", "getSupportedBrowsers", "persistentCaching", "isPersistentCachingEnabled", "rootPath", "turbopack", "root", "outputFileTracingRoot", "project", "turbo", "createProject", "projectPath", "normalizePath", "path", "relative", "nextConfig", "jsConfig", "getTurbopackJsConfig", "watch", "enable", "env", "defineEnv", "createDefineEnv", "isTurbopack", "clientRouterFilters", "fetchCacheKeyPrefix", "middlewareMatchers", "undefined", "browserslistQuery", "join", "memoryLimit", "turbopackMemoryLimit", "dependencyTracking", "isCi", "isCI", "backgroundLogCompilationEvents", "fs", "writeFile", "mkdir", "recursive", "JSON", "stringify", "type", "entrypoints", "writeAllEntrypointsToDisk", "manifest<PERSON><PERSON>der", "TurbopackManifestLoader", "topLevelErrors", "topLevelWarnings", "issue", "issues", "severity", "push", "formatIssue", "isRelevantWarning", "length", "console", "warn", "Error", "currentEntrypoints", "rawEntrypointsToEntrypoints", "promises", "page", "route", "handleRouteType", "app", "Promise", "all", "loadBuildManifest", "loadPagesManifest", "loadFontManifest", "instrumentation", "loadMiddlewareManifest", "middleware", "writeManifests", "devRewrites", "productionRewrites", "shutdownPromise", "shutdown", "time", "duration", "buildTraceContext", "err", "workerData", "Object", "assign", "buildContext", "loadConfig", "PHASE_PRODUCTION_BUILD", "debugPrerender", "hasCustomExportOutput", "telemetry", "Telemetry", "setGlobal", "result"], "mappings": ";;;;;;;;;;;;;;;;IA0BsBA,cAAc;eAAdA;;IAiOAC,eAAe;eAAfA;;IA/BAC,UAAU;eAAVA;;;6DA5NL;kCACuB;uBAMjC;8BAC0B;qBACa;mCAIvC;gCACiC;oBACT;2BACQ;+DAChB;wBACe;yBACZ;uBACA;wBACL;mCAC0B;wBACV;+BACP;;;;;;AAEvB,eAAeF;QAuBgBG,sBAMnBA,mBAGHA,oBAgCGA;IA3DjB,MAAMC,IAAAA,yCAAuB,EAAC;QAC5BC,KAAKC,8BAAgB,CAACD,GAAG;QACzBE,OAAO;IACT;IAEA,MAAMJ,SAASG,8BAAgB,CAACH,MAAM;IACtC,MAAME,MAAMC,8BAAgB,CAACD,GAAG;IAChC,MAAMG,UAAUF,8BAAgB,CAACE,OAAO;IACxC,MAAMC,UAAUH,8BAAgB,CAACG,OAAO;IACxC,MAAMC,gBAAgBJ,8BAAgB,CAACI,aAAa;IACpD,MAAMC,eAAeL,8BAAgB,CAACK,YAAY;IAClD,MAAMC,cAAcN,8BAAgB,CAACM,WAAW;IAChD,MAAMC,WAAWP,8BAAgB,CAACO,QAAQ;IAC1C,MAAMC,aAAaR,8BAAgB,CAACQ,UAAU;IAC9C,MAAMC,aAAaT,8BAAgB,CAACS,UAAU;IAC9C,MAAMC,uBAAuBC,QAAQC,QAAQ,CAACC,IAAI;IAElD,MAAMC,YAAYH,QAAQI,MAAM;IAChC,MAAMC,WAAW,MAAMC,IAAAA,iBAAY,EAACpB,2BAAAA,uBAAAA,OAAQqB,YAAY,qBAApBrB,qBAAsBsB,aAAa;IACvE,MAAMC,MAAM;IAEZ,MAAMC,oBAAoB,MAAMC,IAAAA,4BAAoB,EAACvB,KAAKqB;IAE1D,MAAMG,oBAAoBC,IAAAA,iCAA0B,EAAC3B;IACrD,MAAM4B,WAAW5B,EAAAA,oBAAAA,OAAO6B,SAAS,qBAAhB7B,kBAAkB8B,IAAI,KAAI9B,OAAO+B,qBAAqB,IAAI7B;IAC3E,MAAM8B,UAAU,MAAMb,SAASc,KAAK,CAACC,aAAa,CAChD;QACEN,UAAU5B,EAAAA,qBAAAA,OAAO6B,SAAS,qBAAhB7B,mBAAkB8B,IAAI,KAAI9B,OAAO+B,qBAAqB,IAAI7B;QACpEiC,aAAaC,IAAAA,4BAAa,EAACC,aAAI,CAACC,QAAQ,CAACV,UAAU1B,QAAQ;QAC3DG;QACAkC,YAAYvC;QACZwC,UAAU,MAAMC,IAAAA,2BAAoB,EAACvC,KAAKF;QAC1C0C,OAAO;YACLC,QAAQ;QACV;QACApB;QACAqB,KAAK9B,QAAQ8B,GAAG;QAChBC,WAAWC,IAAAA,oBAAe,EAAC;YACzBC,aAAa;YACbC,qBAAqB7C,8BAAgB,CAAC6C,mBAAmB;YACzDhD;YACAuB;YACAlB;YACA8B,aAAajC;YACb+C,qBAAqBjD,OAAOqB,YAAY,CAAC4B,mBAAmB;YAC5DxC;YACA,uEAAuE;YACvEyC,oBAAoBC;YACpBzC;QACF;QACAJ;QACAC;QACAC;QACA4C,mBAAmB5B,kBAAkB6B,IAAI,CAAC;QAC1CzC;QACAC;IACF,GACA;QACEa;QACA4B,WAAW,GAAEtD,wBAAAA,OAAOqB,YAAY,qBAAnBrB,sBAAqBuD,oBAAoB;QACtDC,oBAAoB9B;QACpB+B,MAAMC,YAAI;IACZ;IAEF,IAAI;QACFC,IAAAA,iDAA8B,EAAC3B;QAE/B,kFAAkF;QAClF,MAAM4B,YAAE,CAACC,SAAS,CAACxB,aAAI,CAACgB,IAAI,CAAChD,SAAS,cAAc;QAEpD,MAAMuD,YAAE,CAACE,KAAK,CAACzB,aAAI,CAACgB,IAAI,CAAChD,SAAS,WAAW;YAAE0D,WAAW;QAAK;QAC/D,MAAMH,YAAE,CAACE,KAAK,CAACzB,aAAI,CAACgB,IAAI,CAAChD,SAAS,UAAUC,UAAU;YACpDyD,WAAW;QACb;QACA,MAAMH,YAAE,CAACC,SAAS,CAChBxB,aAAI,CAACgB,IAAI,CAAChD,SAAS,iBACnB2D,KAAKC,SAAS,CACZ;YACEC,MAAM;QACR,GACA,MACA;QAIJ,6DAA6D;QAC7D,MAAMC,cAAc,MAAMnC,QAAQoC,yBAAyB,CAACzD;QAE5D,MAAM0D,iBAAiB,IAAIC,uCAAuB,CAAC;YACjDhE;YACAD;YACAE;QACF;QAEA,MAAMgE,iBAAiB,EAAE;QACzB,MAAMC,mBAAmB,EAAE;QAC3B,KAAK,MAAMC,SAASN,YAAYO,MAAM,CAAE;YACtC,IAAID,MAAME,QAAQ,KAAK,WAAWF,MAAME,QAAQ,KAAK,SAAS;gBAC5DJ,eAAeK,IAAI,CAACC,IAAAA,kBAAW,EAACJ;YAClC,OAAO,IAAIK,IAAAA,wBAAiB,EAACL,QAAQ;gBACnCD,iBAAiBI,IAAI,CAACC,IAAAA,kBAAW,EAACJ;YACpC;QACF;QAEA,IAAID,iBAAiBO,MAAM,GAAG,GAAG;YAC/BC,QAAQC,IAAI,CACV,CAAC,4BAA4B,EAC3BT,iBAAiBO,MAAM,CACxB,YAAY,EAAEP,iBAAiBnB,IAAI,CAAC,OAAO;QAEhD;QAEA,IAAIkB,eAAeQ,MAAM,GAAG,GAAG;YAC7B,MAAM,qBAIL,CAJK,IAAIG,MACR,CAAC,4BAA4B,EAC3BX,eAAeQ,MAAM,CACtB,UAAU,EAAER,eAAelB,IAAI,CAAC,OAAO,GAHpC,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;QAEA,MAAM8B,qBAAqB,MAAMC,IAAAA,8CAA2B,EAACjB;QAE7D,MAAMkB,WAA2B,EAAE;QAEnC,IAAI,CAAC1E,YAAY;YACf,KAAK,MAAM,CAAC2E,MAAMC,MAAM,IAAIJ,mBAAmBG,IAAI,CAAE;gBACnDD,SAAST,IAAI,CACXY,IAAAA,kCAAe,EAAC;oBACdF;oBACAC;oBACAlB;gBACF;YAEJ;QACF;QAEA,KAAK,MAAM,CAACiB,MAAMC,MAAM,IAAIJ,mBAAmBM,GAAG,CAAE;YAClDJ,SAAST,IAAI,CACXY,IAAAA,kCAAe,EAAC;gBACdF;gBACAC;gBACAlB;YACF;QAEJ;QAEA,MAAMqB,QAAQC,GAAG,CAACN;QAElB,MAAMK,QAAQC,GAAG,CAAC;YAChBtB,eAAeuB,iBAAiB,CAAC;YACjCvB,eAAewB,iBAAiB,CAAC;YACjCxB,eAAeyB,gBAAgB,CAAC;YAChCzB,eAAewB,iBAAiB,CAAC;YACjCxB,eAAeuB,iBAAiB,CAAC;YACjCvB,eAAewB,iBAAiB,CAAC;YACjCxB,eAAeyB,gBAAgB,CAAC;YAChC3B,YAAY4B,eAAe,IACzB1B,eAAe2B,sBAAsB,CACnC,mBACA;YAEJ7B,YAAY8B,UAAU,IACnB,MAAM5B,eAAe2B,sBAAsB,CAC1C,cACA;SAEL;QAED,MAAM3B,eAAe6B,cAAc,CAAC;YAClCC,aAAahD;YACbiD,oBAAoB1F;YACpByD,aAAagB;QACf;QAEA,MAAMkB,kBAAkBrE,QAAQsE,QAAQ;QAExC,MAAMC,OAAOzF,QAAQI,MAAM,CAACD;QAC5B,OAAO;YACLuF,UAAUD,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,GAAG;YAC9BE,mBAAmBtD;YACnBkD;QACF;IACF,EAAE,OAAOK,KAAK;QACZ,MAAM1E,QAAQsE,QAAQ;QACtB,MAAMI;IACR;AACF;AAEA,IAAIL;AACG,eAAetG,WAAW4G,UAEhC;IACC,0EAA0E;IAC1EC,OAAOC,MAAM,CAAC1G,8BAAgB,EAAEwG,WAAWG,YAAY;IAEvD,iDAAiD;IACjD3G,8BAAgB,CAACH,MAAM,GAAG,MAAM+G,IAAAA,eAAU,EACxCC,iCAAsB,EACtB7G,8BAAgB,CAACD,GAAG,EACpB;QAAE+G,gBAAgB9G,8BAAgB,CAAC8G,cAAc;IAAC;IAGpD,qCAAqC;IACrC,6HAA6H;IAC7H,kDAAkD;IAClD,IAAIC,IAAAA,6BAAqB,EAAC/G,8BAAgB,CAACH,MAAM,GAAG;QAClDG,8BAAgB,CAACH,MAAM,CAACK,OAAO,GAAG;IACpC;IAEA,iCAAiC;IACjC,MAAM8G,YAAY,IAAIC,kBAAS,CAAC;QAC9B/G,SAASF,8BAAgB,CAACH,MAAM,CAACK,OAAO;IAC1C;IACAgH,IAAAA,gBAAS,EAAC,aAAaF;IAEvB,MAAMG,SAAS,MAAMzH;IACrBwG,kBAAkBiB,OAAOjB,eAAe;IACxC,OAAOiB;AACT;AAEO,eAAexH;IACpB,IAAIuG,iBAAiB;QACnB,MAAMA;IACR;AACF", "ignoreList": [0]}