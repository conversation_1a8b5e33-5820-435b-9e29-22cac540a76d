"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@scena+event-emitter@1.0.5";
exports.ids = ["vendor-chunks/@scena+event-emitter@1.0.5"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@scena+event-emitter@1.0.5/node_modules/@scena/event-emitter/dist/event-emitter.esm.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@scena+event-emitter@1.0.5/node_modules/@scena/event-emitter/dist/event-emitter.esm.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2019 Daybrush\nname: @scena/event-emitter\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/gesture.git\nversion: 1.0.5\n*/\n\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n\n  for (var r = Array(s), k = 0, i = 0; i < il; i++) for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++) r[k] = a[j];\n\n  return r;\n}\n\n/**\n * Implement EventEmitter on object or component.\n */\n\nvar EventEmitter =\n/*#__PURE__*/\nfunction () {\n  function EventEmitter() {\n    this._events = {};\n  }\n  /**\n   * Add a listener to the registered event.\n   * @param - Name of the event to be added\n   * @param - listener function of the event to be added\n   * @example\n   * import EventEmitter from \"@scena/event-emitter\";\n   * cosnt emitter = new EventEmitter();\n   *\n   * // Add listener in \"a\" event\n   * emitter.on(\"a\", () => {\n   * });\n   * // Add listeners\n   * emitter.on({\n   *  a: () => {},\n   *  b: () => {},\n   * });\n   */\n\n\n  var __proto = EventEmitter.prototype;\n\n  __proto.on = function (eventName, listener) {\n    if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(eventName)) {\n      for (var name in eventName) {\n        this.on(name, eventName[name]);\n      }\n    } else {\n      this._addEvent(eventName, listener, {});\n    }\n\n    return this;\n  };\n  /**\n   * Remove listeners registered in the event target.\n   * @param - Name of the event to be removed\n   * @param - listener function of the event to be removed\n   * @example\n   * import EventEmitter from \"@scena/event-emitter\";\n   * cosnt emitter = new EventEmitter();\n   *\n   * // Remove all listeners.\n   * emitter.off();\n   *\n   * // Remove all listeners in \"A\" event.\n   * emitter.off(\"a\");\n   *\n   *\n   * // Remove \"listener\" listener in \"a\" event.\n   * emitter.off(\"a\", listener);\n   */\n\n\n  __proto.off = function (eventName, listener) {\n    if (!eventName) {\n      this._events = {};\n    } else if ((0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.isObject)(eventName)) {\n      for (var name in eventName) {\n        this.off(name);\n      }\n    } else if (!listener) {\n      this._events[eventName] = [];\n    } else {\n      var events = this._events[eventName];\n\n      if (events) {\n        var index = (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.findIndex)(events, function (e) {\n          return e.listener === listener;\n        });\n\n        if (index > -1) {\n          events.splice(index, 1);\n        }\n      }\n    }\n\n    return this;\n  };\n  /**\n   * Add a disposable listener and Use promise to the registered event.\n   * @param - Name of the event to be added\n   * @param - disposable listener function of the event to be added\n   * @example\n   * import EventEmitter from \"@scena/event-emitter\";\n   * cosnt emitter = new EventEmitter();\n   *\n   * // Add a disposable listener in \"a\" event\n   * emitter.once(\"a\", () => {\n   * });\n   *\n   * // Use Promise\n   * emitter.once(\"a\").then(e => {\n   * });\n   */\n\n\n  __proto.once = function (eventName, listener) {\n    var _this = this;\n\n    if (listener) {\n      this._addEvent(eventName, listener, {\n        once: true\n      });\n    }\n\n    return new Promise(function (resolve) {\n      _this._addEvent(eventName, resolve, {\n        once: true\n      });\n    });\n  };\n  /**\n   * Fires an event to call listeners.\n   * @param - Event name\n   * @param - Event parameter\n   * @return If false, stop the event.\n   * @example\n   *\n   * import EventEmitter from \"@scena/event-emitter\";\n   *\n   *\n   * const emitter = new EventEmitter();\n   *\n   * emitter.on(\"a\", e => {\n   * });\n   *\n   *\n   * emitter.emit(\"a\", {\n   *   a: 1,\n   * });\n   */\n\n\n  __proto.emit = function (eventName, param) {\n    var _this = this;\n\n    if (param === void 0) {\n      param = {};\n    }\n\n    var events = this._events[eventName];\n\n    if (!eventName || !events) {\n      return true;\n    }\n\n    var isStop = false;\n    param.eventType = eventName;\n\n    param.stop = function () {\n      isStop = true;\n    };\n\n    param.currentTarget = this;\n\n    __spreadArrays(events).forEach(function (info) {\n      info.listener(param);\n\n      if (info.once) {\n        _this.off(eventName, info.listener);\n      }\n    });\n\n    return !isStop;\n  };\n  /**\n   * Fires an event to call listeners.\n   * @param - Event name\n   * @param - Event parameter\n   * @return If false, stop the event.\n   * @example\n   *\n   * import EventEmitter from \"@scena/event-emitter\";\n   *\n   *\n   * const emitter = new EventEmitter();\n   *\n   * emitter.on(\"a\", e => {\n   * });\n   *\n   *\n   * emitter.emit(\"a\", {\n   *   a: 1,\n   * });\n   */\n\n  /**\n  * Fires an event to call listeners.\n  * @param - Event name\n  * @param - Event parameter\n  * @return If false, stop the event.\n  * @example\n  *\n  * import EventEmitter from \"@scena/event-emitter\";\n  *\n  *\n  * const emitter = new EventEmitter();\n  *\n  * emitter.on(\"a\", e => {\n  * });\n  *\n  * // emit\n  * emitter.trigger(\"a\", {\n  *   a: 1,\n  * });\n  */\n\n\n  __proto.trigger = function (eventName, param) {\n    if (param === void 0) {\n      param = {};\n    }\n\n    return this.emit(eventName, param);\n  };\n\n  __proto._addEvent = function (eventName, listener, options) {\n    var events = this._events;\n    events[eventName] = events[eventName] || [];\n    var listeners = events[eventName];\n    listeners.push(__assign({\n      listener: listener\n    }, options));\n  };\n\n  return EventEmitter;\n}();\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EventEmitter);\n//# sourceMappingURL=event-emitter.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@scena+event-emitter@1.0.5/node_modules/@scena/event-emitter/dist/event-emitter.esm.js\n");

/***/ })

};
;