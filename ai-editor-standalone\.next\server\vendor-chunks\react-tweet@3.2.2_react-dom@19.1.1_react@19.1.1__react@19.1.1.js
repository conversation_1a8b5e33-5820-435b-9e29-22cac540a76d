/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1";
exports.ids = ["vendor-chunks/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/api/fetch-tweet.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/api/fetch-tweet.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TwitterApiError: () => (/* binding */ TwitterApiError),\n/* harmony export */   fetchTweet: () => (/* binding */ fetchTweet)\n/* harmony export */ });\nconst SYNDICATION_URL = 'https://cdn.syndication.twimg.com';\nclass TwitterApiError extends Error {\n    constructor({ message, status, data }){\n        super(message);\n        this.name = 'TwitterApiError';\n        this.status = status;\n        this.data = data;\n    }\n}\nconst TWEET_ID = /^[0-9]+$/;\nfunction getToken(id) {\n    return (Number(id) / 1e15 * Math.PI).toString(6 ** 2).replace(/(0+|\\.)/g, '');\n}\n/**\n * Fetches a tweet from the Twitter syndication API.\n */ async function fetchTweet(id, fetchOptions) {\n    var _res_headers_get;\n    if (id.length > 40 || !TWEET_ID.test(id)) {\n        throw new Error(`Invalid tweet id: ${id}`);\n    }\n    const url = new URL(`${SYNDICATION_URL}/tweet-result`);\n    url.searchParams.set('id', id);\n    url.searchParams.set('lang', 'en');\n    url.searchParams.set('features', [\n        'tfw_timeline_list:',\n        'tfw_follower_count_sunset:true',\n        'tfw_tweet_edit_backend:on',\n        'tfw_refsrc_session:on',\n        'tfw_fosnr_soft_interventions_enabled:on',\n        'tfw_show_birdwatch_pivots_enabled:on',\n        'tfw_show_business_verified_badge:on',\n        'tfw_duplicate_scribes_to_settings:on',\n        'tfw_use_profile_image_shape_enabled:on',\n        'tfw_show_blue_verified_badge:on',\n        'tfw_legacy_timeline_sunset:true',\n        'tfw_show_gov_verified_badge:on',\n        'tfw_show_business_affiliate_badge:on',\n        'tfw_tweet_edit_frontend:on'\n    ].join(';'));\n    url.searchParams.set('token', getToken(id));\n    const res = await fetch(url.toString(), fetchOptions);\n    const isJson = (_res_headers_get = res.headers.get('content-type')) == null ? void 0 : _res_headers_get.includes('application/json');\n    const data = isJson ? await res.json() : undefined;\n    if (res.ok) {\n        if ((data == null ? void 0 : data.__typename) === 'TweetTombstone') {\n            return {\n                tombstone: true\n            };\n        }\n        return {\n            data\n        };\n    }\n    if (res.status === 404) {\n        return {\n            notFound: true\n        };\n    }\n    throw new TwitterApiError({\n        message: typeof data.error === 'string' ? data.error : `Failed to fetch tweet at \"${url}\" with \"${res.status}\".`,\n        status: res.status,\n        data\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/api/fetch-tweet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/date-utils.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/date-utils.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDate: () => (/* binding */ formatDate)\n/* harmony export */ });\nconst options = {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n    weekday: 'short',\n    month: 'short',\n    day: 'numeric',\n    year: 'numeric'\n};\nconst formatter = new Intl.DateTimeFormat('en-US', options);\nconst partsArrayToObject = (parts)=>{\n    const result = {};\n    for (const part of parts){\n        result[part.type] = part.value;\n    }\n    return result;\n};\nconst formatDate = (date)=>{\n    const parts = partsArrayToObject(formatter.formatToParts(date));\n    const formattedTime = `${parts.hour}:${parts.minute} ${parts.dayPeriod}`;\n    const formattedDate = `${parts.month} ${parts.day}, ${parts.year}`;\n    return `${formattedTime} · ${formattedDate}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC9kYXRlLXV0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLDZCQUE2QixXQUFXLEdBQUcsY0FBYyxFQUFFLGdCQUFnQjtBQUMzRSw2QkFBNkIsYUFBYSxFQUFFLFVBQVUsSUFBSSxXQUFXO0FBQ3JFLGNBQWMsZUFBZSxJQUFJLGNBQWM7QUFDL0MiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC10d2VldEAzLjIuMl9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFxkYXRlLXV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG9wdGlvbnMgPSB7XG4gICAgaG91cjogJ251bWVyaWMnLFxuICAgIG1pbnV0ZTogJzItZGlnaXQnLFxuICAgIGhvdXIxMjogdHJ1ZSxcbiAgICB3ZWVrZGF5OiAnc2hvcnQnLFxuICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgIGRheTogJ251bWVyaWMnLFxuICAgIHllYXI6ICdudW1lcmljJ1xufTtcbmNvbnN0IGZvcm1hdHRlciA9IG5ldyBJbnRsLkRhdGVUaW1lRm9ybWF0KCdlbi1VUycsIG9wdGlvbnMpO1xuY29uc3QgcGFydHNBcnJheVRvT2JqZWN0ID0gKHBhcnRzKT0+e1xuICAgIGNvbnN0IHJlc3VsdCA9IHt9O1xuICAgIGZvciAoY29uc3QgcGFydCBvZiBwYXJ0cyl7XG4gICAgICAgIHJlc3VsdFtwYXJ0LnR5cGVdID0gcGFydC52YWx1ZTtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbn07XG5leHBvcnQgY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlKT0+e1xuICAgIGNvbnN0IHBhcnRzID0gcGFydHNBcnJheVRvT2JqZWN0KGZvcm1hdHRlci5mb3JtYXRUb1BhcnRzKGRhdGUpKTtcbiAgICBjb25zdCBmb3JtYXR0ZWRUaW1lID0gYCR7cGFydHMuaG91cn06JHtwYXJ0cy5taW51dGV9ICR7cGFydHMuZGF5UGVyaW9kfWA7XG4gICAgY29uc3QgZm9ybWF0dGVkRGF0ZSA9IGAke3BhcnRzLm1vbnRofSAke3BhcnRzLmRheX0sICR7cGFydHMueWVhcn1gO1xuICAgIHJldHVybiBgJHtmb3JtYXR0ZWRUaW1lfSDCtyAke2Zvcm1hdHRlZERhdGV9YDtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/date-utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/hooks.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/hooks.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMounted: () => (/* binding */ useMounted),\n/* harmony export */   useTweet: () => (/* binding */ useTweet)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! swr */ \"(ssr)/./node_modules/.pnpm/swr@2.3.4_react@19.1.1/node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var _api_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api/index.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/api/fetch-tweet.js\");\n/* __next_internal_client_entry_do_not_use__ useTweet,useMounted auto */ \n\n\n// Avoids an error when used in the pages directory where useSWR might be in `default`.\nconst useSWR = swr__WEBPACK_IMPORTED_MODULE_1__[\"default\"][\"default\"] || swr__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\nconst host = 'https://react-tweet.vercel.app';\nasync function fetcher([url, fetchOptions]) {\n    const res = await fetch(url, fetchOptions);\n    const json = await res.json();\n    // We return null in case `json.data` is undefined, that way we can check for \"loading\" by\n    // checking if data is `undefined`. `null` means it was fetched.\n    if (res.ok) return json.data || null;\n    throw new _api_index_js__WEBPACK_IMPORTED_MODULE_2__.TwitterApiError({\n        message: `Failed to fetch tweet at \"${url}\" with \"${res.status}\".`,\n        data: json,\n        status: res.status\n    });\n}\n/**\n * SWR hook for fetching a tweet in the browser.\n */ const useTweet = (id, apiUrl, fetchOptions)=>{\n    const { isLoading, data, error } = useSWR({\n        \"useTweet.useSWR\": ()=>apiUrl || id ? [\n                apiUrl || id && `${host}/api/tweet/${id}`,\n                fetchOptions\n            ] : null\n    }[\"useTweet.useSWR\"], fetcher, {\n        revalidateIfStale: false,\n        revalidateOnFocus: false,\n        shouldRetryOnError: false\n    });\n    return {\n        // If data is `undefined` then it might be the first render where SWR hasn't started doing\n        // any work, so we set `isLoading` to `true`.\n        isLoading: Boolean(isLoading || data === undefined && !error),\n        data,\n        error\n    };\n};\nconst useMounted = ()=>{\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMounted.useEffect\": ()=>setMounted(true)\n    }[\"useMounted.useEffect\"], []);\n    return mounted;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/hooks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/swr.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/swr.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tweet: () => (/* binding */ Tweet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./twitter-theme/components.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js\");\n/* harmony import */ var _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./twitter-theme/components.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js\");\n/* harmony import */ var _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./twitter-theme/components.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js\");\n/* harmony import */ var _hooks_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hooks.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/hooks.js\");\n/* __next_internal_client_entry_do_not_use__ Tweet auto */ \n\n\nconst Tweet = ({ id, apiUrl, fallback = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_1__.TweetSkeleton, {}), components, fetchOptions, onError })=>{\n    const { data, error, isLoading } = (0,_hooks_js__WEBPACK_IMPORTED_MODULE_2__.useTweet)(id, apiUrl, fetchOptions);\n    if (isLoading) return fallback;\n    if (error || !data) {\n        const NotFound = (components == null ? void 0 : components.TweetNotFound) || _twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_3__.TweetNotFound;\n        return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(NotFound, {\n            error: onError ? onError(error) : error\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_twitter_theme_components_js__WEBPACK_IMPORTED_MODULE_4__.EmbeddedTweet, {\n        tweet: data,\n        components: components\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/swr.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/avatar-img.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/avatar-img.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AvatarImg: () => (/* binding */ AvatarImg)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n// eslint-disable-next-line jsx-a11y/alt-text -- The alt text is part of `...props`\nconst AvatarImg = (props)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"img\", {\n        ...props\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL2F2YXRhci1pbWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0Q7QUFDaEQ7QUFDTyx5Q0FBeUMsc0RBQUk7QUFDcEQ7QUFDQSxLQUFLIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcYXZhdGFyLWltZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGpzeC1hMTF5L2FsdC10ZXh0IC0tIFRoZSBhbHQgdGV4dCBpcyBwYXJ0IG9mIGAuLi5wcm9wc2BcbmV4cG9ydCBjb25zdCBBdmF0YXJJbWcgPSAocHJvcHMpPT4vKiNfX1BVUkVfXyovIF9qc3goXCJpbWdcIiwge1xuICAgICAgICAuLi5wcm9wc1xuICAgIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/avatar-img.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmbeddedTweet: () => (/* binding */ EmbeddedTweet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-container.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-container.js\");\n/* harmony import */ var _tweet_header_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tweet-header.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-header.js\");\n/* harmony import */ var _tweet_in_reply_to_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tweet-in-reply-to.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js\");\n/* harmony import */ var _tweet_body_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tweet-body.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-body.js\");\n/* harmony import */ var _tweet_media_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./tweet-media.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media.js\");\n/* harmony import */ var _tweet_info_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./tweet-info.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info.js\");\n/* harmony import */ var _tweet_actions_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./tweet-actions.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions.js\");\n/* harmony import */ var _tweet_replies_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./tweet-replies.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-replies.js\");\n/* harmony import */ var _quoted_tweet_index_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./quoted-tweet/index.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst EmbeddedTweet = ({ tweet: t, components })=>{\n    var _tweet_mediaDetails;\n    // useMemo does nothing for RSC but it helps when the component is used in the client (e.g by SWR)\n    const tweet = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.enrichTweet)(t), [\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.TweetContainer, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_header_js__WEBPACK_IMPORTED_MODULE_4__.TweetHeader, {\n                tweet: tweet,\n                components: components\n            }),\n            tweet.in_reply_to_status_id_str && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_in_reply_to_js__WEBPACK_IMPORTED_MODULE_5__.TweetInReplyTo, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_body_js__WEBPACK_IMPORTED_MODULE_6__.TweetBody, {\n                tweet: tweet\n            }),\n            ((_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_media_js__WEBPACK_IMPORTED_MODULE_7__.TweetMedia, {\n                tweet: tweet,\n                components: components\n            }) : null,\n            tweet.quoted_tweet && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_quoted_tweet_index_js__WEBPACK_IMPORTED_MODULE_8__.QuotedTweet, {\n                tweet: tweet.quoted_tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_info_js__WEBPACK_IMPORTED_MODULE_9__.TweetInfo, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_actions_js__WEBPACK_IMPORTED_MODULE_10__.TweetActions, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_replies_js__WEBPACK_IMPORTED_MODULE_11__.TweetReplies, {\n                tweet: tweet\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css ***!
  \*************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"verified\": \"icons_verified__JsE0v\"\n};\n\nmodule.exports.__checksum = \"0f8120cd7100\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL2ljb25zL2ljb25zLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcaWNvbnNcXGljb25zLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwidmVyaWZpZWRcIjogXCJpY29uc192ZXJpZmllZF9fSnNFMHZcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiMGY4MTIwY2Q3MTAwXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VerifiedBusiness: () => (/* binding */ VerifiedBusiness)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _icons_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\");\n\n\nconst VerifiedBusiness = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        viewBox: \"0 0 22 22\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: _icons_module_css__WEBPACK_IMPORTED_MODULE_1__.verified,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"g\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"linearGradient\", {\n                    gradientUnits: \"userSpaceOnUse\",\n                    id: \"0-a\",\n                    x1: \"4.411\",\n                    x2: \"18.083\",\n                    y1: \"2.495\",\n                    y2: \"21.508\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"0\",\n                            stopColor: \"#f4e72a\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".539\",\n                            stopColor: \"#cd8105\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".68\",\n                            stopColor: \"#cb7b00\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"1\",\n                            stopColor: \"#f4ec26\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"1\",\n                            stopColor: \"#f4e72a\"\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"linearGradient\", {\n                    gradientUnits: \"userSpaceOnUse\",\n                    id: \"0-b\",\n                    x1: \"5.355\",\n                    x2: \"16.361\",\n                    y1: \"3.395\",\n                    y2: \"19.133\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \"0\",\n                            stopColor: \"#f9e87f\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".406\",\n                            stopColor: \"#e2b719\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"stop\", {\n                            offset: \".989\",\n                            stopColor: \"#e2b719\"\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"g\", {\n                    clipRule: \"evenodd\",\n                    fillRule: \"evenodd\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M13.324 3.848L11 1.6 8.676 3.848l-3.201-.453-.559 3.184L2.06 8.095 3.48 11l-1.42 2.904 2.856 1.516.559 3.184 3.201-.452L11 20.4l2.324-2.248 3.201.452.559-3.184 2.856-1.516L18.52 11l1.42-2.905-2.856-1.516-.559-3.184zm-7.09 7.575l3.428 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                            fill: \"url(#0-a)\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M13.101 4.533L11 2.5 8.899 4.533l-2.895-.41-.505 2.88-2.583 1.37L4.2 11l-1.284 2.627 2.583 1.37.505 2.88 2.895-.41L11 19.5l2.101-2.033 2.895.41.505-2.88 2.583-1.37L17.8 11l1.284-2.627-2.583-1.37-.505-2.88zm-6.868 6.89l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                            fill: \"url(#0-b)\"\n                        }),\n                        /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M6.233 11.423l3.429 3.428 5.65-6.17.038-.033-.005 1.398-5.683 6.206-3.429-3.429-.003-1.405.005.003z\",\n                            fill: \"#d18800\"\n                        })\n                    ]\n                })\n            ]\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VerifiedGovernment: () => (/* binding */ VerifiedGovernment)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _icons_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\");\n\n\nconst VerifiedGovernment = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        viewBox: \"0 0 22 22\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: _icons_module_css__WEBPACK_IMPORTED_MODULE_1__.verified,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                clipRule: \"evenodd\",\n                d: \"M12.05 2.056c-.568-.608-1.532-.608-2.1 0l-1.393 1.49c-.284.303-.685.47-1.1.455L5.42 3.932c-.832-.028-1.514.654-1.486 1.486l.069 2.039c.014.415-.152.816-.456 1.1l-1.49 1.392c-.608.568-.608 1.533 0 2.101l1.49 1.393c.304.284.47.684.456 1.1l-.07 2.038c-.027.832.655 1.514 1.487 1.486l2.038-.069c.415-.014.816.152 1.1.455l1.392 1.49c.569.609 1.533.609 2.102 0l1.393-1.49c.283-.303.684-.47 1.099-.455l2.038.069c.832.028 1.515-.654 1.486-1.486L18 14.542c-.015-.415.152-.815.455-1.099l1.49-1.393c.608-.568.608-1.533 0-2.101l-1.49-1.393c-.303-.283-.47-.684-.455-1.1l.068-2.038c.029-.832-.654-1.514-1.486-1.486l-2.038.07c-.415.013-.816-.153-1.1-.456zm-5.817 9.367l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                fillRule: \"evenodd\"\n            })\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Verified: () => (/* binding */ Verified)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _icons_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css\");\n\n\nconst Verified = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        viewBox: \"0 0 24 24\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: _icons_module_css__WEBPACK_IMPORTED_MODULE_1__.verified,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                d: \"M22.25 12c0-1.43-.88-2.67-2.19-3.34.46-1.39.2-2.9-.81-3.91s-2.52-1.27-3.91-.81c-.66-1.31-1.91-2.19-3.34-2.19s-2.67.88-3.33 2.19c-1.4-.46-2.91-.2-3.92.81s-1.26 2.52-.8 3.91c-1.31.67-2.2 1.91-2.2 3.34s.89 2.67 2.2 3.34c-.46 1.39-.21 2.9.8 3.91s2.52 1.26 3.91.81c.67 1.31 1.91 2.19 3.34 2.19s2.68-.88 3.34-2.19c1.39.45 2.9.2 3.91-.81s1.27-2.52.81-3.91c1.31-.67 2.19-1.91 2.19-3.34zm-11.71 4.2L6.8 12.46l1.41-1.42 2.26 2.26 4.8-5.23 1.47 1.36-6.2 6.77z\"\n            })\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/media-img.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/media-img.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MediaImg: () => (/* binding */ MediaImg)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n// eslint-disable-next-line jsx-a11y/alt-text -- The alt text is part of `...props`\nconst MediaImg = (props)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"img\", {\n        ...props\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL21lZGlhLWltZy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDtBQUNoRDtBQUNPLHdDQUF3QyxzREFBSTtBQUNuRDtBQUNBLEtBQUsiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC10d2VldEAzLjIuMl9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFxtZWRpYS1pbWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBqc3gtYTExeS9hbHQtdGV4dCAtLSBUaGUgYWx0IHRleHQgaXMgcGFydCBvZiBgLi4ucHJvcHNgXG5leHBvcnQgY29uc3QgTWVkaWFJbWcgPSAocHJvcHMpPT4vKiNfX1BVUkVfXyovIF9qc3goXCJpbWdcIiwge1xuICAgICAgICAuLi5wcm9wc1xuICAgIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/media-img.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweetBody: () => (/* binding */ QuotedTweetBody)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _quoted_tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./quoted-tweet-body.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css\");\n\n\nconst QuotedTweetBody = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n        className: _quoted_tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        lang: tweet.lang,\n        dir: \"auto\",\n        children: tweet.entities.map((item, i)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                dangerouslySetInnerHTML: {\n                    __html: item.text\n                }\n            }, i))\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3F1b3RlZC10d2VldC9xdW90ZWQtdHdlZXQtYm9keS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0Q7QUFDRDtBQUN4QywyQkFBMkIsT0FBTyxpQkFBaUIsc0RBQUk7QUFDOUQsbUJBQW1CLCtEQUFNO0FBQ3pCO0FBQ0E7QUFDQSw4REFBOEQsc0RBQUk7QUFDbEU7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLEtBQUsiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC10d2VldEAzLjIuMl9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFxxdW90ZWQtdHdlZXRcXHF1b3RlZC10d2VldC1ib2R5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgcyBmcm9tICcuL3F1b3RlZC10d2VldC1ib2R5Lm1vZHVsZS5jc3MnO1xuZXhwb3J0IGNvbnN0IFF1b3RlZFR3ZWV0Qm9keSA9ICh7IHR3ZWV0IH0pPT4vKiNfX1BVUkVfXyovIF9qc3goXCJwXCIsIHtcbiAgICAgICAgY2xhc3NOYW1lOiBzLnJvb3QsXG4gICAgICAgIGxhbmc6IHR3ZWV0LmxhbmcsXG4gICAgICAgIGRpcjogXCJhdXRvXCIsXG4gICAgICAgIGNoaWxkcmVuOiB0d2VldC5lbnRpdGllcy5tYXAoKGl0ZW0sIGkpPT4vKiNfX1BVUkVfXyovIF9qc3goXCJzcGFuXCIsIHtcbiAgICAgICAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTDoge1xuICAgICAgICAgICAgICAgICAgICBfX2h0bWw6IGl0ZW0udGV4dFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sIGkpKVxuICAgIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css":
/*!********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css ***!
  \********************************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"quoted-tweet-body_root__vpKsC\"\n};\n\nmodule.exports.__checksum = \"46a0279f89a4\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3F1b3RlZC10d2VldC9xdW90ZWQtdHdlZXQtYm9keS5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXR3ZWV0QDMuMi4yX3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHF1b3RlZC10d2VldFxccXVvdGVkLXR3ZWV0LWJvZHkubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJyb290XCI6IFwicXVvdGVkLXR3ZWV0LWJvZHlfcm9vdF9fdnBLc0NcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiNDZhMDI3OWY4OWE0XCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweetContainer: () => (/* binding */ QuotedTweetContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _quoted_tweet_container_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./quoted-tweet-container.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css\");\n/* __next_internal_client_entry_do_not_use__ QuotedTweetContainer auto */ \n\nconst QuotedTweetContainer = ({ tweet, children })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: _quoted_tweet_container_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        onClick: (e)=>{\n            e.preventDefault();\n            window.open(tweet.url, '_blank');\n        },\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"article\", {\n            className: _quoted_tweet_container_module_css__WEBPACK_IMPORTED_MODULE_1__.article,\n            children: children\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3F1b3RlZC10d2VldC9xdW90ZWQtdHdlZXQtY29udGFpbmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OzswRUFDZ0Q7QUFDSTtBQUM3QyxNQUFNRyx1QkFBdUIsQ0FBQyxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBRSxHQUFHLFdBQVcsR0FBR0osc0RBQUlBLENBQUMsT0FBTztRQUM3RUssV0FBV0osb0VBQU07UUFDakJNLFNBQVMsQ0FBQ0M7WUFDTkEsRUFBRUMsY0FBYztZQUNoQkMsT0FBT0MsSUFBSSxDQUFDUixNQUFNUyxHQUFHLEVBQUU7UUFDM0I7UUFDQVIsVUFBVSxXQUFXLEdBQUdKLHNEQUFJQSxDQUFDLFdBQVc7WUFDcENLLFdBQVdKLHVFQUFTO1lBQ3BCRyxVQUFVQTtRQUNkO0lBQ0osR0FBRyIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXR3ZWV0QDMuMi4yX3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHF1b3RlZC10d2VldFxccXVvdGVkLXR3ZWV0LWNvbnRhaW5lci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5pbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0IHMgZnJvbSAnLi9xdW90ZWQtdHdlZXQtY29udGFpbmVyLm1vZHVsZS5jc3MnO1xuZXhwb3J0IGNvbnN0IFF1b3RlZFR3ZWV0Q29udGFpbmVyID0gKHsgdHdlZXQsIGNoaWxkcmVuIH0pPT4vKiNfX1BVUkVfXyovIF9qc3goXCJkaXZcIiwge1xuICAgICAgICBjbGFzc05hbWU6IHMucm9vdCxcbiAgICAgICAgb25DbGljazogKGUpPT57XG4gICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICB3aW5kb3cub3Blbih0d2VldC51cmwsICdfYmxhbmsnKTtcbiAgICAgICAgfSxcbiAgICAgICAgY2hpbGRyZW46IC8qI19fUFVSRV9fKi8gX2pzeChcImFydGljbGVcIiwge1xuICAgICAgICAgICAgY2xhc3NOYW1lOiBzLmFydGljbGUsXG4gICAgICAgICAgICBjaGlsZHJlbjogY2hpbGRyZW5cbiAgICAgICAgfSlcbiAgICB9KTtcbiJdLCJuYW1lcyI6WyJqc3giLCJfanN4IiwicyIsIlF1b3RlZFR3ZWV0Q29udGFpbmVyIiwidHdlZXQiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsInJvb3QiLCJvbkNsaWNrIiwiZSIsInByZXZlbnREZWZhdWx0Iiwid2luZG93Iiwib3BlbiIsInVybCIsImFydGljbGUiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"quoted-tweet-container_root__hVLNr\",\n\t\"article\": \"quoted-tweet-container_article__PuOAk\"\n};\n\nmodule.exports.__checksum = \"92c82e71fd18\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3F1b3RlZC10d2VldC9xdW90ZWQtdHdlZXQtY29udGFpbmVyLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC10d2VldEAzLjIuMl9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFxxdW90ZWQtdHdlZXRcXHF1b3RlZC10d2VldC1jb250YWluZXIubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJyb290XCI6IFwicXVvdGVkLXR3ZWV0LWNvbnRhaW5lcl9yb290X19oVkxOclwiLFxuXHRcImFydGljbGVcIjogXCJxdW90ZWQtdHdlZXQtY29udGFpbmVyX2FydGljbGVfX1B1T0FrXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjkyYzgyZTcxZmQxOFwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweetHeader: () => (/* binding */ QuotedTweetHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _avatar_img_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../avatar-img.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/avatar-img.js\");\n/* harmony import */ var _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./quoted-tweet-header.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css\");\n/* harmony import */ var _verified_badge_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../verified-badge.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/verified-badge.js\");\n\n\n\n\n\nconst QuotedTweetHeader = ({ tweet })=>{\n    const { user } = tweet;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.header,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                href: tweet.url,\n                className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatar,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarOverflow, user.profile_image_shape === 'Square' && _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarSquare),\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_avatar_img_js__WEBPACK_IMPORTED_MODULE_3__.AvatarImg, {\n                        src: user.profile_image_url_https,\n                        alt: user.name,\n                        width: 20,\n                        height: 20\n                    })\n                })\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.author,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorText,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                            title: user.name,\n                            children: user.name\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_verified_badge_js__WEBPACK_IMPORTED_MODULE_4__.VerifiedBadge, {\n                        user: user\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _quoted_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.username,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", {\n                            title: `@${user.screen_name}`,\n                            children: [\n                                \"@\",\n                                user.screen_name\n                            ]\n                        })\n                    })\n                ]\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"header\": \"quoted-tweet-header_header__aEmpv\",\n\t\"avatar\": \"quoted-tweet-header_avatar__qKTUo\",\n\t\"avatarSquare\": \"quoted-tweet-header_avatarSquare___RW5c\",\n\t\"author\": \"quoted-tweet-header_author__qNLin\",\n\t\"authorText\": \"quoted-tweet-header_authorText__C9jwc\",\n\t\"username\": \"quoted-tweet-header_username__56f4G\"\n};\n\nmodule.exports.__checksum = \"d9d5d82e76cc\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3F1b3RlZC10d2VldC9xdW90ZWQtdHdlZXQtaGVhZGVyLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXR3ZWV0QDMuMi4yX3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHF1b3RlZC10d2VldFxccXVvdGVkLXR3ZWV0LWhlYWRlci5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImhlYWRlclwiOiBcInF1b3RlZC10d2VldC1oZWFkZXJfaGVhZGVyX19hRW1wdlwiLFxuXHRcImF2YXRhclwiOiBcInF1b3RlZC10d2VldC1oZWFkZXJfYXZhdGFyX19xS1RVb1wiLFxuXHRcImF2YXRhclNxdWFyZVwiOiBcInF1b3RlZC10d2VldC1oZWFkZXJfYXZhdGFyU3F1YXJlX19fUlc1Y1wiLFxuXHRcImF1dGhvclwiOiBcInF1b3RlZC10d2VldC1oZWFkZXJfYXV0aG9yX19xTkxpblwiLFxuXHRcImF1dGhvclRleHRcIjogXCJxdW90ZWQtdHdlZXQtaGVhZGVyX2F1dGhvclRleHRfX0M5andjXCIsXG5cdFwidXNlcm5hbWVcIjogXCJxdW90ZWQtdHdlZXQtaGVhZGVyX3VzZXJuYW1lX181NmY0R1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJkOWQ1ZDgyZTc2Y2NcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuotedTweet: () => (/* binding */ QuotedTweet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _quoted_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./quoted-tweet-container.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js\");\n/* harmony import */ var _quoted_tweet_header_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./quoted-tweet-header.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js\");\n/* harmony import */ var _quoted_tweet_body_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./quoted-tweet-body.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js\");\n/* harmony import */ var _tweet_media_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../tweet-media.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media.js\");\n\n\n\n\n\nconst QuotedTweet = ({ tweet })=>{\n    var _tweet_mediaDetails;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_quoted_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.QuotedTweetContainer, {\n        tweet: tweet,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_quoted_tweet_header_js__WEBPACK_IMPORTED_MODULE_2__.QuotedTweetHeader, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_quoted_tweet_body_js__WEBPACK_IMPORTED_MODULE_3__.QuotedTweetBody, {\n                tweet: tweet\n            }),\n            ((_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_media_js__WEBPACK_IMPORTED_MODULE_4__.TweetMedia, {\n                quoted: true,\n                tweet: tweet\n            }) : null\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/skeleton.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/skeleton.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _skeleton_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./skeleton.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/skeleton.module.css\");\n\n\nconst Skeleton = ({ style })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n        className: _skeleton_module_css__WEBPACK_IMPORTED_MODULE_1__.skeleton,\n        style: style\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3NrZWxldG9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNMO0FBQ3BDLG9CQUFvQixPQUFPLGlCQUFpQixzREFBSTtBQUN2RCxtQkFBbUIsMERBQWU7QUFDbEM7QUFDQSxLQUFLIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcc2tlbGV0b24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmltcG9ydCBzdHlsZXMgZnJvbSAnLi9za2VsZXRvbi5tb2R1bGUuY3NzJztcbmV4cG9ydCBjb25zdCBTa2VsZXRvbiA9ICh7IHN0eWxlIH0pPT4vKiNfX1BVUkVfXyovIF9qc3goXCJzcGFuXCIsIHtcbiAgICAgICAgY2xhc3NOYW1lOiBzdHlsZXMuc2tlbGV0b24sXG4gICAgICAgIHN0eWxlOiBzdHlsZVxuICAgIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/skeleton.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/skeleton.module.css":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/skeleton.module.css ***!
  \**********************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"skeleton\": \"skeleton_skeleton__E_zRO\",\n\t\"loading\": \"skeleton_loading__crVLX\"\n};\n\nmodule.exports.__checksum = \"e6dd45682b1c\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3NrZWxldG9uLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC10d2VldEAzLjIuMl9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFxza2VsZXRvbi5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInNrZWxldG9uXCI6IFwic2tlbGV0b25fc2tlbGV0b25fX0VfelJPXCIsXG5cdFwibG9hZGluZ1wiOiBcInNrZWxldG9uX2xvYWRpbmdfX2NyVkxYXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImU2ZGQ0NTY4MmIxY1wiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/skeleton.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/theme.css":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/theme.css ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"049ecef6f3cd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3RoZW1lLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXR3ZWV0QDMuMi4yX3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHRoZW1lLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA0OWVjZWY2ZjNjZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/theme.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetActionsCopy: () => (/* binding */ TweetActionsCopy)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-actions.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css\");\n/* __next_internal_client_entry_do_not_use__ TweetActionsCopy auto */ \n\n\nconst TweetActionsCopy = ({ tweet })=>{\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopy = ()=>{\n        navigator.clipboard.writeText(tweet.url);\n        setCopied(true);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TweetActionsCopy.useEffect\": ()=>{\n            if (copied) {\n                const timeout = setTimeout({\n                    \"TweetActionsCopy.useEffect.timeout\": ()=>{\n                        setCopied(false);\n                    }\n                }[\"TweetActionsCopy.useEffect.timeout\"], 6000);\n                return ({\n                    \"TweetActionsCopy.useEffect\": ()=>clearTimeout(timeout)\n                })[\"TweetActionsCopy.useEffect\"];\n            }\n        }\n    }[\"TweetActionsCopy.useEffect\"], [\n        copied\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"button\", {\n        type: \"button\",\n        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copy,\n        \"aria-label\": \"Copy link\",\n        onClick: handleCopy,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyIconWrapper,\n                children: copied ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M9.64 18.952l-5.55-4.861 1.317-1.504 3.951 3.459 8.459-10.948L19.4 6.32 9.64 18.952z\"\n                        })\n                    })\n                }) : /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M18.36 5.64c-1.95-1.96-5.11-1.96-7.07 0L9.88 7.05 8.46 5.64l1.42-1.42c2.73-2.73 7.16-2.73 9.9 0 2.73 2.74 2.73 7.17 0 9.9l-1.42 1.42-1.41-1.42 1.41-1.41c1.96-1.96 1.96-5.12 0-7.07zm-2.12 3.53l-7.07 7.07-1.41-1.41 7.07-7.07 1.41 1.41zm-12.02.71l1.42-1.42 1.41 1.42-1.41 1.41c-1.96 1.96-1.96 5.12 0 7.07 1.95 1.96 5.11 1.96 7.07 0l1.41-1.41 1.42 1.41-1.42 1.42c-2.73 2.73-7.16 2.73-9.9 0-2.73-2.74-2.73-7.17 0-9.9z\"\n                        })\n                    })\n                })\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_2__.copyText,\n                children: copied ? 'Copied!' : 'Copy link'\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetActions: () => (/* binding */ TweetActions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_actions_copy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tweet-actions-copy.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js\");\n/* harmony import */ var _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-actions.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css\");\n\n\n\n\nconst TweetActions = ({ tweet })=>{\n    const favoriteCount = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(tweet.favorite_count);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.actions,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.like,\n                href: tweet.like_url,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": `Like. This Tweet has ${favoriteCount} likes`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.likeIconWrapper,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                            viewBox: \"0 0 24 24\",\n                            className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.likeIcon,\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                                    d: \"M20.884 13.19c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z\"\n                                })\n                            })\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.likeCount,\n                        children: favoriteCount\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.reply,\n                href: tweet.reply_url,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"Reply to this Tweet on Twitter\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.replyIconWrapper,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                            viewBox: \"0 0 24 24\",\n                            className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.replyIcon,\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                                    d: \"M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01z\"\n                                })\n                            })\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                        className: _tweet_actions_module_css__WEBPACK_IMPORTED_MODULE_1__.replyText,\n                        children: \"Reply\"\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_actions_copy_js__WEBPACK_IMPORTED_MODULE_3__.TweetActionsCopy, {\n                tweet: tweet\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css ***!
  \***************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"actions\": \"tweet-actions_actions__ej_PY\",\n\t\"like\": \"tweet-actions_like__tBRly\",\n\t\"reply\": \"tweet-actions_reply__8avB1\",\n\t\"copy\": \"tweet-actions_copy__zjnRT\",\n\t\"likeIconWrapper\": \"tweet-actions_likeIconWrapper__ZuwMg\",\n\t\"likeCount\": \"tweet-actions_likeCount__W1j4c\",\n\t\"replyIconWrapper\": \"tweet-actions_replyIconWrapper__PBYdV\",\n\t\"copyIconWrapper\": \"tweet-actions_copyIconWrapper___AmyB\",\n\t\"likeIcon\": \"tweet-actions_likeIcon__FyBtq\",\n\t\"replyIcon\": \"tweet-actions_replyIcon__9uuwp\",\n\t\"copyIcon\": \"tweet-actions_copyIcon__UogPG\",\n\t\"replyText\": \"tweet-actions_replyText__t32uM\",\n\t\"copyText\": \"tweet-actions_copyText__TygY9\"\n};\n\nmodule.exports.__checksum = \"d22217f39dad\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWFjdGlvbnMubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC10d2VldEAzLjIuMl9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1hY3Rpb25zLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiYWN0aW9uc1wiOiBcInR3ZWV0LWFjdGlvbnNfYWN0aW9uc19fZWpfUFlcIixcblx0XCJsaWtlXCI6IFwidHdlZXQtYWN0aW9uc19saWtlX190QlJseVwiLFxuXHRcInJlcGx5XCI6IFwidHdlZXQtYWN0aW9uc19yZXBseV9fOGF2QjFcIixcblx0XCJjb3B5XCI6IFwidHdlZXQtYWN0aW9uc19jb3B5X196am5SVFwiLFxuXHRcImxpa2VJY29uV3JhcHBlclwiOiBcInR3ZWV0LWFjdGlvbnNfbGlrZUljb25XcmFwcGVyX19adXdNZ1wiLFxuXHRcImxpa2VDb3VudFwiOiBcInR3ZWV0LWFjdGlvbnNfbGlrZUNvdW50X19XMWo0Y1wiLFxuXHRcInJlcGx5SWNvbldyYXBwZXJcIjogXCJ0d2VldC1hY3Rpb25zX3JlcGx5SWNvbldyYXBwZXJfX1BCWWRWXCIsXG5cdFwiY29weUljb25XcmFwcGVyXCI6IFwidHdlZXQtYWN0aW9uc19jb3B5SWNvbldyYXBwZXJfX19BbXlCXCIsXG5cdFwibGlrZUljb25cIjogXCJ0d2VldC1hY3Rpb25zX2xpa2VJY29uX19GeUJ0cVwiLFxuXHRcInJlcGx5SWNvblwiOiBcInR3ZWV0LWFjdGlvbnNfcmVwbHlJY29uX185dXV3cFwiLFxuXHRcImNvcHlJY29uXCI6IFwidHdlZXQtYWN0aW9uc19jb3B5SWNvbl9fVW9nUEdcIixcblx0XCJyZXBseVRleHRcIjogXCJ0d2VldC1hY3Rpb25zX3JlcGx5VGV4dF9fdDMydU1cIixcblx0XCJjb3B5VGV4dFwiOiBcInR3ZWV0LWFjdGlvbnNfY29weVRleHRfX1R5Z1k5XCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImQyMjIxN2YzOWRhZFwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-body.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-body.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetBody: () => (/* binding */ TweetBody)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_link_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-link.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-link.js\");\n/* harmony import */ var _tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-body.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css\");\n\n\n\nconst TweetBody = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n        className: _tweet_body_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        lang: tweet.lang,\n        dir: \"auto\",\n        children: tweet.entities.map((item, i)=>{\n            switch(item.type){\n                case 'hashtag':\n                case 'mention':\n                case 'url':\n                case 'symbol':\n                    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_link_js__WEBPACK_IMPORTED_MODULE_2__.TweetLink, {\n                        href: item.href,\n                        children: item.text\n                    }, i);\n                case 'media':\n                    // Media text is currently never displayed, some tweets however might have indices\n                    // that do match `display_text_range` so for those cases we ignore the content.\n                    return;\n                default:\n                    // We use `dangerouslySetInnerHTML` to preserve the text encoding.\n                    // https://github.com/vercel-labs/react-tweet/issues/29\n                    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                        dangerouslySetInnerHTML: {\n                            __html: item.text\n                        }\n                    }, i);\n            }\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-body.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css ***!
  \************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-body_root__weWas\"\n};\n\nmodule.exports.__checksum = \"80a811e77734\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWJvZHkubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC10d2VldEAzLjIuMl9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1ib2R5Lm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicm9vdFwiOiBcInR3ZWV0LWJvZHlfcm9vdF9fd2VXYXNcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiODBhODExZTc3NzM0XCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-container.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-container.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetContainer: () => (/* binding */ TweetContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _tweet_container_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-container.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css\");\n/* harmony import */ var _theme_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./theme.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/theme.css\");\n\n\n\n\nconst TweetContainer = ({ className, children })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])('react-tweet-theme', _tweet_container_module_css__WEBPACK_IMPORTED_MODULE_2__.root, className),\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"article\", {\n            className: _tweet_container_module_css__WEBPACK_IMPORTED_MODULE_2__.article,\n            children: children\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWNvbnRhaW5lci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFnRDtBQUN4QjtBQUNxQjtBQUN4QjtBQUNkLDBCQUEwQixxQkFBcUIsaUJBQWlCLHNEQUFJO0FBQzNFLG1CQUFtQixnREFBSSxzQkFBc0IsNkRBQU07QUFDbkQsZ0NBQWdDLHNEQUFJO0FBQ3BDLHVCQUF1QixnRUFBUztBQUNoQztBQUNBLFNBQVM7QUFDVCxLQUFLIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtY29udGFpbmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgY2xzeCBmcm9tICdjbHN4JztcbmltcG9ydCBzIGZyb20gJy4vdHdlZXQtY29udGFpbmVyLm1vZHVsZS5jc3MnO1xuaW1wb3J0ICcuL3RoZW1lLmNzcyc7XG5leHBvcnQgY29uc3QgVHdlZXRDb250YWluZXIgPSAoeyBjbGFzc05hbWUsIGNoaWxkcmVuIH0pPT4vKiNfX1BVUkVfXyovIF9qc3goXCJkaXZcIiwge1xuICAgICAgICBjbGFzc05hbWU6IGNsc3goJ3JlYWN0LXR3ZWV0LXRoZW1lJywgcy5yb290LCBjbGFzc05hbWUpLFxuICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyBfanN4KFwiYXJ0aWNsZVwiLCB7XG4gICAgICAgICAgICBjbGFzc05hbWU6IHMuYXJ0aWNsZSxcbiAgICAgICAgICAgIGNoaWxkcmVuOiBjaGlsZHJlblxuICAgICAgICB9KVxuICAgIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-container.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css":
/*!*****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css ***!
  \*****************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-container_root__LdZIw\",\n\t\"article\": \"tweet-container_article__j9DMk\"\n};\n\nmodule.exports.__checksum = \"fbf73421bd20\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWNvbnRhaW5lci5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtY29udGFpbmVyLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicm9vdFwiOiBcInR3ZWV0LWNvbnRhaW5lcl9yb290X19MZFpJd1wiLFxuXHRcImFydGljbGVcIjogXCJ0d2VldC1jb250YWluZXJfYXJ0aWNsZV9fajlETWtcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiZmJmNzM0MjFiZDIwXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-header.js":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-header.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetHeader: () => (/* binding */ TweetHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _avatar_img_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./avatar-img.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/avatar-img.js\");\n/* harmony import */ var _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-header.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css\");\n/* harmony import */ var _verified_badge_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./verified-badge.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/verified-badge.js\");\n\n\n\n\n\nconst TweetHeader = ({ tweet, components })=>{\n    var _components_AvatarImg;\n    const Img = (_components_AvatarImg = components == null ? void 0 : components.AvatarImg) != null ? _components_AvatarImg : _avatar_img_js__WEBPACK_IMPORTED_MODULE_3__.AvatarImg;\n    const { user } = tweet;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.header,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                href: tweet.url,\n                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatar,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarOverflow, user.profile_image_shape === 'Square' && _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarSquare),\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Img, {\n                            src: user.profile_image_url_https,\n                            alt: user.name,\n                            width: 48,\n                            height: 48\n                        })\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarOverflow,\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                            className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.avatarShadow\n                        })\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.author,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                        href: tweet.url,\n                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorLink,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorLinkText,\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                                    title: user.name,\n                                    children: user.name\n                                })\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_verified_badge_js__WEBPACK_IMPORTED_MODULE_4__.VerifiedBadge, {\n                                user: user,\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorVerified\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorMeta,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                                href: tweet.url,\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.username,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", {\n                                    title: `@${user.screen_name}`,\n                                    children: [\n                                        \"@\",\n                                        user.screen_name\n                                    ]\n                                })\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.authorFollow,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.separator,\n                                        children: \"·\"\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                                        href: user.follow_url,\n                                        className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.follow,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        children: \"Follow\"\n                                    })\n                                ]\n                            })\n                        ]\n                    })\n                ]\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                href: tweet.url,\n                className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.brand,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"View on Twitter\",\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    className: _tweet_header_module_css__WEBPACK_IMPORTED_MODULE_2__.twitterIcon,\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"\n                        })\n                    })\n                })\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-header.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css ***!
  \**************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"header\": \"tweet-header_header__EPMkk\",\n\t\"avatar\": \"tweet-header_avatar__ZLTDw\",\n\t\"avatarOverflow\": \"tweet-header_avatarOverflow__6N_2N\",\n\t\"avatarSquare\": \"tweet-header_avatarSquare__K_diY\",\n\t\"avatarShadow\": \"tweet-header_avatarShadow__lGoQ8\",\n\t\"author\": \"tweet-header_author__AJyCK\",\n\t\"authorLink\": \"tweet-header_authorLink__ZVc2S\",\n\t\"authorVerified\": \"tweet-header_authorVerified__b202y\",\n\t\"authorLinkText\": \"tweet-header_authorLinkText__pL4AF\",\n\t\"authorMeta\": \"tweet-header_authorMeta__U9HZQ\",\n\t\"authorFollow\": \"tweet-header_authorFollow__6vmcJ\",\n\t\"username\": \"tweet-header_username__lCnTz\",\n\t\"follow\": \"tweet-header_follow__ImR8J\",\n\t\"separator\": \"tweet-header_separator__Ed3cQ\",\n\t\"brand\": \"tweet-header_brand__a3WLu\",\n\t\"twitterIcon\": \"tweet-header_twitterIcon__sclTd\"\n};\n\nmodule.exports.__checksum = \"6b9f2c6fd9a5\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetInReplyTo: () => (/* binding */ TweetInReplyTo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_in_reply_to_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-in-reply-to.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css\");\n\n\nconst TweetInReplyTo = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n        href: tweet.in_reply_to_url,\n        className: _tweet_in_reply_to_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        children: [\n            \"Replying to @\",\n            tweet.in_reply_to_screen_name\n        ]\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWluLXJlcGx5LXRvLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrRDtBQUNIO0FBQ3hDLDBCQUEwQixPQUFPLGlCQUFpQix1REFBSztBQUM5RDtBQUNBLG1CQUFtQiwrREFBTTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtaW4tcmVwbHktdG8uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4cyBhcyBfanN4cyB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuaW1wb3J0IHMgZnJvbSAnLi90d2VldC1pbi1yZXBseS10by5tb2R1bGUuY3NzJztcbmV4cG9ydCBjb25zdCBUd2VldEluUmVwbHlUbyA9ICh7IHR3ZWV0IH0pPT4vKiNfX1BVUkVfXyovIF9qc3hzKFwiYVwiLCB7XG4gICAgICAgIGhyZWY6IHR3ZWV0LmluX3JlcGx5X3RvX3VybCxcbiAgICAgICAgY2xhc3NOYW1lOiBzLnJvb3QsXG4gICAgICAgIHRhcmdldDogXCJfYmxhbmtcIixcbiAgICAgICAgcmVsOiBcIm5vb3BlbmVyIG5vcmVmZXJyZXJcIixcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgICAgIFwiUmVwbHlpbmcgdG8gQFwiLFxuICAgICAgICAgICAgdHdlZXQuaW5fcmVwbHlfdG9fc2NyZWVuX25hbWVcbiAgICAgICAgXVxuICAgIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css ***!
  \*******************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-in-reply-to_root__sbORK\"\n};\n\nmodule.exports.__checksum = \"43db41b0c5c5\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWluLXJlcGx5LXRvLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtaW4tcmVwbHktdG8ubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJyb290XCI6IFwidHdlZXQtaW4tcmVwbHktdG9fcm9vdF9fc2JPUktcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiNDNkYjQxYjBjNWM1XCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetInfoCreatedAt: () => (/* binding */ TweetInfoCreatedAt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _date_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../date-utils.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/date-utils.js\");\n/* harmony import */ var _tweet_info_created_at_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-info-created-at.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css\");\n\n\n\nconst TweetInfoCreatedAt = ({ tweet })=>{\n    const createdAt = new Date(tweet.created_at);\n    const formattedCreatedAtDate = (0,_date_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatDate)(createdAt);\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n        className: _tweet_info_created_at_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        href: tweet.url,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        \"aria-label\": formattedCreatedAtDate,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"time\", {\n            dateTime: createdAt.toISOString(),\n            children: formattedCreatedAtDate\n        })\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWluZm8tY3JlYXRlZC1hdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdEO0FBQ0Y7QUFDSztBQUM1Qyw4QkFBOEIsT0FBTztBQUM1QztBQUNBLG1DQUFtQywwREFBVTtBQUM3Qyx5QkFBeUIsc0RBQUk7QUFDN0IsbUJBQW1CLG1FQUFNO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLHNEQUFJO0FBQ3BDO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtaW5mby1jcmVhdGVkLWF0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgeyBmb3JtYXREYXRlIH0gZnJvbSAnLi4vZGF0ZS11dGlscy5qcyc7XG5pbXBvcnQgcyBmcm9tICcuL3R3ZWV0LWluZm8tY3JlYXRlZC1hdC5tb2R1bGUuY3NzJztcbmV4cG9ydCBjb25zdCBUd2VldEluZm9DcmVhdGVkQXQgPSAoeyB0d2VldCB9KT0+e1xuICAgIGNvbnN0IGNyZWF0ZWRBdCA9IG5ldyBEYXRlKHR3ZWV0LmNyZWF0ZWRfYXQpO1xuICAgIGNvbnN0IGZvcm1hdHRlZENyZWF0ZWRBdERhdGUgPSBmb3JtYXREYXRlKGNyZWF0ZWRBdCk7XG4gICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX2pzeChcImFcIiwge1xuICAgICAgICBjbGFzc05hbWU6IHMucm9vdCxcbiAgICAgICAgaHJlZjogdHdlZXQudXJsLFxuICAgICAgICB0YXJnZXQ6IFwiX2JsYW5rXCIsXG4gICAgICAgIHJlbDogXCJub29wZW5lciBub3JlZmVycmVyXCIsXG4gICAgICAgIFwiYXJpYS1sYWJlbFwiOiBmb3JtYXR0ZWRDcmVhdGVkQXREYXRlLFxuICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyBfanN4KFwidGltZVwiLCB7XG4gICAgICAgICAgICBkYXRlVGltZTogY3JlYXRlZEF0LnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICBjaGlsZHJlbjogZm9ybWF0dGVkQ3JlYXRlZEF0RGF0ZVxuICAgICAgICB9KVxuICAgIH0pO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css":
/*!***********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css ***!
  \***********************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-info-created-at_root__nQIfG\"\n};\n\nmodule.exports.__checksum = \"f27d7f32f0a2\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWluZm8tY3JlYXRlZC1hdC5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXR3ZWV0QDMuMi4yX3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LWluZm8tY3JlYXRlZC1hdC5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInJvb3RcIjogXCJ0d2VldC1pbmZvLWNyZWF0ZWQtYXRfcm9vdF9fblFJZkdcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiZjI3ZDdmMzJmMGEyXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetInfo: () => (/* binding */ TweetInfo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_info_created_at_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-info-created-at.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js\");\n/* harmony import */ var _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-info.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css\");\n\n\n\nconst TweetInfo = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__.info,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_info_created_at_js__WEBPACK_IMPORTED_MODULE_2__.TweetInfoCreatedAt, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                className: _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__.infoLink,\n                href: \"https://help.x.com/en/x-for-websites-ads-info-and-privacy\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"Twitter for Websites, Ads Information and Privacy\",\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    className: _tweet_info_module_css__WEBPACK_IMPORTED_MODULE_1__.infoIcon,\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M13.5 8.5c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5S11.17 7 12 7s1.5.67 1.5 1.5zM13 17v-5h-2v5h2zm-1 5.25c5.66 0 10.25-4.59 10.25-10.25S17.66 1.75 12 1.75 1.75 6.34 1.75 12 6.34 22.25 12 22.25zM20.25 12c0 4.56-3.69 8.25-8.25 8.25S3.75 16.56 3.75 12 7.44 3.75 12 3.75s8.25 3.69 8.25 8.25z\"\n                        })\n                    })\n                })\n            })\n        ]\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css ***!
  \************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"info\": \"tweet-info_info__WSVBu\",\n\t\"infoLink\": \"tweet-info_infoLink__3r0a6\",\n\t\"infoIcon\": \"tweet-info_infoIcon__KdZnx\"\n};\n\nmodule.exports.__checksum = \"7c8d90dde90e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWluZm8ubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtaW5mby5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImluZm9cIjogXCJ0d2VldC1pbmZvX2luZm9fX1dTVkJ1XCIsXG5cdFwiaW5mb0xpbmtcIjogXCJ0d2VldC1pbmZvX2luZm9MaW5rX18zcjBhNlwiLFxuXHRcImluZm9JY29uXCI6IFwidHdlZXQtaW5mb19pbmZvSWNvbl9fS2RabnhcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiN2M4ZDkwZGRlOTBlXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-link.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-link.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetLink: () => (/* binding */ TweetLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_link_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-link.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css\");\n\n\nconst TweetLink = ({ href, children })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n        href: href,\n        className: _tweet_link_module_css__WEBPACK_IMPORTED_MODULE_1__.root,\n        target: \"_blank\",\n        rel: \"noopener noreferrer nofollow\",\n        children: children\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWxpbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEO0FBQ1I7QUFDakMscUJBQXFCLGdCQUFnQixpQkFBaUIsc0RBQUk7QUFDakU7QUFDQSxtQkFBbUIsd0RBQU07QUFDekI7QUFDQTtBQUNBO0FBQ0EsS0FBSyIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXR3ZWV0QDMuMi4yX3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LWxpbmsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IGFzIF9qc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmltcG9ydCBzIGZyb20gJy4vdHdlZXQtbGluay5tb2R1bGUuY3NzJztcbmV4cG9ydCBjb25zdCBUd2VldExpbmsgPSAoeyBocmVmLCBjaGlsZHJlbiB9KT0+LyojX19QVVJFX18qLyBfanN4KFwiYVwiLCB7XG4gICAgICAgIGhyZWY6IGhyZWYsXG4gICAgICAgIGNsYXNzTmFtZTogcy5yb290LFxuICAgICAgICB0YXJnZXQ6IFwiX2JsYW5rXCIsXG4gICAgICAgIHJlbDogXCJub29wZW5lciBub3JlZmVycmVyIG5vZm9sbG93XCIsXG4gICAgICAgIGNoaWxkcmVuOiBjaGlsZHJlblxuICAgIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css ***!
  \************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-link_root__I9rm4\"\n};\n\nmodule.exports.__checksum = \"c9a4d3cfd1ea\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LWxpbmsubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC10d2VldEAzLjIuMl9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1saW5rLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicm9vdFwiOiBcInR3ZWV0LWxpbmtfcm9vdF9fSTlybTRcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiYzlhNGQzY2ZkMWVhXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetMediaVideo: () => (/* binding */ TweetMediaVideo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tweet-media.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css\");\n/* harmony import */ var _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tweet-media-video.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css\");\n/* __next_internal_client_entry_do_not_use__ TweetMediaVideo auto */ \n\n\n\n\n\nconst TweetMediaVideo = ({ tweet, media })=>{\n    const [playButton, setPlayButton] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [ended, setEnded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mp4Video = (0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getMp4Video)(media);\n    let timeout = 0;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"video\", {\n                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.image,\n                poster: (0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getMediaUrl)(media, 'small'),\n                controls: !playButton,\n                playsInline: true,\n                preload: \"none\",\n                tabIndex: playButton ? -1 : 0,\n                onPlay: ()=>{\n                    if (timeout) window.clearTimeout(timeout);\n                    if (!isPlaying) setIsPlaying(true);\n                    if (ended) setEnded(false);\n                },\n                onPause: ()=>{\n                    // When the video is seeked (moved to a different timestamp), it will pause for a moment\n                    // before resuming. We don't want to show the message in that case so we wait a bit.\n                    if (timeout) window.clearTimeout(timeout);\n                    timeout = window.setTimeout(()=>{\n                        if (isPlaying) setIsPlaying(false);\n                        timeout = 0;\n                    }, 100);\n                },\n                onEnded: ()=>{\n                    setEnded(true);\n                },\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"source\", {\n                    src: mp4Video.url,\n                    type: mp4Video.content_type\n                })\n            }),\n            playButton && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", {\n                type: \"button\",\n                className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.videoButton,\n                \"aria-label\": \"View video on X\",\n                onClick: (e)=>{\n                    const video = e.currentTarget.previousSibling;\n                    e.preventDefault();\n                    setPlayButton(false);\n                    video.load();\n                    video.play().then(()=>{\n                        setIsPlaying(true);\n                        video.focus();\n                    }).catch((error)=>{\n                        console.error('Error playing video:', error);\n                        setPlayButton(true);\n                        setIsPlaying(false);\n                    });\n                },\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.videoButtonIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", {\n                        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                            d: \"M21 12L4 2v20l17-10z\"\n                        })\n                    })\n                })\n            }),\n            !isPlaying && !ended && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.watchOnTwitter,\n                children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                    href: tweet.url,\n                    className: _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.anchor,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    children: playButton ? 'Watch on X' : 'Continue watching on X'\n                })\n            }),\n            ended && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n                href: tweet.url,\n                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.anchor, _tweet_media_video_module_css__WEBPACK_IMPORTED_MODULE_4__.viewReplies),\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: \"View replies\"\n            })\n        ]\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css ***!
  \*******************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"anchor\": \"tweet-media-video_anchor__L9Y_X\",\n\t\"videoButton\": \"tweet-media-video_videoButton__4c7xH\",\n\t\"videoButtonIcon\": \"tweet-media-video_videoButtonIcon__CzvB3\",\n\t\"watchOnTwitter\": \"tweet-media-video_watchOnTwitter__p_pru\",\n\t\"viewReplies\": \"tweet-media-video_viewReplies__KOJ9D\"\n};\n\nmodule.exports.__checksum = \"9ff3f02fd44d\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LW1lZGlhLXZpZGVvLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC10d2VldEAzLjIuMl9yZWFjdC1kb21AMTkuMS4xX3JlYWN0QDE5LjEuMV9fcmVhY3RAMTkuMS4xXFxub2RlX21vZHVsZXNcXHJlYWN0LXR3ZWV0XFxkaXN0XFx0d2l0dGVyLXRoZW1lXFx0d2VldC1tZWRpYS12aWRlby5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImFuY2hvclwiOiBcInR3ZWV0LW1lZGlhLXZpZGVvX2FuY2hvcl9fTDlZX1hcIixcblx0XCJ2aWRlb0J1dHRvblwiOiBcInR3ZWV0LW1lZGlhLXZpZGVvX3ZpZGVvQnV0dG9uX180Yzd4SFwiLFxuXHRcInZpZGVvQnV0dG9uSWNvblwiOiBcInR3ZWV0LW1lZGlhLXZpZGVvX3ZpZGVvQnV0dG9uSWNvbl9fQ3p2QjNcIixcblx0XCJ3YXRjaE9uVHdpdHRlclwiOiBcInR3ZWV0LW1lZGlhLXZpZGVvX3dhdGNoT25Ud2l0dGVyX19wX3BydVwiLFxuXHRcInZpZXdSZXBsaWVzXCI6IFwidHdlZXQtbWVkaWEtdmlkZW9fdmlld1JlcGxpZXNfX0tPSjlEXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjlmZjNmMDJmZDQ0ZFwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetMedia: () => (/* binding */ TweetMedia)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_media_video_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./tweet-media-video.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js\");\n/* harmony import */ var _media_img_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./media-img.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/media-img.js\");\n/* harmony import */ var _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tweet-media.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css\");\n\n\n\n\n\n\n\nconst getSkeletonStyle = (media, itemCount)=>{\n    let paddingBottom = 56.25 // default of 16x9\n    ;\n    // if we only have 1 item, show at original ratio\n    if (itemCount === 1) paddingBottom = 100 / media.original_info.width * media.original_info.height;\n    // if we have 2 items, double the default to be 16x9 total\n    if (itemCount === 2) paddingBottom = paddingBottom * 2;\n    return {\n        width: media.type === 'photo' ? undefined : 'unset',\n        paddingBottom: `${paddingBottom}%`\n    };\n};\nconst TweetMedia = ({ tweet, components, quoted })=>{\n    var _tweet_mediaDetails, _tweet_mediaDetails1;\n    var _tweet_mediaDetails_length;\n    const length = (_tweet_mediaDetails_length = (_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) != null ? _tweet_mediaDetails_length : 0;\n    var _components_MediaImg;\n    const Img = (_components_MediaImg = components == null ? void 0 : components.MediaImg) != null ? _components_MediaImg : _media_img_js__WEBPACK_IMPORTED_MODULE_4__.MediaImg;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.root, !quoted && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.rounded),\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaWrapper, length > 1 && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.grid2Columns, length === 3 && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.grid3, length > 4 && _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.grid2x2),\n            children: (_tweet_mediaDetails1 = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails1.map((media)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                    children: media.type === 'photo' ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"a\", {\n                        href: tweet.url,\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaContainer, _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaLink),\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.skeleton,\n                                style: getSkeletonStyle(media, length)\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Img, {\n                                src: (0,_utils_js__WEBPACK_IMPORTED_MODULE_5__.getMediaUrl)(media, 'small'),\n                                alt: media.ext_alt_text || 'Image',\n                                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.image,\n                                draggable: true\n                            })\n                        ]\n                    }, media.media_url_https) : /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                        className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.mediaContainer,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                className: _tweet_media_module_css__WEBPACK_IMPORTED_MODULE_3__.skeleton,\n                                style: getSkeletonStyle(media, length)\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_media_video_js__WEBPACK_IMPORTED_MODULE_6__.TweetMediaVideo, {\n                                tweet: tweet,\n                                media: media\n                            })\n                        ]\n                    }, media.media_url_https)\n                }, media.media_url_https))\n        })\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LW1lZGlhLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQStEO0FBQzlCO0FBQ1Q7QUFDa0I7QUFDZTtBQUNmO0FBQ0Q7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLGNBQWM7QUFDeEM7QUFDQTtBQUNPLHNCQUFzQiwyQkFBMkI7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0SEFBNEgsbURBQVE7QUFDcEkseUJBQXlCLHNEQUFJO0FBQzdCLG1CQUFtQixnREFBSSxDQUFDLHlEQUFNLGFBQWEsNERBQVM7QUFDcEQsZ0NBQWdDLHNEQUFJO0FBQ3BDLHVCQUF1QixnREFBSSxDQUFDLGlFQUFjLGdCQUFnQixpRUFBYyxrQkFBa0IsMERBQU8sZ0JBQWdCLDREQUFTO0FBQzFILHFJQUFxSSxzREFBSSxDQUFDLDJDQUFRO0FBQ2xKLHFFQUFxRSx1REFBSztBQUMxRTtBQUNBLG1DQUFtQyxnREFBSSxDQUFDLG1FQUFnQixFQUFFLDhEQUFXO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBLDBDQUEwQyxzREFBSTtBQUM5QywyQ0FBMkMsNkRBQVU7QUFDckQ7QUFDQSw2QkFBNkI7QUFDN0IsMENBQTBDLHNEQUFJO0FBQzlDLHFDQUFxQyxzREFBVztBQUNoRDtBQUNBLDJDQUEyQywwREFBTztBQUNsRDtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBLHFCQUFxQix5Q0FBeUMsdURBQUs7QUFDbkUsbUNBQW1DLG1FQUFnQjtBQUNuRDtBQUNBLDBDQUEwQyxzREFBSTtBQUM5QywyQ0FBMkMsNkRBQVU7QUFDckQ7QUFDQSw2QkFBNkI7QUFDN0IsMENBQTBDLHNEQUFJLENBQUMsa0VBQWU7QUFDOUQ7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBLHFCQUFxQjtBQUNyQixpQkFBaUI7QUFDakIsU0FBUztBQUNULEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXR3ZWV0QDMuMi4yX3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LW1lZGlhLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4LCBqc3hzIGFzIF9qc3hzIH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgeyBGcmFnbWVudCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBjbHN4IGZyb20gJ2Nsc3gnO1xuaW1wb3J0IHsgZ2V0TWVkaWFVcmwgfSBmcm9tICcuLi91dGlscy5qcyc7XG5pbXBvcnQgeyBUd2VldE1lZGlhVmlkZW8gfSBmcm9tICcuL3R3ZWV0LW1lZGlhLXZpZGVvLmpzJztcbmltcG9ydCB7IE1lZGlhSW1nIH0gZnJvbSAnLi9tZWRpYS1pbWcuanMnO1xuaW1wb3J0IHMgZnJvbSAnLi90d2VldC1tZWRpYS5tb2R1bGUuY3NzJztcbmNvbnN0IGdldFNrZWxldG9uU3R5bGUgPSAobWVkaWEsIGl0ZW1Db3VudCk9PntcbiAgICBsZXQgcGFkZGluZ0JvdHRvbSA9IDU2LjI1IC8vIGRlZmF1bHQgb2YgMTZ4OVxuICAgIDtcbiAgICAvLyBpZiB3ZSBvbmx5IGhhdmUgMSBpdGVtLCBzaG93IGF0IG9yaWdpbmFsIHJhdGlvXG4gICAgaWYgKGl0ZW1Db3VudCA9PT0gMSkgcGFkZGluZ0JvdHRvbSA9IDEwMCAvIG1lZGlhLm9yaWdpbmFsX2luZm8ud2lkdGggKiBtZWRpYS5vcmlnaW5hbF9pbmZvLmhlaWdodDtcbiAgICAvLyBpZiB3ZSBoYXZlIDIgaXRlbXMsIGRvdWJsZSB0aGUgZGVmYXVsdCB0byBiZSAxNng5IHRvdGFsXG4gICAgaWYgKGl0ZW1Db3VudCA9PT0gMikgcGFkZGluZ0JvdHRvbSA9IHBhZGRpbmdCb3R0b20gKiAyO1xuICAgIHJldHVybiB7XG4gICAgICAgIHdpZHRoOiBtZWRpYS50eXBlID09PSAncGhvdG8nID8gdW5kZWZpbmVkIDogJ3Vuc2V0JyxcbiAgICAgICAgcGFkZGluZ0JvdHRvbTogYCR7cGFkZGluZ0JvdHRvbX0lYFxuICAgIH07XG59O1xuZXhwb3J0IGNvbnN0IFR3ZWV0TWVkaWEgPSAoeyB0d2VldCwgY29tcG9uZW50cywgcXVvdGVkIH0pPT57XG4gICAgdmFyIF90d2VldF9tZWRpYURldGFpbHMsIF90d2VldF9tZWRpYURldGFpbHMxO1xuICAgIHZhciBfdHdlZXRfbWVkaWFEZXRhaWxzX2xlbmd0aDtcbiAgICBjb25zdCBsZW5ndGggPSAoX3R3ZWV0X21lZGlhRGV0YWlsc19sZW5ndGggPSAoX3R3ZWV0X21lZGlhRGV0YWlscyA9IHR3ZWV0Lm1lZGlhRGV0YWlscykgPT0gbnVsbCA/IHZvaWQgMCA6IF90d2VldF9tZWRpYURldGFpbHMubGVuZ3RoKSAhPSBudWxsID8gX3R3ZWV0X21lZGlhRGV0YWlsc19sZW5ndGggOiAwO1xuICAgIHZhciBfY29tcG9uZW50c19NZWRpYUltZztcbiAgICBjb25zdCBJbWcgPSAoX2NvbXBvbmVudHNfTWVkaWFJbWcgPSBjb21wb25lbnRzID09IG51bGwgPyB2b2lkIDAgOiBjb21wb25lbnRzLk1lZGlhSW1nKSAhPSBudWxsID8gX2NvbXBvbmVudHNfTWVkaWFJbWcgOiBNZWRpYUltZztcbiAgICByZXR1cm4gLyojX19QVVJFX18qLyBfanN4KFwiZGl2XCIsIHtcbiAgICAgICAgY2xhc3NOYW1lOiBjbHN4KHMucm9vdCwgIXF1b3RlZCAmJiBzLnJvdW5kZWQpLFxuICAgICAgICBjaGlsZHJlbjogLyojX19QVVJFX18qLyBfanN4KFwiZGl2XCIsIHtcbiAgICAgICAgICAgIGNsYXNzTmFtZTogY2xzeChzLm1lZGlhV3JhcHBlciwgbGVuZ3RoID4gMSAmJiBzLmdyaWQyQ29sdW1ucywgbGVuZ3RoID09PSAzICYmIHMuZ3JpZDMsIGxlbmd0aCA+IDQgJiYgcy5ncmlkMngyKSxcbiAgICAgICAgICAgIGNoaWxkcmVuOiAoX3R3ZWV0X21lZGlhRGV0YWlsczEgPSB0d2VldC5tZWRpYURldGFpbHMpID09IG51bGwgPyB2b2lkIDAgOiBfdHdlZXRfbWVkaWFEZXRhaWxzMS5tYXAoKG1lZGlhKT0+LyojX19QVVJFX18qLyBfanN4KEZyYWdtZW50LCB7XG4gICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBtZWRpYS50eXBlID09PSAncGhvdG8nID8gLyojX19QVVJFX18qLyBfanN4cyhcImFcIiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgaHJlZjogdHdlZXQudXJsLFxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lOiBjbHN4KHMubWVkaWFDb250YWluZXIsIHMubWVkaWFMaW5rKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldDogXCJfYmxhbmtcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlbDogXCJub29wZW5lciBub3JlZmVycmVyXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8qI19fUFVSRV9fKi8gX2pzeChcImRpdlwiLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZTogcy5za2VsZXRvbixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU6IGdldFNrZWxldG9uU3R5bGUobWVkaWEsIGxlbmd0aClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvKiNfX1BVUkVfXyovIF9qc3goSW1nLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYzogZ2V0TWVkaWFVcmwobWVkaWEsICdzbWFsbCcpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ6IG1lZGlhLmV4dF9hbHRfdGV4dCB8fCAnSW1hZ2UnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU6IHMuaW1hZ2UsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRyYWdnYWJsZTogdHJ1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICAgICAgICBdXG4gICAgICAgICAgICAgICAgICAgIH0sIG1lZGlhLm1lZGlhX3VybF9odHRwcykgOiAvKiNfX1BVUkVfXyovIF9qc3hzKFwiZGl2XCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZTogcy5tZWRpYUNvbnRhaW5lcixcbiAgICAgICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLyojX19QVVJFX18qLyBfanN4KFwiZGl2XCIsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lOiBzLnNrZWxldG9uLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZTogZ2V0U2tlbGV0b25TdHlsZShtZWRpYSwgbGVuZ3RoKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8qI19fUFVSRV9fKi8gX2pzeChUd2VldE1lZGlhVmlkZW8sIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHdlZXQ6IHR3ZWV0LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZWRpYTogbWVkaWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgXVxuICAgICAgICAgICAgICAgICAgICB9LCBtZWRpYS5tZWRpYV91cmxfaHR0cHMpXG4gICAgICAgICAgICAgICAgfSwgbWVkaWEubWVkaWFfdXJsX2h0dHBzKSlcbiAgICAgICAgfSlcbiAgICB9KTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css ***!
  \*************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-media_root__6szGZ\",\n\t\"rounded\": \"tweet-media_rounded__yiuDQ\",\n\t\"mediaWrapper\": \"tweet-media_mediaWrapper__dngVi\",\n\t\"grid2Columns\": \"tweet-media_grid2Columns__NJns3\",\n\t\"grid3\": \"tweet-media_grid3__0Nwcv\",\n\t\"grid2x2\": \"tweet-media_grid2x2__Xckan\",\n\t\"mediaContainer\": \"tweet-media_mediaContainer__eIDrY\",\n\t\"mediaLink\": \"tweet-media_mediaLink__yVPTy\",\n\t\"skeleton\": \"tweet-media_skeleton__3_DWv\",\n\t\"image\": \"tweet-media_image__AdraN\"\n};\n\nmodule.exports.__checksum = \"b775d73b18e4\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LW1lZGlhLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtbWVkaWEubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJyb290XCI6IFwidHdlZXQtbWVkaWFfcm9vdF9fNnN6R1pcIixcblx0XCJyb3VuZGVkXCI6IFwidHdlZXQtbWVkaWFfcm91bmRlZF9feWl1RFFcIixcblx0XCJtZWRpYVdyYXBwZXJcIjogXCJ0d2VldC1tZWRpYV9tZWRpYVdyYXBwZXJfX2RuZ1ZpXCIsXG5cdFwiZ3JpZDJDb2x1bW5zXCI6IFwidHdlZXQtbWVkaWFfZ3JpZDJDb2x1bW5zX19OSm5zM1wiLFxuXHRcImdyaWQzXCI6IFwidHdlZXQtbWVkaWFfZ3JpZDNfXzBOd2N2XCIsXG5cdFwiZ3JpZDJ4MlwiOiBcInR3ZWV0LW1lZGlhX2dyaWQyeDJfX1hja2FuXCIsXG5cdFwibWVkaWFDb250YWluZXJcIjogXCJ0d2VldC1tZWRpYV9tZWRpYUNvbnRhaW5lcl9fZUlEcllcIixcblx0XCJtZWRpYUxpbmtcIjogXCJ0d2VldC1tZWRpYV9tZWRpYUxpbmtfX3lWUFR5XCIsXG5cdFwic2tlbGV0b25cIjogXCJ0d2VldC1tZWRpYV9za2VsZXRvbl9fM19EV3ZcIixcblx0XCJpbWFnZVwiOiBcInR3ZWV0LW1lZGlhX2ltYWdlX19BZHJhTlwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJiNzc1ZDczYjE4ZTRcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetNotFound: () => (/* binding */ TweetNotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-container.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-container.js\");\n/* harmony import */ var _tweet_not_found_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-not-found.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css\");\n\n\n\nconst TweetNotFound = (_props)=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.TweetContainer, {\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n            className: _tweet_not_found_module_css__WEBPACK_IMPORTED_MODULE_2__.root,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h3\", {\n                    children: \"Tweet not found\"\n                }),\n                /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n                    children: \"The embedded tweet could not be found…\"\n                })\n            ]\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LW5vdC1mb3VuZC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStEO0FBQ1Q7QUFDSjtBQUMzQyw4Q0FBOEMsc0RBQUksQ0FBQywrREFBYztBQUN4RSxnQ0FBZ0MsdURBQUs7QUFDckMsdUJBQXVCLDZEQUFXO0FBQ2xDO0FBQ0EsOEJBQThCLHNEQUFJO0FBQ2xDO0FBQ0EsaUJBQWlCO0FBQ2pCLDhCQUE4QixzREFBSTtBQUNsQztBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLFNBQVM7QUFDVCxLQUFLIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtbm90LWZvdW5kLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGpzeCBhcyBfanN4LCBqc3hzIGFzIF9qc3hzIH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgeyBUd2VldENvbnRhaW5lciB9IGZyb20gJy4vdHdlZXQtY29udGFpbmVyLmpzJztcbmltcG9ydCBzdHlsZXMgZnJvbSAnLi90d2VldC1ub3QtZm91bmQubW9kdWxlLmNzcyc7XG5leHBvcnQgY29uc3QgVHdlZXROb3RGb3VuZCA9IChfcHJvcHMpPT4vKiNfX1BVUkVfXyovIF9qc3goVHdlZXRDb250YWluZXIsIHtcbiAgICAgICAgY2hpbGRyZW46IC8qI19fUFVSRV9fKi8gX2pzeHMoXCJkaXZcIiwge1xuICAgICAgICAgICAgY2xhc3NOYW1lOiBzdHlsZXMucm9vdCxcbiAgICAgICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICAgICAgICAgLyojX19QVVJFX18qLyBfanN4KFwiaDNcIiwge1xuICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbjogXCJUd2VldCBub3QgZm91bmRcIlxuICAgICAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgICAgIC8qI19fUFVSRV9fKi8gX2pzeChcInBcIiwge1xuICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbjogXCJUaGUgZW1iZWRkZWQgdHdlZXQgY291bGQgbm90IGJlIGZvdW5k4oCmXCJcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgXVxuICAgICAgICB9KVxuICAgIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css":
/*!*****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css ***!
  \*****************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-not-found_root__7mVOa\"\n};\n\nmodule.exports.__checksum = \"a15e2c8756e4\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LW5vdC1mb3VuZC5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXR3ZWV0QDMuMi4yX3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHR3ZWV0LW5vdC1mb3VuZC5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInJvb3RcIjogXCJ0d2VldC1ub3QtZm91bmRfcm9vdF9fN21WT2FcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiYTE1ZTJjODc1NmU0XCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-replies.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-replies.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetReplies: () => (/* binding */ TweetReplies)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/utils.js\");\n/* harmony import */ var _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-replies.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css\");\n\n\n\nconst TweetReplies = ({ tweet })=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__.replies,\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n            className: _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__.link,\n            href: tweet.url,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                className: _tweet_replies_module_css__WEBPACK_IMPORTED_MODULE_1__.text,\n                children: tweet.conversation_count === 0 ? 'Read more on X' : tweet.conversation_count === 1 ? `Read ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(tweet.conversation_count)} reply` : `Read ${(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.formatNumber)(tweet.conversation_count)} replies`\n            })\n        })\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-replies.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css ***!
  \***************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"replies\": \"tweet-replies_replies__HekiZ\",\n\t\"link\": \"tweet-replies_link__CpXYY\",\n\t\"text\": \"tweet-replies_text__bz_fP\"\n};\n\nmodule.exports.__checksum = \"0f6de95a7600\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LXJlcGxpZXMubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtcmVwbGllcy5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInJlcGxpZXNcIjogXCJ0d2VldC1yZXBsaWVzX3JlcGxpZXNfX0hla2laXCIsXG5cdFwibGlua1wiOiBcInR3ZWV0LXJlcGxpZXNfbGlua19fQ3BYWVlcIixcblx0XCJ0ZXh0XCI6IFwidHdlZXQtcmVwbGllc190ZXh0X19iel9mUFwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCIwZjZkZTk1YTc2MDBcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TweetSkeleton: () => (/* binding */ TweetSkeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _tweet_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tweet-container.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-container.js\");\n/* harmony import */ var _skeleton_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./skeleton.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/skeleton.js\");\n/* harmony import */ var _tweet_skeleton_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tweet-skeleton.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css\");\n\n\n\n\nconst TweetSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_tweet_container_js__WEBPACK_IMPORTED_MODULE_1__.TweetContainer, {\n        className: _tweet_skeleton_module_css__WEBPACK_IMPORTED_MODULE_2__.root,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '3rem',\n                    marginBottom: '0.75rem'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '6rem',\n                    margin: '0.5rem 0'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                style: {\n                    borderTop: 'var(--tweet-border)',\n                    margin: '0.5rem 0'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '2rem'\n                }\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_skeleton_js__WEBPACK_IMPORTED_MODULE_3__.Skeleton, {\n                style: {\n                    height: '2rem',\n                    borderRadius: '9999px',\n                    marginTop: '0.5rem'\n                }\n            })\n        ]\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css ***!
  \****************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"root\": \"tweet-skeleton_root__4qAK5\"\n};\n\nmodule.exports.__checksum = \"0cdec0a5d3f9\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3R3ZWV0LXNrZWxldG9uLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFxyZWFjdC10d2VldFxcZGlzdFxcdHdpdHRlci10aGVtZVxcdHdlZXQtc2tlbGV0b24ubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJyb290XCI6IFwidHdlZXQtc2tlbGV0b25fcm9vdF9fNHFBSzVcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiMGNkZWMwYTVkM2Y5XCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/verified-badge.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/verified-badge.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VerifiedBadge: () => (/* binding */ VerifiedBadge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.4.5_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _icons_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/index.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified.js\");\n/* harmony import */ var _icons_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/index.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js\");\n/* harmony import */ var _icons_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/index.js */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js\");\n/* harmony import */ var _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./verified-badge.module.css */ \"(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css\");\n\n\n\n\nconst VerifiedBadge = ({ user, className })=>{\n    const verified = user.verified || user.is_blue_verified || user.verified_type;\n    let icon = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_icons_index_js__WEBPACK_IMPORTED_MODULE_3__.Verified, {});\n    let iconClassName = _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__.verifiedBlue;\n    if (verified) {\n        if (!user.is_blue_verified) {\n            iconClassName = _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__.verifiedOld;\n        }\n        switch(user.verified_type){\n            case 'Government':\n                icon = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_icons_index_js__WEBPACK_IMPORTED_MODULE_4__.VerifiedGovernment, {});\n                iconClassName = _verified_badge_module_css__WEBPACK_IMPORTED_MODULE_2__.verifiedGovernment;\n                break;\n            case 'Business':\n                icon = /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_icons_index_js__WEBPACK_IMPORTED_MODULE_5__.VerifiedBusiness, {});\n                iconClassName = null;\n                break;\n        }\n    }\n    return verified ? /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(className, iconClassName),\n        children: icon\n    }) : null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/verified-badge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css ***!
  \****************************************************************************************************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"verifiedOld\": \"verified-badge_verifiedOld___55JX\",\n\t\"verifiedBlue\": \"verified-badge_verifiedBlue__csDmG\",\n\t\"verifiedGovernment\": \"verified-badge_verifiedGovernment__P_P9A\"\n};\n\nmodule.exports.__checksum = \"40b4beabc0ac\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtdHdlZXRAMy4yLjJfcmVhY3QtZG9tQDE5LjEuMV9yZWFjdEAxOS4xLjFfX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvcmVhY3QtdHdlZXQvZGlzdC90d2l0dGVyLXRoZW1lL3ZlcmlmaWVkLWJhZGdlLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LXR3ZWV0QDMuMi4yX3JlYWN0LWRvbUAxOS4xLjFfcmVhY3RAMTkuMS4xX19yZWFjdEAxOS4xLjFcXG5vZGVfbW9kdWxlc1xccmVhY3QtdHdlZXRcXGRpc3RcXHR3aXR0ZXItdGhlbWVcXHZlcmlmaWVkLWJhZGdlLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwidmVyaWZpZWRPbGRcIjogXCJ2ZXJpZmllZC1iYWRnZV92ZXJpZmllZE9sZF9fXzU1SlhcIixcblx0XCJ2ZXJpZmllZEJsdWVcIjogXCJ2ZXJpZmllZC1iYWRnZV92ZXJpZmllZEJsdWVfX2NzRG1HXCIsXG5cdFwidmVyaWZpZWRHb3Zlcm5tZW50XCI6IFwidmVyaWZpZWQtYmFkZ2VfdmVyaWZpZWRHb3Zlcm5tZW50X19QX1A5QVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI0MGI0YmVhYmMwYWNcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/utils.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/utils.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enrichTweet: () => (/* binding */ enrichTweet),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   getMediaUrl: () => (/* binding */ getMediaUrl),\n/* harmony export */   getMp4Video: () => (/* binding */ getMp4Video),\n/* harmony export */   getMp4Videos: () => (/* binding */ getMp4Videos)\n/* harmony export */ });\nconst getTweetUrl = (tweet)=>`https://x.com/${tweet.user.screen_name}/status/${tweet.id_str}`;\nconst getUserUrl = (usernameOrTweet)=>`https://x.com/${typeof usernameOrTweet === 'string' ? usernameOrTweet : usernameOrTweet.user.screen_name}`;\nconst getLikeUrl = (tweet)=>`https://x.com/intent/like?tweet_id=${tweet.id_str}`;\nconst getReplyUrl = (tweet)=>`https://x.com/intent/tweet?in_reply_to=${tweet.id_str}`;\nconst getFollowUrl = (tweet)=>`https://x.com/intent/follow?screen_name=${tweet.user.screen_name}`;\nconst getHashtagUrl = (hashtag)=>`https://x.com/hashtag/${hashtag.text}`;\nconst getSymbolUrl = (symbol)=>`https://x.com/search?q=%24${symbol.text}`;\nconst getInReplyToUrl = (tweet)=>`https://x.com/${tweet.in_reply_to_screen_name}/status/${tweet.in_reply_to_status_id_str}`;\nconst getMediaUrl = (media, size)=>{\n    const url = new URL(media.media_url_https);\n    const extension = url.pathname.split('.').pop();\n    if (!extension) return media.media_url_https;\n    url.pathname = url.pathname.replace(`.${extension}`, '');\n    url.searchParams.set('format', extension);\n    url.searchParams.set('name', size);\n    return url.toString();\n};\nconst getMp4Videos = (media)=>{\n    const { variants } = media.video_info;\n    const sortedMp4Videos = variants.filter((vid)=>vid.content_type === 'video/mp4').sort((a, b)=>{\n        var _b_bitrate, _a_bitrate;\n        return ((_b_bitrate = b.bitrate) != null ? _b_bitrate : 0) - ((_a_bitrate = a.bitrate) != null ? _a_bitrate : 0);\n    });\n    return sortedMp4Videos;\n};\nconst getMp4Video = (media)=>{\n    const mp4Videos = getMp4Videos(media);\n    // Skip the highest quality video and use the next quality\n    return mp4Videos.length > 1 ? mp4Videos[1] : mp4Videos[0];\n};\nconst formatNumber = (n)=>{\n    if (n > 999999) return `${(n / 1000000).toFixed(1)}M`;\n    if (n > 999) return `${(n / 1000).toFixed(1)}K`;\n    return n.toString();\n};\nfunction getEntities(tweet) {\n    const textMap = Array.from(tweet.text);\n    const result = [\n        {\n            indices: tweet.display_text_range,\n            type: 'text'\n        }\n    ];\n    addEntities(result, 'hashtag', tweet.entities.hashtags);\n    addEntities(result, 'mention', tweet.entities.user_mentions);\n    addEntities(result, 'url', tweet.entities.urls);\n    addEntities(result, 'symbol', tweet.entities.symbols);\n    if (tweet.entities.media) {\n        addEntities(result, 'media', tweet.entities.media);\n    }\n    fixRange(tweet, result);\n    return result.map((entity)=>{\n        const text = textMap.slice(entity.indices[0], entity.indices[1]).join('');\n        switch(entity.type){\n            case 'hashtag':\n                return Object.assign(entity, {\n                    href: getHashtagUrl(entity),\n                    text\n                });\n            case 'mention':\n                return Object.assign(entity, {\n                    href: getUserUrl(entity.screen_name),\n                    text\n                });\n            case 'url':\n            case 'media':\n                return Object.assign(entity, {\n                    href: entity.expanded_url,\n                    text: entity.display_url\n                });\n            case 'symbol':\n                return Object.assign(entity, {\n                    href: getSymbolUrl(entity),\n                    text\n                });\n            default:\n                return Object.assign(entity, {\n                    text\n                });\n        }\n    });\n}\nfunction addEntities(result, type, entities) {\n    for (const entity of entities){\n        for (const [i, item] of result.entries()){\n            if (item.indices[0] > entity.indices[0] || item.indices[1] < entity.indices[1]) {\n                continue;\n            }\n            const items = [\n                {\n                    ...entity,\n                    type\n                }\n            ];\n            if (item.indices[0] < entity.indices[0]) {\n                items.unshift({\n                    indices: [\n                        item.indices[0],\n                        entity.indices[0]\n                    ],\n                    type: 'text'\n                });\n            }\n            if (item.indices[1] > entity.indices[1]) {\n                items.push({\n                    indices: [\n                        entity.indices[1],\n                        item.indices[1]\n                    ],\n                    type: 'text'\n                });\n            }\n            result.splice(i, 1, ...items);\n            break; // Break out of the loop to avoid iterating over the new items\n        }\n    }\n}\n/**\n * Update display_text_range to work w/ Array.from\n * Array.from is unicode aware, unlike string.slice()\n */ function fixRange(tweet, entities) {\n    if (tweet.entities.media && tweet.entities.media[0].indices[0] < tweet.display_text_range[1]) {\n        tweet.display_text_range[1] = tweet.entities.media[0].indices[0];\n    }\n    const lastEntity = entities.at(-1);\n    if (lastEntity && lastEntity.indices[1] > tweet.display_text_range[1]) {\n        lastEntity.indices[1] = tweet.display_text_range[1];\n    }\n}\n/**\n * Enriches a tweet with additional data used to more easily use the tweet in a UI.\n */ const enrichTweet = (tweet)=>({\n        ...tweet,\n        url: getTweetUrl(tweet),\n        user: {\n            ...tweet.user,\n            url: getUserUrl(tweet),\n            follow_url: getFollowUrl(tweet)\n        },\n        like_url: getLikeUrl(tweet),\n        reply_url: getReplyUrl(tweet),\n        in_reply_to_url: tweet.in_reply_to_screen_name ? getInReplyToUrl(tweet) : undefined,\n        entities: getEntities(tweet),\n        quoted_tweet: tweet.quoted_tweet ? {\n            ...tweet.quoted_tweet,\n            url: getTweetUrl(tweet.quoted_tweet),\n            entities: getEntities(tweet.quoted_tweet)\n        } : undefined\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tweet@3.2.2_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/react-tweet/dist/utils.js\n");

/***/ })

};
;