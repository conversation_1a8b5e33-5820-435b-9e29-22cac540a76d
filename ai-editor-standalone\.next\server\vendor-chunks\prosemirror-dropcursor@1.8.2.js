"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-dropcursor@1.8.2";
exports.ids = ["vendor-chunks/prosemirror-dropcursor@1.8.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/prosemirror-dropcursor@1.8.2/node_modules/prosemirror-dropcursor/dist/index.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/prosemirror-dropcursor@1.8.2/node_modules/prosemirror-dropcursor/dist/index.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dropCursor: () => (/* binding */ dropCursor)\n/* harmony export */ });\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/.pnpm/prosemirror-state@1.4.3/node_modules/prosemirror-state/dist/index.js\");\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/.pnpm/prosemirror-transform@1.10.4/node_modules/prosemirror-transform/dist/index.js\");\n\n\n\n/**\nCreate a plugin that, when added to a ProseMirror instance,\ncauses a decoration to show up at the drop position when something\nis dragged over the editor.\n\nNodes may add a `disableDropCursor` property to their spec to\ncontrol the showing of a drop cursor inside them. This may be a\nboolean or a function, which will be called with a view and a\nposition, and should return a boolean.\n*/\nfunction dropCursor(options = {}) {\n    return new prosemirror_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        view(editorView) { return new DropCursorView(editorView, options); }\n    });\n}\nclass DropCursorView {\n    constructor(editorView, options) {\n        var _a;\n        this.editorView = editorView;\n        this.cursorPos = null;\n        this.element = null;\n        this.timeout = -1;\n        this.width = (_a = options.width) !== null && _a !== void 0 ? _a : 1;\n        this.color = options.color === false ? undefined : (options.color || \"black\");\n        this.class = options.class;\n        this.handlers = [\"dragover\", \"dragend\", \"drop\", \"dragleave\"].map(name => {\n            let handler = (e) => { this[name](e); };\n            editorView.dom.addEventListener(name, handler);\n            return { name, handler };\n        });\n    }\n    destroy() {\n        this.handlers.forEach(({ name, handler }) => this.editorView.dom.removeEventListener(name, handler));\n    }\n    update(editorView, prevState) {\n        if (this.cursorPos != null && prevState.doc != editorView.state.doc) {\n            if (this.cursorPos > editorView.state.doc.content.size)\n                this.setCursor(null);\n            else\n                this.updateOverlay();\n        }\n    }\n    setCursor(pos) {\n        if (pos == this.cursorPos)\n            return;\n        this.cursorPos = pos;\n        if (pos == null) {\n            this.element.parentNode.removeChild(this.element);\n            this.element = null;\n        }\n        else {\n            this.updateOverlay();\n        }\n    }\n    updateOverlay() {\n        let $pos = this.editorView.state.doc.resolve(this.cursorPos);\n        let isBlock = !$pos.parent.inlineContent, rect;\n        let editorDOM = this.editorView.dom, editorRect = editorDOM.getBoundingClientRect();\n        let scaleX = editorRect.width / editorDOM.offsetWidth, scaleY = editorRect.height / editorDOM.offsetHeight;\n        if (isBlock) {\n            let before = $pos.nodeBefore, after = $pos.nodeAfter;\n            if (before || after) {\n                let node = this.editorView.nodeDOM(this.cursorPos - (before ? before.nodeSize : 0));\n                if (node) {\n                    let nodeRect = node.getBoundingClientRect();\n                    let top = before ? nodeRect.bottom : nodeRect.top;\n                    if (before && after)\n                        top = (top + this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top) / 2;\n                    let halfWidth = (this.width / 2) * scaleY;\n                    rect = { left: nodeRect.left, right: nodeRect.right, top: top - halfWidth, bottom: top + halfWidth };\n                }\n            }\n        }\n        if (!rect) {\n            let coords = this.editorView.coordsAtPos(this.cursorPos);\n            let halfWidth = (this.width / 2) * scaleX;\n            rect = { left: coords.left - halfWidth, right: coords.left + halfWidth, top: coords.top, bottom: coords.bottom };\n        }\n        let parent = this.editorView.dom.offsetParent;\n        if (!this.element) {\n            this.element = parent.appendChild(document.createElement(\"div\"));\n            if (this.class)\n                this.element.className = this.class;\n            this.element.style.cssText = \"position: absolute; z-index: 50; pointer-events: none;\";\n            if (this.color) {\n                this.element.style.backgroundColor = this.color;\n            }\n        }\n        this.element.classList.toggle(\"prosemirror-dropcursor-block\", isBlock);\n        this.element.classList.toggle(\"prosemirror-dropcursor-inline\", !isBlock);\n        let parentLeft, parentTop;\n        if (!parent || parent == document.body && getComputedStyle(parent).position == \"static\") {\n            parentLeft = -pageXOffset;\n            parentTop = -pageYOffset;\n        }\n        else {\n            let rect = parent.getBoundingClientRect();\n            let parentScaleX = rect.width / parent.offsetWidth, parentScaleY = rect.height / parent.offsetHeight;\n            parentLeft = rect.left - parent.scrollLeft * parentScaleX;\n            parentTop = rect.top - parent.scrollTop * parentScaleY;\n        }\n        this.element.style.left = (rect.left - parentLeft) / scaleX + \"px\";\n        this.element.style.top = (rect.top - parentTop) / scaleY + \"px\";\n        this.element.style.width = (rect.right - rect.left) / scaleX + \"px\";\n        this.element.style.height = (rect.bottom - rect.top) / scaleY + \"px\";\n    }\n    scheduleRemoval(timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(() => this.setCursor(null), timeout);\n    }\n    dragover(event) {\n        if (!this.editorView.editable)\n            return;\n        let pos = this.editorView.posAtCoords({ left: event.clientX, top: event.clientY });\n        let node = pos && pos.inside >= 0 && this.editorView.state.doc.nodeAt(pos.inside);\n        let disableDropCursor = node && node.type.spec.disableDropCursor;\n        let disabled = typeof disableDropCursor == \"function\"\n            ? disableDropCursor(this.editorView, pos, event)\n            : disableDropCursor;\n        if (pos && !disabled) {\n            let target = pos.pos;\n            if (this.editorView.dragging && this.editorView.dragging.slice) {\n                let point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.dropPoint)(this.editorView.state.doc, target, this.editorView.dragging.slice);\n                if (point != null)\n                    target = point;\n            }\n            this.setCursor(target);\n            this.scheduleRemoval(5000);\n        }\n    }\n    dragend() {\n        this.scheduleRemoval(20);\n    }\n    drop() {\n        this.scheduleRemoval(20);\n    }\n    dragleave(event) {\n        if (!this.editorView.dom.contains(event.relatedTarget))\n            this.setCursor(null);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/prosemirror-dropcursor@1.8.2/node_modules/prosemirror-dropcursor/dist/index.js\n");

/***/ })

};
;