"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-code-block/dist/index.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-code-block/dist/index.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CodeBlock: () => (/* binding */ CodeBlock),\n/* harmony export */   backtickInputRegex: () => (/* binding */ backtickInputRegex),\n/* harmony export */   \"default\": () => (/* binding */ CodeBlock),\n/* harmony export */   tildeInputRegex: () => (/* binding */ tildeInputRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n\n\n\n/**\n * Matches a code block with backticks.\n */\nconst backtickInputRegex = /^```([a-z]+)?[\\s\\n]$/;\n/**\n * Matches a code block with tildes.\n */\nconst tildeInputRegex = /^~~~([a-z]+)?[\\s\\n]$/;\n/**\n * This extension allows you to create code blocks.\n * @see https://tiptap.dev/api/nodes/code-block\n */\nconst CodeBlock = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Node.create({\n    name: 'codeBlock',\n    addOptions() {\n        return {\n            languageClassPrefix: 'language-',\n            exitOnTripleEnter: true,\n            exitOnArrowDown: true,\n            defaultLanguage: null,\n            HTMLAttributes: {},\n        };\n    },\n    content: 'text*',\n    marks: '',\n    group: 'block',\n    code: true,\n    defining: true,\n    addAttributes() {\n        return {\n            language: {\n                default: this.options.defaultLanguage,\n                parseHTML: element => {\n                    var _a;\n                    const { languageClassPrefix } = this.options;\n                    const classNames = [...(((_a = element.firstElementChild) === null || _a === void 0 ? void 0 : _a.classList) || [])];\n                    const languages = classNames\n                        .filter(className => className.startsWith(languageClassPrefix))\n                        .map(className => className.replace(languageClassPrefix, ''));\n                    const language = languages[0];\n                    if (!language) {\n                        return null;\n                    }\n                    return language;\n                },\n                rendered: false,\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'pre',\n                preserveWhitespace: 'full',\n            },\n        ];\n    },\n    renderHTML({ node, HTMLAttributes }) {\n        return [\n            'pre',\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes),\n            [\n                'code',\n                {\n                    class: node.attrs.language\n                        ? this.options.languageClassPrefix + node.attrs.language\n                        : null,\n                },\n                0,\n            ],\n        ];\n    },\n    addCommands() {\n        return {\n            setCodeBlock: attributes => ({ commands }) => {\n                return commands.setNode(this.name, attributes);\n            },\n            toggleCodeBlock: attributes => ({ commands }) => {\n                return commands.toggleNode(this.name, 'paragraph', attributes);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-Alt-c': () => this.editor.commands.toggleCodeBlock(),\n            // remove code block when at start of document or code block is empty\n            Backspace: () => {\n                const { empty, $anchor } = this.editor.state.selection;\n                const isAtStart = $anchor.pos === 1;\n                if (!empty || $anchor.parent.type.name !== this.name) {\n                    return false;\n                }\n                if (isAtStart || !$anchor.parent.textContent.length) {\n                    return this.editor.commands.clearNodes();\n                }\n                return false;\n            },\n            // exit node on triple enter\n            Enter: ({ editor }) => {\n                if (!this.options.exitOnTripleEnter) {\n                    return false;\n                }\n                const { state } = editor;\n                const { selection } = state;\n                const { $from, empty } = selection;\n                if (!empty || $from.parent.type !== this.type) {\n                    return false;\n                }\n                const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2;\n                const endsWithDoubleNewline = $from.parent.textContent.endsWith('\\n\\n');\n                if (!isAtEnd || !endsWithDoubleNewline) {\n                    return false;\n                }\n                return editor\n                    .chain()\n                    .command(({ tr }) => {\n                    tr.delete($from.pos - 2, $from.pos);\n                    return true;\n                })\n                    .exitCode()\n                    .run();\n            },\n            // exit node on arrow down\n            ArrowDown: ({ editor }) => {\n                if (!this.options.exitOnArrowDown) {\n                    return false;\n                }\n                const { state } = editor;\n                const { selection, doc } = state;\n                const { $from, empty } = selection;\n                if (!empty || $from.parent.type !== this.type) {\n                    return false;\n                }\n                const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2;\n                if (!isAtEnd) {\n                    return false;\n                }\n                const after = $from.after();\n                if (after === undefined) {\n                    return false;\n                }\n                const nodeAfter = doc.nodeAt(after);\n                if (nodeAfter) {\n                    return editor.commands.command(({ tr }) => {\n                        tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Selection.near(doc.resolve(after)));\n                        return true;\n                    });\n                }\n                return editor.commands.exitCode();\n            },\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.textblockTypeInputRule)({\n                find: backtickInputRegex,\n                type: this.type,\n                getAttributes: match => ({\n                    language: match[1],\n                }),\n            }),\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.textblockTypeInputRule)({\n                find: tildeInputRegex,\n                type: this.type,\n                getAttributes: match => ({\n                    language: match[1],\n                }),\n            }),\n        ];\n    },\n    addProseMirrorPlugins() {\n        return [\n            // this plugin creates a code block for pasted content from VS Code\n            // we can also detect the copied code language\n            new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n                key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('codeBlockVSCodeHandler'),\n                props: {\n                    handlePaste: (view, event) => {\n                        if (!event.clipboardData) {\n                            return false;\n                        }\n                        // don’t create a new code block within code blocks\n                        if (this.editor.isActive(this.type.name)) {\n                            return false;\n                        }\n                        const text = event.clipboardData.getData('text/plain');\n                        const vscode = event.clipboardData.getData('vscode-editor-data');\n                        const vscodeData = vscode ? JSON.parse(vscode) : undefined;\n                        const language = vscodeData === null || vscodeData === void 0 ? void 0 : vscodeData.mode;\n                        if (!text || !language) {\n                            return false;\n                        }\n                        const { tr, schema } = view.state;\n                        // prepare a text node\n                        // strip carriage return chars from text pasted as code\n                        // see: https://github.com/ProseMirror/prosemirror-view/commit/a50a6bcceb4ce52ac8fcc6162488d8875613aacd\n                        const textNode = schema.text(text.replace(/\\r\\n?/g, '\\n'));\n                        // create a code block with the text node\n                        // replace selection with the code block\n                        tr.replaceSelectionWith(this.type.create({ language }, textNode));\n                        if (tr.selection.$from.parent.type !== this.type) {\n                            // put cursor inside the newly created code block\n                            tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.near(tr.doc.resolve(Math.max(0, tr.selection.from - 2))));\n                        }\n                        // store meta information\n                        // this is useful for other plugins that depends on the paste event\n                        // like the paste rule plugin\n                        tr.setMeta('paste', true);\n                        view.dispatch(tr);\n                        return true;\n                    },\n                },\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-code-block/dist/index.js\n");

/***/ })

};
;