"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@egjs+agent@2.4.4";
exports.ids = ["vendor-chunks/@egjs+agent@2.4.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@egjs+agent@2.4.4/node_modules/@egjs/agent/dist/agent.esm.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/@egjs+agent@2.4.4/node_modules/@egjs/agent/dist/agent.esm.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAccurateAgent: () => (/* binding */ getAccurateAgent),\n/* harmony export */   getLegacyAgent: () => (/* binding */ getLegacyAgent)\n/* harmony export */ });\n/*\nCopyright (c) 2015 NAVER Corp.\nname: @egjs/agent\nlicense: MIT\nauthor: NAVER Corp.\nrepository: git+https://github.com/naver/egjs-agent.git\nversion: 2.4.4\n*/\nfunction some(arr, callback) {\n  var length = arr.length;\n\n  for (var i = 0; i < length; ++i) {\n    if (callback(arr[i], i)) {\n      return true;\n    }\n  }\n\n  return false;\n}\nfunction find(arr, callback) {\n  var length = arr.length;\n\n  for (var i = 0; i < length; ++i) {\n    if (callback(arr[i], i)) {\n      return arr[i];\n    }\n  }\n\n  return null;\n}\nfunction getUserAgentString(agent) {\n  var userAgent = agent;\n\n  if (typeof userAgent === \"undefined\") {\n    if (typeof navigator === \"undefined\" || !navigator) {\n      return \"\";\n    }\n\n    userAgent = navigator.userAgent || \"\";\n  }\n\n  return userAgent.toLowerCase();\n}\nfunction execRegExp(pattern, text) {\n  try {\n    return new RegExp(pattern, \"g\").exec(text);\n  } catch (e) {\n    return null;\n  }\n}\nfunction hasUserAgentData() {\n  if (typeof navigator === \"undefined\" || !navigator || !navigator.userAgentData) {\n    return false;\n  }\n\n  var userAgentData = navigator.userAgentData;\n  var brands = userAgentData.brands || userAgentData.uaList;\n  return !!(brands && brands.length);\n}\nfunction findVersion(versionTest, userAgent) {\n  var result = execRegExp(\"(\" + versionTest + \")((?:\\\\/|\\\\s|:)([0-9|\\\\.|_]+))\", userAgent);\n  return result ? result[3] : \"\";\n}\nfunction convertVersion(text) {\n  return text.replace(/_/g, \".\");\n}\nfunction findPreset(presets, userAgent) {\n  var userPreset = null;\n  var version = \"-1\";\n  some(presets, function (preset) {\n    var result = execRegExp(\"(\" + preset.test + \")((?:\\\\/|\\\\s|:)([0-9|\\\\.|_]+))?\", userAgent);\n\n    if (!result || preset.brand) {\n      return false;\n    }\n\n    userPreset = preset;\n    version = result[3] || \"-1\";\n\n    if (preset.versionAlias) {\n      version = preset.versionAlias;\n    } else if (preset.versionTest) {\n      version = findVersion(preset.versionTest.toLowerCase(), userAgent) || version;\n    }\n\n    version = convertVersion(version);\n    return true;\n  });\n  return {\n    preset: userPreset,\n    version: version\n  };\n}\nfunction findPresetBrand(presets, brands) {\n  var brandInfo = {\n    brand: \"\",\n    version: \"-1\"\n  };\n  some(presets, function (preset) {\n    var result = findBrand(brands, preset);\n\n    if (!result) {\n      return false;\n    }\n\n    brandInfo.brand = preset.id;\n    brandInfo.version = preset.versionAlias || result.version;\n    return brandInfo.version !== \"-1\";\n  });\n  return brandInfo;\n}\nfunction findBrand(brands, preset) {\n  return find(brands, function (_a) {\n    var brand = _a.brand;\n    return execRegExp(\"\" + preset.test, brand.toLowerCase());\n  });\n}\n\nvar BROWSER_PRESETS = [{\n  test: \"phantomjs\",\n  id: \"phantomjs\"\n}, {\n  test: \"whale\",\n  id: \"whale\"\n}, {\n  test: \"edgios|edge|edg\",\n  id: \"edge\"\n}, {\n  test: \"msie|trident|windows phone\",\n  id: \"ie\",\n  versionTest: \"iemobile|msie|rv\"\n}, {\n  test: \"miuibrowser\",\n  id: \"miui browser\"\n}, {\n  test: \"samsungbrowser\",\n  id: \"samsung internet\"\n}, {\n  test: \"samsung\",\n  id: \"samsung internet\",\n  versionTest: \"version\"\n}, {\n  test: \"chrome|crios\",\n  id: \"chrome\"\n}, {\n  test: \"firefox|fxios\",\n  id: \"firefox\"\n}, {\n  test: \"android\",\n  id: \"android browser\",\n  versionTest: \"version\"\n}, {\n  test: \"safari|iphone|ipad|ipod\",\n  id: \"safari\",\n  versionTest: \"version\"\n}]; // chromium's engine(blink) is based on applewebkit 537.36.\n\nvar CHROMIUM_PRESETS = [{\n  test: \"(?=.*applewebkit/(53[0-7]|5[0-2]|[0-4]))(?=.*\\\\schrome)\",\n  id: \"chrome\",\n  versionTest: \"chrome\"\n}, {\n  test: \"chromium\",\n  id: \"chrome\"\n}, {\n  test: \"whale\",\n  id: \"chrome\",\n  versionAlias: \"-1\",\n  brand: true\n}];\nvar WEBKIT_PRESETS = [{\n  test: \"applewebkit\",\n  id: \"webkit\",\n  versionTest: \"applewebkit|safari\"\n}];\nvar WEBVIEW_PRESETS = [{\n  test: \"(?=(iphone|ipad))(?!(.*version))\",\n  id: \"webview\"\n}, {\n  test: \"(?=(android|iphone|ipad))(?=.*(naver|daum|; wv))\",\n  id: \"webview\"\n}, {\n  // test webview\n  test: \"webview\",\n  id: \"webview\"\n}];\nvar OS_PRESETS = [{\n  test: \"windows phone\",\n  id: \"windows phone\"\n}, {\n  test: \"windows 2000\",\n  id: \"window\",\n  versionAlias: \"5.0\"\n}, {\n  test: \"windows nt\",\n  id: \"window\"\n}, {\n  test: \"win32|windows\",\n  id: \"window\"\n}, {\n  test: \"iphone|ipad|ipod\",\n  id: \"ios\",\n  versionTest: \"iphone os|cpu os\"\n}, {\n  test: \"macos|macintel|mac os x\",\n  id: \"mac\"\n}, {\n  test: \"android|linux armv81\",\n  id: \"android\"\n}, {\n  test: \"tizen\",\n  id: \"tizen\"\n}, {\n  test: \"webos|web0s\",\n  id: \"webos\"\n}];\n\nfunction isWebView(userAgent) {\n  return !!findPreset(WEBVIEW_PRESETS, userAgent).preset;\n}\nfunction getLegacyAgent(userAgent) {\n  var nextAgent = getUserAgentString(userAgent);\n  var isMobile = !!/mobi/g.exec(nextAgent);\n  var browser = {\n    name: \"unknown\",\n    version: \"-1\",\n    majorVersion: -1,\n    webview: isWebView(nextAgent),\n    chromium: false,\n    chromiumVersion: \"-1\",\n    webkit: false,\n    webkitVersion: \"-1\"\n  };\n  var os = {\n    name: \"unknown\",\n    version: \"-1\",\n    majorVersion: -1\n  };\n\n  var _a = findPreset(BROWSER_PRESETS, nextAgent),\n      browserPreset = _a.preset,\n      browserVersion = _a.version;\n\n  var _b = findPreset(OS_PRESETS, nextAgent),\n      osPreset = _b.preset,\n      osVersion = _b.version;\n\n  var chromiumPreset = findPreset(CHROMIUM_PRESETS, nextAgent);\n  browser.chromium = !!chromiumPreset.preset;\n  browser.chromiumVersion = chromiumPreset.version;\n\n  if (!browser.chromium) {\n    var webkitPreset = findPreset(WEBKIT_PRESETS, nextAgent);\n    browser.webkit = !!webkitPreset.preset;\n    browser.webkitVersion = webkitPreset.version;\n  }\n\n  if (osPreset) {\n    os.name = osPreset.id;\n    os.version = osVersion;\n    os.majorVersion = parseInt(osVersion, 10);\n  }\n\n  if (browserPreset) {\n    browser.name = browserPreset.id;\n    browser.version = browserVersion; // Early whale bugs\n\n    if (browser.webview && os.name === \"ios\" && browser.name !== \"safari\") {\n      browser.webview = false;\n    }\n  }\n\n  browser.majorVersion = parseInt(browser.version, 10);\n  return {\n    browser: browser,\n    os: os,\n    isMobile: isMobile,\n    isHints: false\n  };\n}\n\nfunction getClientHintsAgent(osData) {\n  var userAgentData = navigator.userAgentData;\n  var brands = (userAgentData.uaList || userAgentData.brands).slice();\n  var fullVersionList = osData && osData.fullVersionList;\n  var isMobile = userAgentData.mobile || false;\n  var firstBrand = brands[0];\n  var platform = (osData && osData.platform || userAgentData.platform || navigator.platform).toLowerCase();\n  var browser = {\n    name: firstBrand.brand,\n    version: firstBrand.version,\n    majorVersion: -1,\n    webkit: false,\n    webkitVersion: \"-1\",\n    chromium: false,\n    chromiumVersion: \"-1\",\n    webview: !!findPresetBrand(WEBVIEW_PRESETS, brands).brand || isWebView(getUserAgentString())\n  };\n  var os = {\n    name: \"unknown\",\n    version: \"-1\",\n    majorVersion: -1\n  };\n  browser.webkit = !browser.chromium && some(WEBKIT_PRESETS, function (preset) {\n    return findBrand(brands, preset);\n  });\n  var chromiumBrand = findPresetBrand(CHROMIUM_PRESETS, brands);\n  browser.chromium = !!chromiumBrand.brand;\n  browser.chromiumVersion = chromiumBrand.version || \"-1\";\n\n  if (!browser.chromium) {\n    var webkitBrand = findPresetBrand(WEBKIT_PRESETS, brands);\n    browser.webkit = !!webkitBrand.brand;\n    browser.webkitVersion = webkitBrand.version || \"-1\";\n  }\n\n  var platfomResult = find(OS_PRESETS, function (preset) {\n    return new RegExp(\"\" + preset.test, \"g\").exec(platform);\n  });\n  os.name = platfomResult ? platfomResult.id : \"\";\n\n  if (osData) {\n    os.version = osData.platformVersion || \"-1\";\n  }\n\n  if (fullVersionList && fullVersionList.length) {\n    var browserBrandByFullVersionList = findPresetBrand(BROWSER_PRESETS, fullVersionList);\n    browser.name = browserBrandByFullVersionList.brand || browser.name;\n    browser.version = browserBrandByFullVersionList.version || browser.version;\n  } else {\n    var browserBrand = findPresetBrand(BROWSER_PRESETS, brands);\n    browser.name = browserBrand.brand || browser.name;\n    browser.version = browserBrand.brand && osData ? osData.uaFullVersion : browserBrand.version;\n  }\n\n  if (browser.webkit) {\n    os.name = isMobile ? \"ios\" : \"mac\";\n  }\n\n  if (os.name === \"ios\" && browser.webview) {\n    browser.version = \"-1\";\n  }\n\n  os.version = convertVersion(os.version);\n  browser.version = convertVersion(browser.version);\n  os.majorVersion = parseInt(os.version, 10);\n  browser.majorVersion = parseInt(browser.version, 10);\n  return {\n    browser: browser,\n    os: os,\n    isMobile: isMobile,\n    isHints: true\n  };\n}\n\n/**\n * @namespace eg.agent\n */\n\n/**\n* Extracts accuate browser and operating system information from the user agent string or client hints.\n* @ko 유저 에이전트 문자열 또는 client hints에서 정확한 브라우저와 운영체제 정보를 추출한다.\n* @function eg.agent#getAccurateAgent\n* @param - Callback function to get the accuate agent <ko>정확한 에이전트를 가져오기 위한 callback 함수</ko>\n* @return - get the accuate agent promise. If Promise are not supported, null is returned. <ko> 정확한 에이전트 promise를 가져온다. Promise를 지원 하지 않는 경우, null을 반환한다. </ko>\n* @example\nimport { getAccurateAgent } from \"@egjs/agent\";\n// eg.agent.getAccurateAgent()\ngetAccurateAgent().then(agent => {\n   const { os, browser, isMobile } = agent;\n});\ngetAccurateAgent(agent => {\n    const { os, browser, isMobile } = agent;\n});\n*/\n\nfunction getAccurateAgent(callback) {\n  if (hasUserAgentData()) {\n    return navigator.userAgentData.getHighEntropyValues([\"architecture\", \"model\", \"platform\", \"platformVersion\", \"uaFullVersion\", \"fullVersionList\"]).then(function (info) {\n      var agentInfo = getClientHintsAgent(info);\n      callback && callback(agentInfo);\n      return agentInfo;\n    });\n  }\n\n  callback && callback(agent());\n\n  if (typeof Promise === \"undefined\" || !Promise) {\n    return null;\n  }\n\n  return Promise.resolve(agent());\n}\n/**\n * Extracts browser and operating system information from the user agent string.\n * @ko 유저 에이전트 문자열에서 브라우저와 운영체제 정보를 추출한다.\n * @function eg.agent#agent\n * @param - user agent string to parse <ko>파싱할 유저에이전트 문자열</ko>\n * @return - agent Info <ko> 에이전트 정보 </ko>\n * @example\nimport agent from \"@egjs/agent\";\n// eg.agent();\nconst { os, browser, isMobile } = agent();\n */\n\nfunction agent(userAgent) {\n  if (typeof userAgent === \"undefined\" && hasUserAgentData()) {\n    return getClientHintsAgent();\n  } else {\n    return getLegacyAgent(userAgent);\n  }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (agent);\n\n//# sourceMappingURL=agent.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGVnanMrYWdlbnRAMi40LjQvbm9kZV9tb2R1bGVzL0BlZ2pzL2FnZW50L2Rpc3QvYWdlbnQuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixZQUFZO0FBQzlCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixZQUFZO0FBQzlCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUMsR0FBRzs7QUFFSjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxxREFBcUQ7QUFDckQ7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxzQ0FBc0M7O0FBRXRDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLG1CQUFtQjtBQUM1QjtBQUNBO0FBQ0EsV0FBVyx3QkFBd0I7QUFDbkMsQ0FBQztBQUNEO0FBQ0EsWUFBWSx3QkFBd0I7QUFDcEMsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsd0JBQXdCO0FBQ2hDOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7O0FBRUEsaUVBQWUsS0FBSyxFQUFDO0FBQ3VCO0FBQzVDIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFxpbnRlbGxpZ2VudF9yZXBvcnRfZ2VuZXJhdG9yXFxhaS1lZGl0b3Itc3RhbmRhbG9uZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGVnanMrYWdlbnRAMi40LjRcXG5vZGVfbW9kdWxlc1xcQGVnanNcXGFnZW50XFxkaXN0XFxhZ2VudC5lc20uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbkNvcHlyaWdodCAoYykgMjAxNSBOQVZFUiBDb3JwLlxubmFtZTogQGVnanMvYWdlbnRcbmxpY2Vuc2U6IE1JVFxuYXV0aG9yOiBOQVZFUiBDb3JwLlxucmVwb3NpdG9yeTogZ2l0K2h0dHBzOi8vZ2l0aHViLmNvbS9uYXZlci9lZ2pzLWFnZW50LmdpdFxudmVyc2lvbjogMi40LjRcbiovXG5mdW5jdGlvbiBzb21lKGFyciwgY2FsbGJhY2spIHtcbiAgdmFyIGxlbmd0aCA9IGFyci5sZW5ndGg7XG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBsZW5ndGg7ICsraSkge1xuICAgIGlmIChjYWxsYmFjayhhcnJbaV0sIGkpKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gIH1cblxuICByZXR1cm4gZmFsc2U7XG59XG5mdW5jdGlvbiBmaW5kKGFyciwgY2FsbGJhY2spIHtcbiAgdmFyIGxlbmd0aCA9IGFyci5sZW5ndGg7XG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBsZW5ndGg7ICsraSkge1xuICAgIGlmIChjYWxsYmFjayhhcnJbaV0sIGkpKSB7XG4gICAgICByZXR1cm4gYXJyW2ldO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBudWxsO1xufVxuZnVuY3Rpb24gZ2V0VXNlckFnZW50U3RyaW5nKGFnZW50KSB7XG4gIHZhciB1c2VyQWdlbnQgPSBhZ2VudDtcblxuICBpZiAodHlwZW9mIHVzZXJBZ2VudCA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgIGlmICh0eXBlb2YgbmF2aWdhdG9yID09PSBcInVuZGVmaW5lZFwiIHx8ICFuYXZpZ2F0b3IpIHtcbiAgICAgIHJldHVybiBcIlwiO1xuICAgIH1cblxuICAgIHVzZXJBZ2VudCA9IG5hdmlnYXRvci51c2VyQWdlbnQgfHwgXCJcIjtcbiAgfVxuXG4gIHJldHVybiB1c2VyQWdlbnQudG9Mb3dlckNhc2UoKTtcbn1cbmZ1bmN0aW9uIGV4ZWNSZWdFeHAocGF0dGVybiwgdGV4dCkge1xuICB0cnkge1xuICAgIHJldHVybiBuZXcgUmVnRXhwKHBhdHRlcm4sIFwiZ1wiKS5leGVjKHRleHQpO1xuICB9IGNhdGNoIChlKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn1cbmZ1bmN0aW9uIGhhc1VzZXJBZ2VudERhdGEoKSB7XG4gIGlmICh0eXBlb2YgbmF2aWdhdG9yID09PSBcInVuZGVmaW5lZFwiIHx8ICFuYXZpZ2F0b3IgfHwgIW5hdmlnYXRvci51c2VyQWdlbnREYXRhKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgdmFyIHVzZXJBZ2VudERhdGEgPSBuYXZpZ2F0b3IudXNlckFnZW50RGF0YTtcbiAgdmFyIGJyYW5kcyA9IHVzZXJBZ2VudERhdGEuYnJhbmRzIHx8IHVzZXJBZ2VudERhdGEudWFMaXN0O1xuICByZXR1cm4gISEoYnJhbmRzICYmIGJyYW5kcy5sZW5ndGgpO1xufVxuZnVuY3Rpb24gZmluZFZlcnNpb24odmVyc2lvblRlc3QsIHVzZXJBZ2VudCkge1xuICB2YXIgcmVzdWx0ID0gZXhlY1JlZ0V4cChcIihcIiArIHZlcnNpb25UZXN0ICsgXCIpKCg/OlxcXFwvfFxcXFxzfDopKFswLTl8XFxcXC58X10rKSlcIiwgdXNlckFnZW50KTtcbiAgcmV0dXJuIHJlc3VsdCA/IHJlc3VsdFszXSA6IFwiXCI7XG59XG5mdW5jdGlvbiBjb252ZXJ0VmVyc2lvbih0ZXh0KSB7XG4gIHJldHVybiB0ZXh0LnJlcGxhY2UoL18vZywgXCIuXCIpO1xufVxuZnVuY3Rpb24gZmluZFByZXNldChwcmVzZXRzLCB1c2VyQWdlbnQpIHtcbiAgdmFyIHVzZXJQcmVzZXQgPSBudWxsO1xuICB2YXIgdmVyc2lvbiA9IFwiLTFcIjtcbiAgc29tZShwcmVzZXRzLCBmdW5jdGlvbiAocHJlc2V0KSB7XG4gICAgdmFyIHJlc3VsdCA9IGV4ZWNSZWdFeHAoXCIoXCIgKyBwcmVzZXQudGVzdCArIFwiKSgoPzpcXFxcL3xcXFxcc3w6KShbMC05fFxcXFwufF9dKykpP1wiLCB1c2VyQWdlbnQpO1xuXG4gICAgaWYgKCFyZXN1bHQgfHwgcHJlc2V0LmJyYW5kKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgdXNlclByZXNldCA9IHByZXNldDtcbiAgICB2ZXJzaW9uID0gcmVzdWx0WzNdIHx8IFwiLTFcIjtcblxuICAgIGlmIChwcmVzZXQudmVyc2lvbkFsaWFzKSB7XG4gICAgICB2ZXJzaW9uID0gcHJlc2V0LnZlcnNpb25BbGlhcztcbiAgICB9IGVsc2UgaWYgKHByZXNldC52ZXJzaW9uVGVzdCkge1xuICAgICAgdmVyc2lvbiA9IGZpbmRWZXJzaW9uKHByZXNldC52ZXJzaW9uVGVzdC50b0xvd2VyQ2FzZSgpLCB1c2VyQWdlbnQpIHx8IHZlcnNpb247XG4gICAgfVxuXG4gICAgdmVyc2lvbiA9IGNvbnZlcnRWZXJzaW9uKHZlcnNpb24pO1xuICAgIHJldHVybiB0cnVlO1xuICB9KTtcbiAgcmV0dXJuIHtcbiAgICBwcmVzZXQ6IHVzZXJQcmVzZXQsXG4gICAgdmVyc2lvbjogdmVyc2lvblxuICB9O1xufVxuZnVuY3Rpb24gZmluZFByZXNldEJyYW5kKHByZXNldHMsIGJyYW5kcykge1xuICB2YXIgYnJhbmRJbmZvID0ge1xuICAgIGJyYW5kOiBcIlwiLFxuICAgIHZlcnNpb246IFwiLTFcIlxuICB9O1xuICBzb21lKHByZXNldHMsIGZ1bmN0aW9uIChwcmVzZXQpIHtcbiAgICB2YXIgcmVzdWx0ID0gZmluZEJyYW5kKGJyYW5kcywgcHJlc2V0KTtcblxuICAgIGlmICghcmVzdWx0KSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuXG4gICAgYnJhbmRJbmZvLmJyYW5kID0gcHJlc2V0LmlkO1xuICAgIGJyYW5kSW5mby52ZXJzaW9uID0gcHJlc2V0LnZlcnNpb25BbGlhcyB8fCByZXN1bHQudmVyc2lvbjtcbiAgICByZXR1cm4gYnJhbmRJbmZvLnZlcnNpb24gIT09IFwiLTFcIjtcbiAgfSk7XG4gIHJldHVybiBicmFuZEluZm87XG59XG5mdW5jdGlvbiBmaW5kQnJhbmQoYnJhbmRzLCBwcmVzZXQpIHtcbiAgcmV0dXJuIGZpbmQoYnJhbmRzLCBmdW5jdGlvbiAoX2EpIHtcbiAgICB2YXIgYnJhbmQgPSBfYS5icmFuZDtcbiAgICByZXR1cm4gZXhlY1JlZ0V4cChcIlwiICsgcHJlc2V0LnRlc3QsIGJyYW5kLnRvTG93ZXJDYXNlKCkpO1xuICB9KTtcbn1cblxudmFyIEJST1dTRVJfUFJFU0VUUyA9IFt7XG4gIHRlc3Q6IFwicGhhbnRvbWpzXCIsXG4gIGlkOiBcInBoYW50b21qc1wiXG59LCB7XG4gIHRlc3Q6IFwid2hhbGVcIixcbiAgaWQ6IFwid2hhbGVcIlxufSwge1xuICB0ZXN0OiBcImVkZ2lvc3xlZGdlfGVkZ1wiLFxuICBpZDogXCJlZGdlXCJcbn0sIHtcbiAgdGVzdDogXCJtc2llfHRyaWRlbnR8d2luZG93cyBwaG9uZVwiLFxuICBpZDogXCJpZVwiLFxuICB2ZXJzaW9uVGVzdDogXCJpZW1vYmlsZXxtc2llfHJ2XCJcbn0sIHtcbiAgdGVzdDogXCJtaXVpYnJvd3NlclwiLFxuICBpZDogXCJtaXVpIGJyb3dzZXJcIlxufSwge1xuICB0ZXN0OiBcInNhbXN1bmdicm93c2VyXCIsXG4gIGlkOiBcInNhbXN1bmcgaW50ZXJuZXRcIlxufSwge1xuICB0ZXN0OiBcInNhbXN1bmdcIixcbiAgaWQ6IFwic2Ftc3VuZyBpbnRlcm5ldFwiLFxuICB2ZXJzaW9uVGVzdDogXCJ2ZXJzaW9uXCJcbn0sIHtcbiAgdGVzdDogXCJjaHJvbWV8Y3Jpb3NcIixcbiAgaWQ6IFwiY2hyb21lXCJcbn0sIHtcbiAgdGVzdDogXCJmaXJlZm94fGZ4aW9zXCIsXG4gIGlkOiBcImZpcmVmb3hcIlxufSwge1xuICB0ZXN0OiBcImFuZHJvaWRcIixcbiAgaWQ6IFwiYW5kcm9pZCBicm93c2VyXCIsXG4gIHZlcnNpb25UZXN0OiBcInZlcnNpb25cIlxufSwge1xuICB0ZXN0OiBcInNhZmFyaXxpcGhvbmV8aXBhZHxpcG9kXCIsXG4gIGlkOiBcInNhZmFyaVwiLFxuICB2ZXJzaW9uVGVzdDogXCJ2ZXJzaW9uXCJcbn1dOyAvLyBjaHJvbWl1bSdzIGVuZ2luZShibGluaykgaXMgYmFzZWQgb24gYXBwbGV3ZWJraXQgNTM3LjM2LlxuXG52YXIgQ0hST01JVU1fUFJFU0VUUyA9IFt7XG4gIHRlc3Q6IFwiKD89LiphcHBsZXdlYmtpdC8oNTNbMC03XXw1WzAtMl18WzAtNF0pKSg/PS4qXFxcXHNjaHJvbWUpXCIsXG4gIGlkOiBcImNocm9tZVwiLFxuICB2ZXJzaW9uVGVzdDogXCJjaHJvbWVcIlxufSwge1xuICB0ZXN0OiBcImNocm9taXVtXCIsXG4gIGlkOiBcImNocm9tZVwiXG59LCB7XG4gIHRlc3Q6IFwid2hhbGVcIixcbiAgaWQ6IFwiY2hyb21lXCIsXG4gIHZlcnNpb25BbGlhczogXCItMVwiLFxuICBicmFuZDogdHJ1ZVxufV07XG52YXIgV0VCS0lUX1BSRVNFVFMgPSBbe1xuICB0ZXN0OiBcImFwcGxld2Via2l0XCIsXG4gIGlkOiBcIndlYmtpdFwiLFxuICB2ZXJzaW9uVGVzdDogXCJhcHBsZXdlYmtpdHxzYWZhcmlcIlxufV07XG52YXIgV0VCVklFV19QUkVTRVRTID0gW3tcbiAgdGVzdDogXCIoPz0oaXBob25lfGlwYWQpKSg/ISguKnZlcnNpb24pKVwiLFxuICBpZDogXCJ3ZWJ2aWV3XCJcbn0sIHtcbiAgdGVzdDogXCIoPz0oYW5kcm9pZHxpcGhvbmV8aXBhZCkpKD89LioobmF2ZXJ8ZGF1bXw7IHd2KSlcIixcbiAgaWQ6IFwid2Vidmlld1wiXG59LCB7XG4gIC8vIHRlc3Qgd2Vidmlld1xuICB0ZXN0OiBcIndlYnZpZXdcIixcbiAgaWQ6IFwid2Vidmlld1wiXG59XTtcbnZhciBPU19QUkVTRVRTID0gW3tcbiAgdGVzdDogXCJ3aW5kb3dzIHBob25lXCIsXG4gIGlkOiBcIndpbmRvd3MgcGhvbmVcIlxufSwge1xuICB0ZXN0OiBcIndpbmRvd3MgMjAwMFwiLFxuICBpZDogXCJ3aW5kb3dcIixcbiAgdmVyc2lvbkFsaWFzOiBcIjUuMFwiXG59LCB7XG4gIHRlc3Q6IFwid2luZG93cyBudFwiLFxuICBpZDogXCJ3aW5kb3dcIlxufSwge1xuICB0ZXN0OiBcIndpbjMyfHdpbmRvd3NcIixcbiAgaWQ6IFwid2luZG93XCJcbn0sIHtcbiAgdGVzdDogXCJpcGhvbmV8aXBhZHxpcG9kXCIsXG4gIGlkOiBcImlvc1wiLFxuICB2ZXJzaW9uVGVzdDogXCJpcGhvbmUgb3N8Y3B1IG9zXCJcbn0sIHtcbiAgdGVzdDogXCJtYWNvc3xtYWNpbnRlbHxtYWMgb3MgeFwiLFxuICBpZDogXCJtYWNcIlxufSwge1xuICB0ZXN0OiBcImFuZHJvaWR8bGludXggYXJtdjgxXCIsXG4gIGlkOiBcImFuZHJvaWRcIlxufSwge1xuICB0ZXN0OiBcInRpemVuXCIsXG4gIGlkOiBcInRpemVuXCJcbn0sIHtcbiAgdGVzdDogXCJ3ZWJvc3x3ZWIwc1wiLFxuICBpZDogXCJ3ZWJvc1wiXG59XTtcblxuZnVuY3Rpb24gaXNXZWJWaWV3KHVzZXJBZ2VudCkge1xuICByZXR1cm4gISFmaW5kUHJlc2V0KFdFQlZJRVdfUFJFU0VUUywgdXNlckFnZW50KS5wcmVzZXQ7XG59XG5mdW5jdGlvbiBnZXRMZWdhY3lBZ2VudCh1c2VyQWdlbnQpIHtcbiAgdmFyIG5leHRBZ2VudCA9IGdldFVzZXJBZ2VudFN0cmluZyh1c2VyQWdlbnQpO1xuICB2YXIgaXNNb2JpbGUgPSAhIS9tb2JpL2cuZXhlYyhuZXh0QWdlbnQpO1xuICB2YXIgYnJvd3NlciA9IHtcbiAgICBuYW1lOiBcInVua25vd25cIixcbiAgICB2ZXJzaW9uOiBcIi0xXCIsXG4gICAgbWFqb3JWZXJzaW9uOiAtMSxcbiAgICB3ZWJ2aWV3OiBpc1dlYlZpZXcobmV4dEFnZW50KSxcbiAgICBjaHJvbWl1bTogZmFsc2UsXG4gICAgY2hyb21pdW1WZXJzaW9uOiBcIi0xXCIsXG4gICAgd2Via2l0OiBmYWxzZSxcbiAgICB3ZWJraXRWZXJzaW9uOiBcIi0xXCJcbiAgfTtcbiAgdmFyIG9zID0ge1xuICAgIG5hbWU6IFwidW5rbm93blwiLFxuICAgIHZlcnNpb246IFwiLTFcIixcbiAgICBtYWpvclZlcnNpb246IC0xXG4gIH07XG5cbiAgdmFyIF9hID0gZmluZFByZXNldChCUk9XU0VSX1BSRVNFVFMsIG5leHRBZ2VudCksXG4gICAgICBicm93c2VyUHJlc2V0ID0gX2EucHJlc2V0LFxuICAgICAgYnJvd3NlclZlcnNpb24gPSBfYS52ZXJzaW9uO1xuXG4gIHZhciBfYiA9IGZpbmRQcmVzZXQoT1NfUFJFU0VUUywgbmV4dEFnZW50KSxcbiAgICAgIG9zUHJlc2V0ID0gX2IucHJlc2V0LFxuICAgICAgb3NWZXJzaW9uID0gX2IudmVyc2lvbjtcblxuICB2YXIgY2hyb21pdW1QcmVzZXQgPSBmaW5kUHJlc2V0KENIUk9NSVVNX1BSRVNFVFMsIG5leHRBZ2VudCk7XG4gIGJyb3dzZXIuY2hyb21pdW0gPSAhIWNocm9taXVtUHJlc2V0LnByZXNldDtcbiAgYnJvd3Nlci5jaHJvbWl1bVZlcnNpb24gPSBjaHJvbWl1bVByZXNldC52ZXJzaW9uO1xuXG4gIGlmICghYnJvd3Nlci5jaHJvbWl1bSkge1xuICAgIHZhciB3ZWJraXRQcmVzZXQgPSBmaW5kUHJlc2V0KFdFQktJVF9QUkVTRVRTLCBuZXh0QWdlbnQpO1xuICAgIGJyb3dzZXIud2Via2l0ID0gISF3ZWJraXRQcmVzZXQucHJlc2V0O1xuICAgIGJyb3dzZXIud2Via2l0VmVyc2lvbiA9IHdlYmtpdFByZXNldC52ZXJzaW9uO1xuICB9XG5cbiAgaWYgKG9zUHJlc2V0KSB7XG4gICAgb3MubmFtZSA9IG9zUHJlc2V0LmlkO1xuICAgIG9zLnZlcnNpb24gPSBvc1ZlcnNpb247XG4gICAgb3MubWFqb3JWZXJzaW9uID0gcGFyc2VJbnQob3NWZXJzaW9uLCAxMCk7XG4gIH1cblxuICBpZiAoYnJvd3NlclByZXNldCkge1xuICAgIGJyb3dzZXIubmFtZSA9IGJyb3dzZXJQcmVzZXQuaWQ7XG4gICAgYnJvd3Nlci52ZXJzaW9uID0gYnJvd3NlclZlcnNpb247IC8vIEVhcmx5IHdoYWxlIGJ1Z3NcblxuICAgIGlmIChicm93c2VyLndlYnZpZXcgJiYgb3MubmFtZSA9PT0gXCJpb3NcIiAmJiBicm93c2VyLm5hbWUgIT09IFwic2FmYXJpXCIpIHtcbiAgICAgIGJyb3dzZXIud2VidmlldyA9IGZhbHNlO1xuICAgIH1cbiAgfVxuXG4gIGJyb3dzZXIubWFqb3JWZXJzaW9uID0gcGFyc2VJbnQoYnJvd3Nlci52ZXJzaW9uLCAxMCk7XG4gIHJldHVybiB7XG4gICAgYnJvd3NlcjogYnJvd3NlcixcbiAgICBvczogb3MsXG4gICAgaXNNb2JpbGU6IGlzTW9iaWxlLFxuICAgIGlzSGludHM6IGZhbHNlXG4gIH07XG59XG5cbmZ1bmN0aW9uIGdldENsaWVudEhpbnRzQWdlbnQob3NEYXRhKSB7XG4gIHZhciB1c2VyQWdlbnREYXRhID0gbmF2aWdhdG9yLnVzZXJBZ2VudERhdGE7XG4gIHZhciBicmFuZHMgPSAodXNlckFnZW50RGF0YS51YUxpc3QgfHwgdXNlckFnZW50RGF0YS5icmFuZHMpLnNsaWNlKCk7XG4gIHZhciBmdWxsVmVyc2lvbkxpc3QgPSBvc0RhdGEgJiYgb3NEYXRhLmZ1bGxWZXJzaW9uTGlzdDtcbiAgdmFyIGlzTW9iaWxlID0gdXNlckFnZW50RGF0YS5tb2JpbGUgfHwgZmFsc2U7XG4gIHZhciBmaXJzdEJyYW5kID0gYnJhbmRzWzBdO1xuICB2YXIgcGxhdGZvcm0gPSAob3NEYXRhICYmIG9zRGF0YS5wbGF0Zm9ybSB8fCB1c2VyQWdlbnREYXRhLnBsYXRmb3JtIHx8IG5hdmlnYXRvci5wbGF0Zm9ybSkudG9Mb3dlckNhc2UoKTtcbiAgdmFyIGJyb3dzZXIgPSB7XG4gICAgbmFtZTogZmlyc3RCcmFuZC5icmFuZCxcbiAgICB2ZXJzaW9uOiBmaXJzdEJyYW5kLnZlcnNpb24sXG4gICAgbWFqb3JWZXJzaW9uOiAtMSxcbiAgICB3ZWJraXQ6IGZhbHNlLFxuICAgIHdlYmtpdFZlcnNpb246IFwiLTFcIixcbiAgICBjaHJvbWl1bTogZmFsc2UsXG4gICAgY2hyb21pdW1WZXJzaW9uOiBcIi0xXCIsXG4gICAgd2VidmlldzogISFmaW5kUHJlc2V0QnJhbmQoV0VCVklFV19QUkVTRVRTLCBicmFuZHMpLmJyYW5kIHx8IGlzV2ViVmlldyhnZXRVc2VyQWdlbnRTdHJpbmcoKSlcbiAgfTtcbiAgdmFyIG9zID0ge1xuICAgIG5hbWU6IFwidW5rbm93blwiLFxuICAgIHZlcnNpb246IFwiLTFcIixcbiAgICBtYWpvclZlcnNpb246IC0xXG4gIH07XG4gIGJyb3dzZXIud2Via2l0ID0gIWJyb3dzZXIuY2hyb21pdW0gJiYgc29tZShXRUJLSVRfUFJFU0VUUywgZnVuY3Rpb24gKHByZXNldCkge1xuICAgIHJldHVybiBmaW5kQnJhbmQoYnJhbmRzLCBwcmVzZXQpO1xuICB9KTtcbiAgdmFyIGNocm9taXVtQnJhbmQgPSBmaW5kUHJlc2V0QnJhbmQoQ0hST01JVU1fUFJFU0VUUywgYnJhbmRzKTtcbiAgYnJvd3Nlci5jaHJvbWl1bSA9ICEhY2hyb21pdW1CcmFuZC5icmFuZDtcbiAgYnJvd3Nlci5jaHJvbWl1bVZlcnNpb24gPSBjaHJvbWl1bUJyYW5kLnZlcnNpb24gfHwgXCItMVwiO1xuXG4gIGlmICghYnJvd3Nlci5jaHJvbWl1bSkge1xuICAgIHZhciB3ZWJraXRCcmFuZCA9IGZpbmRQcmVzZXRCcmFuZChXRUJLSVRfUFJFU0VUUywgYnJhbmRzKTtcbiAgICBicm93c2VyLndlYmtpdCA9ICEhd2Via2l0QnJhbmQuYnJhbmQ7XG4gICAgYnJvd3Nlci53ZWJraXRWZXJzaW9uID0gd2Via2l0QnJhbmQudmVyc2lvbiB8fCBcIi0xXCI7XG4gIH1cblxuICB2YXIgcGxhdGZvbVJlc3VsdCA9IGZpbmQoT1NfUFJFU0VUUywgZnVuY3Rpb24gKHByZXNldCkge1xuICAgIHJldHVybiBuZXcgUmVnRXhwKFwiXCIgKyBwcmVzZXQudGVzdCwgXCJnXCIpLmV4ZWMocGxhdGZvcm0pO1xuICB9KTtcbiAgb3MubmFtZSA9IHBsYXRmb21SZXN1bHQgPyBwbGF0Zm9tUmVzdWx0LmlkIDogXCJcIjtcblxuICBpZiAob3NEYXRhKSB7XG4gICAgb3MudmVyc2lvbiA9IG9zRGF0YS5wbGF0Zm9ybVZlcnNpb24gfHwgXCItMVwiO1xuICB9XG5cbiAgaWYgKGZ1bGxWZXJzaW9uTGlzdCAmJiBmdWxsVmVyc2lvbkxpc3QubGVuZ3RoKSB7XG4gICAgdmFyIGJyb3dzZXJCcmFuZEJ5RnVsbFZlcnNpb25MaXN0ID0gZmluZFByZXNldEJyYW5kKEJST1dTRVJfUFJFU0VUUywgZnVsbFZlcnNpb25MaXN0KTtcbiAgICBicm93c2VyLm5hbWUgPSBicm93c2VyQnJhbmRCeUZ1bGxWZXJzaW9uTGlzdC5icmFuZCB8fCBicm93c2VyLm5hbWU7XG4gICAgYnJvd3Nlci52ZXJzaW9uID0gYnJvd3NlckJyYW5kQnlGdWxsVmVyc2lvbkxpc3QudmVyc2lvbiB8fCBicm93c2VyLnZlcnNpb247XG4gIH0gZWxzZSB7XG4gICAgdmFyIGJyb3dzZXJCcmFuZCA9IGZpbmRQcmVzZXRCcmFuZChCUk9XU0VSX1BSRVNFVFMsIGJyYW5kcyk7XG4gICAgYnJvd3Nlci5uYW1lID0gYnJvd3NlckJyYW5kLmJyYW5kIHx8IGJyb3dzZXIubmFtZTtcbiAgICBicm93c2VyLnZlcnNpb24gPSBicm93c2VyQnJhbmQuYnJhbmQgJiYgb3NEYXRhID8gb3NEYXRhLnVhRnVsbFZlcnNpb24gOiBicm93c2VyQnJhbmQudmVyc2lvbjtcbiAgfVxuXG4gIGlmIChicm93c2VyLndlYmtpdCkge1xuICAgIG9zLm5hbWUgPSBpc01vYmlsZSA/IFwiaW9zXCIgOiBcIm1hY1wiO1xuICB9XG5cbiAgaWYgKG9zLm5hbWUgPT09IFwiaW9zXCIgJiYgYnJvd3Nlci53ZWJ2aWV3KSB7XG4gICAgYnJvd3Nlci52ZXJzaW9uID0gXCItMVwiO1xuICB9XG5cbiAgb3MudmVyc2lvbiA9IGNvbnZlcnRWZXJzaW9uKG9zLnZlcnNpb24pO1xuICBicm93c2VyLnZlcnNpb24gPSBjb252ZXJ0VmVyc2lvbihicm93c2VyLnZlcnNpb24pO1xuICBvcy5tYWpvclZlcnNpb24gPSBwYXJzZUludChvcy52ZXJzaW9uLCAxMCk7XG4gIGJyb3dzZXIubWFqb3JWZXJzaW9uID0gcGFyc2VJbnQoYnJvd3Nlci52ZXJzaW9uLCAxMCk7XG4gIHJldHVybiB7XG4gICAgYnJvd3NlcjogYnJvd3NlcixcbiAgICBvczogb3MsXG4gICAgaXNNb2JpbGU6IGlzTW9iaWxlLFxuICAgIGlzSGludHM6IHRydWVcbiAgfTtcbn1cblxuLyoqXG4gKiBAbmFtZXNwYWNlIGVnLmFnZW50XG4gKi9cblxuLyoqXG4qIEV4dHJhY3RzIGFjY3VhdGUgYnJvd3NlciBhbmQgb3BlcmF0aW5nIHN5c3RlbSBpbmZvcm1hdGlvbiBmcm9tIHRoZSB1c2VyIGFnZW50IHN0cmluZyBvciBjbGllbnQgaGludHMuXG4qIEBrbyDsnKDsoIAg7JeQ7J207KCE7Yq4IOusuOyekOyXtCDrmJDripQgY2xpZW50IGhpbnRz7JeQ7IScIOygle2Zle2VnCDruIzrnbzsmrDsoIDsmYAg7Jq07JiB7LK07KCcIOygleuztOulvCDstpTstpztlZzri6QuXG4qIEBmdW5jdGlvbiBlZy5hZ2VudCNnZXRBY2N1cmF0ZUFnZW50XG4qIEBwYXJhbSAtIENhbGxiYWNrIGZ1bmN0aW9uIHRvIGdldCB0aGUgYWNjdWF0ZSBhZ2VudCA8a28+7KCV7ZmV7ZWcIOyXkOydtOyghO2KuOulvCDqsIDsoLjsmKTquLAg7JyE7ZWcIGNhbGxiYWNrIO2VqOyImDwva28+XG4qIEByZXR1cm4gLSBnZXQgdGhlIGFjY3VhdGUgYWdlbnQgcHJvbWlzZS4gSWYgUHJvbWlzZSBhcmUgbm90IHN1cHBvcnRlZCwgbnVsbCBpcyByZXR1cm5lZC4gPGtvPiDsoJXtmZXtlZwg7JeQ7J207KCE7Yq4IHByb21pc2Xrpbwg6rCA7KC47Jio64ukLiBQcm9taXNl66W8IOyngOybkCDtlZjsp4Ag7JWK64qUIOqyveyasCwgbnVsbOydhCDrsJjtmZjtlZzri6QuIDwva28+XG4qIEBleGFtcGxlXG5pbXBvcnQgeyBnZXRBY2N1cmF0ZUFnZW50IH0gZnJvbSBcIkBlZ2pzL2FnZW50XCI7XG4vLyBlZy5hZ2VudC5nZXRBY2N1cmF0ZUFnZW50KClcbmdldEFjY3VyYXRlQWdlbnQoKS50aGVuKGFnZW50ID0+IHtcbiAgIGNvbnN0IHsgb3MsIGJyb3dzZXIsIGlzTW9iaWxlIH0gPSBhZ2VudDtcbn0pO1xuZ2V0QWNjdXJhdGVBZ2VudChhZ2VudCA9PiB7XG4gICAgY29uc3QgeyBvcywgYnJvd3NlciwgaXNNb2JpbGUgfSA9IGFnZW50O1xufSk7XG4qL1xuXG5mdW5jdGlvbiBnZXRBY2N1cmF0ZUFnZW50KGNhbGxiYWNrKSB7XG4gIGlmIChoYXNVc2VyQWdlbnREYXRhKCkpIHtcbiAgICByZXR1cm4gbmF2aWdhdG9yLnVzZXJBZ2VudERhdGEuZ2V0SGlnaEVudHJvcHlWYWx1ZXMoW1wiYXJjaGl0ZWN0dXJlXCIsIFwibW9kZWxcIiwgXCJwbGF0Zm9ybVwiLCBcInBsYXRmb3JtVmVyc2lvblwiLCBcInVhRnVsbFZlcnNpb25cIiwgXCJmdWxsVmVyc2lvbkxpc3RcIl0pLnRoZW4oZnVuY3Rpb24gKGluZm8pIHtcbiAgICAgIHZhciBhZ2VudEluZm8gPSBnZXRDbGllbnRIaW50c0FnZW50KGluZm8pO1xuICAgICAgY2FsbGJhY2sgJiYgY2FsbGJhY2soYWdlbnRJbmZvKTtcbiAgICAgIHJldHVybiBhZ2VudEluZm87XG4gICAgfSk7XG4gIH1cblxuICBjYWxsYmFjayAmJiBjYWxsYmFjayhhZ2VudCgpKTtcblxuICBpZiAodHlwZW9mIFByb21pc2UgPT09IFwidW5kZWZpbmVkXCIgfHwgIVByb21pc2UpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIHJldHVybiBQcm9taXNlLnJlc29sdmUoYWdlbnQoKSk7XG59XG4vKipcbiAqIEV4dHJhY3RzIGJyb3dzZXIgYW5kIG9wZXJhdGluZyBzeXN0ZW0gaW5mb3JtYXRpb24gZnJvbSB0aGUgdXNlciBhZ2VudCBzdHJpbmcuXG4gKiBAa28g7Jyg7KCAIOyXkOydtOyghO2KuCDrrLjsnpDsl7Tsl5DshJwg67iM65287Jqw7KCA7JmAIOyatOyYgeyytOygnCDsoJXrs7Trpbwg7LaU7Lac7ZWc64ukLlxuICogQGZ1bmN0aW9uIGVnLmFnZW50I2FnZW50XG4gKiBAcGFyYW0gLSB1c2VyIGFnZW50IHN0cmluZyB0byBwYXJzZSA8a28+7YyM7Iux7ZWgIOycoOyggOyXkOydtOyghO2KuCDrrLjsnpDsl7Q8L2tvPlxuICogQHJldHVybiAtIGFnZW50IEluZm8gPGtvPiDsl5DsnbTsoITtirgg7KCV67O0IDwva28+XG4gKiBAZXhhbXBsZVxuaW1wb3J0IGFnZW50IGZyb20gXCJAZWdqcy9hZ2VudFwiO1xuLy8gZWcuYWdlbnQoKTtcbmNvbnN0IHsgb3MsIGJyb3dzZXIsIGlzTW9iaWxlIH0gPSBhZ2VudCgpO1xuICovXG5cbmZ1bmN0aW9uIGFnZW50KHVzZXJBZ2VudCkge1xuICBpZiAodHlwZW9mIHVzZXJBZ2VudCA9PT0gXCJ1bmRlZmluZWRcIiAmJiBoYXNVc2VyQWdlbnREYXRhKCkpIHtcbiAgICByZXR1cm4gZ2V0Q2xpZW50SGludHNBZ2VudCgpO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiBnZXRMZWdhY3lBZ2VudCh1c2VyQWdlbnQpO1xuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IGFnZW50O1xuZXhwb3J0IHsgZ2V0QWNjdXJhdGVBZ2VudCwgZ2V0TGVnYWN5QWdlbnQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFnZW50LmVzbS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@egjs+agent@2.4.4/node_modules/@egjs/agent/dist/agent.esm.js\n");

/***/ })

};
;