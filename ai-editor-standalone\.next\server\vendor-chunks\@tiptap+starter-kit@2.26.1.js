"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+starter-kit@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+starter-kit@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StarterKit: () => (/* binding */ StarterKit),\n/* harmony export */   \"default\": () => (/* binding */ StarterKit)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_extension_blockquote__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/extension-blockquote */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-blockquote@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-blockquote/dist/index.js\");\n/* harmony import */ var _tiptap_extension_bold__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/extension-bold */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bold/dist/index.js\");\n/* harmony import */ var _tiptap_extension_bullet_list__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/extension-bullet-list */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-bullet-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bullet-list/dist/index.js\");\n/* harmony import */ var _tiptap_extension_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/extension-code */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-code/dist/index.js\");\n/* harmony import */ var _tiptap_extension_code_block__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tiptap/extension-code-block */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-code-block/dist/index.js\");\n/* harmony import */ var _tiptap_extension_document__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tiptap/extension-document */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-document@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-document/dist/index.js\");\n/* harmony import */ var _tiptap_extension_dropcursor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tiptap/extension-dropcursor */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-dropcursor/dist/index.js\");\n/* harmony import */ var _tiptap_extension_gapcursor__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tiptap/extension-gapcursor */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-gapcursor/dist/index.js\");\n/* harmony import */ var _tiptap_extension_hard_break__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tiptap/extension-hard-break */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-hard-break@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-hard-break/dist/index.js\");\n/* harmony import */ var _tiptap_extension_heading__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tiptap/extension-heading */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-heading/dist/index.js\");\n/* harmony import */ var _tiptap_extension_history__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tiptap/extension-history */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-history/dist/index.js\");\n/* harmony import */ var _tiptap_extension_horizontal_rule__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tiptap/extension-horizontal-rule */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-horizontal-rule/dist/index.js\");\n/* harmony import */ var _tiptap_extension_italic__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tiptap/extension-italic */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-italic/dist/index.js\");\n/* harmony import */ var _tiptap_extension_list_item__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tiptap/extension-list-item */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-list-item@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-list-item/dist/index.js\");\n/* harmony import */ var _tiptap_extension_ordered_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tiptap/extension-ordered-list */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-ordered-list@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-ordered-list/dist/index.js\");\n/* harmony import */ var _tiptap_extension_paragraph__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tiptap/extension-paragraph */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-paragraph@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-paragraph/dist/index.js\");\n/* harmony import */ var _tiptap_extension_strike__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @tiptap/extension-strike */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-strike@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-strike/dist/index.js\");\n/* harmony import */ var _tiptap_extension_text__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @tiptap/extension-text */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-text@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text/dist/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * The starter kit is a collection of essential editor extensions.\n *\n * It’s a good starting point for building your own editor.\n */\nconst StarterKit = _tiptap_core__WEBPACK_IMPORTED_MODULE_18__.Extension.create({\n    name: 'starterKit',\n    addExtensions() {\n        const extensions = [];\n        if (this.options.bold !== false) {\n            extensions.push(_tiptap_extension_bold__WEBPACK_IMPORTED_MODULE_1__.Bold.configure(this.options.bold));\n        }\n        if (this.options.blockquote !== false) {\n            extensions.push(_tiptap_extension_blockquote__WEBPACK_IMPORTED_MODULE_0__.Blockquote.configure(this.options.blockquote));\n        }\n        if (this.options.bulletList !== false) {\n            extensions.push(_tiptap_extension_bullet_list__WEBPACK_IMPORTED_MODULE_2__.BulletList.configure(this.options.bulletList));\n        }\n        if (this.options.code !== false) {\n            extensions.push(_tiptap_extension_code__WEBPACK_IMPORTED_MODULE_3__.Code.configure(this.options.code));\n        }\n        if (this.options.codeBlock !== false) {\n            extensions.push(_tiptap_extension_code_block__WEBPACK_IMPORTED_MODULE_4__.CodeBlock.configure(this.options.codeBlock));\n        }\n        if (this.options.document !== false) {\n            extensions.push(_tiptap_extension_document__WEBPACK_IMPORTED_MODULE_5__.Document.configure(this.options.document));\n        }\n        if (this.options.dropcursor !== false) {\n            extensions.push(_tiptap_extension_dropcursor__WEBPACK_IMPORTED_MODULE_6__.Dropcursor.configure(this.options.dropcursor));\n        }\n        if (this.options.gapcursor !== false) {\n            extensions.push(_tiptap_extension_gapcursor__WEBPACK_IMPORTED_MODULE_7__.Gapcursor.configure(this.options.gapcursor));\n        }\n        if (this.options.hardBreak !== false) {\n            extensions.push(_tiptap_extension_hard_break__WEBPACK_IMPORTED_MODULE_8__.HardBreak.configure(this.options.hardBreak));\n        }\n        if (this.options.heading !== false) {\n            extensions.push(_tiptap_extension_heading__WEBPACK_IMPORTED_MODULE_9__.Heading.configure(this.options.heading));\n        }\n        if (this.options.history !== false) {\n            extensions.push(_tiptap_extension_history__WEBPACK_IMPORTED_MODULE_10__.History.configure(this.options.history));\n        }\n        if (this.options.horizontalRule !== false) {\n            extensions.push(_tiptap_extension_horizontal_rule__WEBPACK_IMPORTED_MODULE_11__.HorizontalRule.configure(this.options.horizontalRule));\n        }\n        if (this.options.italic !== false) {\n            extensions.push(_tiptap_extension_italic__WEBPACK_IMPORTED_MODULE_12__.Italic.configure(this.options.italic));\n        }\n        if (this.options.listItem !== false) {\n            extensions.push(_tiptap_extension_list_item__WEBPACK_IMPORTED_MODULE_13__.ListItem.configure(this.options.listItem));\n        }\n        if (this.options.orderedList !== false) {\n            extensions.push(_tiptap_extension_ordered_list__WEBPACK_IMPORTED_MODULE_14__.OrderedList.configure(this.options.orderedList));\n        }\n        if (this.options.paragraph !== false) {\n            extensions.push(_tiptap_extension_paragraph__WEBPACK_IMPORTED_MODULE_15__.Paragraph.configure(this.options.paragraph));\n        }\n        if (this.options.strike !== false) {\n            extensions.push(_tiptap_extension_strike__WEBPACK_IMPORTED_MODULE_16__.Strike.configure(this.options.strike));\n        }\n        if (this.options.text !== false) {\n            extensions.push(_tiptap_extension_text__WEBPACK_IMPORTED_MODULE_17__.Text.configure(this.options.text));\n        }\n        return extensions;\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+starter-kit@2.26.1/node_modules/@tiptap/starter-kit/dist/index.js\n");

/***/ })

};
;