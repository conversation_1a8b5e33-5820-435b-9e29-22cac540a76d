"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-schema-list@1.5.1";
exports.ids = ["vendor-chunks/prosemirror-schema-list@1.5.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/prosemirror-schema-list@1.5.1/node_modules/prosemirror-schema-list/dist/index.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/prosemirror-schema-list@1.5.1/node_modules/prosemirror-schema-list/dist/index.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addListNodes: () => (/* binding */ addListNodes),\n/* harmony export */   bulletList: () => (/* binding */ bulletList),\n/* harmony export */   liftListItem: () => (/* binding */ liftListItem),\n/* harmony export */   listItem: () => (/* binding */ listItem),\n/* harmony export */   orderedList: () => (/* binding */ orderedList),\n/* harmony export */   sinkListItem: () => (/* binding */ sinkListItem),\n/* harmony export */   splitListItem: () => (/* binding */ splitListItem),\n/* harmony export */   splitListItemKeepMarks: () => (/* binding */ splitListItemKeepMarks),\n/* harmony export */   wrapInList: () => (/* binding */ wrapInList),\n/* harmony export */   wrapRangeInList: () => (/* binding */ wrapRangeInList)\n/* harmony export */ });\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/.pnpm/prosemirror-transform@1.10.4/node_modules/prosemirror-transform/dist/index.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/.pnpm/prosemirror-model@1.25.2/node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/.pnpm/prosemirror-state@1.4.3/node_modules/prosemirror-state/dist/index.js\");\n\n\n\n\nconst olDOM = [\"ol\", 0], ulDOM = [\"ul\", 0], liDOM = [\"li\", 0];\n/**\nAn ordered list [node spec](https://prosemirror.net/docs/ref/#model.NodeSpec). Has a single\nattribute, `order`, which determines the number at which the list\nstarts counting, and defaults to 1. Represented as an `<ol>`\nelement.\n*/\nconst orderedList = {\n    attrs: { order: { default: 1, validate: \"number\" } },\n    parseDOM: [{ tag: \"ol\", getAttrs(dom) {\n                return { order: dom.hasAttribute(\"start\") ? +dom.getAttribute(\"start\") : 1 };\n            } }],\n    toDOM(node) {\n        return node.attrs.order == 1 ? olDOM : [\"ol\", { start: node.attrs.order }, 0];\n    }\n};\n/**\nA bullet list node spec, represented in the DOM as `<ul>`.\n*/\nconst bulletList = {\n    parseDOM: [{ tag: \"ul\" }],\n    toDOM() { return ulDOM; }\n};\n/**\nA list item (`<li>`) spec.\n*/\nconst listItem = {\n    parseDOM: [{ tag: \"li\" }],\n    toDOM() { return liDOM; },\n    defining: true\n};\nfunction add(obj, props) {\n    let copy = {};\n    for (let prop in obj)\n        copy[prop] = obj[prop];\n    for (let prop in props)\n        copy[prop] = props[prop];\n    return copy;\n}\n/**\nConvenience function for adding list-related node types to a map\nspecifying the nodes for a schema. Adds\n[`orderedList`](https://prosemirror.net/docs/ref/#schema-list.orderedList) as `\"ordered_list\"`,\n[`bulletList`](https://prosemirror.net/docs/ref/#schema-list.bulletList) as `\"bullet_list\"`, and\n[`listItem`](https://prosemirror.net/docs/ref/#schema-list.listItem) as `\"list_item\"`.\n\n`itemContent` determines the content expression for the list items.\nIf you want the commands defined in this module to apply to your\nlist structure, it should have a shape like `\"paragraph block*\"` or\n`\"paragraph (ordered_list | bullet_list)*\"`. `listGroup` can be\ngiven to assign a group name to the list node types, for example\n`\"block\"`.\n*/\nfunction addListNodes(nodes, itemContent, listGroup) {\n    return nodes.append({\n        ordered_list: add(orderedList, { content: \"list_item+\", group: listGroup }),\n        bullet_list: add(bulletList, { content: \"list_item+\", group: listGroup }),\n        list_item: add(listItem, { content: itemContent })\n    });\n}\n/**\nReturns a command function that wraps the selection in a list with\nthe given type an attributes. If `dispatch` is null, only return a\nvalue to indicate whether this is possible, but don't actually\nperform the change.\n*/\nfunction wrapInList(listType, attrs = null) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to);\n        if (!range)\n            return false;\n        let tr = dispatch ? state.tr : null;\n        if (!wrapRangeInList(tr, range, listType, attrs))\n            return false;\n        if (dispatch)\n            dispatch(tr.scrollIntoView());\n        return true;\n    };\n}\n/**\nTry to wrap the given node range in a list of the given type.\nReturn `true` when this is possible, `false` otherwise. When `tr`\nis non-null, the wrapping is added to that transaction. When it is\n`null`, the function only queries whether the wrapping is\npossible.\n*/\nfunction wrapRangeInList(tr, range, listType, attrs = null) {\n    let doJoin = false, outerRange = range, doc = range.$from.doc;\n    // This is at the top of an existing list item\n    if (range.depth >= 2 && range.$from.node(range.depth - 1).type.compatibleContent(listType) && range.startIndex == 0) {\n        // Don't do anything if this is the top of the list\n        if (range.$from.index(range.depth - 1) == 0)\n            return false;\n        let $insert = doc.resolve(range.start - 2);\n        outerRange = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.NodeRange($insert, $insert, range.depth);\n        if (range.endIndex < range.parent.childCount)\n            range = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.NodeRange(range.$from, doc.resolve(range.$to.end(range.depth)), range.depth);\n        doJoin = true;\n    }\n    let wrap = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.findWrapping)(outerRange, listType, attrs, range);\n    if (!wrap)\n        return false;\n    if (tr)\n        doWrapInList(tr, range, wrap, doJoin, listType);\n    return true;\n}\nfunction doWrapInList(tr, range, wrappers, joinBefore, listType) {\n    let content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n    for (let i = wrappers.length - 1; i >= 0; i--)\n        content = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(wrappers[i].type.create(wrappers[i].attrs, content));\n    tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceAroundStep(range.start - (joinBefore ? 2 : 0), range.end, range.start, range.end, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(content, 0, 0), wrappers.length, true));\n    let found = 0;\n    for (let i = 0; i < wrappers.length; i++)\n        if (wrappers[i].type == listType)\n            found = i + 1;\n    let splitDepth = wrappers.length - found;\n    let splitPos = range.start + wrappers.length - (joinBefore ? 2 : 0), parent = range.parent;\n    for (let i = range.startIndex, e = range.endIndex, first = true; i < e; i++, first = false) {\n        if (!first && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.canSplit)(tr.doc, splitPos, splitDepth)) {\n            tr.split(splitPos, splitDepth);\n            splitPos += 2 * splitDepth;\n        }\n        splitPos += parent.child(i).nodeSize;\n    }\n    return tr;\n}\n/**\nBuild a command that splits a non-empty textblock at the top level\nof a list item by also splitting that list item.\n*/\nfunction splitListItem(itemType, itemAttrs) {\n    return function (state, dispatch) {\n        let { $from, $to, node } = state.selection;\n        if ((node && node.isBlock) || $from.depth < 2 || !$from.sameParent($to))\n            return false;\n        let grandParent = $from.node(-1);\n        if (grandParent.type != itemType)\n            return false;\n        if ($from.parent.content.size == 0 && $from.node(-1).childCount == $from.indexAfter(-1)) {\n            // In an empty block. If this is a nested list, the wrapping\n            // list item should be split. Otherwise, bail out and let next\n            // command handle lifting.\n            if ($from.depth == 3 || $from.node(-3).type != itemType ||\n                $from.index(-2) != $from.node(-2).childCount - 1)\n                return false;\n            if (dispatch) {\n                let wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty;\n                let depthBefore = $from.index(-1) ? 1 : $from.index(-2) ? 2 : 3;\n                // Build a fragment containing empty versions of the structure\n                // from the outer list item to the parent node of the cursor\n                for (let d = $from.depth - depthBefore; d >= $from.depth - 3; d--)\n                    wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from($from.node(d).copy(wrap));\n                let depthAfter = $from.indexAfter(-1) < $from.node(-2).childCount ? 1\n                    : $from.indexAfter(-2) < $from.node(-3).childCount ? 2 : 3;\n                // Add a second list item with an empty default start node\n                wrap = wrap.append(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(itemType.createAndFill()));\n                let start = $from.before($from.depth - (depthBefore - 1));\n                let tr = state.tr.replace(start, $from.after(-depthAfter), new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(wrap, 4 - depthBefore, 0));\n                let sel = -1;\n                tr.doc.nodesBetween(start, tr.doc.content.size, (node, pos) => {\n                    if (sel > -1)\n                        return false;\n                    if (node.isTextblock && node.content.size == 0)\n                        sel = pos + 1;\n                });\n                if (sel > -1)\n                    tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_2__.Selection.near(tr.doc.resolve(sel)));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n        let nextType = $to.pos == $from.end() ? grandParent.contentMatchAt(0).defaultType : null;\n        let tr = state.tr.delete($from.pos, $to.pos);\n        let types = nextType ? [itemAttrs ? { type: itemType, attrs: itemAttrs } : null, { type: nextType }] : undefined;\n        if (!(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.canSplit)(tr.doc, $from.pos, 2, types))\n            return false;\n        if (dispatch)\n            dispatch(tr.split($from.pos, 2, types).scrollIntoView());\n        return true;\n    };\n}\n/**\nActs like [`splitListItem`](https://prosemirror.net/docs/ref/#schema-list.splitListItem), but\nwithout resetting the set of active marks at the cursor.\n*/\nfunction splitListItemKeepMarks(itemType, itemAttrs) {\n    let split = splitListItem(itemType, itemAttrs);\n    return (state, dispatch) => {\n        return split(state, dispatch && (tr => {\n            let marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks());\n            if (marks)\n                tr.ensureMarks(marks);\n            dispatch(tr);\n        }));\n    };\n}\n/**\nCreate a command to lift the list item around the selection up into\na wrapping list.\n*/\nfunction liftListItem(itemType) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to, node => node.childCount > 0 && node.firstChild.type == itemType);\n        if (!range)\n            return false;\n        if (!dispatch)\n            return true;\n        if ($from.node(range.depth - 1).type == itemType) // Inside a parent list\n            return liftToOuterList(state, dispatch, itemType, range);\n        else // Outer list node\n            return liftOutOfList(state, dispatch, range);\n    };\n}\nfunction liftToOuterList(state, dispatch, itemType, range) {\n    let tr = state.tr, end = range.end, endOfList = range.$to.end(range.depth);\n    if (end < endOfList) {\n        // There are siblings after the lifted items, which must become\n        // children of the last item\n        tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceAroundStep(end - 1, endOfList, end, endOfList, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(itemType.create(null, range.parent.copy())), 1, 0), 1, true));\n        range = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.NodeRange(tr.doc.resolve(range.$from.pos), tr.doc.resolve(endOfList), range.depth);\n    }\n    const target = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.liftTarget)(range);\n    if (target == null)\n        return false;\n    tr.lift(range, target);\n    let $after = tr.doc.resolve(tr.mapping.map(end, -1) - 1);\n    if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.canJoin)(tr.doc, $after.pos) && $after.nodeBefore.type == $after.nodeAfter.type)\n        tr.join($after.pos);\n    dispatch(tr.scrollIntoView());\n    return true;\n}\nfunction liftOutOfList(state, dispatch, range) {\n    let tr = state.tr, list = range.parent;\n    // Merge the list items into a single big item\n    for (let pos = range.end, i = range.endIndex - 1, e = range.startIndex; i > e; i--) {\n        pos -= list.child(i).nodeSize;\n        tr.delete(pos - 1, pos + 1);\n    }\n    let $start = tr.doc.resolve(range.start), item = $start.nodeAfter;\n    if (tr.mapping.map(range.end) != range.start + $start.nodeAfter.nodeSize)\n        return false;\n    let atStart = range.startIndex == 0, atEnd = range.endIndex == list.childCount;\n    let parent = $start.node(-1), indexBefore = $start.index(-1);\n    if (!parent.canReplace(indexBefore + (atStart ? 0 : 1), indexBefore + 1, item.content.append(atEnd ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty : prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(list))))\n        return false;\n    let start = $start.pos, end = start + item.nodeSize;\n    // Strip off the surrounding list. At the sides where we're not at\n    // the end of the list, the existing list is closed. At sides where\n    // this is the end, it is overwritten to its end.\n    tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceAroundStep(start - (atStart ? 1 : 0), end + (atEnd ? 1 : 0), start + 1, end - 1, new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice((atStart ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty : prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(list.copy(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty)))\n        .append(atEnd ? prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty : prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(list.copy(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.empty))), atStart ? 0 : 1, atEnd ? 0 : 1), atStart ? 0 : 1));\n    dispatch(tr.scrollIntoView());\n    return true;\n}\n/**\nCreate a command to sink the list item around the selection down\ninto an inner list.\n*/\nfunction sinkListItem(itemType) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to, node => node.childCount > 0 && node.firstChild.type == itemType);\n        if (!range)\n            return false;\n        let startIndex = range.startIndex;\n        if (startIndex == 0)\n            return false;\n        let parent = range.parent, nodeBefore = parent.child(startIndex - 1);\n        if (nodeBefore.type != itemType)\n            return false;\n        if (dispatch) {\n            let nestedBefore = nodeBefore.lastChild && nodeBefore.lastChild.type == parent.type;\n            let inner = prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(nestedBefore ? itemType.create() : null);\n            let slice = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Slice(prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(itemType.create(null, prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Fragment.from(parent.type.create(null, inner)))), nestedBefore ? 3 : 1, 0);\n            let before = range.start, after = range.end;\n            dispatch(state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_1__.ReplaceAroundStep(before - (nestedBefore ? 3 : 1), after, before, after, slice, 1, true))\n                .scrollIntoView());\n        }\n        return true;\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/prosemirror-schema-list@1.5.1/node_modules/prosemirror-schema-list/dist/index.js\n");

/***/ })

};
;