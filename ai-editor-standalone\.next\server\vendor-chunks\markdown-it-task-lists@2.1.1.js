/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/markdown-it-task-lists@2.1.1";
exports.ids = ["vendor-chunks/markdown-it-task-lists@2.1.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/markdown-it-task-lists@2.1.1/node_modules/markdown-it-task-lists/index.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/markdown-it-task-lists@2.1.1/node_modules/markdown-it-task-lists/index.js ***!
  \******************************************************************************************************/
/***/ ((module) => {

eval("// Markdown-it plugin to render GitHub-style task lists; see\n//\n// https://github.com/blog/1375-task-lists-in-gfm-issues-pulls-comments\n// https://github.com/blog/1825-task-lists-in-all-markdown-documents\n\nvar disableCheckboxes = true;\nvar useLabelWrapper = false;\nvar useLabelAfter = false;\n\nmodule.exports = function(md, options) {\n\tif (options) {\n\t\tdisableCheckboxes = !options.enabled;\n\t\tuseLabelWrapper = !!options.label;\n\t\tuseLabelAfter = !!options.labelAfter;\n\t}\n\n\tmd.core.ruler.after('inline', 'github-task-lists', function(state) {\n\t\tvar tokens = state.tokens;\n\t\tfor (var i = 2; i < tokens.length; i++) {\n\t\t\tif (isTodoItem(tokens, i)) {\n\t\t\t\ttodoify(tokens[i], state.Token);\n\t\t\t\tattrSet(tokens[i-2], 'class', 'task-list-item' + (!disableCheckboxes ? ' enabled' : ''));\n\t\t\t\tattrSet(tokens[parentToken(tokens, i-2)], 'class', 'contains-task-list');\n\t\t\t}\n\t\t}\n\t});\n};\n\nfunction attrSet(token, name, value) {\n\tvar index = token.attrIndex(name);\n\tvar attr = [name, value];\n\n\tif (index < 0) {\n\t\ttoken.attrPush(attr);\n\t} else {\n\t\ttoken.attrs[index] = attr;\n\t}\n}\n\nfunction parentToken(tokens, index) {\n\tvar targetLevel = tokens[index].level - 1;\n\tfor (var i = index - 1; i >= 0; i--) {\n\t\tif (tokens[i].level === targetLevel) {\n\t\t\treturn i;\n\t\t}\n\t}\n\treturn -1;\n}\n\nfunction isTodoItem(tokens, index) {\n\treturn isInline(tokens[index]) &&\n\t       isParagraph(tokens[index - 1]) &&\n\t       isListItem(tokens[index - 2]) &&\n\t       startsWithTodoMarkdown(tokens[index]);\n}\n\nfunction todoify(token, TokenConstructor) {\n\ttoken.children.unshift(makeCheckbox(token, TokenConstructor));\n\ttoken.children[1].content = token.children[1].content.slice(3);\n\ttoken.content = token.content.slice(3);\n\n\tif (useLabelWrapper) {\n\t\tif (useLabelAfter) {\n\t\t\ttoken.children.pop();\n\n\t\t\t// Use large random number as id property of the checkbox.\n\t\t\tvar id = 'task-item-' + Math.ceil(Math.random() * (10000 * 1000) - 1000);\n\t\t\ttoken.children[0].content = token.children[0].content.slice(0, -1) + ' id=\"' + id + '\">';\n\t\t\ttoken.children.push(afterLabel(token.content, id, TokenConstructor));\n\t\t} else {\n\t\t\ttoken.children.unshift(beginLabel(TokenConstructor));\n\t\t\ttoken.children.push(endLabel(TokenConstructor));\n\t\t}\n\t}\n}\n\nfunction makeCheckbox(token, TokenConstructor) {\n\tvar checkbox = new TokenConstructor('html_inline', '', 0);\n\tvar disabledAttr = disableCheckboxes ? ' disabled=\"\" ' : '';\n\tif (token.content.indexOf('[ ] ') === 0) {\n\t\tcheckbox.content = '<input class=\"task-list-item-checkbox\"' + disabledAttr + 'type=\"checkbox\">';\n\t} else if (token.content.indexOf('[x] ') === 0 || token.content.indexOf('[X] ') === 0) {\n\t\tcheckbox.content = '<input class=\"task-list-item-checkbox\" checked=\"\"' + disabledAttr + 'type=\"checkbox\">';\n\t}\n\treturn checkbox;\n}\n\n// these next two functions are kind of hacky; probably should really be a\n// true block-level token with .tag=='label'\nfunction beginLabel(TokenConstructor) {\n\tvar token = new TokenConstructor('html_inline', '', 0);\n\ttoken.content = '<label>';\n\treturn token;\n}\n\nfunction endLabel(TokenConstructor) {\n\tvar token = new TokenConstructor('html_inline', '', 0);\n\ttoken.content = '</label>';\n\treturn token;\n}\n\nfunction afterLabel(content, id, TokenConstructor) {\n\tvar token = new TokenConstructor('html_inline', '', 0);\n\ttoken.content = '<label class=\"task-list-item-label\" for=\"' + id + '\">' + content + '</label>';\n\ttoken.attrs = [{for: id}];\n\treturn token;\n}\n\nfunction isInline(token) { return token.type === 'inline'; }\nfunction isParagraph(token) { return token.type === 'paragraph_open'; }\nfunction isListItem(token) { return token.type === 'list_item_open'; }\n\nfunction startsWithTodoMarkdown(token) {\n\t// leading whitespace in a list item is already trimmed off by markdown-it\n\treturn token.content.indexOf('[ ] ') === 0 || token.content.indexOf('[x] ') === 0 || token.content.indexOf('[X] ') === 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/markdown-it-task-lists@2.1.1/node_modules/markdown-it-task-lists/index.js\n");

/***/ })

};
;