"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-italic/dist/index.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-italic/dist/index.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Italic: () => (/* binding */ Italic),\n/* harmony export */   \"default\": () => (/* binding */ Italic),\n/* harmony export */   starInputRegex: () => (/* binding */ starInputRegex),\n/* harmony export */   starPasteRegex: () => (/* binding */ starPasteRegex),\n/* harmony export */   underscoreInputRegex: () => (/* binding */ underscoreInputRegex),\n/* harmony export */   underscorePasteRegex: () => (/* binding */ underscorePasteRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * Matches an italic to a *italic* on input.\n */\nconst starInputRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))$/;\n/**\n * Matches an italic to a *italic* on paste.\n */\nconst starPasteRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))/g;\n/**\n * Matches an italic to a _italic_ on input.\n */\nconst underscoreInputRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))$/;\n/**\n * Matches an italic to a _italic_ on paste.\n */\nconst underscorePasteRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))/g;\n/**\n * This extension allows you to create italic text.\n * @see https://www.tiptap.dev/api/marks/italic\n */\nconst Italic = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Mark.create({\n    name: 'italic',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'em',\n            },\n            {\n                tag: 'i',\n                getAttrs: node => node.style.fontStyle !== 'normal' && null,\n            },\n            {\n                style: 'font-style=normal',\n                clearMark: mark => mark.type.name === this.name,\n            },\n            {\n                style: 'font-style=italic',\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['em', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setItalic: () => ({ commands }) => {\n                return commands.setMark(this.name);\n            },\n            toggleItalic: () => ({ commands }) => {\n                return commands.toggleMark(this.name);\n            },\n            unsetItalic: () => ({ commands }) => {\n                return commands.unsetMark(this.name);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-i': () => this.editor.commands.toggleItalic(),\n            'Mod-I': () => this.editor.commands.toggleItalic(),\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markInputRule)({\n                find: starInputRegex,\n                type: this.type,\n            }),\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markInputRule)({\n                find: underscoreInputRegex,\n                type: this.type,\n            }),\n        ];\n    },\n    addPasteRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markPasteRule)({\n                find: starPasteRegex,\n                type: this.type,\n            }),\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markPasteRule)({\n                find: underscorePasteRegex,\n                type: this.type,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-italic@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-italic/dist/index.js\n");

/***/ })

};
;