/**
 * <div style={{backgroundColor: "#24292f", display: "flex", justifyContent: "space-between", color: "#fff", padding: 16}}>
 * <span>Built-in <b>Loops</b> integration.</span>
 * <a href="https://loops.so">
 *  <img style={{display: "block"}} src="https://authjs.dev/img/providers/loops.svg" width="48"/>
 * </a>
 * </div>
 *
 * @module providers/loops
 */
/**
 *
 * @param config
 * @returns LoopsConfig
 * @requires LoopsUserConfig
 * @example
 * ```ts
 * Loops({
 *   apiKey: process.env.AUTH_LOOPS_KEY,
 *   transactionalId: process.env.AUTH_LOOPS_TRANSACTIONAL_ID,
 * })
 * ```
 *
 * @typedef LoopsUserConfig
 */
export default function Loops(config) {
    return {
        id: "loops",
        apiKey: "",
        type: "email",
        name: "<PERSON><PERSON>",
        from: "Auth.js <<EMAIL>>",
        maxAge: 24 * 60 * 60,
        transactionalId: config.transactionalId || "",
        async sendVerificationRequest(params) {
            const { identifier: to, provider, url } = params;
            if (!provider.apiKey || !provider.transactionalId)
                throw new TypeError("Missing Loops API Key or TransactionalId");
            const res = await fetch("https://app.loops.so/api/v1/transactional", {
                method: "POST",
                headers: {
                    Authorization: `Bearer ${provider.apiKey}`,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    transactionalId: provider.transactionalId,
                    email: to,
                    dataVariables: {
                        url: url,
                    },
                }),
            });
            if (!res.ok) {
                throw new Error("Loops Send Error: " + JSON.stringify(await res.json()));
            }
        },
        options: config,
    };
}
