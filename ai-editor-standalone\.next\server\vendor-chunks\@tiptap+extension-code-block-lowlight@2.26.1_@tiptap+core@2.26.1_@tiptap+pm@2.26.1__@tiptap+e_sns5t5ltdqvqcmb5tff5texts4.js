"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-code-block-lowlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+e_sns5t5ltdqvqcmb5tff5texts4";
exports.ids = ["vendor-chunks/@tiptap+extension-code-block-lowlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+e_sns5t5ltdqvqcmb5tff5texts4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-code-block-lowlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+e_sns5t5ltdqvqcmb5tff5texts4/node_modules/@tiptap/extension-code-block-lowlight/dist/index.js":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-code-block-lowlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+e_sns5t5ltdqvqcmb5tff5texts4/node_modules/@tiptap/extension-code-block-lowlight/dist/index.js ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CodeBlockLowlight: () => (/* binding */ CodeBlockLowlight),\n/* harmony export */   \"default\": () => (/* binding */ CodeBlockLowlight)\n/* harmony export */ });\n/* harmony import */ var _tiptap_extension_code_block__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/extension-code-block */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-code-block@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-code-block/dist/index.js\");\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/pm/view */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/view/dist/index.js\");\n\n\n\n\n\nfunction getDefaultExportFromCjs (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\n/* eslint-disable no-multi-assign */\n\nfunction deepFreeze(obj) {\n  if (obj instanceof Map) {\n    obj.clear =\n      obj.delete =\n      obj.set =\n        function () {\n          throw new Error('map is read-only');\n        };\n  } else if (obj instanceof Set) {\n    obj.add =\n      obj.clear =\n      obj.delete =\n        function () {\n          throw new Error('set is read-only');\n        };\n  }\n\n  // Freeze self\n  Object.freeze(obj);\n\n  Object.getOwnPropertyNames(obj).forEach((name) => {\n    const prop = obj[name];\n    const type = typeof prop;\n\n    // Freeze prop if it is an object or function and also not already frozen\n    if ((type === 'object' || type === 'function') && !Object.isFrozen(prop)) {\n      deepFreeze(prop);\n    }\n  });\n\n  return obj;\n}\n\n/** @typedef {import('highlight.js').CallbackResponse} CallbackResponse */\n/** @typedef {import('highlight.js').CompiledMode} CompiledMode */\n/** @implements CallbackResponse */\n\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit$1(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function(obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */ (result);\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{scope?: string, language?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = (node) => {\n  // rarely we can have a sublanguage where language is undefined\n  // TODO: track down why\n  return !!node.scope;\n};\n\n/**\n *\n * @param {string} name\n * @param {{prefix:string}} options\n */\nconst scopeToCSSClass = (name, { prefix }) => {\n  // sub-language\n  if (name.startsWith(\"language:\")) {\n    return name.replace(\"language:\", \"language-\");\n  }\n  // tiered scope: comment.line\n  if (name.includes(\".\")) {\n    const pieces = name.split(\".\");\n    return [\n      `${prefix}${pieces.shift()}`,\n      ...(pieces.map((x, i) => `${x}${\"_\".repeat(i + 1)}`))\n    ].join(\" \");\n  }\n  // simple scope\n  return `${prefix}${name}`;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    const className = scopeToCSSClass(node.scope,\n      { prefix: this.classPrefix });\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{scope?: string, language?: string, children: Node[]} | string} Node */\n/** @typedef {{scope?: string, language?: string, children: Node[]} } DataNode */\n/** @typedef {import('highlight.js').Emitter} Emitter */\n/**  */\n\n/** @returns {DataNode} */\nconst newNode = (opts = {}) => {\n  /** @type DataNode */\n  const result = { children: [] };\n  Object.assign(result, opts);\n  return result;\n};\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = newNode();\n    this.stack = [this.rootNode];\n  }\n\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n\n  get root() { return this.rootNode; }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} scope */\n  openNode(scope) {\n    /** @type Node */\n    const node = newNode({ scope });\n    this.add(node);\n    this.stack.push(node);\n  }\n\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach((child) => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach((child) => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addText(text)\n  - __addSublanguage(emitter, subLanguageName)\n  - startScope(scope)\n  - endScope()\n  - finalize()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") { return; }\n\n    this.add(text);\n  }\n\n  /** @param {string} scope */\n  startScope(scope) {\n    this.openNode(scope);\n  }\n\n  endScope() {\n    this.closeNode();\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  __addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    if (name) node.scope = `language:${name}`;\n\n    this.add(node);\n  }\n\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n\n  finalize() {\n    this.closeAllNodes();\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(?:', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(?:', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp | string} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return (new RegExp(re.toString() + '|')).exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// **INTERNAL** Not intended for outside usage\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {{joinWith: string}} opts\n * @returns {string}\n */\nfunction _rewriteBackreferences(regexps, { joinWith }) {\n  let numCaptures = 0;\n\n  return regexps.map((regex) => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(joinWith);\n}\n\n/** @typedef {import('highlight.js').Mode} Mode */\n/** @typedef {import('highlight.js').ModeCallback} ModeCallback */\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(\n      beginShebang,\n      /.*\\b/,\n      opts.binary,\n      /\\b.*/);\n  }\n  return inherit$1({\n    scope: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]', relevance: 0\n};\nconst APOS_STRING_MODE = {\n  scope: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  scope: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function(begin, end, modeOptions = {}) {\n  const mode = inherit$1(\n    {\n      scope: 'comment',\n      begin,\n      end,\n      contains: []\n    },\n    modeOptions\n  );\n  mode.contains.push({\n    scope: 'doctag',\n    // hack to avoid the space from being included. the space is necessary to\n    // match here to prevent the plain text rule below from gobbling up doctags\n    begin: '[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)',\n    end: /(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,\n    excludeBegin: true,\n    relevance: 0\n  });\n  const ENGLISH_WORD = either(\n    // list of common 1 and 2 letter words in English\n    \"I\",\n    \"a\",\n    \"is\",\n    \"so\",\n    \"us\",\n    \"to\",\n    \"at\",\n    \"if\",\n    \"in\",\n    \"it\",\n    \"on\",\n    // note: this is not an exhaustive list of contractions, just popular ones\n    /[A-Za-z]+['](d|ve|re|ll|t|s|n)/, // contractions - can't we'd they're let's, etc\n    /[A-Za-z]+[-][a-z]+/, // `no-way`, etc.\n    /[A-Za-z][a-z]{2,}/ // allow capitalized words at beginning of sentences\n  );\n  // looking like plain text, more likely to be a comment\n  mode.contains.push(\n    {\n      // TODO: how to include \", (, ) without breaking grammars that use these for\n      // comment delimiters?\n      // begin: /[ ]+([()\"]?([A-Za-z'-]{3,}|is|a|I|so|us|[tT][oO]|at|if|in|it|on)[.]?[()\":]?([.][ ]|[ ]|\\))){3}/\n      // ---\n\n      // this tries to find sequences of 3 english words in a row (without any\n      // \"programming\" type syntax) this gives us a strong signal that we've\n      // TRULY found a comment - vs perhaps scanning with the wrong language.\n      // It's possible to find something that LOOKS like the start of the\n      // comment - but then if there is no readable text - good chance it is a\n      // false match and not a comment.\n      //\n      // for a visual example please see:\n      // https://github.com/highlightjs/highlight.js/issues/2827\n\n      begin: concat(\n        /[ ]+/, // necessary to prevent us gobbling up doctags like /* <AUTHOR> Mcgill */\n        '(',\n        ENGLISH_WORD,\n        /[.]?[:]?([.][ ]|[ ])/,\n        '){3}') // look for 3 words in a row\n    }\n  );\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  scope: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  scope: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  scope: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  scope: \"regexp\",\n  begin: /\\/(?=[^/\\n]*\\/)/,\n  end: /\\/[gimuy]*/,\n  contains: [\n    BACKSLASH_ESCAPE,\n    {\n      begin: /\\[/,\n      end: /\\]/,\n      relevance: 0,\n      contains: [BACKSLASH_ESCAPE]\n    }\n  ]\n};\nconst TITLE_MODE = {\n  scope: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  scope: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function(mode) {\n  return Object.assign(mode,\n    {\n      /** @type {ModeCallback} */\n      'on:begin': (m, resp) => { resp.data._beginMatch = m[1]; },\n      /** @type {ModeCallback} */\n      'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); }\n    });\n};\n\nvar MODES = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  APOS_STRING_MODE: APOS_STRING_MODE,\n  BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n  BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n  BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n  COMMENT: COMMENT,\n  C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n  C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n  C_NUMBER_MODE: C_NUMBER_MODE,\n  C_NUMBER_RE: C_NUMBER_RE,\n  END_SAME_AS_BEGIN: END_SAME_AS_BEGIN,\n  HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n  IDENT_RE: IDENT_RE,\n  MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n  METHOD_GUARD: METHOD_GUARD,\n  NUMBER_MODE: NUMBER_MODE,\n  NUMBER_RE: NUMBER_RE,\n  PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n  QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n  REGEXP_MODE: REGEXP_MODE,\n  RE_STARTERS_RE: RE_STARTERS_RE,\n  SHEBANG: SHEBANG,\n  TITLE_MODE: TITLE_MODE,\n  UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n  UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE\n});\n\n/**\n@typedef {import('highlight.js').CallbackResponse} CallbackResponse\n@typedef {import('highlight.js').CompilerExt} CompilerExt\n*/\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfHasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n/**\n *\n * @type {CompilerExt}\n */\nfunction scopeClassName(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.className !== undefined) {\n    mode.scope = mode.className;\n    delete mode.className;\n  }\n}\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfHasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// allow beforeMatch to act as a \"qualifier\" for the match\n// the full match begin must be [beforeMatch][begin]\nconst beforeMatchExt = (mode, parent) => {\n  if (!mode.beforeMatch) return;\n  // starts conflicts with endsParent which we need to make sure the child\n  // rule is not matched multiple times\n  if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n\n  const originalMode = Object.assign({}, mode);\n  Object.keys(mode).forEach((key) => { delete mode[key]; });\n\n  mode.keywords = originalMode.keywords;\n  mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n  mode.starts = {\n    relevance: 0,\n    contains: [\n      Object.assign(originalMode, { endsParent: true })\n    ]\n  };\n  mode.relevance = 0;\n\n  delete originalMode.beforeMatch;\n};\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = [\n  'of',\n  'and',\n  'for',\n  'in',\n  'not',\n  'or',\n  'if',\n  'then',\n  'parent', // common variable name\n  'list', // common variable name\n  'value' // common variable name\n];\n\nconst DEFAULT_KEYWORD_SCOPE = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, scopeName = DEFAULT_KEYWORD_SCOPE) {\n  /** @type {import(\"highlight.js/private\").KeywordDict} */\n  const compiledKeywords = Object.create(null);\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing scopeName (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(scopeName, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(scopeName, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function(scopeName) {\n      // collapse all our objects back into the parent object\n      Object.assign(\n        compiledKeywords,\n        compileKeywords(rawKeywords[scopeName], caseInsensitive, scopeName)\n      );\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} scopeName\n   * @param {Array<string>} keywordList\n   */\n  function compileList(scopeName, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function(keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [scopeName, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-*********\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = (message) => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/* eslint-disable no-throw-literal */\n\n/**\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n*/\n\nconst MultiClassError = new Error();\n\n/**\n * Renumbers labeled scope names to account for additional inner match\n * groups that otherwise would break everything.\n *\n * Lets say we 3 match scopes:\n *\n *   { 1 => ..., 2 => ..., 3 => ... }\n *\n * So what we need is a clean match like this:\n *\n *   (a)(b)(c) => [ \"a\", \"b\", \"c\" ]\n *\n * But this falls apart with inner match groups:\n *\n * (a)(((b)))(c) => [\"a\", \"b\", \"b\", \"b\", \"c\" ]\n *\n * Our scopes are now \"out of alignment\" and we're repeating `b` 3 times.\n * What needs to happen is the numbers are remapped:\n *\n *   { 1 => ..., 2 => ..., 5 => ... }\n *\n * We also need to know that the ONLY groups that should be output\n * are 1, 2, and 5.  This function handles this behavior.\n *\n * @param {CompiledMode} mode\n * @param {Array<RegExp | string>} regexes\n * @param {{key: \"beginScope\"|\"endScope\"}} opts\n */\nfunction remapScopeNames(mode, regexes, { key }) {\n  let offset = 0;\n  const scopeNames = mode[key];\n  /** @type Record<number,boolean> */\n  const emit = {};\n  /** @type Record<number,string> */\n  const positions = {};\n\n  for (let i = 1; i <= regexes.length; i++) {\n    positions[i + offset] = scopeNames[i];\n    emit[i + offset] = true;\n    offset += countMatchGroups(regexes[i - 1]);\n  }\n  // we use _emit to keep track of which match groups are \"top-level\" to avoid double\n  // output from inside match groups\n  mode[key] = positions;\n  mode[key]._emit = emit;\n  mode[key]._multi = true;\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction beginMultiClass(mode) {\n  if (!Array.isArray(mode.begin)) return;\n\n  if (mode.skip || mode.excludeBegin || mode.returnBegin) {\n    error(\"skip, excludeBegin, returnBegin not compatible with beginScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.beginScope !== \"object\" || mode.beginScope === null) {\n    error(\"beginScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.begin, { key: \"beginScope\" });\n  mode.begin = _rewriteBackreferences(mode.begin, { joinWith: \"\" });\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction endMultiClass(mode) {\n  if (!Array.isArray(mode.end)) return;\n\n  if (mode.skip || mode.excludeEnd || mode.returnEnd) {\n    error(\"skip, excludeEnd, returnEnd not compatible with endScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.endScope !== \"object\" || mode.endScope === null) {\n    error(\"endScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.end, { key: \"endScope\" });\n  mode.end = _rewriteBackreferences(mode.end, { joinWith: \"\" });\n}\n\n/**\n * this exists only to allow `scope: {}` to be used beside `match:`\n * Otherwise `beginScope` would necessary and that would look weird\n\n  {\n    match: [ /def/, /\\w+/ ]\n    scope: { 1: \"keyword\" , 2: \"title\" }\n  }\n\n * @param {CompiledMode} mode\n */\nfunction scopeSugar(mode) {\n  if (mode.scope && typeof mode.scope === \"object\" && mode.scope !== null) {\n    mode.beginScope = mode.scope;\n    delete mode.scope;\n  }\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction MultiClass(mode) {\n  scopeSugar(mode);\n\n  if (typeof mode.beginScope === \"string\") {\n    mode.beginScope = { _wrap: mode.beginScope };\n  }\n  if (typeof mode.endScope === \"string\") {\n    mode.endScope = { _wrap: mode.endScope };\n  }\n\n  beginMultiClass(mode);\n  endMultiClass(mode);\n}\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').CompiledLanguage} CompiledLanguage\n*/\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language) {\n  /**\n   * Builds a regex with the case sensitivity of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(\n      source(value),\n      'm'\n      + (language.case_insensitive ? 'i' : '')\n      + (language.unicodeRegex ? 'u' : '')\n      + (global ? 'g' : '')\n    );\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n\n    The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(_rewriteBackreferences(terminators, { joinWith: '|' }), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) { return null; }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n\n    So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n\n    NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n\n    Say this is our search group, and we match regex3, but wish to ignore it.\n\n      regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n\n    What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n\n      regex4 | regex5                               ' ie, startAt = 3\n\n    This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n\n    MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ; else { // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n\n    mode.contains.forEach(term => mm.addRule(term.begin, { rule: term, type: \"begin\" }));\n\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, { type: \"end\" });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, { type: \"illegal\" });\n    }\n\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */ (mode);\n    if (mode.isCompiled) return cmode;\n\n    [\n      scopeClassName,\n      // do this early so compiler extensions generally don't have to worry about\n      // the distinction between match/begin\n      compileMatch,\n      MultiClass,\n      beforeMatchExt\n    ].forEach(ext => ext(mode, parent));\n\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n\n    [\n      beginKeywords,\n      // do this later so compiler extensions that come earlier have access to the\n      // raw array if they wanted to perhaps manipulate it, etc.\n      compileIllegal,\n      // default to 1 relevance if not specified\n      compileRelevance\n    ].forEach(ext => ext(mode, parent));\n\n    mode.isCompiled = true;\n\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\" && mode.keywords.$pattern) {\n      // we need a copy because keywords might be compiled multiple times\n      // so we can't go deleting $pattern from the original on the first\n      // pass\n      mode.keywords = Object.assign({}, mode.keywords);\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n    keywordPattern = keywordPattern || /\\w+/;\n\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(cmode.begin);\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(cmode.end);\n      cmode.terminatorEnd = source(cmode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */ (mode.illegal));\n    if (!mode.contains) mode.contains = [];\n\n    mode.contains = [].concat(...mode.contains.map(function(c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function(c) { compileMode(/** @type Mode */ (c), cmode); });\n\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit$1(language.classNameAliases || {});\n\n  return compileMode(/** @type Mode */ (language));\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function(variant) {\n      return inherit$1(mode, { variants: null }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit$1(mode, { starts: mode.starts ? inherit$1(mode.starts) : null });\n  }\n\n  if (Object.isFrozen(mode)) {\n    return inherit$1(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\n\nvar version = \"11.10.0\";\n\nclass HTMLInjectionError extends Error {\n  constructor(reason, html) {\n    super(reason);\n    this.name = \"HTMLInjectionError\";\n    this.html = html;\n  }\n}\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\n\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').CompiledScope} CompiledScope\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSApi} HLJSApi\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').PluginEvent} PluginEvent\n@typedef {import('highlight.js').HLJSOptions} HLJSOptions\n@typedef {import('highlight.js').LanguageFn} LanguageFn\n@typedef {import('highlight.js').HighlightedHTMLElement} HighlightedHTMLElement\n@typedef {import('highlight.js').BeforeHighlightContext} BeforeHighlightContext\n@typedef {import('highlight.js/private').MatchType} MatchType\n@typedef {import('highlight.js/private').KeywordData} KeywordData\n@typedef {import('highlight.js/private').EnhancedMatch} EnhancedMatch\n@typedef {import('highlight.js/private').AnnotatedError} AnnotatedError\n@typedef {import('highlight.js').AutoHighlightResult} AutoHighlightResult\n@typedef {import('highlight.js').HighlightOptions} HighlightOptions\n@typedef {import('highlight.js').HighlightResult} HighlightResult\n*/\n\n\nconst escape = escapeHTML;\nconst inherit = inherit$1;\nconst NO_MATCH = Symbol(\"nomatch\");\nconst MAX_KEYWORD_HITS = 7;\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = { disableAutodetect: true, name: 'Plain text', contains: [] };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    ignoreUnescapedHTML: false,\n    throwUnescapedHTML: false,\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    cssSelector: 'pre code',\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n\n    return classes\n      .split(/\\s+/)\n      .find((_class) => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrLanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrLanguageName, optionsOrCode, ignoreIllegals) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrLanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrLanguageName;\n      code = optionsOrCode;\n    }\n\n    // https://github.com/highlightjs/highlight.js/issues/3149\n    // eslint-disable-next-line no-undefined\n    if (ignoreIllegals === undefined) { ignoreIllegals = true; }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result\n      ? context.result\n      : _highlight(context.language, context.code, ignoreIllegals);\n\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    const keywordHits = Object.create(null);\n\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {string} matchText - the textual match\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, matchText) {\n      return mode.keywords[matchText];\n    }\n\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const word = language.case_insensitive ? match[0].toLowerCase() : match[0];\n        const data = keywordData(top, word);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n\n          keywordHits[word] = (keywordHits[word] || 0) + 1;\n          if (keywordHits[word] <= MAX_KEYWORD_HITS) relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substring(lastIndex);\n      emitter.addText(buf);\n    }\n\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */ (result._top);\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.__addSublanguage(result._emitter, result.language);\n    }\n\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {string} text\n     * @param {string} scope\n     */\n    function emitKeyword(keyword, scope) {\n      if (keyword === \"\") return;\n\n      emitter.startScope(scope);\n      emitter.addText(keyword);\n      emitter.endScope();\n    }\n\n    /**\n     * @param {CompiledScope} scope\n     * @param {RegExpMatchArray} match\n     */\n    function emitMultiClass(scope, match) {\n      let i = 1;\n      const max = match.length - 1;\n      while (i <= max) {\n        if (!scope._emit[i]) { i++; continue; }\n        const klass = language.classNameAliases[scope[i]] || scope[i];\n        const text = match[i];\n        if (klass) {\n          emitKeyword(text, klass);\n        } else {\n          modeBuffer = text;\n          processKeywords();\n          modeBuffer = \"\";\n        }\n        i++;\n      }\n    }\n\n    /**\n     * @param {CompiledMode} mode - new mode to start\n     * @param {RegExpMatchArray} match\n     */\n    function startNewMode(mode, match) {\n      if (mode.scope && typeof mode.scope === \"string\") {\n        emitter.openNode(language.classNameAliases[mode.scope] || mode.scope);\n      }\n      if (mode.beginScope) {\n        // beginScope just wraps the begin match itself in a scope\n        if (mode.beginScope._wrap) {\n          emitKeyword(modeBuffer, language.classNameAliases[mode.beginScope._wrap] || mode.beginScope._wrap);\n          modeBuffer = \"\";\n        } else if (mode.beginScope._multi) {\n          // at this point modeBuffer should just be the match\n          emitMultiClass(mode.beginScope, match);\n          modeBuffer = \"\";\n        }\n      }\n\n      top = Object.create(mode, { parent: { value: top } });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexes to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode, match);\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substring(match.index);\n\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) { return NO_MATCH; }\n\n      const origin = top;\n      if (top.endScope && top.endScope._wrap) {\n        processBuffer();\n        emitKeyword(lexeme, top.endScope._wrap);\n      } else if (top.endScope && top.endScope._multi) {\n        processBuffer();\n        emitMultiClass(top.endScope, match);\n      } else if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.scope) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        startNewMode(endMode.starts, match);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.scope) {\n          list.unshift(current.scope);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error(`0 width match regex (${languageName})`);\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.scope || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  An potential end match that was\n      triggered but could not be completed.  IE, `doEndMatch` returned NO_MATCH.\n      (this could be because a callback requests the match be ignored, etc)\n\n      This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n\n    const md = compileLanguage(language);\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n\n    try {\n      if (!language.__emitTokens) {\n        top.matcher.considerAll();\n\n        for (;;) {\n          iterations++;\n          if (resumeScanAtSamePosition) {\n            // only regexes not matched previously will now be\n            // considered for a potential match\n            resumeScanAtSamePosition = false;\n          } else {\n            top.matcher.considerAll();\n          }\n          top.matcher.lastIndex = index;\n\n          const match = top.matcher.exec(codeToHighlight);\n          // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n          if (!match) break;\n\n          const beforeMatch = codeToHighlight.substring(index, match.index);\n          const processedCount = processLexeme(beforeMatch, match);\n          index = match.index + processedCount;\n        }\n        processLexeme(codeToHighlight.substring(index));\n      } else {\n        language.__emitTokens(codeToHighlight, emitter);\n      }\n\n      emitter.finalize();\n      result = emitter.toHTML();\n\n      return {\n        language: languageName,\n        value: result,\n        relevance,\n        illegal: false,\n        _emitter: emitter,\n        _top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: true,\n          relevance: 0,\n          _illegalBy: {\n            message: err.message,\n            index,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode,\n            resultSoFar: result\n          },\n          _emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: false,\n          relevance: 0,\n          errorRaised: err,\n          _emitter: emitter,\n          _top: top\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      value: escape(code),\n      illegal: false,\n      relevance: 0,\n      _top: PLAINTEXT_LANGUAGE,\n      _emitter: new options.__emitter(options)\n    };\n    result._emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n\n  - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - secondBest (object with the same structure for second-best heuristically\n    detected language, may be absent)\n\n    @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name =>\n      _highlight(name, code, false)\n    );\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.secondBest = secondBest;\n\n    return result;\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = (currentLang && aliases[currentLang]) || resultLang;\n\n    element.classList.add(\"hljs\");\n    element.classList.add(`language-${language}`);\n  }\n\n  /**\n   * Applies highlighting to a DOM node containing code.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n\n    if (shouldNotHighlight(language)) return;\n\n    fire(\"before:highlightElement\",\n      { el: element, language });\n\n    if (element.dataset.highlighted) {\n      console.log(\"Element previously highlighted. To highlight again, first unset `dataset.highlighted`.\", element);\n      return;\n    }\n\n    // we should be all text, no child nodes (unescaped HTML) - this is possibly\n    // an HTML injection attack - it's likely too late if this is already in\n    // production (the code has likely already done its damage by the time\n    // we're seeing it)... but we yell loudly about this so that hopefully it's\n    // more likely to be caught in development before making it to production\n    if (element.children.length > 0) {\n      if (!options.ignoreUnescapedHTML) {\n        console.warn(\"One of your code blocks includes unescaped HTML. This is a potentially serious security risk.\");\n        console.warn(\"https://github.com/highlightjs/highlight.js/wiki/security\");\n        console.warn(\"The element with unescaped HTML:\");\n        console.warn(element);\n      }\n      if (options.throwUnescapedHTML) {\n        const err = new HTMLInjectionError(\n          \"One of your code blocks includes unescaped HTML.\",\n          element.innerHTML\n        );\n        throw err;\n      }\n    }\n\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, { language, ignoreIllegals: true }) : highlightAuto(text);\n\n    element.innerHTML = result.value;\n    element.dataset.highlighted = \"yes\";\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relevance: result.relevance\n    };\n    if (result.secondBest) {\n      element.secondBest = {\n        language: result.secondBest.language,\n        relevance: result.secondBest.relevance\n      };\n    }\n\n    fire(\"after:highlightElement\", { el: element, result, text });\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    options = inherit(options, userOptions);\n  }\n\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlighting() deprecated.  Use highlightAll() now.\");\n  };\n\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() deprecated.  Use highlightAll() now.\");\n  }\n\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      wantsHighlight = true;\n      return;\n    }\n\n    const blocks = document.querySelectorAll(options.cssSelector);\n    blocks.forEach(highlightElement);\n  }\n\n  function boot() {\n    // if a highlight was requested before DOM was loaded, do now\n    if (wantsHighlight) highlightAll();\n  }\n\n  // make sure we are in the browser environment\n  if (typeof window !== 'undefined' && window.addEventListener) {\n    window.addEventListener('DOMContentLoaded', boot, false);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) { throw error$1; } else { error(error$1); }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n\n    if (lang.aliases) {\n      registerAliases(lang.aliases, { languageName });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, { languageName }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => { aliases[alias.toLowerCase()] = languageName; });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = (data) => {\n        plugin[\"before:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = (data) => {\n        plugin[\"after:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function removePlugin(plugin) {\n    const index = plugins.indexOf(plugin);\n    if (index !== -1) {\n      plugins.splice(index, 1);\n    }\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function(plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n   * DEPRECATED\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    autoDetection,\n    inherit,\n    addPlugin,\n    removePlugin\n  });\n\n  hljs.debugMode = function() { SAFE_MODE = false; };\n  hljs.safeMode = function() { SAFE_MODE = true; };\n  hljs.versionString = version;\n\n  hljs.regex = {\n    concat: concat,\n    lookahead: lookahead,\n    either: either,\n    optional: optional,\n    anyNumberOfTimes: anyNumberOfTimes\n  };\n\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreeze(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexes into our main object\n  Object.assign(hljs, MODES);\n\n  return hljs;\n};\n\n// Other names for the variable may break build script\nconst highlight = HLJS({});\n\n// returns a new instance of the highlighter to be used for extensions\n// check https://github.com/wooorm/lowlight/issues/47\nhighlight.newInstance = () => HLJS({});\n\nvar core = highlight;\nhighlight.HighlightJS = highlight;\nhighlight.default = highlight;\n\nvar HighlightJS = /*@__PURE__*/getDefaultExportFromCjs(core);\n\nfunction parseNodes(nodes, className = []) {\n    return nodes\n        .map(node => {\n        const classes = [...className, ...(node.properties ? node.properties.className : [])];\n        if (node.children) {\n            return parseNodes(node.children, classes);\n        }\n        return {\n            text: node.value,\n            classes,\n        };\n    })\n        .flat();\n}\nfunction getHighlightNodes(result) {\n    // `.value` for lowlight v1, `.children` for lowlight v2\n    return result.value || result.children || [];\n}\nfunction registered(aliasOrLanguage) {\n    return Boolean(HighlightJS.getLanguage(aliasOrLanguage));\n}\nfunction getDecorations({ doc, name, lowlight, defaultLanguage, }) {\n    const decorations = [];\n    (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_3__.findChildren)(doc, node => node.type.name === name).forEach(block => {\n        var _a;\n        let from = block.pos + 1;\n        const language = block.node.attrs.language || defaultLanguage;\n        const languages = lowlight.listLanguages();\n        const nodes = language && (languages.includes(language) || registered(language) || ((_a = lowlight.registered) === null || _a === void 0 ? void 0 : _a.call(lowlight, language)))\n            ? getHighlightNodes(lowlight.highlight(language, block.node.textContent))\n            : getHighlightNodes(lowlight.highlightAuto(block.node.textContent));\n        parseNodes(nodes).forEach(node => {\n            const to = from + node.text.length;\n            if (node.classes.length) {\n                const decoration = _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.inline(from, to, {\n                    class: node.classes.join(' '),\n                });\n                decorations.push(decoration);\n            }\n            from = to;\n        });\n    });\n    return _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_2__.DecorationSet.create(doc, decorations);\n}\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction isFunction(param) {\n    return typeof param === 'function';\n}\nfunction LowlightPlugin({ name, lowlight, defaultLanguage, }) {\n    if (!['highlight', 'highlightAuto', 'listLanguages'].every(api => isFunction(lowlight[api]))) {\n        throw Error('You should provide an instance of lowlight to use the code-block-lowlight extension');\n    }\n    const lowlightPlugin = new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.Plugin({\n        key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_1__.PluginKey('lowlight'),\n        state: {\n            init: (_, { doc }) => getDecorations({\n                doc,\n                name,\n                lowlight,\n                defaultLanguage,\n            }),\n            apply: (transaction, decorationSet, oldState, newState) => {\n                const oldNodeName = oldState.selection.$head.parent.type.name;\n                const newNodeName = newState.selection.$head.parent.type.name;\n                const oldNodes = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_3__.findChildren)(oldState.doc, node => node.type.name === name);\n                const newNodes = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_3__.findChildren)(newState.doc, node => node.type.name === name);\n                if (transaction.docChanged\n                    // Apply decorations if:\n                    // selection includes named node,\n                    && ([oldNodeName, newNodeName].includes(name)\n                        // OR transaction adds/removes named node,\n                        || newNodes.length !== oldNodes.length\n                        // OR transaction has changes that completely encapsulte a node\n                        // (for example, a transaction that affects the entire document).\n                        // Such transactions can happen during collab syncing via y-prosemirror, for example.\n                        || transaction.steps.some(step => {\n                            // @ts-ignore\n                            return (\n                            // @ts-ignore\n                            step.from !== undefined\n                                // @ts-ignore\n                                && step.to !== undefined\n                                && oldNodes.some(node => {\n                                    // @ts-ignore\n                                    return (\n                                    // @ts-ignore\n                                    node.pos >= step.from\n                                        // @ts-ignore\n                                        && node.pos + node.node.nodeSize <= step.to);\n                                }));\n                        }))) {\n                    return getDecorations({\n                        doc: transaction.doc,\n                        name,\n                        lowlight,\n                        defaultLanguage,\n                    });\n                }\n                return decorationSet.map(transaction.mapping, transaction.doc);\n            },\n        },\n        props: {\n            decorations(state) {\n                return lowlightPlugin.getState(state);\n            },\n        },\n    });\n    return lowlightPlugin;\n}\n\n/**\n * This extension allows you to highlight code blocks with lowlight.\n * @see https://tiptap.dev/api/nodes/code-block-lowlight\n */\nconst CodeBlockLowlight = _tiptap_extension_code_block__WEBPACK_IMPORTED_MODULE_0__[\"default\"].extend({\n    addOptions() {\n        var _a;\n        return {\n            ...(_a = this.parent) === null || _a === void 0 ? void 0 : _a.call(this),\n            lowlight: {},\n            languageClassPrefix: 'language-',\n            exitOnTripleEnter: true,\n            exitOnArrowDown: true,\n            defaultLanguage: null,\n            HTMLAttributes: {},\n        };\n    },\n    addProseMirrorPlugins() {\n        var _a;\n        return [\n            ...((_a = this.parent) === null || _a === void 0 ? void 0 : _a.call(this)) || [],\n            LowlightPlugin({\n                name: this.name,\n                lowlight: this.options.lowlight,\n                defaultLanguage: this.options.defaultLanguage,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-code-block-lowlight@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+e_sns5t5ltdqvqcmb5tff5texts4/node_modules/@tiptap/extension-code-block-lowlight/dist/index.js\n");

/***/ })

};
;