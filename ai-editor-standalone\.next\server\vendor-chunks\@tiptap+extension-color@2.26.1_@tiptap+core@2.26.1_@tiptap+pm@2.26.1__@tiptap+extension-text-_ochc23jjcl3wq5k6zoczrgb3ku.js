"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku";
exports.ids = ["vendor-chunks/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Color: () => (/* binding */ Color),\n/* harmony export */   \"default\": () => (/* binding */ Color)\n/* harmony export */ });\n/* harmony import */ var _tiptap_extension_text_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/extension-text-style */ \"(ssr)/./node_modules/.pnpm/@tiptap+extension-text-style@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-text-style/dist/index.js\");\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n\n/**\n * This extension allows you to color your text.\n * @see https://tiptap.dev/api/extensions/color\n */\nconst Color = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'color',\n    addOptions() {\n        return {\n            types: ['textStyle'],\n        };\n    },\n    addGlobalAttributes() {\n        return [\n            {\n                types: this.options.types,\n                attributes: {\n                    color: {\n                        default: null,\n                        parseHTML: element => { var _a; return (_a = element.style.color) === null || _a === void 0 ? void 0 : _a.replace(/['\"]+/g, ''); },\n                        renderHTML: attributes => {\n                            if (!attributes.color) {\n                                return {};\n                            }\n                            return {\n                                style: `color: ${attributes.color}`,\n                            };\n                        },\n                    },\n                },\n            },\n        ];\n    },\n    addCommands() {\n        return {\n            setColor: color => ({ chain }) => {\n                return chain()\n                    .setMark('textStyle', { color })\n                    .run();\n            },\n            unsetColor: () => ({ chain }) => {\n                return chain()\n                    .setMark('textStyle', { color: null })\n                    .removeEmptyTextStyle()\n                    .run();\n            },\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-color@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+extension-text-_ochc23jjcl3wq5k6zoczrgb3ku/node_modules/@tiptap/extension-color/dist/index.js\n");

/***/ })

};
;