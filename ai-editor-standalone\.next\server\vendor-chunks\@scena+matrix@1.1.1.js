"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@scena+matrix@1.1.1";
exports.ids = ["vendor-chunks/@scena+matrix@1.1.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@scena+matrix@1.1.1/node_modules/@scena/matrix/dist/matrix.esm.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@scena+matrix@1.1.1/node_modules/@scena/matrix/dist/matrix.esm.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculate: () => (/* binding */ calculate),\n/* harmony export */   convertCSStoMatrix: () => (/* binding */ convertCSStoMatrix),\n/* harmony export */   convertDimension: () => (/* binding */ convertDimension),\n/* harmony export */   convertMatrixtoCSS: () => (/* binding */ convertMatrixtoCSS),\n/* harmony export */   convertPositionMatrix: () => (/* binding */ convertPositionMatrix),\n/* harmony export */   createIdentityMatrix: () => (/* binding */ createIdentityMatrix),\n/* harmony export */   createOriginMatrix: () => (/* binding */ createOriginMatrix),\n/* harmony export */   createRotateMatrix: () => (/* binding */ createRotateMatrix),\n/* harmony export */   createScaleMatrix: () => (/* binding */ createScaleMatrix),\n/* harmony export */   createWarpMatrix: () => (/* binding */ createWarpMatrix),\n/* harmony export */   fromTranslation: () => (/* binding */ fromTranslation),\n/* harmony export */   getCenter: () => (/* binding */ getCenter),\n/* harmony export */   getOrigin: () => (/* binding */ getOrigin),\n/* harmony export */   ignoreDimension: () => (/* binding */ ignoreDimension),\n/* harmony export */   invert: () => (/* binding */ invert),\n/* harmony export */   matrix3d: () => (/* binding */ matrix3d),\n/* harmony export */   minus: () => (/* binding */ minus),\n/* harmony export */   multiplies: () => (/* binding */ multiplies),\n/* harmony export */   multiply: () => (/* binding */ multiply),\n/* harmony export */   plus: () => (/* binding */ plus),\n/* harmony export */   rotate: () => (/* binding */ rotate),\n/* harmony export */   rotateX3d: () => (/* binding */ rotateX3d),\n/* harmony export */   rotateY3d: () => (/* binding */ rotateY3d),\n/* harmony export */   rotateZ3d: () => (/* binding */ rotateZ3d),\n/* harmony export */   scale3d: () => (/* binding */ scale3d),\n/* harmony export */   translate3d: () => (/* binding */ translate3d),\n/* harmony export */   transpose: () => (/* binding */ transpose)\n/* harmony export */ });\n/* harmony import */ var _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @daybrush/utils */ \"(ssr)/./node_modules/.pnpm/@daybrush+utils@1.13.0/node_modules/@daybrush/utils/dist/utils.esm.js\");\n/*\nCopyright (c) 2020 Daybrush\nname: @scena/matrix\nlicense: MIT\nauthor: Daybrush\nrepository: git+https://github.com/daybrush/matrix\nversion: 1.1.1\n*/\n\n\nfunction add(matrix, inverseMatrix, startIndex, fromIndex, n, k) {\n  for (var i = 0; i < n; ++i) {\n    var x = startIndex + i * n;\n    var fromX = fromIndex + i * n;\n    matrix[x] += matrix[fromX] * k;\n    inverseMatrix[x] += inverseMatrix[fromX] * k;\n  }\n}\n\nfunction swap(matrix, inverseMatrix, startIndex, fromIndex, n) {\n  for (var i = 0; i < n; ++i) {\n    var x = startIndex + i * n;\n    var fromX = fromIndex + i * n;\n    var v = matrix[x];\n    var iv = inverseMatrix[x];\n    matrix[x] = matrix[fromX];\n    matrix[fromX] = v;\n    inverseMatrix[x] = inverseMatrix[fromX];\n    inverseMatrix[fromX] = iv;\n  }\n}\n\nfunction divide(matrix, inverseMatrix, startIndex, n, k) {\n  for (var i = 0; i < n; ++i) {\n    var x = startIndex + i * n;\n    matrix[x] /= k;\n    inverseMatrix[x] /= k;\n  }\n}\n/**\n *\n * @namespace Matrix\n */\n\n/**\n * @memberof Matrix\n */\n\n\nfunction ignoreDimension(matrix, m, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = matrix.slice();\n\n  for (var i = 0; i < n; ++i) {\n    newMatrix[i * n + m - 1] = 0;\n    newMatrix[(m - 1) * n + i] = 0;\n  }\n\n  newMatrix[(m - 1) * (n + 1)] = 1;\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction invert(matrix, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = matrix.slice();\n  var inverseMatrix = createIdentityMatrix(n);\n\n  for (var i = 0; i < n; ++i) {\n    // diagonal\n    var identityIndex = n * i + i;\n\n    if (!(0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.throttle)(newMatrix[identityIndex], _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM)) {\n      // newMatrix[identityIndex] = 0;\n      for (var j = i + 1; j < n; ++j) {\n        if (newMatrix[n * i + j]) {\n          swap(newMatrix, inverseMatrix, i, j, n);\n          break;\n        }\n      }\n    }\n\n    if (!(0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.throttle)(newMatrix[identityIndex], _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM)) {\n      // no inverse matrix\n      return [];\n    }\n\n    divide(newMatrix, inverseMatrix, i, n, newMatrix[identityIndex]);\n\n    for (var j = 0; j < n; ++j) {\n      var targetStartIndex = j;\n      var targetIndex = j + i * n;\n      var target = newMatrix[targetIndex];\n\n      if (!(0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.throttle)(target, _daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.TINY_NUM) || i === j) {\n        continue;\n      }\n\n      add(newMatrix, inverseMatrix, targetStartIndex, i, n, -target);\n    }\n  }\n\n  return inverseMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction transpose(matrix, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = [];\n\n  for (var i = 0; i < n; ++i) {\n    for (var j = 0; j < n; ++j) {\n      newMatrix[j * n + i] = matrix[n * i + j];\n    }\n  }\n\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction getOrigin(matrix, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var originMatrix = [];\n  var w = matrix[n * n - 1];\n\n  for (var i = 0; i < n - 1; ++i) {\n    originMatrix[i] = matrix[n * (n - 1) + i] / w;\n  }\n\n  originMatrix[n - 1] = 0;\n  return originMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction fromTranslation(pos, n) {\n  var newMatrix = createIdentityMatrix(n);\n\n  for (var i = 0; i < n - 1; ++i) {\n    newMatrix[n * (n - 1) + i] = pos[i] || 0;\n  }\n\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertPositionMatrix(matrix, n) {\n  var newMatrix = matrix.slice();\n\n  for (var i = matrix.length; i < n - 1; ++i) {\n    newMatrix[i] = 0;\n  }\n\n  newMatrix[n - 1] = 1;\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertDimension(matrix, n, m) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  } // n < m\n\n\n  if (n === m) {\n    return matrix;\n  }\n\n  var newMatrix = createIdentityMatrix(m);\n  var length = Math.min(n, m);\n\n  for (var i = 0; i < length - 1; ++i) {\n    for (var j = 0; j < length - 1; ++j) {\n      newMatrix[i * m + j] = matrix[i * n + j];\n    }\n\n    newMatrix[(i + 1) * m - 1] = matrix[(i + 1) * n - 1];\n    newMatrix[(m - 1) * m + i] = matrix[(n - 1) * n + i];\n  }\n\n  newMatrix[m * m - 1] = matrix[n * n - 1];\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction multiplies(n) {\n  var matrixes = [];\n\n  for (var _i = 1; _i < arguments.length; _i++) {\n    matrixes[_i - 1] = arguments[_i];\n  }\n\n  var m = createIdentityMatrix(n);\n  matrixes.forEach(function (matrix) {\n    m = multiply(m, matrix, n);\n  });\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction multiply(matrix, matrix2, n) {\n  if (n === void 0) {\n    n = Math.sqrt(matrix.length);\n  }\n\n  var newMatrix = []; // 1 y: n\n  // 1 x: m\n  // 2 x: m\n  // 2 y: k\n  // n * m X m * k\n\n  var m = matrix.length / n;\n  var k = matrix2.length / m;\n\n  if (!m) {\n    return matrix2;\n  } else if (!k) {\n    return matrix;\n  }\n\n  for (var i = 0; i < n; ++i) {\n    for (var j = 0; j < k; ++j) {\n      newMatrix[j * n + i] = 0;\n\n      for (var l = 0; l < m; ++l) {\n        // m1 x: m(l), y: n(i)\n        // m2 x: k(j):  y: m(l)\n        // nw x: n(i), y: k(j)\n        newMatrix[j * n + i] += matrix[l * n + i] * matrix2[j * m + l];\n      }\n    }\n  } // n * k\n\n\n  return newMatrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction plus(pos1, pos2) {\n  var length = Math.min(pos1.length, pos2.length);\n  var nextPos = pos1.slice();\n\n  for (var i = 0; i < length; ++i) {\n    nextPos[i] = nextPos[i] + pos2[i];\n  }\n\n  return nextPos;\n}\n/**\n * @memberof Matrix\n */\n\nfunction minus(pos1, pos2) {\n  var length = Math.min(pos1.length, pos2.length);\n  var nextPos = pos1.slice();\n\n  for (var i = 0; i < length; ++i) {\n    nextPos[i] = nextPos[i] - pos2[i];\n  }\n\n  return nextPos;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertCSStoMatrix(a, is2d) {\n  if (is2d === void 0) {\n    is2d = a.length === 6;\n  }\n\n  if (is2d) {\n    return [a[0], a[1], 0, a[2], a[3], 0, a[4], a[5], 1];\n  }\n\n  return a;\n}\n/**\n * @memberof Matrix\n */\n\nfunction convertMatrixtoCSS(a, is2d) {\n  if (is2d === void 0) {\n    is2d = a.length === 9;\n  }\n\n  if (is2d) {\n    return [a[0], a[1], a[3], a[4], a[6], a[7]];\n  }\n\n  return a;\n}\n/**\n * @memberof Matrix\n */\n\nfunction calculate(matrix, matrix2, n) {\n  if (n === void 0) {\n    n = matrix2.length;\n  }\n\n  var result = multiply(matrix, matrix2, n);\n  var k = result[n - 1];\n  return result.map(function (v) {\n    return v / k;\n  });\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotateX3d(matrix, rad) {\n  return multiply(matrix, [1, 0, 0, 0, 0, Math.cos(rad), Math.sin(rad), 0, 0, -Math.sin(rad), Math.cos(rad), 0, 0, 0, 0, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotateY3d(matrix, rad) {\n  return multiply(matrix, [Math.cos(rad), 0, -Math.sin(rad), 0, 0, 1, 0, 0, Math.sin(rad), 0, Math.cos(rad), 0, 0, 0, 0, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotateZ3d(matrix, rad) {\n  return multiply(matrix, createRotateMatrix(rad, 4));\n}\n/**\n * @memberof Matrix\n */\n\nfunction scale3d(matrix, _a) {\n  var _b = _a[0],\n      sx = _b === void 0 ? 1 : _b,\n      _c = _a[1],\n      sy = _c === void 0 ? 1 : _c,\n      _d = _a[2],\n      sz = _d === void 0 ? 1 : _d;\n  return multiply(matrix, [sx, 0, 0, 0, 0, sy, 0, 0, 0, 0, sz, 0, 0, 0, 0, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction rotate(pos, rad) {\n  return calculate(createRotateMatrix(rad, 3), convertPositionMatrix(pos, 3));\n}\n/**\n * @memberof Matrix\n */\n\nfunction translate3d(matrix, _a) {\n  var _b = _a[0],\n      tx = _b === void 0 ? 0 : _b,\n      _c = _a[1],\n      ty = _c === void 0 ? 0 : _c,\n      _d = _a[2],\n      tz = _d === void 0 ? 0 : _d;\n  return multiply(matrix, [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, tx, ty, tz, 1], 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction matrix3d(matrix1, matrix2) {\n  return multiply(matrix1, matrix2, 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction createRotateMatrix(rad, n) {\n  var cos = Math.cos(rad);\n  var sin = Math.sin(rad);\n  var m = createIdentityMatrix(n); // cos -sin\n  // sin cos\n\n  m[0] = cos;\n  m[1] = sin;\n  m[n] = -sin;\n  m[n + 1] = cos;\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createIdentityMatrix(n) {\n  var length = n * n;\n  var matrix = [];\n\n  for (var i = 0; i < length; ++i) {\n    matrix[i] = i % (n + 1) ? 0 : 1;\n  }\n\n  return matrix;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createScaleMatrix(scale, n) {\n  var m = createIdentityMatrix(n);\n  var length = Math.min(scale.length, n - 1);\n\n  for (var i = 0; i < length; ++i) {\n    m[(n + 1) * i] = scale[i];\n  }\n\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createOriginMatrix(origin, n) {\n  var m = createIdentityMatrix(n);\n  var length = Math.min(origin.length, n - 1);\n\n  for (var i = 0; i < length; ++i) {\n    m[n * (n - 1) + i] = origin[i];\n  }\n\n  return m;\n}\n/**\n * @memberof Matrix\n */\n\nfunction createWarpMatrix(pos0, pos1, pos2, pos3, nextPos0, nextPos1, nextPos2, nextPos3) {\n  var x0 = pos0[0],\n      y0 = pos0[1];\n  var x1 = pos1[0],\n      y1 = pos1[1];\n  var x2 = pos2[0],\n      y2 = pos2[1];\n  var x3 = pos3[0],\n      y3 = pos3[1];\n  var u0 = nextPos0[0],\n      v0 = nextPos0[1];\n  var u1 = nextPos1[0],\n      v1 = nextPos1[1];\n  var u2 = nextPos2[0],\n      v2 = nextPos2[1];\n  var u3 = nextPos3[0],\n      v3 = nextPos3[1];\n  var matrix = [x0, 0, x1, 0, x2, 0, x3, 0, y0, 0, y1, 0, y2, 0, y3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, x0, 0, x1, 0, x2, 0, x3, 0, y0, 0, y1, 0, y2, 0, y3, 0, 1, 0, 1, 0, 1, 0, 1, -u0 * x0, -v0 * x0, -u1 * x1, -v1 * x1, -u2 * x2, -v2 * x2, -u3 * x3, -v3 * x3, -u0 * y0, -v0 * y0, -u1 * y1, -v1 * y1, -u2 * y2, -v2 * y2, -u3 * y3, -v3 * y3];\n  var inverseMatrix = invert(matrix, 8);\n\n  if (!inverseMatrix.length) {\n    return [];\n  }\n\n  var h = multiply(inverseMatrix, [u0, v0, u1, v1, u2, v2, u3, v3], 8);\n  h[8] = 1;\n  return convertDimension(transpose(h), 3, 4);\n}\n/**\n * @memberof Matrix\n */\n\nfunction getCenter(points) {\n  return [0, 1].map(function (i) {\n    return (0,_daybrush_utils__WEBPACK_IMPORTED_MODULE_0__.average)(points.map(function (pos) {\n      return pos[i];\n    }));\n  });\n}\n\n\n//# sourceMappingURL=matrix.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNjZW5hK21hdHJpeEAxLjEuMS9ub2RlX21vZHVsZXMvQHNjZW5hL21hdHJpeC9kaXN0L21hdHJpeC5lc20uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDOEQ7O0FBRTlEO0FBQ0Esa0JBQWtCLE9BQU87QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0JBQWtCLE9BQU87QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxrQkFBa0IsT0FBTztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxrQkFBa0IsT0FBTztBQUN6QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsa0JBQWtCLE9BQU87QUFDekI7QUFDQTs7QUFFQSxTQUFTLHlEQUFRLDJCQUEyQixxREFBUTtBQUNwRDtBQUNBLDBCQUEwQixPQUFPO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxTQUFTLHlEQUFRLDJCQUEyQixxREFBUTtBQUNwRDtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsb0JBQW9CLE9BQU87QUFDM0I7QUFDQTtBQUNBOztBQUVBLFdBQVcseURBQVEsU0FBUyxxREFBUTtBQUNwQztBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLGtCQUFrQixPQUFPO0FBQ3pCLG9CQUFvQixPQUFPO0FBQzNCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsa0JBQWtCLFdBQVc7QUFDN0I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxrQkFBa0IsV0FBVztBQUM3QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSw4QkFBOEIsV0FBVztBQUN6QztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJOzs7QUFHSjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxrQkFBa0IsZ0JBQWdCO0FBQ2xDLG9CQUFvQixnQkFBZ0I7QUFDcEM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsbUJBQW1CLHVCQUF1QjtBQUMxQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQSxrQkFBa0IsT0FBTztBQUN6QixvQkFBb0IsT0FBTztBQUMzQjs7QUFFQSxzQkFBc0IsT0FBTztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJOzs7QUFHSjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxrQkFBa0IsWUFBWTtBQUM5QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixZQUFZO0FBQzlCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DO0FBQ25DOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsa0JBQWtCLFlBQVk7QUFDOUI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxrQkFBa0IsWUFBWTtBQUM5QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixZQUFZO0FBQzlCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFdBQVcsd0RBQU87QUFDbEI7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIOztBQUVnWTtBQUNoWSIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzY2VuYSttYXRyaXhAMS4xLjFcXG5vZGVfbW9kdWxlc1xcQHNjZW5hXFxtYXRyaXhcXGRpc3RcXG1hdHJpeC5lc20uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbkNvcHlyaWdodCAoYykgMjAyMCBEYXlicnVzaFxubmFtZTogQHNjZW5hL21hdHJpeFxubGljZW5zZTogTUlUXG5hdXRob3I6IERheWJydXNoXG5yZXBvc2l0b3J5OiBnaXQraHR0cHM6Ly9naXRodWIuY29tL2RheWJydXNoL21hdHJpeFxudmVyc2lvbjogMS4xLjFcbiovXG5pbXBvcnQgeyB0aHJvdHRsZSwgVElOWV9OVU0sIGF2ZXJhZ2UgfSBmcm9tICdAZGF5YnJ1c2gvdXRpbHMnO1xuXG5mdW5jdGlvbiBhZGQobWF0cml4LCBpbnZlcnNlTWF0cml4LCBzdGFydEluZGV4LCBmcm9tSW5kZXgsIG4sIGspIHtcbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBuOyArK2kpIHtcbiAgICB2YXIgeCA9IHN0YXJ0SW5kZXggKyBpICogbjtcbiAgICB2YXIgZnJvbVggPSBmcm9tSW5kZXggKyBpICogbjtcbiAgICBtYXRyaXhbeF0gKz0gbWF0cml4W2Zyb21YXSAqIGs7XG4gICAgaW52ZXJzZU1hdHJpeFt4XSArPSBpbnZlcnNlTWF0cml4W2Zyb21YXSAqIGs7XG4gIH1cbn1cblxuZnVuY3Rpb24gc3dhcChtYXRyaXgsIGludmVyc2VNYXRyaXgsIHN0YXJ0SW5kZXgsIGZyb21JbmRleCwgbikge1xuICBmb3IgKHZhciBpID0gMDsgaSA8IG47ICsraSkge1xuICAgIHZhciB4ID0gc3RhcnRJbmRleCArIGkgKiBuO1xuICAgIHZhciBmcm9tWCA9IGZyb21JbmRleCArIGkgKiBuO1xuICAgIHZhciB2ID0gbWF0cml4W3hdO1xuICAgIHZhciBpdiA9IGludmVyc2VNYXRyaXhbeF07XG4gICAgbWF0cml4W3hdID0gbWF0cml4W2Zyb21YXTtcbiAgICBtYXRyaXhbZnJvbVhdID0gdjtcbiAgICBpbnZlcnNlTWF0cml4W3hdID0gaW52ZXJzZU1hdHJpeFtmcm9tWF07XG4gICAgaW52ZXJzZU1hdHJpeFtmcm9tWF0gPSBpdjtcbiAgfVxufVxuXG5mdW5jdGlvbiBkaXZpZGUobWF0cml4LCBpbnZlcnNlTWF0cml4LCBzdGFydEluZGV4LCBuLCBrKSB7XG4gIGZvciAodmFyIGkgPSAwOyBpIDwgbjsgKytpKSB7XG4gICAgdmFyIHggPSBzdGFydEluZGV4ICsgaSAqIG47XG4gICAgbWF0cml4W3hdIC89IGs7XG4gICAgaW52ZXJzZU1hdHJpeFt4XSAvPSBrO1xuICB9XG59XG4vKipcbiAqXG4gKiBAbmFtZXNwYWNlIE1hdHJpeFxuICovXG5cbi8qKlxuICogQG1lbWJlcm9mIE1hdHJpeFxuICovXG5cblxuZnVuY3Rpb24gaWdub3JlRGltZW5zaW9uKG1hdHJpeCwgbSwgbikge1xuICBpZiAobiA9PT0gdm9pZCAwKSB7XG4gICAgbiA9IE1hdGguc3FydChtYXRyaXgubGVuZ3RoKTtcbiAgfVxuXG4gIHZhciBuZXdNYXRyaXggPSBtYXRyaXguc2xpY2UoKTtcblxuICBmb3IgKHZhciBpID0gMDsgaSA8IG47ICsraSkge1xuICAgIG5ld01hdHJpeFtpICogbiArIG0gLSAxXSA9IDA7XG4gICAgbmV3TWF0cml4WyhtIC0gMSkgKiBuICsgaV0gPSAwO1xuICB9XG5cbiAgbmV3TWF0cml4WyhtIC0gMSkgKiAobiArIDEpXSA9IDE7XG4gIHJldHVybiBuZXdNYXRyaXg7XG59XG4vKipcbiAqIEBtZW1iZXJvZiBNYXRyaXhcbiAqL1xuXG5mdW5jdGlvbiBpbnZlcnQobWF0cml4LCBuKSB7XG4gIGlmIChuID09PSB2b2lkIDApIHtcbiAgICBuID0gTWF0aC5zcXJ0KG1hdHJpeC5sZW5ndGgpO1xuICB9XG5cbiAgdmFyIG5ld01hdHJpeCA9IG1hdHJpeC5zbGljZSgpO1xuICB2YXIgaW52ZXJzZU1hdHJpeCA9IGNyZWF0ZUlkZW50aXR5TWF0cml4KG4pO1xuXG4gIGZvciAodmFyIGkgPSAwOyBpIDwgbjsgKytpKSB7XG4gICAgLy8gZGlhZ29uYWxcbiAgICB2YXIgaWRlbnRpdHlJbmRleCA9IG4gKiBpICsgaTtcblxuICAgIGlmICghdGhyb3R0bGUobmV3TWF0cml4W2lkZW50aXR5SW5kZXhdLCBUSU5ZX05VTSkpIHtcbiAgICAgIC8vIG5ld01hdHJpeFtpZGVudGl0eUluZGV4XSA9IDA7XG4gICAgICBmb3IgKHZhciBqID0gaSArIDE7IGogPCBuOyArK2opIHtcbiAgICAgICAgaWYgKG5ld01hdHJpeFtuICogaSArIGpdKSB7XG4gICAgICAgICAgc3dhcChuZXdNYXRyaXgsIGludmVyc2VNYXRyaXgsIGksIGosIG4pO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKCF0aHJvdHRsZShuZXdNYXRyaXhbaWRlbnRpdHlJbmRleF0sIFRJTllfTlVNKSkge1xuICAgICAgLy8gbm8gaW52ZXJzZSBtYXRyaXhcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG5cbiAgICBkaXZpZGUobmV3TWF0cml4LCBpbnZlcnNlTWF0cml4LCBpLCBuLCBuZXdNYXRyaXhbaWRlbnRpdHlJbmRleF0pO1xuXG4gICAgZm9yICh2YXIgaiA9IDA7IGogPCBuOyArK2opIHtcbiAgICAgIHZhciB0YXJnZXRTdGFydEluZGV4ID0gajtcbiAgICAgIHZhciB0YXJnZXRJbmRleCA9IGogKyBpICogbjtcbiAgICAgIHZhciB0YXJnZXQgPSBuZXdNYXRyaXhbdGFyZ2V0SW5kZXhdO1xuXG4gICAgICBpZiAoIXRocm90dGxlKHRhcmdldCwgVElOWV9OVU0pIHx8IGkgPT09IGopIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG5cbiAgICAgIGFkZChuZXdNYXRyaXgsIGludmVyc2VNYXRyaXgsIHRhcmdldFN0YXJ0SW5kZXgsIGksIG4sIC10YXJnZXQpO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBpbnZlcnNlTWF0cml4O1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gdHJhbnNwb3NlKG1hdHJpeCwgbikge1xuICBpZiAobiA9PT0gdm9pZCAwKSB7XG4gICAgbiA9IE1hdGguc3FydChtYXRyaXgubGVuZ3RoKTtcbiAgfVxuXG4gIHZhciBuZXdNYXRyaXggPSBbXTtcblxuICBmb3IgKHZhciBpID0gMDsgaSA8IG47ICsraSkge1xuICAgIGZvciAodmFyIGogPSAwOyBqIDwgbjsgKytqKSB7XG4gICAgICBuZXdNYXRyaXhbaiAqIG4gKyBpXSA9IG1hdHJpeFtuICogaSArIGpdO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBuZXdNYXRyaXg7XG59XG4vKipcbiAqIEBtZW1iZXJvZiBNYXRyaXhcbiAqL1xuXG5mdW5jdGlvbiBnZXRPcmlnaW4obWF0cml4LCBuKSB7XG4gIGlmIChuID09PSB2b2lkIDApIHtcbiAgICBuID0gTWF0aC5zcXJ0KG1hdHJpeC5sZW5ndGgpO1xuICB9XG5cbiAgdmFyIG9yaWdpbk1hdHJpeCA9IFtdO1xuICB2YXIgdyA9IG1hdHJpeFtuICogbiAtIDFdO1xuXG4gIGZvciAodmFyIGkgPSAwOyBpIDwgbiAtIDE7ICsraSkge1xuICAgIG9yaWdpbk1hdHJpeFtpXSA9IG1hdHJpeFtuICogKG4gLSAxKSArIGldIC8gdztcbiAgfVxuXG4gIG9yaWdpbk1hdHJpeFtuIC0gMV0gPSAwO1xuICByZXR1cm4gb3JpZ2luTWF0cml4O1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gZnJvbVRyYW5zbGF0aW9uKHBvcywgbikge1xuICB2YXIgbmV3TWF0cml4ID0gY3JlYXRlSWRlbnRpdHlNYXRyaXgobik7XG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBuIC0gMTsgKytpKSB7XG4gICAgbmV3TWF0cml4W24gKiAobiAtIDEpICsgaV0gPSBwb3NbaV0gfHwgMDtcbiAgfVxuXG4gIHJldHVybiBuZXdNYXRyaXg7XG59XG4vKipcbiAqIEBtZW1iZXJvZiBNYXRyaXhcbiAqL1xuXG5mdW5jdGlvbiBjb252ZXJ0UG9zaXRpb25NYXRyaXgobWF0cml4LCBuKSB7XG4gIHZhciBuZXdNYXRyaXggPSBtYXRyaXguc2xpY2UoKTtcblxuICBmb3IgKHZhciBpID0gbWF0cml4Lmxlbmd0aDsgaSA8IG4gLSAxOyArK2kpIHtcbiAgICBuZXdNYXRyaXhbaV0gPSAwO1xuICB9XG5cbiAgbmV3TWF0cml4W24gLSAxXSA9IDE7XG4gIHJldHVybiBuZXdNYXRyaXg7XG59XG4vKipcbiAqIEBtZW1iZXJvZiBNYXRyaXhcbiAqL1xuXG5mdW5jdGlvbiBjb252ZXJ0RGltZW5zaW9uKG1hdHJpeCwgbiwgbSkge1xuICBpZiAobiA9PT0gdm9pZCAwKSB7XG4gICAgbiA9IE1hdGguc3FydChtYXRyaXgubGVuZ3RoKTtcbiAgfSAvLyBuIDwgbVxuXG5cbiAgaWYgKG4gPT09IG0pIHtcbiAgICByZXR1cm4gbWF0cml4O1xuICB9XG5cbiAgdmFyIG5ld01hdHJpeCA9IGNyZWF0ZUlkZW50aXR5TWF0cml4KG0pO1xuICB2YXIgbGVuZ3RoID0gTWF0aC5taW4obiwgbSk7XG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBsZW5ndGggLSAxOyArK2kpIHtcbiAgICBmb3IgKHZhciBqID0gMDsgaiA8IGxlbmd0aCAtIDE7ICsraikge1xuICAgICAgbmV3TWF0cml4W2kgKiBtICsgal0gPSBtYXRyaXhbaSAqIG4gKyBqXTtcbiAgICB9XG5cbiAgICBuZXdNYXRyaXhbKGkgKyAxKSAqIG0gLSAxXSA9IG1hdHJpeFsoaSArIDEpICogbiAtIDFdO1xuICAgIG5ld01hdHJpeFsobSAtIDEpICogbSArIGldID0gbWF0cml4WyhuIC0gMSkgKiBuICsgaV07XG4gIH1cblxuICBuZXdNYXRyaXhbbSAqIG0gLSAxXSA9IG1hdHJpeFtuICogbiAtIDFdO1xuICByZXR1cm4gbmV3TWF0cml4O1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gbXVsdGlwbGllcyhuKSB7XG4gIHZhciBtYXRyaXhlcyA9IFtdO1xuXG4gIGZvciAodmFyIF9pID0gMTsgX2kgPCBhcmd1bWVudHMubGVuZ3RoOyBfaSsrKSB7XG4gICAgbWF0cml4ZXNbX2kgLSAxXSA9IGFyZ3VtZW50c1tfaV07XG4gIH1cblxuICB2YXIgbSA9IGNyZWF0ZUlkZW50aXR5TWF0cml4KG4pO1xuICBtYXRyaXhlcy5mb3JFYWNoKGZ1bmN0aW9uIChtYXRyaXgpIHtcbiAgICBtID0gbXVsdGlwbHkobSwgbWF0cml4LCBuKTtcbiAgfSk7XG4gIHJldHVybiBtO1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gbXVsdGlwbHkobWF0cml4LCBtYXRyaXgyLCBuKSB7XG4gIGlmIChuID09PSB2b2lkIDApIHtcbiAgICBuID0gTWF0aC5zcXJ0KG1hdHJpeC5sZW5ndGgpO1xuICB9XG5cbiAgdmFyIG5ld01hdHJpeCA9IFtdOyAvLyAxIHk6IG5cbiAgLy8gMSB4OiBtXG4gIC8vIDIgeDogbVxuICAvLyAyIHk6IGtcbiAgLy8gbiAqIG0gWCBtICoga1xuXG4gIHZhciBtID0gbWF0cml4Lmxlbmd0aCAvIG47XG4gIHZhciBrID0gbWF0cml4Mi5sZW5ndGggLyBtO1xuXG4gIGlmICghbSkge1xuICAgIHJldHVybiBtYXRyaXgyO1xuICB9IGVsc2UgaWYgKCFrKSB7XG4gICAgcmV0dXJuIG1hdHJpeDtcbiAgfVxuXG4gIGZvciAodmFyIGkgPSAwOyBpIDwgbjsgKytpKSB7XG4gICAgZm9yICh2YXIgaiA9IDA7IGogPCBrOyArK2opIHtcbiAgICAgIG5ld01hdHJpeFtqICogbiArIGldID0gMDtcblxuICAgICAgZm9yICh2YXIgbCA9IDA7IGwgPCBtOyArK2wpIHtcbiAgICAgICAgLy8gbTEgeDogbShsKSwgeTogbihpKVxuICAgICAgICAvLyBtMiB4OiBrKGopOiAgeTogbShsKVxuICAgICAgICAvLyBudyB4OiBuKGkpLCB5OiBrKGopXG4gICAgICAgIG5ld01hdHJpeFtqICogbiArIGldICs9IG1hdHJpeFtsICogbiArIGldICogbWF0cml4MltqICogbSArIGxdO1xuICAgICAgfVxuICAgIH1cbiAgfSAvLyBuICoga1xuXG5cbiAgcmV0dXJuIG5ld01hdHJpeDtcbn1cbi8qKlxuICogQG1lbWJlcm9mIE1hdHJpeFxuICovXG5cbmZ1bmN0aW9uIHBsdXMocG9zMSwgcG9zMikge1xuICB2YXIgbGVuZ3RoID0gTWF0aC5taW4ocG9zMS5sZW5ndGgsIHBvczIubGVuZ3RoKTtcbiAgdmFyIG5leHRQb3MgPSBwb3MxLnNsaWNlKCk7XG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBsZW5ndGg7ICsraSkge1xuICAgIG5leHRQb3NbaV0gPSBuZXh0UG9zW2ldICsgcG9zMltpXTtcbiAgfVxuXG4gIHJldHVybiBuZXh0UG9zO1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gbWludXMocG9zMSwgcG9zMikge1xuICB2YXIgbGVuZ3RoID0gTWF0aC5taW4ocG9zMS5sZW5ndGgsIHBvczIubGVuZ3RoKTtcbiAgdmFyIG5leHRQb3MgPSBwb3MxLnNsaWNlKCk7XG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBsZW5ndGg7ICsraSkge1xuICAgIG5leHRQb3NbaV0gPSBuZXh0UG9zW2ldIC0gcG9zMltpXTtcbiAgfVxuXG4gIHJldHVybiBuZXh0UG9zO1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gY29udmVydENTU3RvTWF0cml4KGEsIGlzMmQpIHtcbiAgaWYgKGlzMmQgPT09IHZvaWQgMCkge1xuICAgIGlzMmQgPSBhLmxlbmd0aCA9PT0gNjtcbiAgfVxuXG4gIGlmIChpczJkKSB7XG4gICAgcmV0dXJuIFthWzBdLCBhWzFdLCAwLCBhWzJdLCBhWzNdLCAwLCBhWzRdLCBhWzVdLCAxXTtcbiAgfVxuXG4gIHJldHVybiBhO1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gY29udmVydE1hdHJpeHRvQ1NTKGEsIGlzMmQpIHtcbiAgaWYgKGlzMmQgPT09IHZvaWQgMCkge1xuICAgIGlzMmQgPSBhLmxlbmd0aCA9PT0gOTtcbiAgfVxuXG4gIGlmIChpczJkKSB7XG4gICAgcmV0dXJuIFthWzBdLCBhWzFdLCBhWzNdLCBhWzRdLCBhWzZdLCBhWzddXTtcbiAgfVxuXG4gIHJldHVybiBhO1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gY2FsY3VsYXRlKG1hdHJpeCwgbWF0cml4Miwgbikge1xuICBpZiAobiA9PT0gdm9pZCAwKSB7XG4gICAgbiA9IG1hdHJpeDIubGVuZ3RoO1xuICB9XG5cbiAgdmFyIHJlc3VsdCA9IG11bHRpcGx5KG1hdHJpeCwgbWF0cml4Miwgbik7XG4gIHZhciBrID0gcmVzdWx0W24gLSAxXTtcbiAgcmV0dXJuIHJlc3VsdC5tYXAoZnVuY3Rpb24gKHYpIHtcbiAgICByZXR1cm4gdiAvIGs7XG4gIH0pO1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gcm90YXRlWDNkKG1hdHJpeCwgcmFkKSB7XG4gIHJldHVybiBtdWx0aXBseShtYXRyaXgsIFsxLCAwLCAwLCAwLCAwLCBNYXRoLmNvcyhyYWQpLCBNYXRoLnNpbihyYWQpLCAwLCAwLCAtTWF0aC5zaW4ocmFkKSwgTWF0aC5jb3MocmFkKSwgMCwgMCwgMCwgMCwgMV0sIDQpO1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gcm90YXRlWTNkKG1hdHJpeCwgcmFkKSB7XG4gIHJldHVybiBtdWx0aXBseShtYXRyaXgsIFtNYXRoLmNvcyhyYWQpLCAwLCAtTWF0aC5zaW4ocmFkKSwgMCwgMCwgMSwgMCwgMCwgTWF0aC5zaW4ocmFkKSwgMCwgTWF0aC5jb3MocmFkKSwgMCwgMCwgMCwgMCwgMV0sIDQpO1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gcm90YXRlWjNkKG1hdHJpeCwgcmFkKSB7XG4gIHJldHVybiBtdWx0aXBseShtYXRyaXgsIGNyZWF0ZVJvdGF0ZU1hdHJpeChyYWQsIDQpKTtcbn1cbi8qKlxuICogQG1lbWJlcm9mIE1hdHJpeFxuICovXG5cbmZ1bmN0aW9uIHNjYWxlM2QobWF0cml4LCBfYSkge1xuICB2YXIgX2IgPSBfYVswXSxcbiAgICAgIHN4ID0gX2IgPT09IHZvaWQgMCA/IDEgOiBfYixcbiAgICAgIF9jID0gX2FbMV0sXG4gICAgICBzeSA9IF9jID09PSB2b2lkIDAgPyAxIDogX2MsXG4gICAgICBfZCA9IF9hWzJdLFxuICAgICAgc3ogPSBfZCA9PT0gdm9pZCAwID8gMSA6IF9kO1xuICByZXR1cm4gbXVsdGlwbHkobWF0cml4LCBbc3gsIDAsIDAsIDAsIDAsIHN5LCAwLCAwLCAwLCAwLCBzeiwgMCwgMCwgMCwgMCwgMV0sIDQpO1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gcm90YXRlKHBvcywgcmFkKSB7XG4gIHJldHVybiBjYWxjdWxhdGUoY3JlYXRlUm90YXRlTWF0cml4KHJhZCwgMyksIGNvbnZlcnRQb3NpdGlvbk1hdHJpeChwb3MsIDMpKTtcbn1cbi8qKlxuICogQG1lbWJlcm9mIE1hdHJpeFxuICovXG5cbmZ1bmN0aW9uIHRyYW5zbGF0ZTNkKG1hdHJpeCwgX2EpIHtcbiAgdmFyIF9iID0gX2FbMF0sXG4gICAgICB0eCA9IF9iID09PSB2b2lkIDAgPyAwIDogX2IsXG4gICAgICBfYyA9IF9hWzFdLFxuICAgICAgdHkgPSBfYyA9PT0gdm9pZCAwID8gMCA6IF9jLFxuICAgICAgX2QgPSBfYVsyXSxcbiAgICAgIHR6ID0gX2QgPT09IHZvaWQgMCA/IDAgOiBfZDtcbiAgcmV0dXJuIG11bHRpcGx5KG1hdHJpeCwgWzEsIDAsIDAsIDAsIDAsIDEsIDAsIDAsIDAsIDAsIDEsIDAsIHR4LCB0eSwgdHosIDFdLCA0KTtcbn1cbi8qKlxuICogQG1lbWJlcm9mIE1hdHJpeFxuICovXG5cbmZ1bmN0aW9uIG1hdHJpeDNkKG1hdHJpeDEsIG1hdHJpeDIpIHtcbiAgcmV0dXJuIG11bHRpcGx5KG1hdHJpeDEsIG1hdHJpeDIsIDQpO1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gY3JlYXRlUm90YXRlTWF0cml4KHJhZCwgbikge1xuICB2YXIgY29zID0gTWF0aC5jb3MocmFkKTtcbiAgdmFyIHNpbiA9IE1hdGguc2luKHJhZCk7XG4gIHZhciBtID0gY3JlYXRlSWRlbnRpdHlNYXRyaXgobik7IC8vIGNvcyAtc2luXG4gIC8vIHNpbiBjb3NcblxuICBtWzBdID0gY29zO1xuICBtWzFdID0gc2luO1xuICBtW25dID0gLXNpbjtcbiAgbVtuICsgMV0gPSBjb3M7XG4gIHJldHVybiBtO1xufVxuLyoqXG4gKiBAbWVtYmVyb2YgTWF0cml4XG4gKi9cblxuZnVuY3Rpb24gY3JlYXRlSWRlbnRpdHlNYXRyaXgobikge1xuICB2YXIgbGVuZ3RoID0gbiAqIG47XG4gIHZhciBtYXRyaXggPSBbXTtcblxuICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbmd0aDsgKytpKSB7XG4gICAgbWF0cml4W2ldID0gaSAlIChuICsgMSkgPyAwIDogMTtcbiAgfVxuXG4gIHJldHVybiBtYXRyaXg7XG59XG4vKipcbiAqIEBtZW1iZXJvZiBNYXRyaXhcbiAqL1xuXG5mdW5jdGlvbiBjcmVhdGVTY2FsZU1hdHJpeChzY2FsZSwgbikge1xuICB2YXIgbSA9IGNyZWF0ZUlkZW50aXR5TWF0cml4KG4pO1xuICB2YXIgbGVuZ3RoID0gTWF0aC5taW4oc2NhbGUubGVuZ3RoLCBuIC0gMSk7XG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCBsZW5ndGg7ICsraSkge1xuICAgIG1bKG4gKyAxKSAqIGldID0gc2NhbGVbaV07XG4gIH1cblxuICByZXR1cm4gbTtcbn1cbi8qKlxuICogQG1lbWJlcm9mIE1hdHJpeFxuICovXG5cbmZ1bmN0aW9uIGNyZWF0ZU9yaWdpbk1hdHJpeChvcmlnaW4sIG4pIHtcbiAgdmFyIG0gPSBjcmVhdGVJZGVudGl0eU1hdHJpeChuKTtcbiAgdmFyIGxlbmd0aCA9IE1hdGgubWluKG9yaWdpbi5sZW5ndGgsIG4gLSAxKTtcblxuICBmb3IgKHZhciBpID0gMDsgaSA8IGxlbmd0aDsgKytpKSB7XG4gICAgbVtuICogKG4gLSAxKSArIGldID0gb3JpZ2luW2ldO1xuICB9XG5cbiAgcmV0dXJuIG07XG59XG4vKipcbiAqIEBtZW1iZXJvZiBNYXRyaXhcbiAqL1xuXG5mdW5jdGlvbiBjcmVhdGVXYXJwTWF0cml4KHBvczAsIHBvczEsIHBvczIsIHBvczMsIG5leHRQb3MwLCBuZXh0UG9zMSwgbmV4dFBvczIsIG5leHRQb3MzKSB7XG4gIHZhciB4MCA9IHBvczBbMF0sXG4gICAgICB5MCA9IHBvczBbMV07XG4gIHZhciB4MSA9IHBvczFbMF0sXG4gICAgICB5MSA9IHBvczFbMV07XG4gIHZhciB4MiA9IHBvczJbMF0sXG4gICAgICB5MiA9IHBvczJbMV07XG4gIHZhciB4MyA9IHBvczNbMF0sXG4gICAgICB5MyA9IHBvczNbMV07XG4gIHZhciB1MCA9IG5leHRQb3MwWzBdLFxuICAgICAgdjAgPSBuZXh0UG9zMFsxXTtcbiAgdmFyIHUxID0gbmV4dFBvczFbMF0sXG4gICAgICB2MSA9IG5leHRQb3MxWzFdO1xuICB2YXIgdTIgPSBuZXh0UG9zMlswXSxcbiAgICAgIHYyID0gbmV4dFBvczJbMV07XG4gIHZhciB1MyA9IG5leHRQb3MzWzBdLFxuICAgICAgdjMgPSBuZXh0UG9zM1sxXTtcbiAgdmFyIG1hdHJpeCA9IFt4MCwgMCwgeDEsIDAsIHgyLCAwLCB4MywgMCwgeTAsIDAsIHkxLCAwLCB5MiwgMCwgeTMsIDAsIDEsIDAsIDEsIDAsIDEsIDAsIDEsIDAsIDAsIHgwLCAwLCB4MSwgMCwgeDIsIDAsIHgzLCAwLCB5MCwgMCwgeTEsIDAsIHkyLCAwLCB5MywgMCwgMSwgMCwgMSwgMCwgMSwgMCwgMSwgLXUwICogeDAsIC12MCAqIHgwLCAtdTEgKiB4MSwgLXYxICogeDEsIC11MiAqIHgyLCAtdjIgKiB4MiwgLXUzICogeDMsIC12MyAqIHgzLCAtdTAgKiB5MCwgLXYwICogeTAsIC11MSAqIHkxLCAtdjEgKiB5MSwgLXUyICogeTIsIC12MiAqIHkyLCAtdTMgKiB5MywgLXYzICogeTNdO1xuICB2YXIgaW52ZXJzZU1hdHJpeCA9IGludmVydChtYXRyaXgsIDgpO1xuXG4gIGlmICghaW52ZXJzZU1hdHJpeC5sZW5ndGgpIHtcbiAgICByZXR1cm4gW107XG4gIH1cblxuICB2YXIgaCA9IG11bHRpcGx5KGludmVyc2VNYXRyaXgsIFt1MCwgdjAsIHUxLCB2MSwgdTIsIHYyLCB1MywgdjNdLCA4KTtcbiAgaFs4XSA9IDE7XG4gIHJldHVybiBjb252ZXJ0RGltZW5zaW9uKHRyYW5zcG9zZShoKSwgMywgNCk7XG59XG4vKipcbiAqIEBtZW1iZXJvZiBNYXRyaXhcbiAqL1xuXG5mdW5jdGlvbiBnZXRDZW50ZXIocG9pbnRzKSB7XG4gIHJldHVybiBbMCwgMV0ubWFwKGZ1bmN0aW9uIChpKSB7XG4gICAgcmV0dXJuIGF2ZXJhZ2UocG9pbnRzLm1hcChmdW5jdGlvbiAocG9zKSB7XG4gICAgICByZXR1cm4gcG9zW2ldO1xuICAgIH0pKTtcbiAgfSk7XG59XG5cbmV4cG9ydCB7IGNhbGN1bGF0ZSwgY29udmVydENTU3RvTWF0cml4LCBjb252ZXJ0RGltZW5zaW9uLCBjb252ZXJ0TWF0cml4dG9DU1MsIGNvbnZlcnRQb3NpdGlvbk1hdHJpeCwgY3JlYXRlSWRlbnRpdHlNYXRyaXgsIGNyZWF0ZU9yaWdpbk1hdHJpeCwgY3JlYXRlUm90YXRlTWF0cml4LCBjcmVhdGVTY2FsZU1hdHJpeCwgY3JlYXRlV2FycE1hdHJpeCwgZnJvbVRyYW5zbGF0aW9uLCBnZXRDZW50ZXIsIGdldE9yaWdpbiwgaWdub3JlRGltZW5zaW9uLCBpbnZlcnQsIG1hdHJpeDNkLCBtaW51cywgbXVsdGlwbGllcywgbXVsdGlwbHksIHBsdXMsIHJvdGF0ZSwgcm90YXRlWDNkLCByb3RhdGVZM2QsIHJvdGF0ZVozZCwgc2NhbGUzZCwgdHJhbnNsYXRlM2QsIHRyYW5zcG9zZSB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWF0cml4LmVzbS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@scena+matrix@1.1.1/node_modules/@scena/matrix/dist/matrix.esm.js\n");

/***/ })

};
;