"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   \"default\": () => (/* binding */ Image),\n/* harmony export */   inputRegex: () => (/* binding */ inputRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * Matches an image to a ![image](src \"title\") on input.\n */\nconst inputRegex = /(?:^|\\s)(!\\[(.+|:?)]\\((\\S+)(?:(?:\\s+)[\"'](\\S+)[\"'])?\\))$/;\n/**\n * This extension allows you to insert images.\n * @see https://www.tiptap.dev/api/nodes/image\n */\nconst Image = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'image',\n    addOptions() {\n        return {\n            inline: false,\n            allowBase64: false,\n            HTMLAttributes: {},\n        };\n    },\n    inline() {\n        return this.options.inline;\n    },\n    group() {\n        return this.options.inline ? 'inline' : 'block';\n    },\n    draggable: true,\n    addAttributes() {\n        return {\n            src: {\n                default: null,\n            },\n            alt: {\n                default: null,\n            },\n            title: {\n                default: null,\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: this.options.allowBase64\n                    ? 'img[src]'\n                    : 'img[src]:not([src^=\"data:\"])',\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['img', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes)];\n    },\n    addCommands() {\n        return {\n            setImage: options => ({ commands }) => {\n                return commands.insertContent({\n                    type: this.name,\n                    attrs: options,\n                });\n            },\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.nodeInputRule)({\n                find: inputRegex,\n                type: this.type,\n                getAttributes: match => {\n                    const [, , alt, src, title] = match;\n                    return { src, alt, title };\n                },\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-image@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-image/dist/index.js\n");

/***/ })

};
;