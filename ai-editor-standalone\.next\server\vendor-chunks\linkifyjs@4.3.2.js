"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/linkifyjs@4.3.2";
exports.ids = ["vendor-chunks/linkifyjs@4.3.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/linkifyjs@4.3.2/node_modules/linkifyjs/dist/linkify.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/linkifyjs@4.3.2/node_modules/linkifyjs/dist/linkify.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultiToken: () => (/* binding */ MultiToken),\n/* harmony export */   Options: () => (/* binding */ Options),\n/* harmony export */   State: () => (/* binding */ State),\n/* harmony export */   createTokenClass: () => (/* binding */ createTokenClass),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   init: () => (/* binding */ init),\n/* harmony export */   multi: () => (/* binding */ multi),\n/* harmony export */   options: () => (/* binding */ options),\n/* harmony export */   regexp: () => (/* binding */ regexp),\n/* harmony export */   registerCustomProtocol: () => (/* binding */ registerCustomProtocol),\n/* harmony export */   registerPlugin: () => (/* binding */ registerPlugin),\n/* harmony export */   registerTokenPlugin: () => (/* binding */ registerTokenPlugin),\n/* harmony export */   reset: () => (/* binding */ reset),\n/* harmony export */   stringToArray: () => (/* binding */ stringToArray),\n/* harmony export */   test: () => (/* binding */ test),\n/* harmony export */   text: () => (/* binding */ multi),\n/* harmony export */   tokenize: () => (/* binding */ tokenize)\n/* harmony export */ });\n// THIS FILE IS AUTOMATICALLY GENERATED DO NOT EDIT DIRECTLY\n// See update-tlds.js for encoding/decoding format\n// https://data.iana.org/TLD/tlds-alpha-by-domain.txt\nconst encodedTlds = 'aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0axi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2';\n// Internationalized domain names containing non-ASCII\nconst encodedUtlds = 'ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2';\n\n/**\n * Finite State Machine generation utilities\n */\n\n/**\n * @template T\n * @typedef {{ [group: string]: T[] }} Collections\n */\n\n/**\n * @typedef {{ [group: string]: true }} Flags\n */\n\n// Keys in scanner Collections instances\nconst numeric = 'numeric';\nconst ascii = 'ascii';\nconst alpha = 'alpha';\nconst asciinumeric = 'asciinumeric';\nconst alphanumeric = 'alphanumeric';\nconst domain = 'domain';\nconst emoji = 'emoji';\nconst scheme = 'scheme';\nconst slashscheme = 'slashscheme';\nconst whitespace = 'whitespace';\n\n/**\n * @template T\n * @param {string} name\n * @param {Collections<T>} groups to register in\n * @returns {T[]} Current list of tokens in the given collection\n */\nfunction registerGroup(name, groups) {\n  if (!(name in groups)) {\n    groups[name] = [];\n  }\n  return groups[name];\n}\n\n/**\n * @template T\n * @param {T} t token to add\n * @param {Collections<T>} groups\n * @param {Flags} flags\n */\nfunction addToGroups(t, flags, groups) {\n  if (flags[numeric]) {\n    flags[asciinumeric] = true;\n    flags[alphanumeric] = true;\n  }\n  if (flags[ascii]) {\n    flags[asciinumeric] = true;\n    flags[alpha] = true;\n  }\n  if (flags[asciinumeric]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alpha]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alphanumeric]) {\n    flags[domain] = true;\n  }\n  if (flags[emoji]) {\n    flags[domain] = true;\n  }\n  for (const k in flags) {\n    const group = registerGroup(k, groups);\n    if (group.indexOf(t) < 0) {\n      group.push(t);\n    }\n  }\n}\n\n/**\n * @template T\n * @param {T} t token to check\n * @param {Collections<T>} groups\n * @returns {Flags} group flags that contain this token\n */\nfunction flagsForToken(t, groups) {\n  const result = {};\n  for (const c in groups) {\n    if (groups[c].indexOf(t) >= 0) {\n      result[c] = true;\n    }\n  }\n  return result;\n}\n\n/**\n * @template T\n * @typedef {null | T } Transition\n */\n\n/**\n * Define a basic state machine state. j is the list of character transitions,\n * jr is the list of regex-match transitions, jd is the default state to\n * transition to t is the accepting token type, if any. If this is the terminal\n * state, then it does not emit a token.\n *\n * The template type T represents the type of the token this state accepts. This\n * should be a string (such as of the token exports in `text.js`) or a\n * MultiToken subclass (from `multi.js`)\n *\n * @template T\n * @param {T} [token] Token that this state emits\n */\nfunction State(token = null) {\n  // this.n = null; // DEBUG: State name\n  /** @type {{ [input: string]: State<T> }} j */\n  this.j = {}; // IMPLEMENTATION 1\n  // this.j = []; // IMPLEMENTATION 2\n  /** @type {[RegExp, State<T>][]} jr */\n  this.jr = [];\n  /** @type {?State<T>} jd */\n  this.jd = null;\n  /** @type {?T} t */\n  this.t = token;\n}\n\n/**\n * Scanner token groups\n * @type Collections<string>\n */\nState.groups = {};\nState.prototype = {\n  accepts() {\n    return !!this.t;\n  },\n  /**\n   * Follow an existing transition from the given input to the next state.\n   * Does not mutate.\n   * @param {string} input character or token type to transition on\n   * @returns {?State<T>} the next state, if any\n   */\n  go(input) {\n    const state = this;\n    const nextState = state.j[input];\n    if (nextState) {\n      return nextState;\n    }\n    for (let i = 0; i < state.jr.length; i++) {\n      const regex = state.jr[i][0];\n      const nextState = state.jr[i][1]; // note: might be empty to prevent default jump\n      if (nextState && regex.test(input)) {\n        return nextState;\n      }\n    }\n    // Nowhere left to jump! Return default, if any\n    return state.jd;\n  },\n  /**\n   * Whether the state has a transition for the given input. Set the second\n   * argument to true to only look for an exact match (and not a default or\n   * regular-expression-based transition)\n   * @param {string} input\n   * @param {boolean} exactOnly\n   */\n  has(input, exactOnly = false) {\n    return exactOnly ? input in this.j : !!this.go(input);\n  },\n  /**\n   * Short for \"transition all\"; create a transition from the array of items\n   * in the given list to the same final resulting state.\n   * @param {string | string[]} inputs Group of inputs to transition on\n   * @param {Transition<T> | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   */\n  ta(inputs, next, flags, groups) {\n    for (let i = 0; i < inputs.length; i++) {\n      this.tt(inputs[i], next, flags, groups);\n    }\n  },\n  /**\n   * Short for \"take regexp transition\"; defines a transition for this state\n   * when it encounters a token which matches the given regular expression\n   * @param {RegExp} regexp Regular expression transition (populate first)\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  tr(regexp, next, flags, groups) {\n    groups = groups || State.groups;\n    let nextState;\n    if (next && next.j) {\n      nextState = next;\n    } else {\n      // Token with maybe token groups\n      nextState = new State(next);\n      if (flags && groups) {\n        addToGroups(next, flags, groups);\n      }\n    }\n    this.jr.push([regexp, nextState]);\n    return nextState;\n  },\n  /**\n   * Short for \"take transitions\", will take as many sequential transitions as\n   * the length of the given input and returns the\n   * resulting final state.\n   * @param {string | string[]} input\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  ts(input, next, flags, groups) {\n    let state = this;\n    const len = input.length;\n    if (!len) {\n      return state;\n    }\n    for (let i = 0; i < len - 1; i++) {\n      state = state.tt(input[i]);\n    }\n    return state.tt(input[len - 1], next, flags, groups);\n  },\n  /**\n   * Short for \"take transition\", this is a method for building/working with\n   * state machines.\n   *\n   * If a state already exists for the given input, returns it.\n   *\n   * If a token is specified, that state will emit that token when reached by\n   * the linkify engine.\n   *\n   * If no state exists, it will be initialized with some default transitions\n   * that resemble existing default transitions.\n   *\n   * If a state is given for the second argument, that state will be\n   * transitioned to on the given input regardless of what that input\n   * previously did.\n   *\n   * Specify a token group flags to define groups that this token belongs to.\n   * The token will be added to corresponding entires in the given groups\n   * object.\n   *\n   * @param {string} input character, token type to transition on\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of groups\n   * @returns {State<T>} taken after the given input\n   */\n  tt(input, next, flags, groups) {\n    groups = groups || State.groups;\n    const state = this;\n\n    // Check if existing state given, just a basic transition\n    if (next && next.j) {\n      state.j[input] = next;\n      return next;\n    }\n    const t = next;\n\n    // Take the transition with the usual default mechanisms and use that as\n    // a template for creating the next state\n    let nextState,\n      templateState = state.go(input);\n    if (templateState) {\n      nextState = new State();\n      Object.assign(nextState.j, templateState.j);\n      nextState.jr.push.apply(nextState.jr, templateState.jr);\n      nextState.jd = templateState.jd;\n      nextState.t = templateState.t;\n    } else {\n      nextState = new State();\n    }\n    if (t) {\n      // Ensure newly token is in the same groups as the old token\n      if (groups) {\n        if (nextState.t && typeof nextState.t === 'string') {\n          const allFlags = Object.assign(flagsForToken(nextState.t, groups), flags);\n          addToGroups(t, allFlags, groups);\n        } else if (flags) {\n          addToGroups(t, flags, groups);\n        }\n      }\n      nextState.t = t; // overwrite anything that was previously there\n    }\n    state.j[input] = nextState;\n    return nextState;\n  }\n};\n\n// Helper functions to improve minification (not exported outside linkifyjs module)\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ta = (state, input, next, flags, groups) => state.ta(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {RegExp} regexp\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst tr = (state, regexp, next, flags, groups) => state.tr(regexp, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ts = (state, input, next, flags, groups) => state.ts(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string} input\n * @param {T | State<T>} [next]\n * @param {Collections<T>} [groups]\n * @param {Flags} [flags]\n */\nconst tt = (state, input, next, flags, groups) => state.tt(input, next, flags, groups);\n\n/******************************************************************************\nText Tokens\nIdentifiers for token outputs from the regexp scanner\n******************************************************************************/\n\n// A valid web domain token\nconst WORD = 'WORD'; // only contains a-z\nconst UWORD = 'UWORD'; // contains letters other than a-z, used for IDN\nconst ASCIINUMERICAL = 'ASCIINUMERICAL'; // contains a-z, 0-9\nconst ALPHANUMERICAL = 'ALPHANUMERICAL'; // contains numbers and letters other than a-z, used for IDN\n\n// Special case of word\nconst LOCALHOST = 'LOCALHOST';\n\n// Valid top-level domain, special case of WORD (see tlds.js)\nconst TLD = 'TLD';\n\n// Valid IDN TLD, special case of UWORD (see tlds.js)\nconst UTLD = 'UTLD';\n\n// The scheme portion of a web URI protocol. Supported types include: `mailto`,\n// `file`, and user-defined custom protocols. Limited to schemes that contain\n// only letters\nconst SCHEME = 'SCHEME';\n\n// Similar to SCHEME, except makes distinction for schemes that must always be\n// followed by `://`, not just `:`. Supported types include `http`, `https`,\n// `ftp`, `ftps`\nconst SLASH_SCHEME = 'SLASH_SCHEME';\n\n// Any sequence of digits 0-9\nconst NUM = 'NUM';\n\n// Any number of consecutive whitespace characters that are not newline\nconst WS = 'WS';\n\n// New line (unix style)\nconst NL = 'NL'; // \\n\n\n// Opening/closing bracket classes\n// TODO: Rename OPEN -> LEFT and CLOSE -> RIGHT in v5 to fit with Unicode names\n// Also rename angle brackes to LESSTHAN and GREATER THAN\nconst OPENBRACE = 'OPENBRACE'; // {\nconst CLOSEBRACE = 'CLOSEBRACE'; // }\nconst OPENBRACKET = 'OPENBRACKET'; // [\nconst CLOSEBRACKET = 'CLOSEBRACKET'; // ]\nconst OPENPAREN = 'OPENPAREN'; // (\nconst CLOSEPAREN = 'CLOSEPAREN'; // )\nconst OPENANGLEBRACKET = 'OPENANGLEBRACKET'; // <\nconst CLOSEANGLEBRACKET = 'CLOSEANGLEBRACKET'; // >\nconst FULLWIDTHLEFTPAREN = 'FULLWIDTHLEFTPAREN'; // （\nconst FULLWIDTHRIGHTPAREN = 'FULLWIDTHRIGHTPAREN'; // ）\nconst LEFTCORNERBRACKET = 'LEFTCORNERBRACKET'; // 「\nconst RIGHTCORNERBRACKET = 'RIGHTCORNERBRACKET'; // 」\nconst LEFTWHITECORNERBRACKET = 'LEFTWHITECORNERBRACKET'; // 『\nconst RIGHTWHITECORNERBRACKET = 'RIGHTWHITECORNERBRACKET'; // 』\nconst FULLWIDTHLESSTHAN = 'FULLWIDTHLESSTHAN'; // ＜\nconst FULLWIDTHGREATERTHAN = 'FULLWIDTHGREATERTHAN'; // ＞\n\n// Various symbols\nconst AMPERSAND = 'AMPERSAND'; // &\nconst APOSTROPHE = 'APOSTROPHE'; // '\nconst ASTERISK = 'ASTERISK'; // *\nconst AT = 'AT'; // @\nconst BACKSLASH = 'BACKSLASH'; // \\\nconst BACKTICK = 'BACKTICK'; // `\nconst CARET = 'CARET'; // ^\nconst COLON = 'COLON'; // :\nconst COMMA = 'COMMA'; // ,\nconst DOLLAR = 'DOLLAR'; // $\nconst DOT = 'DOT'; // .\nconst EQUALS = 'EQUALS'; // =\nconst EXCLAMATION = 'EXCLAMATION'; // !\nconst HYPHEN = 'HYPHEN'; // -\nconst PERCENT = 'PERCENT'; // %\nconst PIPE = 'PIPE'; // |\nconst PLUS = 'PLUS'; // +\nconst POUND = 'POUND'; // #\nconst QUERY = 'QUERY'; // ?\nconst QUOTE = 'QUOTE'; // \"\nconst FULLWIDTHMIDDLEDOT = 'FULLWIDTHMIDDLEDOT'; // ・\n\nconst SEMI = 'SEMI'; // ;\nconst SLASH = 'SLASH'; // /\nconst TILDE = 'TILDE'; // ~\nconst UNDERSCORE = 'UNDERSCORE'; // _\n\n// Emoji symbol\nconst EMOJI$1 = 'EMOJI';\n\n// Default token - anything that is not one of the above\nconst SYM = 'SYM';\n\nvar tk = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tALPHANUMERICAL: ALPHANUMERICAL,\n\tAMPERSAND: AMPERSAND,\n\tAPOSTROPHE: APOSTROPHE,\n\tASCIINUMERICAL: ASCIINUMERICAL,\n\tASTERISK: ASTERISK,\n\tAT: AT,\n\tBACKSLASH: BACKSLASH,\n\tBACKTICK: BACKTICK,\n\tCARET: CARET,\n\tCLOSEANGLEBRACKET: CLOSEANGLEBRACKET,\n\tCLOSEBRACE: CLOSEBRACE,\n\tCLOSEBRACKET: CLOSEBRACKET,\n\tCLOSEPAREN: CLOSEPAREN,\n\tCOLON: COLON,\n\tCOMMA: COMMA,\n\tDOLLAR: DOLLAR,\n\tDOT: DOT,\n\tEMOJI: EMOJI$1,\n\tEQUALS: EQUALS,\n\tEXCLAMATION: EXCLAMATION,\n\tFULLWIDTHGREATERTHAN: FULLWIDTHGREATERTHAN,\n\tFULLWIDTHLEFTPAREN: FULLWIDTHLEFTPAREN,\n\tFULLWIDTHLESSTHAN: FULLWIDTHLESSTHAN,\n\tFULLWIDTHMIDDLEDOT: FULLWIDTHMIDDLEDOT,\n\tFULLWIDTHRIGHTPAREN: FULLWIDTHRIGHTPAREN,\n\tHYPHEN: HYPHEN,\n\tLEFTCORNERBRACKET: LEFTCORNERBRACKET,\n\tLEFTWHITECORNERBRACKET: LEFTWHITECORNERBRACKET,\n\tLOCALHOST: LOCALHOST,\n\tNL: NL,\n\tNUM: NUM,\n\tOPENANGLEBRACKET: OPENANGLEBRACKET,\n\tOPENBRACE: OPENBRACE,\n\tOPENBRACKET: OPENBRACKET,\n\tOPENPAREN: OPENPAREN,\n\tPERCENT: PERCENT,\n\tPIPE: PIPE,\n\tPLUS: PLUS,\n\tPOUND: POUND,\n\tQUERY: QUERY,\n\tQUOTE: QUOTE,\n\tRIGHTCORNERBRACKET: RIGHTCORNERBRACKET,\n\tRIGHTWHITECORNERBRACKET: RIGHTWHITECORNERBRACKET,\n\tSCHEME: SCHEME,\n\tSEMI: SEMI,\n\tSLASH: SLASH,\n\tSLASH_SCHEME: SLASH_SCHEME,\n\tSYM: SYM,\n\tTILDE: TILDE,\n\tTLD: TLD,\n\tUNDERSCORE: UNDERSCORE,\n\tUTLD: UTLD,\n\tUWORD: UWORD,\n\tWORD: WORD,\n\tWS: WS\n});\n\n// Note that these two Unicode ones expand into a really big one with Babel\nconst ASCII_LETTER = /[a-z]/;\nconst LETTER = /\\p{L}/u; // Any Unicode character with letter data type\nconst EMOJI = /\\p{Emoji}/u; // Any Unicode emoji character\nconst EMOJI_VARIATION$1 = /\\ufe0f/;\nconst DIGIT = /\\d/;\nconst SPACE = /\\s/;\n\nvar regexp = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tASCII_LETTER: ASCII_LETTER,\n\tDIGIT: DIGIT,\n\tEMOJI: EMOJI,\n\tEMOJI_VARIATION: EMOJI_VARIATION$1,\n\tLETTER: LETTER,\n\tSPACE: SPACE\n});\n\n/**\n\tThe scanner provides an interface that takes a string of text as input, and\n\toutputs an array of tokens instances that can be used for easy URL parsing.\n*/\n\nconst CR = '\\r'; // carriage-return character\nconst LF = '\\n'; // line-feed character\nconst EMOJI_VARIATION = '\\ufe0f'; // Variation selector, follows heart and others\nconst EMOJI_JOINER = '\\u200d'; // zero-width joiner\nconst OBJECT_REPLACEMENT = '\\ufffc'; // whitespace placeholder that sometimes appears in rich text editors\n\nlet tlds = null,\n  utlds = null; // don't change so only have to be computed once\n\n/**\n * Scanner output token:\n * - `t` is the token name (e.g., 'NUM', 'EMOJI', 'TLD')\n * - `v` is the value of the token (e.g., '123', '❤️', 'com')\n * - `s` is the start index of the token in the original string\n * - `e` is the end index of the token in the original string\n * @typedef {{t: string, v: string, s: number, e: number}} Token\n */\n\n/**\n * @template T\n * @typedef {{ [collection: string]: T[] }} Collections\n */\n\n/**\n * Initialize the scanner character-based state machine for the given start\n * state\n * @param {[string, boolean][]} customSchemes List of custom schemes, where each\n * item is a length-2 tuple with the first element set to the string scheme, and\n * the second element set to `true` if the `://` after the scheme is optional\n */\nfunction init$2(customSchemes = []) {\n  // Frequently used states (name argument removed during minification)\n  /** @type Collections<string> */\n  const groups = {}; // of tokens\n  State.groups = groups;\n  /** @type State<string> */\n  const Start = new State();\n  if (tlds == null) {\n    tlds = decodeTlds(encodedTlds);\n  }\n  if (utlds == null) {\n    utlds = decodeTlds(encodedUtlds);\n  }\n\n  // States for special URL symbols that accept immediately after start\n  tt(Start, \"'\", APOSTROPHE);\n  tt(Start, '{', OPENBRACE);\n  tt(Start, '}', CLOSEBRACE);\n  tt(Start, '[', OPENBRACKET);\n  tt(Start, ']', CLOSEBRACKET);\n  tt(Start, '(', OPENPAREN);\n  tt(Start, ')', CLOSEPAREN);\n  tt(Start, '<', OPENANGLEBRACKET);\n  tt(Start, '>', CLOSEANGLEBRACKET);\n  tt(Start, '（', FULLWIDTHLEFTPAREN);\n  tt(Start, '）', FULLWIDTHRIGHTPAREN);\n  tt(Start, '「', LEFTCORNERBRACKET);\n  tt(Start, '」', RIGHTCORNERBRACKET);\n  tt(Start, '『', LEFTWHITECORNERBRACKET);\n  tt(Start, '』', RIGHTWHITECORNERBRACKET);\n  tt(Start, '＜', FULLWIDTHLESSTHAN);\n  tt(Start, '＞', FULLWIDTHGREATERTHAN);\n  tt(Start, '&', AMPERSAND);\n  tt(Start, '*', ASTERISK);\n  tt(Start, '@', AT);\n  tt(Start, '`', BACKTICK);\n  tt(Start, '^', CARET);\n  tt(Start, ':', COLON);\n  tt(Start, ',', COMMA);\n  tt(Start, '$', DOLLAR);\n  tt(Start, '.', DOT);\n  tt(Start, '=', EQUALS);\n  tt(Start, '!', EXCLAMATION);\n  tt(Start, '-', HYPHEN);\n  tt(Start, '%', PERCENT);\n  tt(Start, '|', PIPE);\n  tt(Start, '+', PLUS);\n  tt(Start, '#', POUND);\n  tt(Start, '?', QUERY);\n  tt(Start, '\"', QUOTE);\n  tt(Start, '/', SLASH);\n  tt(Start, ';', SEMI);\n  tt(Start, '~', TILDE);\n  tt(Start, '_', UNDERSCORE);\n  tt(Start, '\\\\', BACKSLASH);\n  tt(Start, '・', FULLWIDTHMIDDLEDOT);\n  const Num = tr(Start, DIGIT, NUM, {\n    [numeric]: true\n  });\n  tr(Num, DIGIT, Num);\n  const Asciinumeric = tr(Num, ASCII_LETTER, ASCIINUMERICAL, {\n    [asciinumeric]: true\n  });\n  const Alphanumeric = tr(Num, LETTER, ALPHANUMERICAL, {\n    [alphanumeric]: true\n  });\n\n  // State which emits a word token\n  const Word = tr(Start, ASCII_LETTER, WORD, {\n    [ascii]: true\n  });\n  tr(Word, DIGIT, Asciinumeric);\n  tr(Word, ASCII_LETTER, Word);\n  tr(Asciinumeric, DIGIT, Asciinumeric);\n  tr(Asciinumeric, ASCII_LETTER, Asciinumeric);\n\n  // Same as previous, but specific to non-fsm.ascii alphabet words\n  const UWord = tr(Start, LETTER, UWORD, {\n    [alpha]: true\n  });\n  tr(UWord, ASCII_LETTER); // Non-accepting\n  tr(UWord, DIGIT, Alphanumeric);\n  tr(UWord, LETTER, UWord);\n  tr(Alphanumeric, DIGIT, Alphanumeric);\n  tr(Alphanumeric, ASCII_LETTER); // Non-accepting\n  tr(Alphanumeric, LETTER, Alphanumeric); // Non-accepting\n\n  // Whitespace jumps\n  // Tokens of only non-newline whitespace are arbitrarily long\n  // If any whitespace except newline, more whitespace!\n  const Nl = tt(Start, LF, NL, {\n    [whitespace]: true\n  });\n  const Cr = tt(Start, CR, WS, {\n    [whitespace]: true\n  });\n  const Ws = tr(Start, SPACE, WS, {\n    [whitespace]: true\n  });\n  tt(Start, OBJECT_REPLACEMENT, Ws);\n  tt(Cr, LF, Nl); // \\r\\n\n  tt(Cr, OBJECT_REPLACEMENT, Ws);\n  tr(Cr, SPACE, Ws);\n  tt(Ws, CR); // non-accepting state to avoid mixing whitespaces\n  tt(Ws, LF); // non-accepting state to avoid mixing whitespaces\n  tr(Ws, SPACE, Ws);\n  tt(Ws, OBJECT_REPLACEMENT, Ws);\n\n  // Emoji tokens. They are not grouped by the scanner except in cases where a\n  // zero-width joiner is present\n  const Emoji = tr(Start, EMOJI, EMOJI$1, {\n    [emoji]: true\n  });\n  tt(Emoji, '#'); // no transition, emoji regex seems to match #\n  tr(Emoji, EMOJI, Emoji);\n  tt(Emoji, EMOJI_VARIATION, Emoji);\n  // tt(Start, EMOJI_VARIATION, Emoji); // This one is sketchy\n\n  const EmojiJoiner = tt(Emoji, EMOJI_JOINER);\n  tt(EmojiJoiner, '#');\n  tr(EmojiJoiner, EMOJI, Emoji);\n  // tt(EmojiJoiner, EMOJI_VARIATION, Emoji); // also sketchy\n\n  // Generates states for top-level domains\n  // Note that this is most accurate when tlds are in alphabetical order\n  const wordjr = [[ASCII_LETTER, Word], [DIGIT, Asciinumeric]];\n  const uwordjr = [[ASCII_LETTER, null], [LETTER, UWord], [DIGIT, Alphanumeric]];\n  for (let i = 0; i < tlds.length; i++) {\n    fastts(Start, tlds[i], TLD, WORD, wordjr);\n  }\n  for (let i = 0; i < utlds.length; i++) {\n    fastts(Start, utlds[i], UTLD, UWORD, uwordjr);\n  }\n  addToGroups(TLD, {\n    tld: true,\n    ascii: true\n  }, groups);\n  addToGroups(UTLD, {\n    utld: true,\n    alpha: true\n  }, groups);\n\n  // Collect the states generated by different protocols. NOTE: If any new TLDs\n  // get added that are also protocols, set the token to be the same as the\n  // protocol to ensure parsing works as expected.\n  fastts(Start, 'file', SCHEME, WORD, wordjr);\n  fastts(Start, 'mailto', SCHEME, WORD, wordjr);\n  fastts(Start, 'http', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'https', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftp', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftps', SLASH_SCHEME, WORD, wordjr);\n  addToGroups(SCHEME, {\n    scheme: true,\n    ascii: true\n  }, groups);\n  addToGroups(SLASH_SCHEME, {\n    slashscheme: true,\n    ascii: true\n  }, groups);\n\n  // Register custom schemes. Assumes each scheme is asciinumeric with hyphens\n  customSchemes = customSchemes.sort((a, b) => a[0] > b[0] ? 1 : -1);\n  for (let i = 0; i < customSchemes.length; i++) {\n    const sch = customSchemes[i][0];\n    const optionalSlashSlash = customSchemes[i][1];\n    const flags = optionalSlashSlash ? {\n      [scheme]: true\n    } : {\n      [slashscheme]: true\n    };\n    if (sch.indexOf('-') >= 0) {\n      flags[domain] = true;\n    } else if (!ASCII_LETTER.test(sch)) {\n      flags[numeric] = true; // numbers only\n    } else if (DIGIT.test(sch)) {\n      flags[asciinumeric] = true;\n    } else {\n      flags[ascii] = true;\n    }\n    ts(Start, sch, sch, flags);\n  }\n\n  // Localhost token\n  ts(Start, 'localhost', LOCALHOST, {\n    ascii: true\n  });\n\n  // Set default transition for start state (some symbol)\n  Start.jd = new State(SYM);\n  return {\n    start: Start,\n    tokens: Object.assign({\n      groups\n    }, tk)\n  };\n}\n\n/**\n\tGiven a string, returns an array of TOKEN instances representing the\n\tcomposition of that string.\n\n\t@method run\n\t@param {State<string>} start scanner starting state\n\t@param {string} str input string to scan\n\t@return {Token[]} list of tokens, each with a type and value\n*/\nfunction run$1(start, str) {\n  // State machine is not case sensitive, so input is tokenized in lowercased\n  // form (still returns regular case). Uses selective `toLowerCase` because\n  // lowercasing the entire string causes the length and character position to\n  // vary in some non-English strings with V8-based runtimes.\n  const iterable = stringToArray(str.replace(/[A-Z]/g, c => c.toLowerCase()));\n  const charCount = iterable.length; // <= len if there are emojis, etc\n  const tokens = []; // return value\n\n  // cursor through the string itself, accounting for characters that have\n  // width with length 2 such as emojis\n  let cursor = 0;\n\n  // Cursor through the array-representation of the string\n  let charCursor = 0;\n\n  // Tokenize the string\n  while (charCursor < charCount) {\n    let state = start;\n    let nextState = null;\n    let tokenLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    let charsSinceAccepts = -1;\n    while (charCursor < charCount && (nextState = state.go(iterable[charCursor]))) {\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        charsSinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts += iterable[charCursor].length;\n        charsSinceAccepts++;\n      }\n      tokenLength += iterable[charCursor].length;\n      cursor += iterable[charCursor].length;\n      charCursor++;\n    }\n\n    // Roll back to the latest accepting state\n    cursor -= sinceAccepts;\n    charCursor -= charsSinceAccepts;\n    tokenLength -= sinceAccepts;\n\n    // No more jumps, just make a new token from the last accepting one\n    tokens.push({\n      t: latestAccepting.t,\n      // token type/name\n      v: str.slice(cursor - tokenLength, cursor),\n      // string value\n      s: cursor - tokenLength,\n      // start index\n      e: cursor // end index (excluding)\n    });\n  }\n  return tokens;\n}\n\n/**\n * Convert a String to an Array of characters, taking into account that some\n * characters like emojis take up two string indexes.\n *\n * Adapted from core-js (MIT license)\n * https://github.com/zloirock/core-js/blob/2d69cf5f99ab3ea3463c395df81e5a15b68f49d9/packages/core-js/internals/string-multibyte.js\n *\n * @function stringToArray\n * @param {string} str\n * @returns {string[]}\n */\nfunction stringToArray(str) {\n  const result = [];\n  const len = str.length;\n  let index = 0;\n  while (index < len) {\n    let first = str.charCodeAt(index);\n    let second;\n    let char = first < 0xd800 || first > 0xdbff || index + 1 === len || (second = str.charCodeAt(index + 1)) < 0xdc00 || second > 0xdfff ? str[index] // single character\n    : str.slice(index, index + 2); // two-index characters\n    result.push(char);\n    index += char.length;\n  }\n  return result;\n}\n\n/**\n * Fast version of ts function for when transition defaults are well known\n * @param {State<string>} state\n * @param {string} input\n * @param {string} t\n * @param {string} defaultt\n * @param {[RegExp, State<string>][]} jr\n * @returns {State<string>}\n */\nfunction fastts(state, input, t, defaultt, jr) {\n  let next;\n  const len = input.length;\n  for (let i = 0; i < len - 1; i++) {\n    const char = input[i];\n    if (state.j[char]) {\n      next = state.j[char];\n    } else {\n      next = new State(defaultt);\n      next.jr = jr.slice();\n      state.j[char] = next;\n    }\n    state = next;\n  }\n  next = new State(t);\n  next.jr = jr.slice();\n  state.j[input[len - 1]] = next;\n  return next;\n}\n\n/**\n * Converts a string of Top-Level Domain names encoded in update-tlds.js back\n * into a list of strings.\n * @param {str} encoded encoded TLDs string\n * @returns {str[]} original TLDs list\n */\nfunction decodeTlds(encoded) {\n  const words = [];\n  const stack = [];\n  let i = 0;\n  let digits = '0123456789';\n  while (i < encoded.length) {\n    let popDigitCount = 0;\n    while (digits.indexOf(encoded[i + popDigitCount]) >= 0) {\n      popDigitCount++; // encountered some digits, have to pop to go one level up trie\n    }\n    if (popDigitCount > 0) {\n      words.push(stack.join('')); // whatever preceded the pop digits must be a word\n      for (let popCount = parseInt(encoded.substring(i, i + popDigitCount), 10); popCount > 0; popCount--) {\n        stack.pop();\n      }\n      i += popDigitCount;\n    } else {\n      stack.push(encoded[i]); // drop down a level into the trie\n      i++;\n    }\n  }\n  return words;\n}\n\n/**\n * An object where each key is a valid DOM Event Name such as `click` or `focus`\n * and each value is an event handler function.\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/Element#events\n * @typedef {?{ [event: string]: Function }} EventListeners\n */\n\n/**\n * All formatted properties required to render a link, including `tagName`,\n * `attributes`, `content` and `eventListeners`.\n * @typedef {{ tagName: any, attributes: {[attr: string]: any}, content: string,\n * eventListeners: EventListeners }} IntermediateRepresentation\n */\n\n/**\n * Specify either an object described by the template type `O` or a function.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `O`\n * @template O\n * @typedef {O | ((value: string, type: string, token: MultiToken) => O)} OptObj\n */\n\n/**\n * Specify either a function described by template type `F` or an object.\n *\n * Each key in the object should be a link type (`'url'`, `'hashtag`', etc.). Each\n * value should be a function with template type `F` that is called when the\n * corresponding link type is encountered.\n * @template F\n * @typedef {F | { [type: string]: F}} OptFn\n */\n\n/**\n * Specify either a value with template type `V`, a function that returns `V` or\n * an object where each value resolves to `V`.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `V`\n *\n * For the object, each key should be a link type (`'url'`, `'hashtag`', etc.).\n * Each value should either have type `V` or a function that returns V. This\n * function similarly takes a string value and a token.\n *\n * Example valid types for `Opt<string>`:\n *\n * ```js\n * 'hello'\n * (value, type, token) => 'world'\n * { url: 'hello', email: (value, token) => 'world'}\n * ```\n * @template V\n * @typedef {V | ((value: string, type: string, token: MultiToken) => V) | { [type: string]: V | ((value: string, token: MultiToken) => V) }} Opt\n */\n\n/**\n * See available options: https://linkify.js.org/docs/options.html\n * @typedef {{\n * \tdefaultProtocol?: string,\n *  events?: OptObj<EventListeners>,\n * \tformat?: Opt<string>,\n * \tformatHref?: Opt<string>,\n * \tnl2br?: boolean,\n * \ttagName?: Opt<any>,\n * \ttarget?: Opt<string>,\n * \trel?: Opt<string>,\n * \tvalidate?: Opt<boolean>,\n * \ttruncate?: Opt<number>,\n * \tclassName?: Opt<string>,\n * \tattributes?: OptObj<({ [attr: string]: any })>,\n *  ignoreTags?: string[],\n * \trender?: OptFn<((ir: IntermediateRepresentation) => any)>\n * }} Opts\n */\n\n/**\n * @type Required<Opts>\n */\nconst defaults = {\n  defaultProtocol: 'http',\n  events: null,\n  format: noop,\n  formatHref: noop,\n  nl2br: false,\n  tagName: 'a',\n  target: null,\n  rel: null,\n  validate: true,\n  truncate: Infinity,\n  className: null,\n  attributes: null,\n  ignoreTags: [],\n  render: null\n};\n\n/**\n * Utility class for linkify interfaces to apply specified\n * {@link Opts formatting and rendering options}.\n *\n * @param {Opts | Options} [opts] Option value overrides.\n * @param {(ir: IntermediateRepresentation) => any} [defaultRender] (For\n *   internal use) default render function that determines how to generate an\n *   HTML element based on a link token's derived tagName, attributes and HTML.\n *   Similar to render option\n */\nfunction Options(opts, defaultRender = null) {\n  let o = Object.assign({}, defaults);\n  if (opts) {\n    o = Object.assign(o, opts instanceof Options ? opts.o : opts);\n  }\n\n  // Ensure all ignored tags are uppercase\n  const ignoredTags = o.ignoreTags;\n  const uppercaseIgnoredTags = [];\n  for (let i = 0; i < ignoredTags.length; i++) {\n    uppercaseIgnoredTags.push(ignoredTags[i].toUpperCase());\n  }\n  /** @protected */\n  this.o = o;\n  if (defaultRender) {\n    this.defaultRender = defaultRender;\n  }\n  this.ignoreTags = uppercaseIgnoredTags;\n}\nOptions.prototype = {\n  o: defaults,\n  /**\n   * @type string[]\n   */\n  ignoreTags: [],\n  /**\n   * @param {IntermediateRepresentation} ir\n   * @returns {any}\n   */\n  defaultRender(ir) {\n    return ir;\n  },\n  /**\n   * Returns true or false based on whether a token should be displayed as a\n   * link based on the user options.\n   * @param {MultiToken} token\n   * @returns {boolean}\n   */\n  check(token) {\n    return this.get('validate', token.toString(), token);\n  },\n  // Private methods\n\n  /**\n   * Resolve an option's value based on the value of the option and the given\n   * params. If operator and token are specified and the target option is\n   * callable, automatically calls the function with the given argument.\n   * @template {keyof Opts} K\n   * @param {K} key Name of option to use\n   * @param {string} [operator] will be passed to the target option if it's a\n   * function. If not specified, RAW function value gets returned\n   * @param {MultiToken} [token] The token from linkify.tokenize\n   * @returns {Opts[K] | any}\n   */\n  get(key, operator, token) {\n    const isCallable = operator != null;\n    let option = this.o[key];\n    if (!option) {\n      return option;\n    }\n    if (typeof option === 'object') {\n      option = token.t in option ? option[token.t] : defaults[key];\n      if (typeof option === 'function' && isCallable) {\n        option = option(operator, token);\n      }\n    } else if (typeof option === 'function' && isCallable) {\n      option = option(operator, token.t, token);\n    }\n    return option;\n  },\n  /**\n   * @template {keyof Opts} L\n   * @param {L} key Name of options object to use\n   * @param {string} [operator]\n   * @param {MultiToken} [token]\n   * @returns {Opts[L] | any}\n   */\n  getObj(key, operator, token) {\n    let obj = this.o[key];\n    if (typeof obj === 'function' && operator != null) {\n      obj = obj(operator, token.t, token);\n    }\n    return obj;\n  },\n  /**\n   * Convert the given token to a rendered element that may be added to the\n   * calling-interface's DOM\n   * @param {MultiToken} token Token to render to an HTML element\n   * @returns {any} Render result; e.g., HTML string, DOM element, React\n   *   Component, etc.\n   */\n  render(token) {\n    const ir = token.render(this); // intermediate representation\n    const renderFn = this.get('render', null, token) || this.defaultRender;\n    return renderFn(ir, token.t, token);\n  }\n};\nfunction noop(val) {\n  return val;\n}\n\nvar options = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tOptions: Options,\n\tdefaults: defaults\n});\n\n/******************************************************************************\n\tMulti-Tokens\n\tTokens composed of arrays of TextTokens\n******************************************************************************/\n\n/**\n * @param {string} value\n * @param {Token[]} tokens\n */\nfunction MultiToken(value, tokens) {\n  this.t = 'token';\n  this.v = value;\n  this.tk = tokens;\n}\n\n/**\n * Abstract class used for manufacturing tokens of text tokens. That is rather\n * than the value for a token being a small string of text, it's value an array\n * of text tokens.\n *\n * Used for grouping together URLs, emails, hashtags, and other potential\n * creations.\n * @class MultiToken\n * @property {string} t\n * @property {string} v\n * @property {Token[]} tk\n * @abstract\n */\nMultiToken.prototype = {\n  isLink: false,\n  /**\n   * Return the string this token represents.\n   * @return {string}\n   */\n  toString() {\n    return this.v;\n  },\n  /**\n   * What should the value for this token be in the `href` HTML attribute?\n   * Returns the `.toString` value by default.\n   * @param {string} [scheme]\n   * @return {string}\n   */\n  toHref(scheme) {\n    return this.toString();\n  },\n  /**\n   * @param {Options} options Formatting options\n   * @returns {string}\n   */\n  toFormattedString(options) {\n    const val = this.toString();\n    const truncate = options.get('truncate', val, this);\n    const formatted = options.get('format', val, this);\n    return truncate && formatted.length > truncate ? formatted.substring(0, truncate) + '…' : formatted;\n  },\n  /**\n   *\n   * @param {Options} options\n   * @returns {string}\n   */\n  toFormattedHref(options) {\n    return options.get('formatHref', this.toHref(options.get('defaultProtocol')), this);\n  },\n  /**\n   * The start index of this token in the original input string\n   * @returns {number}\n   */\n  startIndex() {\n    return this.tk[0].s;\n  },\n  /**\n   * The end index of this token in the original input string (up to this\n   * index but not including it)\n   * @returns {number}\n   */\n  endIndex() {\n    return this.tk[this.tk.length - 1].e;\n  },\n  /**\n  \tReturns an object  of relevant values for this token, which includes keys\n  \t* type - Kind of token ('url', 'email', etc.)\n  \t* value - Original text\n  \t* href - The value that should be added to the anchor tag's href\n  \t\tattribute\n  \t\t@method toObject\n  \t@param {string} [protocol] `'http'` by default\n  */\n  toObject(protocol = defaults.defaultProtocol) {\n    return {\n      type: this.t,\n      value: this.toString(),\n      isLink: this.isLink,\n      href: this.toHref(protocol),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   *\n   * @param {Options} options Formatting option\n   */\n  toFormattedObject(options) {\n    return {\n      type: this.t,\n      value: this.toFormattedString(options),\n      isLink: this.isLink,\n      href: this.toFormattedHref(options),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   * Whether this token should be rendered as a link according to the given options\n   * @param {Options} options\n   * @returns {boolean}\n   */\n  validate(options) {\n    return options.get('validate', this.toString(), this);\n  },\n  /**\n   * Return an object that represents how this link should be rendered.\n   * @param {Options} options Formattinng options\n   */\n  render(options) {\n    const token = this;\n    const href = this.toHref(options.get('defaultProtocol'));\n    const formattedHref = options.get('formatHref', href, this);\n    const tagName = options.get('tagName', href, token);\n    const content = this.toFormattedString(options);\n    const attributes = {};\n    const className = options.get('className', href, token);\n    const target = options.get('target', href, token);\n    const rel = options.get('rel', href, token);\n    const attrs = options.getObj('attributes', href, token);\n    const eventListeners = options.getObj('events', href, token);\n    attributes.href = formattedHref;\n    if (className) {\n      attributes.class = className;\n    }\n    if (target) {\n      attributes.target = target;\n    }\n    if (rel) {\n      attributes.rel = rel;\n    }\n    if (attrs) {\n      Object.assign(attributes, attrs);\n    }\n    return {\n      tagName,\n      attributes,\n      content,\n      eventListeners\n    };\n  }\n};\n\n/**\n * Create a new token that can be emitted by the parser state machine\n * @param {string} type readable type of the token\n * @param {object} props properties to assign or override, including isLink = true or false\n * @returns {new (value: string, tokens: Token[]) => MultiToken} new token class\n */\nfunction createTokenClass(type, props) {\n  class Token extends MultiToken {\n    constructor(value, tokens) {\n      super(value, tokens);\n      this.t = type;\n    }\n  }\n  for (const p in props) {\n    Token.prototype[p] = props[p];\n  }\n  Token.t = type;\n  return Token;\n}\n\n/**\n\tRepresents a list of tokens making up a valid email address\n*/\nconst Email = createTokenClass('email', {\n  isLink: true,\n  toHref() {\n    return 'mailto:' + this.toString();\n  }\n});\n\n/**\n\tRepresents some plain text\n*/\nconst Text = createTokenClass('text');\n\n/**\n\tMulti-linebreak token - represents a line break\n\t@class Nl\n*/\nconst Nl = createTokenClass('nl');\n\n/**\n\tRepresents a list of text tokens making up a valid URL\n\t@class Url\n*/\nconst Url = createTokenClass('url', {\n  isLink: true,\n  /**\n  \tLowercases relevant parts of the domain and adds the protocol if\n  \trequired. Note that this will not escape unsafe HTML characters in the\n  \tURL.\n  \t\t@param {string} [scheme] default scheme (e.g., 'https')\n  \t@return {string} the full href\n  */\n  toHref(scheme = defaults.defaultProtocol) {\n    // Check if already has a prefix scheme\n    return this.hasProtocol() ? this.v : `${scheme}://${this.v}`;\n  },\n  /**\n   * Check whether this URL token has a protocol\n   * @return {boolean}\n   */\n  hasProtocol() {\n    const tokens = this.tk;\n    return tokens.length >= 2 && tokens[0].t !== LOCALHOST && tokens[1].t === COLON;\n  }\n});\n\nvar multi = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tBase: MultiToken,\n\tEmail: Email,\n\tMultiToken: MultiToken,\n\tNl: Nl,\n\tText: Text,\n\tUrl: Url,\n\tcreateTokenClass: createTokenClass\n});\n\n/**\n\tNot exactly parser, more like the second-stage scanner (although we can\n\ttheoretically hotswap the code here with a real parser in the future... but\n\tfor a little URL-finding utility abstract syntax trees may be a little\n\toverkill).\n\n\tURL format: http://en.wikipedia.org/wiki/URI_scheme\n\tEmail format: http://en.wikipedia.org/wiki/EmailAddress (links to RFC in\n\treference)\n\n\t@module linkify\n\t@submodule parser\n\t@main run\n*/\n\nconst makeState = arg => new State(arg);\n\n/**\n * Generate the parser multi token-based state machine\n * @param {{ groups: Collections<string> }} tokens\n */\nfunction init$1({\n  groups\n}) {\n  // Types of characters the URL can definitely end in\n  const qsAccepting = groups.domain.concat([AMPERSAND, ASTERISK, AT, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, NUM, PERCENT, PIPE, PLUS, POUND, SLASH, SYM, TILDE, UNDERSCORE]);\n\n  // Types of tokens that can follow a URL and be part of the query string\n  // but cannot be the very last characters\n  // Characters that cannot appear in the URL at all should be excluded\n  const qsNonAccepting = [APOSTROPHE, COLON, COMMA, DOT, EXCLAMATION, PERCENT, QUERY, QUOTE, SEMI, OPENANGLEBRACKET, CLOSEANGLEBRACKET, OPENBRACE, CLOSEBRACE, CLOSEBRACKET, OPENBRACKET, OPENPAREN, CLOSEPAREN, FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN, LEFTCORNERBRACKET, RIGHTCORNERBRACKET, LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET, FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN];\n\n  // For addresses without the mailto prefix\n  // Tokens allowed in the localpart of the email\n  const localpartAccepting = [AMPERSAND, APOSTROPHE, ASTERISK, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, OPENBRACE, CLOSEBRACE, PERCENT, PIPE, PLUS, POUND, QUERY, SLASH, SYM, TILDE, UNDERSCORE];\n\n  // The universal starting state.\n  /**\n   * @type State<Token>\n   */\n  const Start = makeState();\n  const Localpart = tt(Start, TILDE); // Local part of the email address\n  ta(Localpart, localpartAccepting, Localpart);\n  ta(Localpart, groups.domain, Localpart);\n  const Domain = makeState(),\n    Scheme = makeState(),\n    SlashScheme = makeState();\n  ta(Start, groups.domain, Domain); // parsed string ends with a potential domain name (A)\n  ta(Start, groups.scheme, Scheme); // e.g., 'mailto'\n  ta(Start, groups.slashscheme, SlashScheme); // e.g., 'http'\n\n  ta(Domain, localpartAccepting, Localpart);\n  ta(Domain, groups.domain, Domain);\n  const LocalpartAt = tt(Domain, AT); // Local part of the email address plus @\n\n  tt(Localpart, AT, LocalpartAt); // close to an email address now\n\n  // Local part of an email address can be e.g. 'http' or 'mailto'\n  tt(Scheme, AT, LocalpartAt);\n  tt(SlashScheme, AT, LocalpartAt);\n  const LocalpartDot = tt(Localpart, DOT); // Local part of the email address plus '.' (localpart cannot end in .)\n  ta(LocalpartDot, localpartAccepting, Localpart);\n  ta(LocalpartDot, groups.domain, Localpart);\n  const EmailDomain = makeState();\n  ta(LocalpartAt, groups.domain, EmailDomain); // parsed string starts with local email info + @ with a potential domain name\n  ta(EmailDomain, groups.domain, EmailDomain);\n  const EmailDomainDot = tt(EmailDomain, DOT); // domain followed by DOT\n  ta(EmailDomainDot, groups.domain, EmailDomain);\n  const Email$1 = makeState(Email); // Possible email address (could have more tlds)\n  ta(EmailDomainDot, groups.tld, Email$1);\n  ta(EmailDomainDot, groups.utld, Email$1);\n  tt(LocalpartAt, LOCALHOST, Email$1);\n\n  // Hyphen can jump back to a domain name\n  const EmailDomainHyphen = tt(EmailDomain, HYPHEN); // parsed string starts with local email info + @ with a potential domain name\n  tt(EmailDomainHyphen, HYPHEN, EmailDomainHyphen);\n  ta(EmailDomainHyphen, groups.domain, EmailDomain);\n  ta(Email$1, groups.domain, EmailDomain);\n  tt(Email$1, DOT, EmailDomainDot);\n  tt(Email$1, HYPHEN, EmailDomainHyphen);\n\n  // Final possible email states\n  const EmailColon = tt(Email$1, COLON); // URL followed by colon (potential port number here)\n  /*const EmailColonPort = */\n  ta(EmailColon, groups.numeric, Email); // URL followed by colon and port number\n\n  // Account for dots and hyphens. Hyphens are usually parts of domain names\n  // (but not TLDs)\n  const DomainHyphen = tt(Domain, HYPHEN); // domain followed by hyphen\n  const DomainDot = tt(Domain, DOT); // domain followed by DOT\n  tt(DomainHyphen, HYPHEN, DomainHyphen);\n  ta(DomainHyphen, groups.domain, Domain);\n  ta(DomainDot, localpartAccepting, Localpart);\n  ta(DomainDot, groups.domain, Domain);\n  const DomainDotTld = makeState(Url); // Simplest possible URL with no query string\n  ta(DomainDot, groups.tld, DomainDotTld);\n  ta(DomainDot, groups.utld, DomainDotTld);\n  ta(DomainDotTld, groups.domain, Domain);\n  ta(DomainDotTld, localpartAccepting, Localpart);\n  tt(DomainDotTld, DOT, DomainDot);\n  tt(DomainDotTld, HYPHEN, DomainHyphen);\n  tt(DomainDotTld, AT, LocalpartAt);\n  const DomainDotTldColon = tt(DomainDotTld, COLON); // URL followed by colon (potential port number here)\n  const DomainDotTldColonPort = makeState(Url); // TLD followed by a port number\n  ta(DomainDotTldColon, groups.numeric, DomainDotTldColonPort);\n\n  // Long URL with optional port and maybe query string\n  const Url$1 = makeState(Url);\n\n  // URL with extra symbols at the end, followed by an opening bracket\n  const UrlNonaccept = makeState(); // URL followed by some symbols (will not be part of the final URL)\n\n  // Query strings\n  ta(Url$1, qsAccepting, Url$1);\n  ta(Url$1, qsNonAccepting, UrlNonaccept);\n  ta(UrlNonaccept, qsAccepting, Url$1);\n  ta(UrlNonaccept, qsNonAccepting, UrlNonaccept);\n\n  // Become real URLs after `SLASH` or `COLON NUM SLASH`\n  // Here works with or without scheme:// prefix\n  tt(DomainDotTld, SLASH, Url$1);\n  tt(DomainDotTldColonPort, SLASH, Url$1);\n\n  // Note that domains that begin with schemes are treated slighly differently\n  const SchemeColon = tt(Scheme, COLON); // e.g., 'mailto:'\n  const SlashSchemeColon = tt(SlashScheme, COLON); // e.g., 'http:'\n  const SlashSchemeColonSlash = tt(SlashSchemeColon, SLASH); // e.g., 'http:/'\n\n  const UriPrefix = tt(SlashSchemeColonSlash, SLASH); // e.g., 'http://'\n\n  // Scheme states can transition to domain states\n  ta(Scheme, groups.domain, Domain);\n  tt(Scheme, DOT, DomainDot);\n  tt(Scheme, HYPHEN, DomainHyphen);\n  ta(SlashScheme, groups.domain, Domain);\n  tt(SlashScheme, DOT, DomainDot);\n  tt(SlashScheme, HYPHEN, DomainHyphen);\n\n  // Force URL with scheme prefix followed by anything sane\n  ta(SchemeColon, groups.domain, Url$1);\n  tt(SchemeColon, SLASH, Url$1);\n  tt(SchemeColon, QUERY, Url$1);\n  ta(UriPrefix, groups.domain, Url$1);\n  ta(UriPrefix, qsAccepting, Url$1);\n  tt(UriPrefix, SLASH, Url$1);\n  const bracketPairs = [[OPENBRACE, CLOSEBRACE],\n  // {}\n  [OPENBRACKET, CLOSEBRACKET],\n  // []\n  [OPENPAREN, CLOSEPAREN],\n  // ()\n  [OPENANGLEBRACKET, CLOSEANGLEBRACKET],\n  // <>\n  [FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN],\n  // （）\n  [LEFTCORNERBRACKET, RIGHTCORNERBRACKET],\n  // 「」\n  [LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET],\n  // 『』\n  [FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN] // ＜＞\n  ];\n  for (let i = 0; i < bracketPairs.length; i++) {\n    const [OPEN, CLOSE] = bracketPairs[i];\n    const UrlOpen = tt(Url$1, OPEN); // URL followed by open bracket\n\n    // Continue not accepting for open brackets\n    tt(UrlNonaccept, OPEN, UrlOpen);\n\n    // Closing bracket component. This character WILL be included in the URL\n    tt(UrlOpen, CLOSE, Url$1);\n\n    // URL that beings with an opening bracket, followed by a symbols.\n    // Note that the final state can still be `UrlOpen` (if the URL has a\n    // single opening bracket for some reason).\n    const UrlOpenQ = makeState(Url);\n    ta(UrlOpen, qsAccepting, UrlOpenQ);\n    const UrlOpenSyms = makeState(); // UrlOpen followed by some symbols it cannot end it\n    ta(UrlOpen, qsNonAccepting);\n\n    // URL that begins with an opening bracket, followed by some symbols\n    ta(UrlOpenQ, qsAccepting, UrlOpenQ);\n    ta(UrlOpenQ, qsNonAccepting, UrlOpenSyms);\n    ta(UrlOpenSyms, qsAccepting, UrlOpenQ);\n    ta(UrlOpenSyms, qsNonAccepting, UrlOpenSyms);\n\n    // Close brace/bracket to become regular URL\n    tt(UrlOpenQ, CLOSE, Url$1);\n    tt(UrlOpenSyms, CLOSE, Url$1);\n  }\n  tt(Start, LOCALHOST, DomainDotTld); // localhost is a valid URL state\n  tt(Start, NL, Nl); // single new line\n\n  return {\n    start: Start,\n    tokens: tk\n  };\n}\n\n/**\n * Run the parser state machine on a list of scanned string-based tokens to\n * create a list of multi tokens, each of which represents a URL, email address,\n * plain text, etc.\n *\n * @param {State<MultiToken>} start parser start state\n * @param {string} input the original input used to generate the given tokens\n * @param {Token[]} tokens list of scanned tokens\n * @returns {MultiToken[]}\n */\nfunction run(start, input, tokens) {\n  let len = tokens.length;\n  let cursor = 0;\n  let multis = [];\n  let textTokens = [];\n  while (cursor < len) {\n    let state = start;\n    let secondState = null;\n    let nextState = null;\n    let multiLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    while (cursor < len && !(secondState = state.go(tokens[cursor].t))) {\n      // Starting tokens with nowhere to jump to.\n      // Consider these to be just plain text\n      textTokens.push(tokens[cursor++]);\n    }\n    while (cursor < len && (nextState = secondState || state.go(tokens[cursor].t))) {\n      // Get the next state\n      secondState = null;\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts++;\n      }\n      cursor++;\n      multiLength++;\n    }\n    if (sinceAccepts < 0) {\n      // No accepting state was found, part of a regular text token add\n      // the first text token to the text tokens array and try again from\n      // the next\n      cursor -= multiLength;\n      if (cursor < len) {\n        textTokens.push(tokens[cursor]);\n        cursor++;\n      }\n    } else {\n      // Accepting state!\n      // First close off the textTokens (if available)\n      if (textTokens.length > 0) {\n        multis.push(initMultiToken(Text, input, textTokens));\n        textTokens = [];\n      }\n\n      // Roll back to the latest accepting state\n      cursor -= sinceAccepts;\n      multiLength -= sinceAccepts;\n\n      // Create a new multitoken\n      const Multi = latestAccepting.t;\n      const subtokens = tokens.slice(cursor - multiLength, cursor);\n      multis.push(initMultiToken(Multi, input, subtokens));\n    }\n  }\n\n  // Finally close off the textTokens (if available)\n  if (textTokens.length > 0) {\n    multis.push(initMultiToken(Text, input, textTokens));\n  }\n  return multis;\n}\n\n/**\n * Utility function for instantiating a new multitoken with all the relevant\n * fields during parsing.\n * @param {new (value: string, tokens: Token[]) => MultiToken} Multi class to instantiate\n * @param {string} input original input string\n * @param {Token[]} tokens consecutive tokens scanned from input string\n * @returns {MultiToken}\n */\nfunction initMultiToken(Multi, input, tokens) {\n  const startIdx = tokens[0].s;\n  const endIdx = tokens[tokens.length - 1].e;\n  const value = input.slice(startIdx, endIdx);\n  return new Multi(value, tokens);\n}\n\nconst warn = typeof console !== 'undefined' && console && console.warn || (() => {});\nconst warnAdvice = 'until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.';\n\n// Side-effect initialization state\nconst INIT = {\n  scanner: null,\n  parser: null,\n  tokenQueue: [],\n  pluginQueue: [],\n  customSchemes: [],\n  initialized: false\n};\n\n/**\n * @typedef {{\n * \tstart: State<string>,\n * \ttokens: { groups: Collections<string> } & typeof tk\n * }} ScannerInit\n */\n\n/**\n * @typedef {{\n * \tstart: State<MultiToken>,\n * \ttokens: typeof multi\n * }} ParserInit\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit }) => void} TokenPlugin\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit, parser: ParserInit }) => void} Plugin\n */\n\n/**\n * De-register all plugins and reset the internal state-machine. Used for\n * testing; not required in practice.\n * @private\n */\nfunction reset() {\n  State.groups = {};\n  INIT.scanner = null;\n  INIT.parser = null;\n  INIT.tokenQueue = [];\n  INIT.pluginQueue = [];\n  INIT.customSchemes = [];\n  INIT.initialized = false;\n  return INIT;\n}\n\n/**\n * Register a token plugin to allow the scanner to recognize additional token\n * types before the parser state machine is constructed from the results.\n * @param {string} name of plugin to register\n * @param {TokenPlugin} plugin function that accepts the scanner state machine\n * and available scanner tokens and collections and extends the state machine to\n * recognize additional tokens or groups.\n */\nfunction registerTokenPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid token plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    if (name === INIT.tokenQueue[i][0]) {\n      warn(`linkifyjs: token plugin \"${name}\" already registered - will be overwritten`);\n      INIT.tokenQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.tokenQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register token plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Register a linkify plugin\n * @param {string} name of plugin to register\n * @param {Plugin} plugin function that accepts the parser state machine and\n * extends the parser to recognize additional link types\n */\nfunction registerPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    if (name === INIT.pluginQueue[i][0]) {\n      warn(`linkifyjs: plugin \"${name}\" already registered - will be overwritten`);\n      INIT.pluginQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.pluginQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Detect URLs with the following additional protocol. Anything with format\n * \"protocol://...\" will be considered a link. If `optionalSlashSlash` is set to\n * `true`, anything with format \"protocol:...\" will be considered a link.\n * @param {string} scheme\n * @param {boolean} [optionalSlashSlash]\n */\nfunction registerCustomProtocol(scheme, optionalSlashSlash = false) {\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register custom scheme \"${scheme}\" ${warnAdvice}`);\n  }\n  if (!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(scheme)) {\n    throw new Error(`linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or \"-\"\n2. Cannot start or end with \"-\"\n3. \"-\" cannot repeat`);\n  }\n  INIT.customSchemes.push([scheme, optionalSlashSlash]);\n}\n\n/**\n * Initialize the linkify state machine. Called automatically the first time\n * linkify is called on a string, but may be called manually as well.\n */\nfunction init() {\n  // Initialize scanner state machine and plugins\n  INIT.scanner = init$2(INIT.customSchemes);\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    INIT.tokenQueue[i][1]({\n      scanner: INIT.scanner\n    });\n  }\n\n  // Initialize parser state machine and plugins\n  INIT.parser = init$1(INIT.scanner.tokens);\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    INIT.pluginQueue[i][1]({\n      scanner: INIT.scanner,\n      parser: INIT.parser\n    });\n  }\n  INIT.initialized = true;\n  return INIT;\n}\n\n/**\n * Parse a string into tokens that represent linkable and non-linkable sub-components\n * @param {string} str\n * @return {MultiToken[]} tokens\n */\nfunction tokenize(str) {\n  if (!INIT.initialized) {\n    init();\n  }\n  return run(INIT.parser.start, str, run$1(INIT.scanner.start, str));\n}\ntokenize.scan = run$1; // for testing\n\n/**\n * Find a list of linkable items in the given string.\n * @param {string} str string to find links in\n * @param {string | Opts} [type] either formatting options or specific type of\n * links to find, e.g., 'url' or 'email'\n * @param {Opts} [opts] formatting options for final output. Cannot be specified\n * if opts already provided in `type` argument\n */\nfunction find(str, type = null, opts = null) {\n  if (type && typeof type === 'object') {\n    if (opts) {\n      throw Error(`linkifyjs: Invalid link type ${type}; must be a string`);\n    }\n    opts = type;\n    type = null;\n  }\n  const options = new Options(opts);\n  const tokens = tokenize(str);\n  const filtered = [];\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i];\n    if (token.isLink && (!type || token.t === type) && options.check(token)) {\n      filtered.push(token.toFormattedObject(options));\n    }\n  }\n  return filtered;\n}\n\n/**\n * Is the given string valid linkable text of some sort. Note that this does not\n * trim the text for you.\n *\n * Optionally pass in a second `type` param, which is the type of link to test\n * for.\n *\n * For example,\n *\n *     linkify.test(str, 'email');\n *\n * Returns `true` if str is a valid email.\n * @param {string} str string to test for links\n * @param {string} [type] optional specific link type to look for\n * @returns boolean true/false\n */\nfunction test(str, type = null) {\n  const tokens = tokenize(str);\n  return tokens.length === 1 && tokens[0].isLink && (!type || tokens[0].t === type);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/linkifyjs@4.3.2/node_modules/linkifyjs/dist/linkify.mjs\n");

/***/ })

};
;