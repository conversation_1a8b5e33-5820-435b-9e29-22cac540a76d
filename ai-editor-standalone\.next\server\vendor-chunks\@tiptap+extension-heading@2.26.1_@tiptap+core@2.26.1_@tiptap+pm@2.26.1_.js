"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-heading/dist/index.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-heading/dist/index.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Heading: () => (/* binding */ Heading),\n/* harmony export */   \"default\": () => (/* binding */ Heading)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * This extension allows you to create headings.\n * @see https://www.tiptap.dev/api/nodes/heading\n */\nconst Heading = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'heading',\n    addOptions() {\n        return {\n            levels: [1, 2, 3, 4, 5, 6],\n            HTMLAttributes: {},\n        };\n    },\n    content: 'inline*',\n    group: 'block',\n    defining: true,\n    addAttributes() {\n        return {\n            level: {\n                default: 1,\n                rendered: false,\n            },\n        };\n    },\n    parseHTML() {\n        return this.options.levels\n            .map((level) => ({\n            tag: `h${level}`,\n            attrs: { level },\n        }));\n    },\n    renderHTML({ node, HTMLAttributes }) {\n        const hasLevel = this.options.levels.includes(node.attrs.level);\n        const level = hasLevel\n            ? node.attrs.level\n            : this.options.levels[0];\n        return [`h${level}`, (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setHeading: attributes => ({ commands }) => {\n                if (!this.options.levels.includes(attributes.level)) {\n                    return false;\n                }\n                return commands.setNode(this.name, attributes);\n            },\n            toggleHeading: attributes => ({ commands }) => {\n                if (!this.options.levels.includes(attributes.level)) {\n                    return false;\n                }\n                return commands.toggleNode(this.name, 'paragraph', attributes);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return this.options.levels.reduce((items, level) => ({\n            ...items,\n            ...{\n                [`Mod-Alt-${level}`]: () => this.editor.commands.toggleHeading({ level }),\n            },\n        }), {});\n    },\n    addInputRules() {\n        return this.options.levels.map(level => {\n            return (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.textblockTypeInputRule)({\n                find: new RegExp(`^(#{${Math.min(...this.options.levels)},${level}})\\\\s$`),\n                type: this.type,\n                getAttributes: {\n                    level,\n                },\n            });\n        });\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-heading@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-heading/dist/index.js\n");

/***/ })

};
;