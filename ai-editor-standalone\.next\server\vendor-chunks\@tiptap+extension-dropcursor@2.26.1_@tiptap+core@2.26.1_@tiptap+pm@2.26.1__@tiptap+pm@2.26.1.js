"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-dropcursor/dist/index.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-dropcursor/dist/index.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dropcursor: () => (/* binding */ Dropcursor),\n/* harmony export */   \"default\": () => (/* binding */ Dropcursor)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_dropcursor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/dropcursor */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/dropcursor/dist/index.js\");\n\n\n\n/**\n * This extension allows you to add a drop cursor to your editor.\n * A drop cursor is a line that appears when you drag and drop content\n * inbetween nodes.\n * @see https://tiptap.dev/api/extensions/dropcursor\n */\nconst Dropcursor = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'dropCursor',\n    addOptions() {\n        return {\n            color: 'currentColor',\n            width: 1,\n            class: undefined,\n        };\n    },\n    addProseMirrorPlugins() {\n        return [\n            (0,_tiptap_pm_dropcursor__WEBPACK_IMPORTED_MODULE_0__.dropCursor)(this.options),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtleHRlbnNpb24tZHJvcGN1cnNvckAyLjI2LjFfQHRpcHRhcCtjb3JlQDIuMjYuMV9AdGlwdGFwK3BtQDIuMjYuMV9fQHRpcHRhcCtwbUAyLjI2LjEvbm9kZV9tb2R1bGVzL0B0aXB0YXAvZXh0ZW5zaW9uLWRyb3BjdXJzb3IvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXlDO0FBQ1U7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixtREFBUztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWSxpRUFBVTtBQUN0QjtBQUNBLEtBQUs7QUFDTCxDQUFDOztBQUU0QztBQUM3QyIsInNvdXJjZXMiOlsiRDpcXGdpdGh1YlxcaW50ZWxsaWdlbnRfcmVwb3J0X2dlbmVyYXRvclxcYWktZWRpdG9yLXN0YW5kYWxvbmVcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEB0aXB0YXArZXh0ZW5zaW9uLWRyb3BjdXJzb3JAMi4yNi4xX0B0aXB0YXArY29yZUAyLjI2LjFfQHRpcHRhcCtwbUAyLjI2LjFfX0B0aXB0YXArcG1AMi4yNi4xXFxub2RlX21vZHVsZXNcXEB0aXB0YXBcXGV4dGVuc2lvbi1kcm9wY3Vyc29yXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFeHRlbnNpb24gfSBmcm9tICdAdGlwdGFwL2NvcmUnO1xuaW1wb3J0IHsgZHJvcEN1cnNvciB9IGZyb20gJ0B0aXB0YXAvcG0vZHJvcGN1cnNvcic7XG5cbi8qKlxuICogVGhpcyBleHRlbnNpb24gYWxsb3dzIHlvdSB0byBhZGQgYSBkcm9wIGN1cnNvciB0byB5b3VyIGVkaXRvci5cbiAqIEEgZHJvcCBjdXJzb3IgaXMgYSBsaW5lIHRoYXQgYXBwZWFycyB3aGVuIHlvdSBkcmFnIGFuZCBkcm9wIGNvbnRlbnRcbiAqIGluYmV0d2VlbiBub2Rlcy5cbiAqIEBzZWUgaHR0cHM6Ly90aXB0YXAuZGV2L2FwaS9leHRlbnNpb25zL2Ryb3BjdXJzb3JcbiAqL1xuY29uc3QgRHJvcGN1cnNvciA9IEV4dGVuc2lvbi5jcmVhdGUoe1xuICAgIG5hbWU6ICdkcm9wQ3Vyc29yJyxcbiAgICBhZGRPcHRpb25zKCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgY29sb3I6ICdjdXJyZW50Q29sb3InLFxuICAgICAgICAgICAgd2lkdGg6IDEsXG4gICAgICAgICAgICBjbGFzczogdW5kZWZpbmVkLFxuICAgICAgICB9O1xuICAgIH0sXG4gICAgYWRkUHJvc2VNaXJyb3JQbHVnaW5zKCkge1xuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgZHJvcEN1cnNvcih0aGlzLm9wdGlvbnMpLFxuICAgICAgICBdO1xuICAgIH0sXG59KTtcblxuZXhwb3J0IHsgRHJvcGN1cnNvciwgRHJvcGN1cnNvciBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-dropcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-dropcursor/dist/index.js\n");

/***/ })

};
;