{"version": 3, "sources": ["../../src/build/index.ts"], "sourcesContent": ["import type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from './webpack/plugins/pages-manifest-plugin'\nimport type { ExportPathMap, NextConfigComplete } from '../server/config-shared'\nimport type { MiddlewareManifest } from './webpack/plugins/middleware-plugin'\nimport type { ActionManifest } from './webpack/plugins/flight-client-entry-plugin'\nimport type { CacheControl, Revalidate } from '../server/lib/cache-control'\n\nimport '../lib/setup-exception-listeners'\n\nimport { loadEnvConfig, type LoadedEnvFiles } from '@next/env'\nimport { bold, yellow } from '../lib/picocolors'\nimport { makeRe } from 'next/dist/compiled/picomatch'\nimport { existsSync, promises as fs } from 'fs'\nimport os from 'os'\nimport { Worker } from '../lib/worker'\nimport { defaultConfig } from '../server/config-shared'\nimport devalue from 'next/dist/compiled/devalue'\nimport findUp from 'next/dist/compiled/find-up'\nimport { nanoid } from 'next/dist/compiled/nanoid/index.cjs'\nimport path from 'path'\nimport {\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  PUBLIC_DIR_MIDDLEWARE_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  PAGES_DIR_ALIAS,\n  INSTRUMENTATION_HOOK_FILENAME,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SUFFIX,\n  NEXT_RESUME_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  MATCHED_PATH_HEADER,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n} from '../lib/constants'\nimport { FileType, fileExists } from '../lib/file-exists'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport loadCustomRoutes, {\n  normalizeRouteRegex,\n} from '../lib/load-custom-routes'\nimport type {\n  CustomRoutes,\n  Header,\n  Redirect,\n  Rewrite,\n  RouteHas,\n} from '../lib/load-custom-routes'\nimport { nonNullable } from '../lib/non-nullable'\nimport { recursiveDelete } from '../lib/recursive-delete'\nimport { verifyPartytownSetup } from '../lib/verify-partytown-setup'\nimport {\n  BUILD_ID_FILE,\n  BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  IMAGES_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_PRODUCTION_BUILD,\n  PRERENDER_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  ROUTES_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_FILES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  MIDDLEWARE_MANIFEST,\n  APP_PATHS_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  APP_BUILD_MANIFEST,\n  RSC_MODULE_TYPES,\n  NEXT_FONT_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  DYNAMIC_CSS_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\nimport {\n  getSortedRoutes,\n  isDynamicRoute,\n  getSortedRouteObjects,\n} from '../shared/lib/router/utils'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport loadConfig from '../server/config'\nimport type { BuildManifest } from '../server/get-page-files'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getPagePath } from '../server/require'\nimport * as ciEnvironment from '../server/ci-info'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n  writeTurborepoAccessTraceResult,\n} from './turborepo-access-trace'\n\nimport {\n  eventBuildOptimize,\n  eventCliSession,\n  eventBuildFeatureUsage,\n  eventNextPlugins,\n  EVENT_BUILD_FEATURE_USAGE,\n  eventPackageUsedInGetServerSideProps,\n  eventBuildCompleted,\n  eventBuildFailed,\n} from '../telemetry/events'\nimport type { EventBuildFeatureUsage } from '../telemetry/events'\nimport { Telemetry } from '../telemetry/storage'\nimport {\n  createPagesMapping,\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from './entries'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { generateBuildId } from './generate-build-id'\nimport { isWriteable } from './is-writeable'\nimport * as Log from './output/log'\nimport createSpinner from './spinner'\nimport { trace, flushAllTraces, setGlobal, type Span } from '../trace'\nimport {\n  detectConflictingPaths,\n  computeFromManifest,\n  getJsPageSizeInKb,\n  printCustomRoutes,\n  printTreeView,\n  copyTracedFiles,\n  isReservedPage,\n  isAppBuiltinNotFoundPage,\n  collectRoutesUsingEdgeRuntime,\n  collectMeta,\n} from './utils'\nimport type { PageInfo, PageInfos } from './utils'\nimport type { PrerenderedRoute } from './static-paths/types'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport { writeBuildId } from './write-build-id'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport isError from '../lib/is-error'\nimport type { NextError } from '../lib/is-error'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport { recursiveReadDir } from '../lib/recursive-readdir'\nimport { lockfilePatchPromise, teardownTraceSubscriber } from './swc'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getFilesInDir } from '../lib/get-files-in-dir'\nimport { eventSwcPlugins } from '../telemetry/events/swc-plugins'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport {\n  ACTION_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n} from '../client/components/app-router-headers'\nimport { webpackBuild } from './webpack-build'\nimport { NextBuildContext, type MappedPages } from './build-context'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { createClientRouterFilter } from '../lib/create-client-router-filter'\nimport { createValidFileMatcher } from '../server/lib/find-page-file'\nimport { startTypeChecking } from './type-check'\nimport { generateInterceptionRoutesRewrites } from '../lib/generate-interception-routes-rewrites'\n\nimport { buildDataRoute } from '../server/lib/router-utils/build-data-route'\nimport { collectBuildTraces } from './collect-build-traces'\nimport type { BuildTraceContext } from './webpack/plugins/next-trace-entrypoints-plugin'\nimport { formatManifest } from './manifests/formatter/format-manifest'\nimport {\n  recordFrameworkVersion,\n  updateBuildDiagnostics,\n  recordFetchMetrics,\n} from '../diagnostics/build-diagnostics'\nimport { getStartServerInfo, logStartInfo } from '../server/lib/app-info-log'\nimport type { NextEnabledDirectories } from '../server/base-server'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { buildCustomRoute } from '../lib/build-custom-route'\nimport { traceMemoryUsage } from '../lib/memory/trace'\nimport { generateEncryptionKeyBase64 } from '../server/app-render/encryption-utils-server'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport uploadTrace from '../trace/upload-trace'\nimport {\n  checkIsAppPPREnabled,\n  checkIsRoutePPREnabled,\n} from '../server/lib/experimental/ppr'\nimport { FallbackMode, fallbackModeToFallbackField } from '../lib/fallback'\nimport { RenderingMode } from './rendering-mode'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport type { UseCacheTrackerKey } from './webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport {\n  buildInversePrefetchSegmentDataRoute,\n  buildPrefetchSegmentDataRoute,\n  type PrefetchSegmentDataRoute,\n} from '../server/lib/router-utils/build-prefetch-segment-data-route'\n\nimport { turbopackBuild } from './turbopack-build'\nimport { isPersistentCachingEnabled } from '../shared/lib/turbopack/utils'\nimport { inlineStaticEnv } from '../lib/inline-static-env'\nimport { populateStaticEnv } from '../lib/static-env'\nimport { durationToString } from './duration-to-string'\nimport { traceGlobals } from '../trace/shared'\nimport { extractNextErrorCode } from '../lib/error-telemetry-utils'\nimport { runAfterProductionCompile } from './after-production-compile'\nimport { generatePreviewKeys } from './preview-key-utils'\nimport { handleBuildComplete } from './adapter/build-complete'\n\ntype Fallback = null | boolean | string\n\nexport interface PrerenderManifestRoute {\n  dataRoute: string | null\n  experimentalBypassFor?: RouteHas[]\n\n  /**\n   * The headers that should be served along side this prerendered route.\n   */\n  initialHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be served along side this prerendered route.\n   */\n  initialStatus?: number\n\n  /**\n   * The revalidate value for this route. This might be inferred from:\n   * - route segment configs\n   * - fetch calls\n   * - unstable_cache\n   * - \"use cache\"\n   */\n  initialRevalidateSeconds: Revalidate\n\n  /**\n   * The expire value for this route, which is inferred from the \"use cache\"\n   * functions that are used by the route, or the expireTime config.\n   */\n  initialExpireSeconds: number | undefined\n\n  /**\n   * The prefetch data route associated with this page. If not defined, this\n   * page does not support prefetching.\n   */\n  prefetchDataRoute: string | null | undefined\n\n  /**\n   * The dynamic route that this statically prerendered route is based on. If\n   * this is null, then the route was not based on a dynamic route.\n   */\n  srcRoute: string | null\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\nexport interface DynamicPrerenderManifestRoute {\n  dataRoute: string | null\n  dataRouteRegex: string | null\n  experimentalBypassFor?: RouteHas[]\n  fallback: Fallback\n\n  /**\n   * When defined, it describes the revalidation configuration for the fallback\n   * route.\n   */\n  fallbackRevalidate: Revalidate | undefined\n\n  /**\n   * When defined, it describes the expire configuration for the fallback route.\n   */\n  fallbackExpire: number | undefined\n\n  /**\n   * The headers that should used when serving the fallback.\n   */\n  fallbackHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be used when serving the fallback.\n   */\n  fallbackStatus?: number\n\n  /**\n   * The root params that are unknown for this fallback route.\n   */\n  fallbackRootParams: readonly string[] | undefined\n\n  /**\n   * The source route that this fallback route is based on. This is a reference\n   * so that we can associate this dynamic route with the correct source.\n   */\n  fallbackSourceRoute: string | undefined\n\n  prefetchDataRoute: string | null | undefined\n  prefetchDataRouteRegex: string | null | undefined\n  routeRegex: string\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\n/**\n * The headers that are allowed to be used when revalidating routes. Currently\n * this includes both headers used by the pages and app routers.\n */\nconst ALLOWED_HEADERS: string[] = [\n  'host',\n  MATCHED_PATH_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n]\n\nexport type PrerenderManifest = {\n  version: 4\n  routes: { [route: string]: PrerenderManifestRoute }\n  dynamicRoutes: { [route: string]: DynamicPrerenderManifestRoute }\n  notFoundRoutes: string[]\n  preview: __ApiPreviewProps\n}\n\ntype ManifestBuiltRoute = {\n  /**\n   * The route pattern used to match requests for this route.\n   */\n  regex: string\n}\n\nexport enum RouteType {\n  /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */\n  PAGES = 'PAGES',\n  /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */\n  PAGES_API = 'PAGES_API',\n  /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */\n  APP_PAGE = 'APP_PAGE',\n  /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */\n  APP_ROUTE = 'APP_ROUTE',\n\n  /**\n   * `IMAGE` represents all the images that are generated by `next/image`.\n   */\n  // IMAGE = 'IMAGE',\n\n  /**\n   * `STATIC_FILE` represents a static file (ie /_next/static)\n   */\n  STATIC_FILE = 'STATIC_FILE',\n\n  MIDDLEWARE = 'MIDDLEWARE',\n}\n\nexport type ManifestRewriteRoute = ManifestBuiltRoute & Rewrite\nexport type ManifestRedirectRoute = ManifestBuiltRoute & Redirect\nexport type ManifestHeaderRoute = ManifestBuiltRoute & Header\n\nexport type ManifestRoute = ManifestBuiltRoute & {\n  page: string\n  namedRegex?: string\n  routeKeys?: { [key: string]: string }\n  prefetchSegmentDataRoutes?: PrefetchSegmentDataRoute[]\n}\n\ntype ManifestDataRoute = {\n  page: string\n  routeKeys?: { [key: string]: string }\n  dataRouteRegex: string\n  namedDataRouteRegex?: string\n}\n\nexport type RoutesManifest = {\n  version: number\n  pages404: boolean\n  basePath: string\n  redirects: Array<ManifestRedirectRoute>\n  rewrites: {\n    beforeFiles: Array<ManifestRewriteRoute>\n    afterFiles: Array<ManifestRewriteRoute>\n    fallback: Array<ManifestRewriteRoute>\n  }\n  headers: Array<ManifestHeaderRoute>\n  staticRoutes: Array<ManifestRoute>\n  dynamicRoutes: Array<ManifestRoute>\n  dataRoutes: Array<ManifestDataRoute>\n  i18n?: {\n    domains?: ReadonlyArray<{\n      http?: true\n      domain: string\n      locales?: readonly string[]\n      defaultLocale: string\n    }>\n    locales: readonly string[]\n    defaultLocale: string\n    localeDetection?: false\n  }\n  rsc: {\n    header: typeof RSC_HEADER\n    didPostponeHeader: typeof NEXT_DID_POSTPONE_HEADER\n    contentTypeHeader: typeof RSC_CONTENT_TYPE_HEADER\n    varyHeader: string\n    prefetchHeader: typeof NEXT_ROUTER_PREFETCH_HEADER\n    suffix: typeof RSC_SUFFIX\n    prefetchSuffix: typeof RSC_PREFETCH_SUFFIX\n    prefetchSegmentHeader: typeof NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n    prefetchSegmentDirSuffix: typeof RSC_SEGMENTS_DIR_SUFFIX\n    prefetchSegmentSuffix: typeof RSC_SEGMENT_SUFFIX\n  }\n  rewriteHeaders: {\n    pathHeader: typeof NEXT_REWRITTEN_PATH_HEADER\n    queryHeader: typeof NEXT_REWRITTEN_QUERY_HEADER\n  }\n  skipMiddlewareUrlNormalize?: boolean\n  caseSensitive?: boolean\n  /**\n   * Configuration related to Partial Prerendering.\n   */\n  ppr?: {\n    /**\n     * The chained response for the PPR resume.\n     */\n    chain: {\n      /**\n       * The headers that will indicate to Next.js that the request is for a PPR\n       * resume.\n       */\n      headers: Record<string, string>\n    }\n  }\n}\n\nfunction pageToRoute(page: string): ManifestRoute {\n  const routeRegex = getNamedRouteRegex(page, {\n    prefixRouteKeys: true,\n  })\n  return {\n    page,\n    regex: normalizeRouteRegex(routeRegex.re.source),\n    routeKeys: routeRegex.routeKeys,\n    namedRegex: routeRegex.namedRegex,\n  }\n}\n\nfunction getCacheDir(distDir: string): string {\n  const cacheDir = path.join(distDir, 'cache')\n  if (ciEnvironment.isCI && !ciEnvironment.hasNextSupport) {\n    const hasCache = existsSync(cacheDir)\n\n    if (!hasCache) {\n      // Intentionally not piping to stderr which is what `Log.warn` does in case people fail in CI when\n      // stderr is detected.\n      console.log(\n        `${Log.prefixes.warn} No build cache found. Please configure build caching for faster rebuilds. Read more: https://nextjs.org/docs/messages/no-cache`\n      )\n    }\n  }\n  return cacheDir\n}\n\nasync function writeFileUtf8(filePath: string, content: string): Promise<void> {\n  await fs.writeFile(filePath, content, 'utf-8')\n}\n\nfunction readFileUtf8(filePath: string): Promise<string> {\n  return fs.readFile(filePath, 'utf8')\n}\n\nasync function writeManifest<T extends object>(\n  filePath: string,\n  manifest: T\n): Promise<void> {\n  await writeFileUtf8(filePath, formatManifest(manifest))\n}\n\nasync function readManifest<T extends object>(filePath: string): Promise<T> {\n  return JSON.parse(await readFileUtf8(filePath))\n}\n\nasync function writePrerenderManifest(\n  distDir: string,\n  manifest: DeepReadonly<PrerenderManifest>\n): Promise<void> {\n  await writeManifest(path.join(distDir, PRERENDER_MANIFEST), manifest)\n}\n\nasync function writeClientSsgManifest(\n  prerenderManifest: DeepReadonly<PrerenderManifest>,\n  {\n    buildId,\n    distDir,\n    locales,\n  }: {\n    buildId: string\n    distDir: string\n    locales: readonly string[] | undefined\n  }\n) {\n  const ssgPages = new Set<string>(\n    [\n      ...Object.entries(prerenderManifest.routes)\n        // Filter out dynamic routes\n        .filter(([, { srcRoute }]) => srcRoute == null)\n        .map(([route]) => normalizeLocalePath(route, locales).pathname),\n      ...Object.keys(prerenderManifest.dynamicRoutes),\n    ].sort()\n  )\n\n  const clientSsgManifestContent = `self.__SSG_MANIFEST=${devalue(\n    ssgPages\n  )};self.__SSG_MANIFEST_CB&&self.__SSG_MANIFEST_CB()`\n\n  await writeFileUtf8(\n    path.join(distDir, CLIENT_STATIC_FILES_PATH, buildId, '_ssgManifest.js'),\n    clientSsgManifestContent\n  )\n}\n\nexport interface FunctionsConfigManifest {\n  version: number\n  functions: Record<\n    string,\n    {\n      maxDuration?: number | undefined\n      runtime?: 'nodejs'\n      regions?: string[] | string\n      matchers?: Array<{\n        regexp: string\n        originalSource: string\n        has?: Rewrite['has']\n        missing?: Rewrite['has']\n      }>\n    }\n  >\n}\n\nasync function writeFunctionsConfigManifest(\n  distDir: string,\n  manifest: FunctionsConfigManifest\n): Promise<void> {\n  await writeManifest(\n    path.join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n    manifest\n  )\n}\n\nexport interface RequiredServerFilesManifest {\n  version: number\n  config: NextConfigComplete\n  appDir: string\n  relativeAppDir: string\n  files: string[]\n  ignore: string[]\n}\n\nasync function writeRequiredServerFilesManifest(\n  distDir: string,\n  requiredServerFiles: RequiredServerFilesManifest\n) {\n  await writeManifest(\n    path.join(distDir, SERVER_FILES_MANIFEST),\n    requiredServerFiles\n  )\n}\n\nasync function writeImagesManifest(\n  distDir: string,\n  config: NextConfigComplete\n): Promise<void> {\n  const images = { ...config.images }\n  const { deviceSizes, imageSizes } = images\n  ;(images as any).sizes = [...deviceSizes, ...imageSizes]\n\n  // By default, remotePatterns will allow no remote images ([])\n  images.remotePatterns = (config?.images?.remotePatterns || []).map((p) => ({\n    // Modifying the manifest should also modify matchRemotePattern()\n    protocol: p.protocol?.replace(/:$/, '') as 'http' | 'https' | undefined,\n    hostname: makeRe(p.hostname).source,\n    port: p.port,\n    pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n    search: p.search,\n  }))\n\n  // By default, localPatterns will allow all local images (undefined)\n  if (config?.images?.localPatterns) {\n    images.localPatterns = config.images.localPatterns.map((p) => ({\n      // Modifying the manifest should also modify matchLocalPattern()\n      pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n      search: p.search,\n    }))\n  }\n\n  await writeManifest(path.join(distDir, IMAGES_MANIFEST), {\n    version: 1,\n    images,\n  })\n}\n\nconst STANDALONE_DIRECTORY = 'standalone' as const\nasync function writeStandaloneDirectory(\n  nextBuildSpan: Span,\n  distDir: string,\n  pageKeys: { pages: string[]; app: string[] | undefined },\n  denormalizedAppPages: string[] | undefined,\n  outputFileTracingRoot: string,\n  requiredServerFiles: RequiredServerFilesManifest,\n  middlewareManifest: MiddlewareManifest,\n  hasNodeMiddleware: boolean,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>,\n  loadedEnvFiles: LoadedEnvFiles,\n  appDir: string | undefined\n) {\n  await nextBuildSpan\n    .traceChild('write-standalone-directory')\n    .traceAsyncFn(async () => {\n      await copyTracedFiles(\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        requiredServerFiles.appDir,\n        distDir,\n        pageKeys.pages,\n        denormalizedAppPages,\n        outputFileTracingRoot,\n        requiredServerFiles.config,\n        middlewareManifest,\n        hasNodeMiddleware,\n        hasInstrumentationHook,\n        staticPages\n      )\n\n      for (const file of [\n        ...requiredServerFiles.files,\n        path.join(requiredServerFiles.config.distDir, SERVER_FILES_MANIFEST),\n        ...loadedEnvFiles.reduce<string[]>((acc, envFile) => {\n          if (['.env', '.env.production'].includes(envFile.path)) {\n            acc.push(envFile.path)\n          }\n          return acc\n        }, []),\n      ]) {\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        const filePath = path.join(requiredServerFiles.appDir, file)\n        const outputPath = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, filePath)\n        )\n        await fs.mkdir(path.dirname(outputPath), {\n          recursive: true,\n        })\n        await fs.copyFile(filePath, outputPath)\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareOutput = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'middleware.js'\n        )\n\n        await fs.mkdir(path.dirname(middlewareOutput), { recursive: true })\n        await fs.copyFile(\n          path.join(distDir, SERVER_DIRECTORY, 'middleware.js'),\n          middlewareOutput\n        )\n      }\n\n      await recursiveCopy(\n        path.join(distDir, SERVER_DIRECTORY, 'pages'),\n        path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'pages'\n        ),\n        { overwrite: true }\n      )\n      if (appDir) {\n        const originalServerApp = path.join(distDir, SERVER_DIRECTORY, 'app')\n        if (existsSync(originalServerApp)) {\n          await recursiveCopy(\n            originalServerApp,\n            path.join(\n              distDir,\n              STANDALONE_DIRECTORY,\n              path.relative(outputFileTracingRoot, distDir),\n              SERVER_DIRECTORY,\n              'app'\n            ),\n            { overwrite: true }\n          )\n        }\n      }\n    })\n}\n\nfunction getNumberOfWorkers(config: NextConfigComplete) {\n  if (\n    config.experimental.cpus &&\n    config.experimental.cpus !== defaultConfig.experimental!.cpus\n  ) {\n    return config.experimental.cpus\n  }\n\n  if (config.experimental.memoryBasedWorkersCount) {\n    return Math.max(\n      Math.min(config.experimental.cpus || 1, Math.floor(os.freemem() / 1e9)),\n      // enforce a minimum of 4 workers\n      4\n    )\n  }\n\n  if (config.experimental.cpus) {\n    return config.experimental.cpus\n  }\n\n  // Fall back to 4 workers if a count is not specified\n  return 4\n}\n\nconst staticWorkerPath = require.resolve('./worker')\nconst staticWorkerExposedMethods = [\n  'hasCustomGetInitialProps',\n  'isPageStatic',\n  'getDefinedNamedExports',\n  'exportPages',\n] as const\nexport type StaticWorker = typeof import('./worker') & Worker\nexport function createStaticWorker(\n  config: NextConfigComplete,\n  options: {\n    debuggerPortOffset: number\n    progress?: {\n      run: () => void\n      clear: () => void\n    }\n  }\n): StaticWorker {\n  const { debuggerPortOffset, progress } = options\n  return new Worker(staticWorkerPath, {\n    logger: Log,\n    numWorkers: getNumberOfWorkers(config),\n    onActivity: () => {\n      progress?.run()\n    },\n    onActivityAbort: () => {\n      progress?.clear()\n    },\n    debuggerPortOffset,\n    enableSourceMaps: config.experimental.enablePrerenderSourceMaps,\n    // remove --max-old-space-size flag as it can cause memory issues.\n    isolatedMemory: true,\n    enableWorkerThreads: config.experimental.workerThreads,\n    exposedMethods: staticWorkerExposedMethods,\n  }) as StaticWorker\n}\n\nasync function writeFullyStaticExport(\n  config: NextConfigComplete,\n  dir: string,\n  enabledDirectories: NextEnabledDirectories,\n  configOutDir: string,\n  nextBuildSpan: Span\n): Promise<void> {\n  const exportApp = (require('../export') as typeof import('../export'))\n    .default as typeof import('../export').default\n\n  await exportApp(\n    dir,\n    {\n      buildExport: false,\n      nextConfig: config,\n      enabledDirectories,\n      silent: true,\n      outdir: path.join(dir, configOutDir),\n      numWorkers: getNumberOfWorkers(config),\n    },\n    nextBuildSpan\n  )\n}\n\nasync function getBuildId(\n  isGenerateMode: boolean,\n  distDir: string,\n  nextBuildSpan: Span,\n  config: NextConfigComplete\n) {\n  if (isGenerateMode) {\n    return await fs.readFile(path.join(distDir, 'BUILD_ID'), 'utf8')\n  }\n  return await nextBuildSpan\n    .traceChild('generate-buildid')\n    .traceAsyncFn(() => generateBuildId(config.generateBuildId, nanoid))\n}\n\nexport default async function build(\n  dir: string,\n  reactProductionProfiling = false,\n  debugOutput = false,\n  debugPrerender = false,\n  runLint = true,\n  noMangling = false,\n  appDirOnly = false,\n  isTurbopack = false,\n  experimentalBuildMode: 'default' | 'compile' | 'generate' | 'generate-env',\n  traceUploadUrl: string | undefined\n): Promise<void> {\n  const isCompileMode = experimentalBuildMode === 'compile'\n  const isGenerateMode = experimentalBuildMode === 'generate'\n  NextBuildContext.isCompileMode = isCompileMode\n  const buildStartTime = Date.now()\n\n  let loadedConfig: NextConfigComplete | undefined\n  try {\n    const nextBuildSpan = trace('next-build', undefined, {\n      buildMode: experimentalBuildMode,\n      isTurboBuild: String(isTurbopack),\n      version: process.env.__NEXT_VERSION as string,\n    })\n\n    NextBuildContext.nextBuildSpan = nextBuildSpan\n    NextBuildContext.dir = dir\n    NextBuildContext.appDirOnly = appDirOnly\n    NextBuildContext.reactProductionProfiling = reactProductionProfiling\n    NextBuildContext.noMangling = noMangling\n    NextBuildContext.debugPrerender = debugPrerender\n\n    await nextBuildSpan.traceAsyncFn(async () => {\n      // attempt to load global env values so they are available in next.config.js\n      const { loadedEnvFiles } = nextBuildSpan\n        .traceChild('load-dotenv')\n        .traceFn(() => loadEnvConfig(dir, false, Log))\n      NextBuildContext.loadedEnvFiles = loadedEnvFiles\n\n      const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n      const config: NextConfigComplete = await nextBuildSpan\n        .traceChild('load-next-config')\n        .traceAsyncFn(() =>\n          turborepoTraceAccess(\n            () =>\n              loadConfig(PHASE_PRODUCTION_BUILD, dir, {\n                // Log for next.config loading process\n                silent: false,\n                reactProductionProfiling,\n                debugPrerender,\n              }),\n            turborepoAccessTraceResult\n          )\n        )\n      loadedConfig = config\n\n      process.env.NEXT_DEPLOYMENT_ID = config.deploymentId || ''\n      NextBuildContext.config = config\n\n      let configOutDir = 'out'\n      if (hasCustomExportOutput(config)) {\n        configOutDir = config.distDir\n        config.distDir = '.next'\n      }\n      const distDir = path.join(dir, config.distDir)\n      NextBuildContext.distDir = distDir\n      setGlobal('phase', PHASE_PRODUCTION_BUILD)\n      setGlobal('distDir', distDir)\n\n      const buildId = await getBuildId(\n        isGenerateMode,\n        distDir,\n        nextBuildSpan,\n        config\n      )\n      NextBuildContext.buildId = buildId\n\n      if (experimentalBuildMode === 'generate-env') {\n        if (isTurbopack) {\n          Log.warn('generate-env is not needed with turbopack')\n          process.exit(0)\n        }\n        Log.info('Inlining static env ...')\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n\n        Log.info('Complete')\n        await flushAllTraces()\n        teardownTraceSubscriber()\n        process.exit(0)\n      }\n\n      // when using compile mode static env isn't inlined so we\n      // need to populate in normal runtime env\n      if (isCompileMode || isGenerateMode) {\n        populateStaticEnv(config)\n      }\n\n      const customRoutes: CustomRoutes = await nextBuildSpan\n        .traceChild('load-custom-routes')\n        .traceAsyncFn(() => loadCustomRoutes(config))\n\n      const { headers, rewrites, redirects } = customRoutes\n      const combinedRewrites: Rewrite[] = [\n        ...rewrites.beforeFiles,\n        ...rewrites.afterFiles,\n        ...rewrites.fallback,\n      ]\n      const hasRewrites = combinedRewrites.length > 0\n      NextBuildContext.hasRewrites = hasRewrites\n      NextBuildContext.originalRewrites = config._originalRewrites\n      NextBuildContext.originalRedirects = config._originalRedirects\n\n      const cacheDir = getCacheDir(distDir)\n\n      const telemetry = new Telemetry({ distDir })\n\n      setGlobal('telemetry', telemetry)\n\n      const publicDir = path.join(dir, 'public')\n      const { pagesDir, appDir } = findPagesDir(dir)\n      NextBuildContext.pagesDir = pagesDir\n      NextBuildContext.appDir = appDir\n\n      const enabledDirectories: NextEnabledDirectories = {\n        app: typeof appDir === 'string',\n        pages: typeof pagesDir === 'string',\n      }\n\n      // Generate a random encryption key for this build.\n      // This key is used to encrypt cross boundary values and can be used to generate hashes.\n      const encryptionKey = await generateEncryptionKeyBase64({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.encryptionKey = encryptionKey\n\n      const isSrcDir = path\n        .relative(dir, pagesDir || appDir || '')\n        .startsWith('src')\n      const hasPublicDir = existsSync(publicDir)\n\n      telemetry.record(\n        eventCliSession(dir, config, {\n          webpackVersion: 5,\n          cliCommand: 'build',\n          isSrcDir,\n          hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n          isCustomServer: null,\n          turboFlag: false,\n          pagesDir: !!pagesDir,\n          appDir: !!appDir,\n        })\n      )\n\n      eventNextPlugins(path.resolve(dir)).then((events) =>\n        telemetry.record(events)\n      )\n\n      eventSwcPlugins(path.resolve(dir), config).then((events) =>\n        telemetry.record(events)\n      )\n\n      // Always log next version first then start rest jobs\n      const { envInfo, experimentalFeatures } = await getStartServerInfo({\n        dir,\n        dev: false,\n        debugPrerender,\n      })\n\n      logStartInfo({\n        networkUrl: null,\n        appUrl: null,\n        envInfo,\n        experimentalFeatures,\n      })\n\n      const ignoreESLint = Boolean(config.eslint.ignoreDuringBuilds)\n      const shouldLint = !ignoreESLint && runLint\n\n      const typeCheckingOptions: Parameters<typeof startTypeChecking>[0] = {\n        dir,\n        appDir,\n        pagesDir,\n        runLint,\n        shouldLint,\n        ignoreESLint,\n        telemetry,\n        nextBuildSpan,\n        config,\n        cacheDir,\n      }\n\n      const distDirCreated = await nextBuildSpan\n        .traceChild('create-dist-dir')\n        .traceAsyncFn(async () => {\n          try {\n            await fs.mkdir(distDir, { recursive: true })\n            return true\n          } catch (err) {\n            if (isError(err) && err.code === 'EPERM') {\n              return false\n            }\n            throw err\n          }\n        })\n\n      if (!distDirCreated || !(await isWriteable(distDir))) {\n        throw new Error(\n          '> Build directory is not writeable. https://nextjs.org/docs/messages/build-dir-not-writeable'\n        )\n      }\n\n      if (config.cleanDistDir && !isGenerateMode) {\n        await recursiveDelete(distDir, /^cache/)\n      }\n\n      // For app directory, we run type checking after build. That's because\n      // we dynamically generate types for each layout and page in the app\n      // directory.\n      if (!appDir && !isCompileMode)\n        await startTypeChecking(typeCheckingOptions)\n\n      if (appDir && 'exportPathMap' in config) {\n        Log.error(\n          'The \"exportPathMap\" configuration cannot be used with the \"app\" directory. Please use generateStaticParams() instead.'\n        )\n        await telemetry.flush()\n        process.exit(1)\n      }\n\n      const buildLintEvent: EventBuildFeatureUsage = {\n        featureName: 'build-lint',\n        invocationCount: shouldLint ? 1 : 0,\n      }\n      telemetry.record({\n        eventName: EVENT_BUILD_FEATURE_USAGE,\n        payload: buildLintEvent,\n      })\n\n      const validFileMatcher = createValidFileMatcher(\n        config.pageExtensions,\n        appDir\n      )\n\n      const providedPagePaths: string[] = JSON.parse(\n        process.env.NEXT_PRIVATE_PAGE_PATHS || '[]'\n      )\n\n      let pagesPaths = Boolean(process.env.NEXT_PRIVATE_PAGE_PATHS)\n        ? providedPagePaths\n        : !appDirOnly && pagesDir\n          ? await nextBuildSpan.traceChild('collect-pages').traceAsyncFn(() =>\n              recursiveReadDir(pagesDir, {\n                pathnameFilter: validFileMatcher.isPageFile,\n              })\n            )\n          : []\n\n      const middlewareDetectionRegExp = new RegExp(\n        `^${MIDDLEWARE_FILENAME}\\\\.(?:${config.pageExtensions.join('|')})$`\n      )\n\n      const instrumentationHookDetectionRegExp = new RegExp(\n        `^${INSTRUMENTATION_HOOK_FILENAME}\\\\.(?:${config.pageExtensions.join(\n          '|'\n        )})$`\n      )\n\n      const rootDir = path.join((pagesDir || appDir)!, '..')\n      const includes = [\n        middlewareDetectionRegExp,\n        instrumentationHookDetectionRegExp,\n      ]\n\n      const rootPaths = Array.from(await getFilesInDir(rootDir))\n        .filter((file) => includes.some((include) => include.test(file)))\n        .sort(sortByPageExts(config.pageExtensions))\n        .map((file) => path.join(rootDir, file).replace(dir, ''))\n\n      const hasInstrumentationHook = rootPaths.some((p) =>\n        p.includes(INSTRUMENTATION_HOOK_FILENAME)\n      )\n      const hasMiddlewareFile = rootPaths.some((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n\n      NextBuildContext.hasInstrumentationHook = hasInstrumentationHook\n\n      const previewProps: __ApiPreviewProps = await generatePreviewKeys({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.previewProps = previewProps\n\n      const mappedPages = await nextBuildSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: false,\n            pageExtensions: config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagesPaths,\n            pagesDir,\n            appDir,\n          })\n        )\n      NextBuildContext.mappedPages = mappedPages\n\n      let mappedAppPages: MappedPages | undefined\n      let denormalizedAppPages: string[] | undefined\n\n      if (appDir) {\n        const providedAppPaths: string[] = JSON.parse(\n          process.env.NEXT_PRIVATE_APP_PATHS || '[]'\n        )\n\n        let appPaths = Boolean(process.env.NEXT_PRIVATE_APP_PATHS)\n          ? providedAppPaths\n          : await nextBuildSpan\n              .traceChild('collect-app-paths')\n              .traceAsyncFn(() =>\n                recursiveReadDir(appDir, {\n                  pathnameFilter: (absolutePath) =>\n                    validFileMatcher.isAppRouterPage(absolutePath) ||\n                    // For now we only collect the root /not-found page in the app\n                    // directory as the 404 fallback\n                    validFileMatcher.isRootNotFound(absolutePath),\n                  ignorePartFilter: (part) => part.startsWith('_'),\n                })\n              )\n\n        mappedAppPages = await nextBuildSpan\n          .traceChild('create-app-mapping')\n          .traceAsyncFn(() =>\n            createPagesMapping({\n              pagePaths: appPaths,\n              isDev: false,\n              pagesType: PAGE_TYPES.APP,\n              pageExtensions: config.pageExtensions,\n              pagesDir,\n              appDir,\n            })\n          )\n\n        NextBuildContext.mappedAppPages = mappedAppPages\n      }\n\n      const mappedRootPaths = await createPagesMapping({\n        isDev: false,\n        pageExtensions: config.pageExtensions,\n        pagePaths: rootPaths,\n        pagesType: PAGE_TYPES.ROOT,\n        pagesDir: pagesDir,\n        appDir,\n      })\n      NextBuildContext.mappedRootPaths = mappedRootPaths\n\n      const pagesPageKeys = Object.keys(mappedPages)\n\n      const conflictingAppPagePaths: [pagePath: string, appPath: string][] = []\n      const appPageKeys = new Set<string>()\n      if (mappedAppPages) {\n        denormalizedAppPages = Object.keys(mappedAppPages)\n        for (const appKey of denormalizedAppPages) {\n          const normalizedAppPageKey = normalizeAppPath(appKey)\n          const pagePath = mappedPages[normalizedAppPageKey]\n          if (pagePath) {\n            const appPath = mappedAppPages[appKey]\n            conflictingAppPagePaths.push([\n              pagePath.replace(/^private-next-pages/, 'pages'),\n              appPath.replace(/^private-next-app-dir/, 'app'),\n            ])\n          }\n          appPageKeys.add(normalizedAppPageKey)\n        }\n      }\n\n      const appPaths = Array.from(appPageKeys)\n      // Interception routes are modelled as beforeFiles rewrites\n      rewrites.beforeFiles.push(\n        ...generateInterceptionRoutesRewrites(appPaths, config.basePath)\n      )\n\n      NextBuildContext.rewrites = rewrites\n\n      const totalAppPagesCount = appPaths.length\n\n      const pageKeys = {\n        pages: pagesPageKeys,\n        app: appPaths.length > 0 ? appPaths : undefined,\n      }\n\n      // Turbopack already handles conflicting app and page routes.\n      if (!isTurbopack) {\n        const numConflictingAppPaths = conflictingAppPagePaths.length\n        if (mappedAppPages && numConflictingAppPaths > 0) {\n          Log.error(\n            `Conflicting app and page file${\n              numConflictingAppPaths === 1 ? ' was' : 's were'\n            } found, please remove the conflicting files to continue:`\n          )\n          for (const [pagePath, appPath] of conflictingAppPagePaths) {\n            Log.error(`  \"${pagePath}\" - \"${appPath}\"`)\n          }\n          await telemetry.flush()\n          process.exit(1)\n        }\n      }\n\n      const conflictingPublicFiles: string[] = []\n      const hasPages404 = mappedPages['/404']?.startsWith(PAGES_DIR_ALIAS)\n      const hasApp404 = !!mappedAppPages?.[UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]\n      const hasCustomErrorPage =\n        mappedPages['/_error'].startsWith(PAGES_DIR_ALIAS)\n\n      if (hasPublicDir) {\n        const hasPublicUnderScoreNextDir = existsSync(\n          path.join(publicDir, '_next')\n        )\n        if (hasPublicUnderScoreNextDir) {\n          throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n        }\n      }\n\n      await nextBuildSpan\n        .traceChild('public-dir-conflict-check')\n        .traceAsyncFn(async () => {\n          // Check if pages conflict with files in `public`\n          // Only a page of public file can be served, not both.\n          for (const page in mappedPages) {\n            const hasPublicPageFile = await fileExists(\n              path.join(publicDir, page === '/' ? '/index' : page),\n              FileType.File\n            )\n            if (hasPublicPageFile) {\n              conflictingPublicFiles.push(page)\n            }\n          }\n\n          const numConflicting = conflictingPublicFiles.length\n\n          if (numConflicting) {\n            throw new Error(\n              `Conflicting public and page file${\n                numConflicting === 1 ? ' was' : 's were'\n              } found. https://nextjs.org/docs/messages/conflicting-public-file-page\\n${conflictingPublicFiles.join(\n                '\\n'\n              )}`\n            )\n          }\n        })\n\n      const nestedReservedPages = pageKeys.pages.filter((page) => {\n        return (\n          page.match(/\\/(_app|_document|_error)$/) && path.dirname(page) !== '/'\n        )\n      })\n\n      if (nestedReservedPages.length) {\n        Log.warn(\n          `The following reserved Next.js pages were detected not directly under the pages directory:\\n` +\n            nestedReservedPages.join('\\n') +\n            `\\nSee more info here: https://nextjs.org/docs/messages/nested-reserved-page\\n`\n        )\n      }\n\n      const restrictedRedirectPaths = ['/_next'].map((p) =>\n        config.basePath ? `${config.basePath}${p}` : p\n      )\n\n      const isAppDynamicIOEnabled = Boolean(config.experimental.dynamicIO)\n      const isAuthInterruptsEnabled = Boolean(\n        config.experimental.authInterrupts\n      )\n      const isAppPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n\n      const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n      const routesManifest: RoutesManifest = nextBuildSpan\n        .traceChild('generate-routes-manifest')\n        .traceFn(() => {\n          const sortedRoutes = getSortedRoutes([\n            ...pageKeys.pages,\n            ...(pageKeys.app ?? []),\n          ])\n          const dynamicRoutes: Array<ManifestRoute> = []\n          const staticRoutes: Array<ManifestRoute> = []\n\n          for (const route of sortedRoutes) {\n            if (isDynamicRoute(route)) {\n              dynamicRoutes.push(pageToRoute(route))\n            } else if (!isReservedPage(route)) {\n              staticRoutes.push(pageToRoute(route))\n            }\n          }\n\n          return {\n            version: 3,\n            pages404: true,\n            caseSensitive: !!config.experimental.caseSensitiveRoutes,\n            basePath: config.basePath,\n            redirects: redirects.map((r) =>\n              buildCustomRoute('redirect', r, restrictedRedirectPaths)\n            ),\n            headers: headers.map((r) => buildCustomRoute('header', r)),\n            rewrites: {\n              beforeFiles: [],\n              afterFiles: [],\n              fallback: [],\n            },\n            dynamicRoutes,\n            staticRoutes,\n            dataRoutes: [],\n            i18n: config.i18n || undefined,\n            rsc: {\n              header: RSC_HEADER,\n              // This vary header is used as a default. It is technically re-assigned in `base-server`,\n              // and may include an additional Vary option for `Next-URL`.\n              varyHeader: `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`,\n              prefetchHeader: NEXT_ROUTER_PREFETCH_HEADER,\n              didPostponeHeader: NEXT_DID_POSTPONE_HEADER,\n              contentTypeHeader: RSC_CONTENT_TYPE_HEADER,\n              suffix: RSC_SUFFIX,\n              prefetchSuffix: RSC_PREFETCH_SUFFIX,\n              prefetchSegmentHeader: NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n              prefetchSegmentSuffix: RSC_SEGMENT_SUFFIX,\n              prefetchSegmentDirSuffix: RSC_SEGMENTS_DIR_SUFFIX,\n            },\n            rewriteHeaders: {\n              pathHeader: NEXT_REWRITTEN_PATH_HEADER,\n              queryHeader: NEXT_REWRITTEN_QUERY_HEADER,\n            },\n            skipMiddlewareUrlNormalize: config.skipMiddlewareUrlNormalize,\n            ppr: isAppPPREnabled\n              ? {\n                  chain: {\n                    headers: {\n                      [NEXT_RESUME_HEADER]: '1',\n                    },\n                  },\n                }\n              : undefined,\n          } satisfies RoutesManifest\n        })\n\n      routesManifest.rewrites = {\n        beforeFiles: rewrites.beforeFiles.map((r) =>\n          buildCustomRoute('rewrite', r)\n        ),\n        afterFiles: rewrites.afterFiles.map((r) =>\n          buildCustomRoute('rewrite', r)\n        ),\n        fallback: rewrites.fallback.map((r) => buildCustomRoute('rewrite', r)),\n      }\n\n      let clientRouterFilters:\n        | undefined\n        | ReturnType<typeof createClientRouterFilter>\n\n      if (config.experimental.clientRouterFilter) {\n        const nonInternalRedirects = (config._originalRedirects || []).filter(\n          (r: any) => !r.internal\n        )\n        clientRouterFilters = createClientRouterFilter(\n          [...appPaths],\n          config.experimental.clientRouterFilterRedirects\n            ? nonInternalRedirects\n            : [],\n          config.experimental.clientRouterFilterAllowedRate\n        )\n        NextBuildContext.clientRouterFilters = clientRouterFilters\n      }\n\n      // Ensure commonjs handling is used for files in the distDir (generally .next)\n      // Files outside of the distDir can be \"type\": \"module\"\n      await writeFileUtf8(\n        path.join(distDir, 'package.json'),\n        '{\"type\": \"commonjs\"}'\n      )\n\n      // These are written to distDir, so they need to come after creating and cleaning distDr.\n      await recordFrameworkVersion(process.env.__NEXT_VERSION as string)\n      await updateBuildDiagnostics({\n        buildStage: 'start',\n      })\n\n      const outputFileTracingRoot = config.outputFileTracingRoot || dir\n\n      const pagesManifestPath = path.join(\n        distDir,\n        SERVER_DIRECTORY,\n        PAGES_MANIFEST\n      )\n\n      let buildTraceContext: undefined | BuildTraceContext\n      let buildTracesPromise: Promise<any> | undefined = undefined\n\n      // If there's has a custom webpack config and disable the build worker.\n      // Otherwise respect the option if it's set.\n      const useBuildWorker =\n        config.experimental.webpackBuildWorker ||\n        (config.experimental.webpackBuildWorker === undefined &&\n          !config.webpack)\n      const runServerAndEdgeInParallel =\n        config.experimental.parallelServerCompiles\n      const collectServerBuildTracesInParallel =\n        config.experimental.parallelServerBuildTraces ||\n        (config.experimental.parallelServerBuildTraces === undefined &&\n          isCompileMode)\n\n      nextBuildSpan.setAttribute(\n        'has-custom-webpack-config',\n        String(!!config.webpack)\n      )\n      nextBuildSpan.setAttribute('use-build-worker', String(useBuildWorker))\n\n      if (\n        !useBuildWorker &&\n        (runServerAndEdgeInParallel || collectServerBuildTracesInParallel)\n      ) {\n        throw new Error(\n          'The \"parallelServerBuildTraces\" and \"parallelServerCompiles\" options may only be used when build workers can be used. Read more: https://nextjs.org/docs/messages/parallel-build-without-worker'\n        )\n      }\n\n      Log.info('Creating an optimized production build ...')\n      traceMemoryUsage('Starting build', nextBuildSpan)\n\n      await updateBuildDiagnostics({\n        buildStage: 'compile',\n        buildOptions: {\n          useBuildWorker: String(useBuildWorker),\n        },\n      })\n\n      let shutdownPromise = Promise.resolve()\n      if (!isGenerateMode) {\n        if (isTurbopack) {\n          const {\n            duration: compilerDuration,\n            shutdownPromise: p,\n            ...rest\n          } = await turbopackBuild(\n            process.env.NEXT_TURBOPACK_USE_WORKER === undefined ||\n              process.env.NEXT_TURBOPACK_USE_WORKER !== '0'\n          )\n          shutdownPromise = p\n          traceMemoryUsage('Finished build', nextBuildSpan)\n\n          buildTraceContext = rest.buildTraceContext\n\n          const durationString = durationToString(compilerDuration)\n          Log.event(`Compiled successfully in ${durationString}`)\n\n          telemetry.record(\n            eventBuildCompleted(pagesPaths, {\n              bundler: 'turbopack',\n              durationInSeconds: Math.round(compilerDuration),\n              totalAppPagesCount,\n            })\n          )\n        } else {\n          if (\n            runServerAndEdgeInParallel ||\n            collectServerBuildTracesInParallel\n          ) {\n            let durationInSeconds = 0\n\n            await updateBuildDiagnostics({\n              buildStage: 'compile-server',\n            })\n\n            const serverBuildPromise = webpackBuild(useBuildWorker, [\n              'server',\n            ]).then((res) => {\n              traceMemoryUsage('Finished server compilation', nextBuildSpan)\n              buildTraceContext = res.buildTraceContext\n              durationInSeconds += res.duration\n\n              if (collectServerBuildTracesInParallel) {\n                const buildTraceWorker = new Worker(\n                  require.resolve('./collect-build-traces'),\n                  {\n                    debuggerPortOffset: -1,\n                    isolatedMemory: false,\n                    numWorkers: 1,\n                    exposedMethods: ['collectBuildTraces'],\n                  }\n                ) as Worker & typeof import('./collect-build-traces')\n\n                buildTracesPromise = buildTraceWorker\n                  .collectBuildTraces({\n                    dir,\n                    config,\n                    distDir,\n                    // Serialize Map as this is sent to the worker.\n                    edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(new Map()),\n                    staticPages: [],\n                    hasSsrAmpPages: false,\n                    buildTraceContext,\n                    outputFileTracingRoot,\n                    isTurbopack: false,\n                  })\n                  .catch((err) => {\n                    console.error(err)\n                    process.exit(1)\n                  })\n              }\n            })\n            if (!runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n\n            const edgeBuildPromise = webpackBuild(useBuildWorker, [\n              'edge-server',\n            ]).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage(\n                'Finished edge-server compilation',\n                nextBuildSpan\n              )\n            })\n            if (runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n            await edgeBuildPromise\n\n            await updateBuildDiagnostics({\n              buildStage: 'webpack-compile-client',\n            })\n\n            await webpackBuild(useBuildWorker, ['client']).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage('Finished client compilation', nextBuildSpan)\n            })\n\n            const durationString = durationToString(durationInSeconds)\n            Log.event(`Compiled successfully in ${durationString}`)\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds,\n                totalAppPagesCount,\n              })\n            )\n          } else {\n            const { duration: compilerDuration, ...rest } = await webpackBuild(\n              useBuildWorker,\n              null\n            )\n            traceMemoryUsage('Finished build', nextBuildSpan)\n\n            buildTraceContext = rest.buildTraceContext\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                bundler: getBundlerForTelemetry(isTurbopack),\n                durationInSeconds: compilerDuration,\n                totalAppPagesCount,\n              })\n            )\n          }\n        }\n        await runAfterProductionCompile({\n          config,\n          buildSpan: nextBuildSpan,\n          telemetry,\n          metadata: {\n            projectDir: dir,\n            distDir,\n          },\n        })\n      }\n\n      // For app directory, we run type checking after build.\n      if (appDir && !isCompileMode && !isGenerateMode) {\n        await updateBuildDiagnostics({\n          buildStage: 'type-checking',\n        })\n        await startTypeChecking(typeCheckingOptions)\n        traceMemoryUsage('Finished type checking', nextBuildSpan)\n      }\n\n      const postCompileSpinner = createSpinner('Collecting page data')\n\n      const buildManifestPath = path.join(distDir, BUILD_MANIFEST)\n      const appBuildManifestPath = path.join(distDir, APP_BUILD_MANIFEST)\n\n      let staticAppPagesCount = 0\n      let serverAppPagesCount = 0\n      let edgeRuntimeAppCount = 0\n      let edgeRuntimePagesCount = 0\n      const ssgPages = new Set<string>()\n      const ssgStaticFallbackPages = new Set<string>()\n      const ssgBlockingFallbackPages = new Set<string>()\n      const staticPages = new Set<string>()\n      const invalidPages = new Set<string>()\n      const hybridAmpPages = new Set<string>()\n      const serverPropsPages = new Set<string>()\n      const additionalPaths = new Map<string, PrerenderedRoute[]>()\n      const staticPaths = new Map<string, PrerenderedRoute[]>()\n      const appNormalizedPaths = new Map<string, string>()\n      const fallbackModes = new Map<string, FallbackMode>()\n      const appDefaultConfigs = new Map<string, AppSegmentConfig>()\n      const pageInfos: PageInfos = new Map<string, PageInfo>()\n      let pagesManifest = await readManifest<PagesManifest>(pagesManifestPath)\n      const buildManifest = await readManifest<BuildManifest>(buildManifestPath)\n      const appBuildManifest = appDir\n        ? await readManifest<AppBuildManifest>(appBuildManifestPath)\n        : undefined\n\n      const appPathRoutes: Record<string, string> = {}\n\n      if (appDir) {\n        const appPathsManifest = await readManifest<Record<string, string>>(\n          path.join(distDir, SERVER_DIRECTORY, APP_PATHS_MANIFEST)\n        )\n\n        for (const key in appPathsManifest) {\n          appPathRoutes[key] = normalizeAppPath(key)\n        }\n\n        await writeManifest(\n          path.join(distDir, APP_PATH_ROUTES_MANIFEST),\n          appPathRoutes\n        )\n      }\n\n      process.env.NEXT_PHASE = PHASE_PRODUCTION_BUILD\n\n      const worker = createStaticWorker(config, { debuggerPortOffset: -1 })\n\n      const analysisBegin = process.hrtime()\n      const staticCheckSpan = nextBuildSpan.traceChild('static-check')\n\n      const functionsConfigManifest: FunctionsConfigManifest = {\n        version: 1,\n        functions: {},\n      }\n\n      const {\n        customAppGetInitialProps,\n        namedExports,\n        isNextImageImported,\n        hasSsrAmpPages,\n        hasNonStaticErrorPage,\n      } = await staticCheckSpan.traceAsyncFn(async () => {\n        if (isCompileMode) {\n          return {\n            customAppGetInitialProps: false,\n            namedExports: [],\n            isNextImageImported: true,\n            hasSsrAmpPages: !!pagesDir,\n            hasNonStaticErrorPage: true,\n          }\n        }\n\n        const { configFileName, publicRuntimeConfig, serverRuntimeConfig } =\n          config\n        const runtimeEnvConfig = { publicRuntimeConfig, serverRuntimeConfig }\n        const sriEnabled = Boolean(config.experimental.sri?.algorithm)\n\n        const nonStaticErrorPageSpan = staticCheckSpan.traceChild(\n          'check-static-error-page'\n        )\n        const errorPageHasCustomGetInitialProps =\n          nonStaticErrorPageSpan.traceAsyncFn(\n            async () =>\n              hasCustomErrorPage &&\n              (await worker.hasCustomGetInitialProps({\n                page: '/_error',\n                distDir,\n                runtimeEnvConfig,\n                checkingApp: false,\n                sriEnabled,\n              }))\n          )\n\n        const errorPageStaticResult = nonStaticErrorPageSpan.traceAsyncFn(\n          async () =>\n            hasCustomErrorPage &&\n            worker.isPageStatic({\n              dir,\n              page: '/_error',\n              distDir,\n              configFileName,\n              runtimeEnvConfig,\n              dynamicIO: isAppDynamicIOEnabled,\n              authInterrupts: isAuthInterruptsEnabled,\n              httpAgentOptions: config.httpAgentOptions,\n              locales: config.i18n?.locales,\n              defaultLocale: config.i18n?.defaultLocale,\n              nextConfigOutput: config.output,\n              pprConfig: config.experimental.ppr,\n              cacheLifeProfiles: config.experimental.cacheLife,\n              buildId,\n              sriEnabled,\n            })\n        )\n\n        const appPageToCheck = '/_app'\n\n        const customAppGetInitialPropsPromise = worker.hasCustomGetInitialProps(\n          {\n            page: appPageToCheck,\n            distDir,\n            runtimeEnvConfig,\n            checkingApp: true,\n            sriEnabled,\n          }\n        )\n\n        const namedExportsPromise = worker.getDefinedNamedExports({\n          page: appPageToCheck,\n          distDir,\n          runtimeEnvConfig,\n          sriEnabled,\n        })\n\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let isNextImageImported: boolean | undefined\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let hasSsrAmpPages = false\n\n        const computedManifestData = await computeFromManifest(\n          { build: buildManifest, app: appBuildManifest },\n          distDir,\n          config.experimental.gzipSize\n        )\n\n        const middlewareManifest: MiddlewareManifest = require(\n          path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n        )\n\n        const actionManifest = appDir\n          ? (require(\n              path.join(\n                distDir,\n                SERVER_DIRECTORY,\n                SERVER_REFERENCE_MANIFEST + '.json'\n              )\n            ) as ActionManifest)\n          : null\n        const entriesWithAction = actionManifest ? new Set() : null\n        if (actionManifest && entriesWithAction) {\n          for (const id in actionManifest.node) {\n            for (const entry in actionManifest.node[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n          for (const id in actionManifest.edge) {\n            for (const entry in actionManifest.edge[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n        }\n\n        for (const key of Object.keys(middlewareManifest?.functions)) {\n          if (key.startsWith('/api')) {\n            edgeRuntimePagesCount++\n          }\n        }\n\n        await Promise.all(\n          Object.entries(pageKeys)\n            .reduce<Array<{ pageType: keyof typeof pageKeys; page: string }>>(\n              (acc, [key, files]) => {\n                if (!files) {\n                  return acc\n                }\n\n                const pageType = key as keyof typeof pageKeys\n\n                for (const page of files) {\n                  acc.push({ pageType, page })\n                }\n\n                return acc\n              },\n              []\n            )\n            .map(({ pageType, page }) => {\n              const checkPageSpan = staticCheckSpan.traceChild('check-page', {\n                page,\n              })\n              return checkPageSpan.traceAsyncFn(async () => {\n                const actualPage = normalizePagePath(page)\n                const [size, totalSize] = await getJsPageSizeInKb(\n                  pageType,\n                  actualPage,\n                  distDir,\n                  buildManifest,\n                  appBuildManifest,\n                  config.experimental.gzipSize,\n                  computedManifestData\n                )\n\n                let isRoutePPREnabled = false\n                let isSSG = false\n                let isStatic = false\n                let isServerComponent = false\n                let isHybridAmp = false\n                let ssgPageRoutes: string[] | null = null\n                let pagePath = ''\n\n                if (pageType === 'pages') {\n                  pagePath =\n                    pagesPaths.find((p) => {\n                      p = normalizePathSep(p)\n                      return (\n                        p.startsWith(actualPage + '.') ||\n                        p.startsWith(actualPage + '/index.')\n                      )\n                    }) || ''\n                }\n                let originalAppPath: string | undefined\n\n                if (pageType === 'app' && mappedAppPages) {\n                  for (const [originalPath, normalizedPath] of Object.entries(\n                    appPathRoutes\n                  )) {\n                    if (normalizedPath === page) {\n                      pagePath = mappedAppPages[originalPath].replace(\n                        /^private-next-app-dir/,\n                        ''\n                      )\n                      originalAppPath = originalPath\n                      break\n                    }\n                  }\n                }\n\n                const pageFilePath = isAppBuiltinNotFoundPage(pagePath)\n                  ? require.resolve(\n                      'next/dist/client/components/builtin/not-found'\n                    )\n                  : path.join(\n                      (pageType === 'pages' ? pagesDir : appDir) || '',\n                      pagePath\n                    )\n\n                const isInsideAppDir = pageType === 'app'\n                const staticInfo = pagePath\n                  ? await getStaticInfoIncludingLayouts({\n                      isInsideAppDir,\n                      pageFilePath,\n                      pageExtensions: config.pageExtensions,\n                      appDir,\n                      config,\n                      isDev: false,\n                      // If this route is an App Router page route, inherit the\n                      // route segment configs (e.g. `runtime`) from the layout by\n                      // passing the `originalAppPath`, which should end with `/page`.\n                      page: isInsideAppDir ? originalAppPath! : page,\n                    })\n                  : undefined\n\n                if (staticInfo?.hadUnsupportedValue) {\n                  errorFromUnsupportedSegmentConfig()\n                }\n\n                // If there's any thing that would contribute to the functions\n                // configuration, we need to add it to the manifest.\n                if (\n                  typeof staticInfo?.runtime !== 'undefined' ||\n                  typeof staticInfo?.maxDuration !== 'undefined' ||\n                  typeof staticInfo?.preferredRegion !== 'undefined'\n                ) {\n                  const regions = staticInfo?.preferredRegion\n                    ? typeof staticInfo.preferredRegion === 'string'\n                      ? [staticInfo.preferredRegion]\n                      : staticInfo.preferredRegion\n                    : undefined\n\n                  functionsConfigManifest.functions[page] = {\n                    maxDuration: staticInfo?.maxDuration,\n                    ...(regions && { regions }),\n                  }\n                }\n\n                const pageRuntime = middlewareManifest.functions[\n                  originalAppPath || page\n                ]\n                  ? 'edge'\n                  : staticInfo?.runtime\n\n                if (!isCompileMode) {\n                  isServerComponent =\n                    pageType === 'app' &&\n                    staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n                  if (pageType === 'app' || !isReservedPage(page)) {\n                    try {\n                      let edgeInfo: any\n\n                      if (isEdgeRuntime(pageRuntime)) {\n                        if (pageType === 'app') {\n                          edgeRuntimeAppCount++\n                        } else {\n                          edgeRuntimePagesCount++\n                        }\n\n                        const manifestKey =\n                          pageType === 'pages' ? page : originalAppPath || ''\n\n                        edgeInfo = middlewareManifest.functions[manifestKey]\n                      }\n\n                      let isPageStaticSpan =\n                        checkPageSpan.traceChild('is-page-static')\n                      let workerResult = await isPageStaticSpan.traceAsyncFn(\n                        () => {\n                          return worker.isPageStatic({\n                            dir,\n                            page,\n                            originalAppPath,\n                            distDir,\n                            configFileName,\n                            runtimeEnvConfig,\n                            httpAgentOptions: config.httpAgentOptions,\n                            locales: config.i18n?.locales,\n                            defaultLocale: config.i18n?.defaultLocale,\n                            parentId: isPageStaticSpan.getId(),\n                            pageRuntime,\n                            edgeInfo,\n                            pageType,\n                            dynamicIO: isAppDynamicIOEnabled,\n                            authInterrupts: isAuthInterruptsEnabled,\n                            cacheHandler: config.cacheHandler,\n                            cacheHandlers: config.experimental.cacheHandlers,\n                            isrFlushToDisk: ciEnvironment.hasNextSupport\n                              ? false\n                              : config.experimental.isrFlushToDisk,\n                            maxMemoryCacheSize: config.cacheMaxMemorySize,\n                            nextConfigOutput: config.output,\n                            pprConfig: config.experimental.ppr,\n                            cacheLifeProfiles: config.experimental.cacheLife,\n                            buildId,\n                            sriEnabled,\n                          })\n                        }\n                      )\n\n                      if (pageType === 'app' && originalAppPath) {\n                        appNormalizedPaths.set(originalAppPath, page)\n                        // TODO-APP: handle prerendering with edge\n                        if (isEdgeRuntime(pageRuntime)) {\n                          isStatic = false\n                          isSSG = false\n\n                          Log.warnOnce(\n                            `Using edge runtime on a page currently disables static generation for that page`\n                          )\n                        } else {\n                          const isDynamic = isDynamicRoute(page)\n\n                          if (\n                            typeof workerResult.isRoutePPREnabled === 'boolean'\n                          ) {\n                            isRoutePPREnabled = workerResult.isRoutePPREnabled\n                          }\n\n                          // If this route can be partially pre-rendered, then\n                          // mark it as such and mark that it can be\n                          // generated server-side.\n                          if (workerResult.isRoutePPREnabled) {\n                            isSSG = true\n                            isStatic = true\n\n                            staticPaths.set(originalAppPath, [])\n                          }\n\n                          if (workerResult.prerenderedRoutes) {\n                            staticPaths.set(\n                              originalAppPath,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                            isSSG = true\n                          }\n\n                          const appConfig = workerResult.appConfig || {}\n                          if (appConfig.revalidate !== 0) {\n                            const hasGenerateStaticParams =\n                              workerResult.prerenderedRoutes &&\n                              workerResult.prerenderedRoutes.length > 0\n\n                            if (\n                              config.output === 'export' &&\n                              isDynamic &&\n                              !hasGenerateStaticParams\n                            ) {\n                              throw new Error(\n                                `Page \"${page}\" is missing \"generateStaticParams()\" so it cannot be used with \"output: export\" config.`\n                              )\n                            }\n\n                            // Mark the app as static if:\n                            // - It has no dynamic param\n                            // - It doesn't have generateStaticParams but `dynamic` is set to\n                            //   `error` or `force-static`\n                            if (!isDynamic) {\n                              staticPaths.set(originalAppPath, [\n                                {\n                                  params: {},\n                                  pathname: page,\n                                  encodedPathname: page,\n                                  fallbackRouteParams: [],\n                                  fallbackMode:\n                                    workerResult.prerenderFallbackMode,\n                                  fallbackRootParams: [],\n                                  throwOnEmptyStaticShell: true,\n                                },\n                              ])\n                              isStatic = true\n                            } else if (\n                              !hasGenerateStaticParams &&\n                              (appConfig.dynamic === 'error' ||\n                                appConfig.dynamic === 'force-static')\n                            ) {\n                              staticPaths.set(originalAppPath, [])\n                              isStatic = true\n                              isRoutePPREnabled = false\n                            }\n                          }\n\n                          if (workerResult.prerenderFallbackMode) {\n                            fallbackModes.set(\n                              originalAppPath,\n                              workerResult.prerenderFallbackMode\n                            )\n                          }\n\n                          appDefaultConfigs.set(originalAppPath, appConfig)\n                        }\n                      } else {\n                        if (isEdgeRuntime(pageRuntime)) {\n                          if (workerResult.hasStaticProps) {\n                            console.warn(\n                              `\"getStaticProps\" is not yet supported fully with \"experimental-edge\", detected on ${page}`\n                            )\n                          }\n                          workerResult.isStatic = false\n                          workerResult.hasStaticProps = false\n                        }\n\n                        if (\n                          workerResult.isStatic === false &&\n                          (workerResult.isHybridAmp || workerResult.isAmpOnly)\n                        ) {\n                          hasSsrAmpPages = true\n                        }\n\n                        if (workerResult.isHybridAmp) {\n                          isHybridAmp = true\n                          hybridAmpPages.add(page)\n                        }\n\n                        if (workerResult.isNextImageImported) {\n                          isNextImageImported = true\n                        }\n\n                        if (workerResult.hasStaticProps) {\n                          ssgPages.add(page)\n                          isSSG = true\n\n                          if (\n                            workerResult.prerenderedRoutes &&\n                            workerResult.prerenderedRoutes.length > 0\n                          ) {\n                            additionalPaths.set(\n                              page,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                          }\n\n                          if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.BLOCKING_STATIC_RENDER\n                          ) {\n                            ssgBlockingFallbackPages.add(page)\n                          } else if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.PRERENDER\n                          ) {\n                            ssgStaticFallbackPages.add(page)\n                          }\n                        } else if (workerResult.hasServerProps) {\n                          serverPropsPages.add(page)\n                        } else if (\n                          workerResult.isStatic &&\n                          !isServerComponent &&\n                          (await customAppGetInitialPropsPromise) === false\n                        ) {\n                          staticPages.add(page)\n                          isStatic = true\n                        } else if (isServerComponent) {\n                          // This is a static server component page that doesn't have\n                          // gSP or gSSP. We still treat it as a SSG page.\n                          ssgPages.add(page)\n                          isSSG = true\n                        }\n\n                        if (hasPages404 && page === '/404') {\n                          if (\n                            !workerResult.isStatic &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            throw new Error(\n                              `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                            )\n                          }\n                          // we need to ensure the 404 lambda is present since we use\n                          // it when _app has getInitialProps\n                          if (\n                            (await customAppGetInitialPropsPromise) &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            staticPages.delete(page)\n                          }\n                        }\n\n                        if (\n                          STATIC_STATUS_PAGES.includes(page) &&\n                          !workerResult.isStatic &&\n                          !workerResult.hasStaticProps\n                        ) {\n                          throw new Error(\n                            `\\`pages${page}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                          )\n                        }\n                      }\n                    } catch (err) {\n                      if (\n                        !isError(err) ||\n                        err.message !== 'INVALID_DEFAULT_EXPORT'\n                      )\n                        throw err\n                      invalidPages.add(page)\n                    }\n                  }\n\n                  if (pageType === 'app') {\n                    if (isSSG || isStatic) {\n                      staticAppPagesCount++\n                    } else {\n                      serverAppPagesCount++\n                    }\n                  }\n                }\n\n                pageInfos.set(page, {\n                  originalAppPath,\n                  size,\n                  totalSize,\n                  isStatic,\n                  isSSG,\n                  isRoutePPREnabled,\n                  isHybridAmp,\n                  ssgPageRoutes,\n                  initialCacheControl: undefined,\n                  runtime: pageRuntime,\n                  pageDuration: undefined,\n                  ssgPageDurations: undefined,\n                  hasEmptyStaticShell: undefined,\n                })\n              })\n            })\n        )\n\n        const errorPageResult = await errorPageStaticResult\n        const nonStaticErrorPage =\n          (await errorPageHasCustomGetInitialProps) ||\n          (errorPageResult && errorPageResult.hasServerProps)\n\n        const returnValue = {\n          customAppGetInitialProps: await customAppGetInitialPropsPromise,\n          namedExports: await namedExportsPromise,\n          isNextImageImported,\n          hasSsrAmpPages,\n          hasNonStaticErrorPage: nonStaticErrorPage,\n        }\n\n        return returnValue\n      })\n\n      if (postCompileSpinner) postCompileSpinner.stopAndPersist()\n      traceMemoryUsage('Finished collecting page data', nextBuildSpan)\n\n      if (customAppGetInitialProps) {\n        console.warn(\n          bold(yellow(`Warning: `)) +\n            yellow(\n              `You have opted-out of Automatic Static Optimization due to \\`getInitialProps\\` in \\`pages/_app\\`. This does not opt-out pages with \\`getStaticProps\\``\n            )\n        )\n        console.warn(\n          'Read more: https://nextjs.org/docs/messages/opt-out-auto-static-optimization\\n'\n        )\n      }\n\n      const { cacheHandler } = config\n\n      const instrumentationHookEntryFiles: string[] = []\n      if (hasInstrumentationHook) {\n        instrumentationHookEntryFiles.push(\n          path.join(SERVER_DIRECTORY, `${INSTRUMENTATION_HOOK_FILENAME}.js`)\n        )\n        // If there's edge routes, append the edge instrumentation hook\n        // Turbopack generates this chunk with a hashed name and references it in middleware-manifest.\n        if (!isTurbopack && (edgeRuntimeAppCount || edgeRuntimePagesCount)) {\n          instrumentationHookEntryFiles.push(\n            path.join(\n              SERVER_DIRECTORY,\n              `edge-${INSTRUMENTATION_HOOK_FILENAME}.js`\n            )\n          )\n        }\n      }\n\n      const requiredServerFilesManifest = nextBuildSpan\n        .traceChild('generate-required-server-files')\n        .traceFn(() => {\n          const normalizedCacheHandlers: Record<string, string> = {}\n\n          for (const [key, value] of Object.entries(\n            config.experimental.cacheHandlers || {}\n          )) {\n            if (key && value) {\n              normalizedCacheHandlers[key] = path.relative(distDir, value)\n            }\n          }\n\n          const serverFilesManifest: RequiredServerFilesManifest = {\n            version: 1,\n            config: {\n              ...config,\n              configFile: undefined,\n              ...(ciEnvironment.hasNextSupport\n                ? {\n                    compress: false,\n                  }\n                : {}),\n              cacheHandler: cacheHandler\n                ? path.relative(distDir, cacheHandler)\n                : config.cacheHandler,\n              experimental: {\n                ...config.experimental,\n                cacheHandlers: normalizedCacheHandlers,\n                trustHostHeader: ciEnvironment.hasNextSupport,\n                isExperimentalCompile: isCompileMode,\n              },\n            },\n            appDir: dir,\n            relativeAppDir: path.relative(outputFileTracingRoot, dir),\n            files: [\n              ROUTES_MANIFEST,\n              path.relative(distDir, pagesManifestPath),\n              BUILD_MANIFEST,\n              PRERENDER_MANIFEST,\n              path.join(SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_BUILD_MANIFEST + '.js'),\n              ...(!isTurbopack\n                ? [\n                    path.join(\n                      SERVER_DIRECTORY,\n                      MIDDLEWARE_REACT_LOADABLE_MANIFEST + '.js'\n                    ),\n                    REACT_LOADABLE_MANIFEST,\n                  ]\n                : []),\n              ...(appDir\n                ? [\n                    ...(config.experimental.sri\n                      ? [\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.js'\n                          ),\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.json'\n                          ),\n                        ]\n                      : []),\n                    path.join(SERVER_DIRECTORY, APP_PATHS_MANIFEST),\n                    path.join(APP_PATH_ROUTES_MANIFEST),\n                    APP_BUILD_MANIFEST,\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.js'\n                    ),\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.json'\n                    ),\n                  ]\n                : []),\n              ...(pagesDir && !isTurbopack\n                ? [\n                    DYNAMIC_CSS_MANIFEST + '.json',\n                    path.join(SERVER_DIRECTORY, DYNAMIC_CSS_MANIFEST + '.js'),\n                  ]\n                : []),\n              BUILD_ID_FILE,\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.js'),\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.json'),\n              SERVER_FILES_MANIFEST,\n              ...instrumentationHookEntryFiles,\n            ]\n              .filter(nonNullable)\n              .map((file) => path.join(config.distDir, file)),\n            ignore: [] as string[],\n          }\n\n          return serverFilesManifest\n        })\n\n      if (!hasSsrAmpPages) {\n        requiredServerFilesManifest.ignore.push(\n          path.relative(\n            dir,\n            path.join(\n              path.dirname(\n                require.resolve(\n                  'next/dist/compiled/@ampproject/toolbox-optimizer'\n                )\n              ),\n              '**/*'\n            )\n          )\n        )\n      }\n\n      const middlewareFile = rootPaths.find((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n      let hasNodeMiddleware = false\n\n      if (middlewareFile) {\n        const staticInfo = await getStaticInfoIncludingLayouts({\n          isInsideAppDir: false,\n          pageFilePath: path.join(dir, middlewareFile),\n          config,\n          appDir,\n          pageExtensions: config.pageExtensions,\n          isDev: false,\n          page: 'middleware',\n        })\n\n        if (staticInfo.hadUnsupportedValue) {\n          errorFromUnsupportedSegmentConfig()\n        }\n\n        if (staticInfo.runtime === 'nodejs') {\n          hasNodeMiddleware = true\n          functionsConfigManifest.functions['/_middleware'] = {\n            runtime: staticInfo.runtime,\n            matchers: staticInfo.middleware?.matchers ?? [\n              {\n                regexp: '^.*$',\n                originalSource: '/:path*',\n              },\n            ],\n          }\n\n          if (isTurbopack) {\n            await writeManifest(\n              path.join(\n                distDir,\n                'static',\n                buildId,\n                TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST\n              ),\n              functionsConfigManifest.functions['/_middleware'].matchers || []\n            )\n          }\n        }\n      }\n\n      await writeFunctionsConfigManifest(distDir, functionsConfigManifest)\n\n      if (!isGenerateMode && !buildTracesPromise) {\n        buildTracesPromise = collectBuildTraces({\n          dir,\n          config,\n          distDir,\n          edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(pageInfos),\n          staticPages: [...staticPages],\n          nextBuildSpan,\n          hasSsrAmpPages,\n          buildTraceContext,\n          outputFileTracingRoot,\n          isTurbopack: true,\n        }).catch((err) => {\n          console.error(err)\n          process.exit(1)\n        })\n      }\n\n      if (serverPropsPages.size > 0 || ssgPages.size > 0) {\n        // We update the routes manifest after the build with the\n        // data routes since we can't determine these until after build\n        routesManifest.dataRoutes = getSortedRoutes([\n          ...serverPropsPages,\n          ...ssgPages,\n        ]).map((page) => {\n          return buildDataRoute(page, buildId)\n        })\n      }\n\n      // We need to write the manifest with rewrites before build\n      await nextBuildSpan\n        .traceChild('write-routes-manifest')\n        .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n\n      // Since custom _app.js can wrap the 404 page we have to opt-out of static optimization if it has getInitialProps\n      // Only export the static 404 when there is no /_error present\n      const useStaticPages404 =\n        !customAppGetInitialProps && (!hasNonStaticErrorPage || hasPages404)\n\n      if (invalidPages.size > 0) {\n        const err = new Error(\n          `Build optimization failed: found page${\n            invalidPages.size === 1 ? '' : 's'\n          } without a React Component as default export in \\n${[...invalidPages]\n            .map((pg) => `pages${pg}`)\n            .join(\n              '\\n'\n            )}\\n\\nSee https://nextjs.org/docs/messages/page-without-valid-component for more info.\\n`\n        ) as NextError\n        err.code = 'BUILD_OPTIMIZATION_FAILED'\n        throw err\n      }\n\n      await writeBuildId(distDir, buildId)\n\n      if (config.experimental.optimizeCss) {\n        const globOrig =\n          require('next/dist/compiled/glob') as typeof import('next/dist/compiled/glob')\n\n        const cssFilePaths = await new Promise<string[]>((resolve, reject) => {\n          globOrig(\n            '**/*.css',\n            { cwd: path.join(distDir, 'static') },\n            (err, files) => {\n              if (err) {\n                return reject(err)\n              }\n              resolve(files)\n            }\n          )\n        })\n\n        requiredServerFilesManifest.files.push(\n          ...cssFilePaths.map((filePath) =>\n            path.join(config.distDir, 'static', filePath)\n          )\n        )\n      }\n\n      const features: EventBuildFeatureUsage[] = [\n        {\n          featureName: 'experimental/dynamicIO',\n          invocationCount: config.experimental.dynamicIO ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/optimizeCss',\n          invocationCount: config.experimental.optimizeCss ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/nextScriptWorkers',\n          invocationCount: config.experimental.nextScriptWorkers ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/ppr',\n          invocationCount: config.experimental.ppr ? 1 : 0,\n        },\n        {\n          featureName: 'turbopackPersistentCaching',\n          invocationCount: isPersistentCachingEnabled(config) ? 1 : 0,\n        },\n      ]\n      telemetry.record(\n        features.map((feature) => {\n          return {\n            eventName: EVENT_BUILD_FEATURE_USAGE,\n            payload: feature,\n          }\n        })\n      )\n\n      await writeRequiredServerFilesManifest(\n        distDir,\n        requiredServerFilesManifest\n      )\n\n      // we don't need to inline for turbopack build as\n      // it will handle it's own caching separate of compile\n      if (isGenerateMode && !isTurbopack) {\n        Log.info('Inlining static env ...')\n\n        await nextBuildSpan\n          .traceChild('inline-static-env')\n          .traceAsyncFn(async () => {\n            await inlineStaticEnv({\n              distDir,\n              config,\n            })\n          })\n      }\n\n      const middlewareManifest: MiddlewareManifest = await readManifest(\n        path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      )\n\n      const prerenderManifest: PrerenderManifest = {\n        version: 4,\n        routes: {},\n        dynamicRoutes: {},\n        notFoundRoutes: [],\n        preview: previewProps,\n      }\n\n      const tbdPrerenderRoutes: string[] = []\n\n      const { i18n } = config\n\n      const usedStaticStatusPages = STATIC_STATUS_PAGES.filter(\n        (page) =>\n          mappedPages[page] &&\n          mappedPages[page].startsWith('private-next-pages')\n      )\n      usedStaticStatusPages.forEach((page) => {\n        if (!ssgPages.has(page) && !customAppGetInitialProps) {\n          staticPages.add(page)\n        }\n      })\n\n      const hasPages500 = usedStaticStatusPages.includes('/500')\n      const useDefaultStatic500 =\n        !hasPages500 && !hasNonStaticErrorPage && !customAppGetInitialProps\n\n      const combinedPages = [...staticPages, ...ssgPages]\n      const isApp404Static = staticPaths.has(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      const hasStaticApp404 = hasApp404 && isApp404Static\n\n      await updateBuildDiagnostics({\n        buildStage: 'static-generation',\n      })\n\n      // we need to trigger automatic exporting when we have\n      // - static 404/500\n      // - getStaticProps paths\n      // - experimental app is enabled\n      if (\n        !isCompileMode &&\n        (combinedPages.length > 0 ||\n          useStaticPages404 ||\n          useDefaultStatic500 ||\n          appDir)\n      ) {\n        const staticGenerationSpan =\n          nextBuildSpan.traceChild('static-generation')\n        await staticGenerationSpan.traceAsyncFn(async () => {\n          detectConflictingPaths(\n            [\n              ...combinedPages,\n              ...pageKeys.pages.filter((page) => !combinedPages.includes(page)),\n            ],\n            ssgPages,\n            new Map(\n              Array.from(additionalPaths.entries()).map(\n                ([page, routes]): [string, string[]] => {\n                  return [page, routes.map((route) => route.pathname)]\n                }\n              )\n            )\n          )\n\n          const exportApp = (require('../export') as typeof import('../export'))\n            .default as typeof import('../export').default\n\n          const exportConfig: NextConfigComplete = {\n            ...config,\n            // Default map will be the collection of automatic statically exported\n            // pages and incremental pages.\n            // n.b. we cannot handle this above in combinedPages because the dynamic\n            // page must be in the `pages` array, but not in the mapping.\n            exportPathMap: (defaultMap: ExportPathMap) => {\n              // Dynamically routed pages should be prerendered to be used as\n              // a client-side skeleton (fallback) while data is being fetched.\n              // This ensures the end-user never sees a 500 or slow response from the\n              // server.\n              //\n              // Note: prerendering disables automatic static optimization.\n              ssgPages.forEach((page) => {\n                if (isDynamicRoute(page)) {\n                  tbdPrerenderRoutes.push(page)\n\n                  if (ssgStaticFallbackPages.has(page)) {\n                    // Override the rendering for the dynamic page to be treated as a\n                    // fallback render.\n                    if (i18n) {\n                      defaultMap[`/${i18n.defaultLocale}${page}`] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    } else {\n                      defaultMap[page] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    }\n                  } else {\n                    // Remove dynamically routed pages from the default path map when\n                    // fallback behavior is disabled.\n                    delete defaultMap[page]\n                  }\n                }\n              })\n\n              // Append the \"well-known\" routes we should prerender for, e.g. blog\n              // post slugs.\n              additionalPaths.forEach((routes, page) => {\n                routes.forEach((route) => {\n                  defaultMap[route.pathname] = {\n                    page,\n                    _ssgPath: route.encodedPathname,\n                  }\n                })\n              })\n\n              if (useStaticPages404) {\n                defaultMap['/404'] = {\n                  page: hasPages404 ? '/404' : '/_error',\n                }\n              }\n\n              if (useDefaultStatic500) {\n                defaultMap['/500'] = {\n                  page: '/_error',\n                }\n              }\n\n              // TODO: output manifest specific to app paths and their\n              // revalidate periods and dynamicParams settings\n              staticPaths.forEach((routes, originalAppPath) => {\n                const appConfig = appDefaultConfigs.get(originalAppPath)\n                const isDynamicError = appConfig?.dynamic === 'error'\n\n                const isRoutePPREnabled: boolean = appConfig\n                  ? checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                  : false\n\n                routes.forEach((route) => {\n                  // If the route has any dynamic root segments, we need to skip\n                  // rendering the route. This is because we don't support\n                  // revalidating the shells without the parameters present.\n                  if (\n                    route.fallbackRootParams &&\n                    route.fallbackRootParams.length > 0\n                  ) {\n                    return\n                  }\n\n                  defaultMap[route.pathname] = {\n                    page: originalAppPath,\n                    _ssgPath: route.encodedPathname,\n                    _fallbackRouteParams: route.fallbackRouteParams,\n                    _isDynamicError: isDynamicError,\n                    _isAppDir: true,\n                    _isRoutePPREnabled: isRoutePPREnabled,\n                    _allowEmptyStaticShell: !route.throwOnEmptyStaticShell,\n                  }\n                })\n              })\n\n              if (i18n) {\n                for (const page of [\n                  ...staticPages,\n                  ...ssgPages,\n                  ...(useStaticPages404 ? ['/404'] : []),\n                  ...(useDefaultStatic500 ? ['/500'] : []),\n                ]) {\n                  const isSsg = ssgPages.has(page)\n                  const isDynamic = isDynamicRoute(page)\n                  const isFallback = isSsg && ssgStaticFallbackPages.has(page)\n\n                  for (const locale of i18n.locales) {\n                    // skip fallback generation for SSG pages without fallback mode\n                    if (isSsg && isDynamic && !isFallback) continue\n                    const outputPath = `/${locale}${page === '/' ? '' : page}`\n\n                    defaultMap[outputPath] = {\n                      page: defaultMap[page]?.page || page,\n                      _locale: locale,\n                      _pagesFallback: isFallback,\n                    }\n                  }\n\n                  if (isSsg) {\n                    // remove non-locale prefixed variant from defaultMap\n                    delete defaultMap[page]\n                  }\n                }\n              }\n\n              return defaultMap\n            },\n          }\n\n          const outdir = path.join(distDir, 'export')\n          const exportResult = await exportApp(\n            dir,\n            {\n              nextConfig: exportConfig,\n              enabledDirectories,\n              silent: true,\n              buildExport: true,\n              debugOutput,\n              debugPrerender,\n              pages: combinedPages,\n              outdir,\n              statusMessage: 'Generating static pages',\n              numWorkers: getNumberOfWorkers(exportConfig),\n            },\n            nextBuildSpan\n          )\n\n          // If there was no result, there's nothing more to do.\n          if (!exportResult) return\n\n          const getFallbackMode = (route: PrerenderedRoute) => {\n            const hasEmptyStaticShell = exportResult.byPath.get(\n              route.pathname\n            )?.hasEmptyStaticShell\n\n            // If the route has an empty static shell and is not configured to\n            // throw on empty static shell, then we should use the blocking\n            // static render mode.\n            if (\n              hasEmptyStaticShell &&\n              !route.throwOnEmptyStaticShell &&\n              route.fallbackMode === FallbackMode.PRERENDER\n            ) {\n              return FallbackMode.BLOCKING_STATIC_RENDER\n            }\n\n            // If the route has no fallback mode, then we should use the\n            // `NOT_FOUND` fallback mode.\n            if (!route.fallbackMode) {\n              return FallbackMode.NOT_FOUND\n            }\n\n            return route.fallbackMode\n          }\n\n          const getCacheControl = (\n            exportPath: string,\n            defaultRevalidate: Revalidate = false\n          ): CacheControl => {\n            const cacheControl =\n              exportResult.byPath.get(exportPath)?.cacheControl\n\n            if (!cacheControl) {\n              return { revalidate: defaultRevalidate, expire: undefined }\n            }\n\n            if (\n              cacheControl.revalidate !== false &&\n              cacheControl.revalidate > 0 &&\n              cacheControl.expire === undefined\n            ) {\n              return {\n                revalidate: cacheControl.revalidate,\n                expire: config.expireTime,\n              }\n            }\n\n            return cacheControl\n          }\n\n          if (debugOutput || process.env.NEXT_SSG_FETCH_METRICS === '1') {\n            recordFetchMetrics(exportResult)\n          }\n\n          writeTurborepoAccessTraceResult({\n            distDir: config.distDir,\n            traces: [\n              turborepoAccessTraceResult,\n              ...exportResult.turborepoAccessTraceResults.values(),\n            ],\n          })\n\n          prerenderManifest.notFoundRoutes = Array.from(\n            exportResult.ssgNotFoundPaths\n          )\n\n          // remove server bundles that were exported\n          for (const page of staticPages) {\n            const serverBundle = getPagePath(page, distDir, undefined, false)\n            await fs.unlink(serverBundle)\n          }\n\n          staticPaths.forEach((prerenderedRoutes, originalAppPath) => {\n            const page = appNormalizedPaths.get(originalAppPath)\n            if (!page) throw new InvariantError('Page not found')\n\n            const appConfig = appDefaultConfigs.get(originalAppPath)\n            if (!appConfig) throw new InvariantError('App config not found')\n\n            let hasRevalidateZero =\n              appConfig.revalidate === 0 ||\n              getCacheControl(page).revalidate === 0\n\n            if (hasRevalidateZero && pageInfos.get(page)?.isStatic) {\n              // if the page was marked as being static, but it contains dynamic data\n              // (ie, in the case of a static generation bailout), then it should be marked dynamic\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                isStatic: false,\n                isSSG: false,\n              })\n            }\n\n            const isAppRouteHandler = isAppRouteRoute(originalAppPath)\n\n            // When this is an app page and PPR is enabled, the route supports\n            // partial pre-rendering.\n            const isRoutePPREnabled: true | undefined =\n              !isAppRouteHandler &&\n              checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                ? true\n                : undefined\n\n            const htmlBotsRegexString =\n              // The htmlLimitedBots has been converted to a string during loadConfig\n              config.htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING\n\n            // this flag is used to selectively bypass the static cache and invoke the lambda directly\n            // to enable server actions on static routes\n            const bypassFor: RouteHas[] = [\n              { type: 'header', key: ACTION_HEADER },\n              {\n                type: 'header',\n                key: 'content-type',\n                value: 'multipart/form-data;.*',\n              },\n              // If it's PPR rendered non-static page, bypass the PPR cache when streaming metadata is enabled.\n              // This will skip the postpone data for those bots requests and instead produce a dynamic render.\n              ...(isRoutePPREnabled\n                ? [\n                    {\n                      type: 'header',\n                      key: 'user-agent',\n                      value: htmlBotsRegexString,\n                    },\n                  ]\n                : []),\n            ]\n\n            // We should collect all the dynamic routes into a single array for\n            // this page. Including the full fallback route (the original\n            // route), any routes that were generated with unknown route params\n            // should be collected and included in the dynamic routes part\n            // of the manifest instead.\n            const routes: PrerenderedRoute[] = []\n            const dynamicRoutes: PrerenderedRoute[] = []\n\n            // Sort the outputted routes to ensure consistent output. Any route\n            // though that has unknown route params will be pulled and sorted\n            // independently. This is because the routes with unknown route\n            // params will contain the dynamic path parameters, some of which\n            // may conflict with the actual prerendered routes.\n            let unknownPrerenderRoutes: PrerenderedRoute[] = []\n            let knownPrerenderRoutes: PrerenderedRoute[] = []\n            for (const prerenderedRoute of prerenderedRoutes) {\n              if (\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                unknownPrerenderRoutes.push(prerenderedRoute)\n              } else {\n                knownPrerenderRoutes.push(prerenderedRoute)\n              }\n            }\n\n            unknownPrerenderRoutes = getSortedRouteObjects(\n              unknownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n            knownPrerenderRoutes = getSortedRouteObjects(\n              knownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n\n            prerenderedRoutes = [\n              ...knownPrerenderRoutes,\n              ...unknownPrerenderRoutes,\n            ]\n\n            for (const prerenderedRoute of prerenderedRoutes) {\n              // TODO: check if still needed?\n              // Exclude the /_not-found route.\n              if (prerenderedRoute.pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n                continue\n              }\n\n              if (\n                isRoutePPREnabled &&\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                // If the route has unknown params, then we need to add it to\n                // the list of dynamic routes.\n                dynamicRoutes.push(prerenderedRoute)\n              } else {\n                // If the route doesn't have unknown params, then we need to\n                // add it to the list of routes.\n                routes.push(prerenderedRoute)\n              }\n            }\n\n            // Handle all the static routes.\n            for (const route of routes) {\n              if (isDynamicRoute(page) && route.pathname === page) continue\n              if (route.pathname === UNDERSCORE_NOT_FOUND_ROUTE) continue\n\n              const {\n                metadata = {},\n                hasEmptyStaticShell,\n                hasPostponed,\n              } = exportResult.byPath.get(route.pathname) ?? {}\n\n              const cacheControl = getCacheControl(\n                route.pathname,\n                appConfig.revalidate\n              )\n\n              pageInfos.set(route.pathname, {\n                ...(pageInfos.get(route.pathname) as PageInfo),\n                hasPostponed,\n                hasEmptyStaticShell,\n                initialCacheControl: cacheControl,\n              })\n\n              // update the page (eg /blog/[slug]) to also have the postpone metadata\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                hasPostponed,\n                hasEmptyStaticShell,\n                initialCacheControl: cacheControl,\n              })\n\n              if (cacheControl.revalidate !== 0) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                let dataRoute: string | null\n                if (isAppRouteHandler) {\n                  dataRoute = null\n                } else {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | null | undefined\n                // While we may only write the `.rsc` when the route does not\n                // have PPR enabled, we still want to generate the route when\n                // deployed so it doesn't 404. If the app has PPR enabled, we\n                // should add this key.\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                const meta = collectMeta(metadata)\n\n                prerenderManifest.routes[route.pathname] = {\n                  initialStatus: meta.status,\n                  initialHeaders: meta.headers,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalPPR: isRoutePPREnabled,\n                  experimentalBypassFor: bypassFor,\n                  initialRevalidateSeconds: cacheControl.revalidate,\n                  initialExpireSeconds: cacheControl.expire,\n                  srcRoute: page,\n                  dataRoute,\n                  prefetchDataRoute,\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              } else {\n                hasRevalidateZero = true\n                // we might have determined during prerendering that this page\n                // used dynamic data\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isSSG: false,\n                  isStatic: false,\n                })\n              }\n            }\n\n            if (!hasRevalidateZero && isDynamicRoute(page)) {\n              // When PPR fallbacks aren't used, we need to include it here. If\n              // they are enabled, then it'll already be included in the\n              // prerendered routes.\n              if (!isRoutePPREnabled) {\n                dynamicRoutes.push({\n                  params: {},\n                  pathname: page,\n                  encodedPathname: page,\n                  fallbackRouteParams: [],\n                  fallbackMode:\n                    fallbackModes.get(originalAppPath) ??\n                    FallbackMode.NOT_FOUND,\n                  fallbackRootParams: [],\n                  throwOnEmptyStaticShell: true,\n                })\n              }\n\n              for (const route of dynamicRoutes) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                const metadata = exportResult.byPath.get(\n                  route.pathname\n                )?.metadata\n\n                const cacheControl = getCacheControl(route.pathname)\n\n                let dataRoute: string | null = null\n                if (!isAppRouteHandler) {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | undefined\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                if (!isAppRouteHandler && metadata?.segmentPaths) {\n                  const dynamicRoute = routesManifest.dynamicRoutes.find(\n                    (r) => r.page === page\n                  )\n                  if (!dynamicRoute) {\n                    throw new Error('Dynamic route not found')\n                  }\n\n                  dynamicRoute.prefetchSegmentDataRoutes ??= []\n                  for (const segmentPath of metadata.segmentPaths) {\n                    dynamicRoute.prefetchSegmentDataRoutes.push(\n                      buildPrefetchSegmentDataRoute(route.pathname, segmentPath)\n                    )\n                  }\n                }\n\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isDynamicAppRoute: true,\n                  // if PPR is turned on and the route contains a dynamic segment,\n                  // we assume it'll be partially prerendered\n                  hasPostponed: isRoutePPREnabled,\n                })\n\n                const fallbackMode = getFallbackMode(route)\n\n                // When the route is configured to serve a prerender, we should\n                // use the cache control from the export result. If it can't be\n                // found, mark that we should keep the shell forever\n                // (revalidate: `false` via `getCacheControl()`).\n                const fallbackCacheControl =\n                  isRoutePPREnabled && fallbackMode === FallbackMode.PRERENDER\n                    ? cacheControl\n                    : undefined\n\n                const fallback: Fallback = fallbackModeToFallbackField(\n                  fallbackMode,\n                  route.pathname\n                )\n\n                const meta =\n                  metadata &&\n                  isRoutePPREnabled &&\n                  fallbackMode === FallbackMode.PRERENDER\n                    ? collectMeta(metadata)\n                    : {}\n\n                prerenderManifest.dynamicRoutes[route.pathname] = {\n                  experimentalPPR: isRoutePPREnabled,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalBypassFor: bypassFor,\n                  routeRegex: normalizeRouteRegex(\n                    getNamedRouteRegex(route.pathname, {\n                      prefixRouteKeys: false,\n                    }).re.source\n                  ),\n                  dataRoute,\n                  fallback,\n                  fallbackRevalidate: fallbackCacheControl?.revalidate,\n                  fallbackExpire: fallbackCacheControl?.expire,\n                  fallbackStatus: meta.status,\n                  fallbackHeaders: meta.headers,\n                  fallbackRootParams: fallback\n                    ? route.fallbackRootParams\n                    : undefined,\n                  fallbackSourceRoute: route.fallbackRouteParams?.length\n                    ? page\n                    : undefined,\n                  dataRouteRegex: !dataRoute\n                    ? null\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(dataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  prefetchDataRoute,\n                  prefetchDataRouteRegex: !prefetchDataRoute\n                    ? undefined\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(prefetchDataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              }\n            }\n          })\n\n          const moveExportedPage = async (\n            originPage: string,\n            page: string,\n            file: string,\n            isSsg: boolean,\n            ext: 'html' | 'json',\n            additionalSsgFile = false\n          ) => {\n            return staticGenerationSpan\n              .traceChild('move-exported-page')\n              .traceAsyncFn(async () => {\n                file = `${file}.${ext}`\n                const orig = path.join(outdir, file)\n                const pagePath = getPagePath(\n                  originPage,\n                  distDir,\n                  undefined,\n                  false\n                )\n\n                const relativeDest = path\n                  .relative(\n                    path.join(distDir, SERVER_DIRECTORY),\n                    path.join(\n                      path.join(\n                        pagePath,\n                        // strip leading / and then recurse number of nested dirs\n                        // to place from base folder\n                        originPage\n                          .slice(1)\n                          .split('/')\n                          .map(() => '..')\n                          .join('/')\n                      ),\n                      file\n                    )\n                  )\n                  .replace(/\\\\/g, '/')\n\n                if (\n                  !isSsg &&\n                  !(\n                    // don't add static status page to manifest if it's\n                    // the default generated version e.g. no pages/500\n                    (\n                      STATIC_STATUS_PAGES.includes(page) &&\n                      !usedStaticStatusPages.includes(page)\n                    )\n                  )\n                ) {\n                  pagesManifest[page] = relativeDest\n                }\n\n                const dest = path.join(distDir, SERVER_DIRECTORY, relativeDest)\n                const isNotFound =\n                  prerenderManifest.notFoundRoutes.includes(page)\n\n                // for SSG files with i18n the non-prerendered variants are\n                // output with the locale prefixed so don't attempt moving\n                // without the prefix\n                if ((!i18n || additionalSsgFile) && !isNotFound) {\n                  await fs.mkdir(path.dirname(dest), { recursive: true })\n                  await fs.rename(orig, dest)\n                } else if (i18n && !isSsg) {\n                  // this will be updated with the locale prefixed variant\n                  // since all files are output with the locale prefix\n                  delete pagesManifest[page]\n                }\n\n                if (i18n) {\n                  if (additionalSsgFile) return\n\n                  const localeExt = page === '/' ? path.extname(file) : ''\n                  const relativeDestNoPages = relativeDest.slice(\n                    'pages/'.length\n                  )\n\n                  for (const locale of i18n.locales) {\n                    const curPath = `/${locale}${page === '/' ? '' : page}`\n\n                    if (\n                      isSsg &&\n                      prerenderManifest.notFoundRoutes.includes(curPath)\n                    ) {\n                      continue\n                    }\n\n                    const updatedRelativeDest = path\n                      .join(\n                        'pages',\n                        locale + localeExt,\n                        // if it's the top-most index page we want it to be locale.EXT\n                        // instead of locale/index.html\n                        page === '/' ? '' : relativeDestNoPages\n                      )\n                      .replace(/\\\\/g, '/')\n\n                    const updatedOrig = path.join(\n                      outdir,\n                      locale + localeExt,\n                      page === '/' ? '' : file\n                    )\n                    const updatedDest = path.join(\n                      distDir,\n                      SERVER_DIRECTORY,\n                      updatedRelativeDest\n                    )\n\n                    if (!isSsg) {\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                    await fs.mkdir(path.dirname(updatedDest), {\n                      recursive: true,\n                    })\n                    await fs.rename(updatedOrig, updatedDest)\n                  }\n                }\n              })\n          }\n\n          async function moveExportedAppNotFoundTo404() {\n            return staticGenerationSpan\n              .traceChild('move-exported-app-not-found-')\n              .traceAsyncFn(async () => {\n                const orig = path.join(\n                  distDir,\n                  'server',\n                  'app',\n                  '_not-found.html'\n                )\n                const updatedRelativeDest = path\n                  .join('pages', '404.html')\n                  .replace(/\\\\/g, '/')\n\n                if (existsSync(orig)) {\n                  await fs.copyFile(\n                    orig,\n                    path.join(distDir, 'server', updatedRelativeDest)\n                  )\n\n                  // since the app router not found is prioritized over pages router,\n                  // we have to ensure the app router entries are available for all locales\n                  if (i18n) {\n                    for (const locale of i18n.locales) {\n                      const curPath = `/${locale}/404`\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                  }\n\n                  pagesManifest['/404'] = updatedRelativeDest\n                }\n              })\n          }\n\n          // If there's /not-found inside app, we prefer it over the pages 404\n          if (hasStaticApp404) {\n            await moveExportedAppNotFoundTo404()\n          } else {\n            // Only move /404 to /404 when there is no custom 404 as in that case we don't know about the 404 page\n            if (!hasPages404 && !hasApp404 && useStaticPages404) {\n              await moveExportedPage('/_error', '/404', '/404', false, 'html')\n            }\n          }\n\n          if (useDefaultStatic500) {\n            await moveExportedPage('/_error', '/500', '/500', false, 'html')\n          }\n\n          for (const page of combinedPages) {\n            const isSsg = ssgPages.has(page)\n            const isStaticSsgFallback = ssgStaticFallbackPages.has(page)\n            const isDynamic = isDynamicRoute(page)\n            const hasAmp = hybridAmpPages.has(page)\n            const file = normalizePagePath(page)\n\n            const pageInfo = pageInfos.get(page)\n            const durationInfo = exportResult.byPage.get(page)\n            if (pageInfo && durationInfo) {\n              // Set Build Duration\n              if (pageInfo.ssgPageRoutes) {\n                pageInfo.ssgPageDurations = pageInfo.ssgPageRoutes.map(\n                  (pagePath) => {\n                    const duration = durationInfo.durationsByPath.get(pagePath)\n                    if (typeof duration === 'undefined') {\n                      throw new Error(\"Invariant: page wasn't built\")\n                    }\n\n                    return duration\n                  }\n                )\n              }\n              pageInfo.pageDuration = durationInfo.durationsByPath.get(page)\n            }\n\n            // The dynamic version of SSG pages are only prerendered if the\n            // fallback is enabled. Below, we handle the specific prerenders\n            // of these.\n            const hasHtmlOutput = !(isSsg && isDynamic && !isStaticSsgFallback)\n\n            if (hasHtmlOutput) {\n              await moveExportedPage(page, page, file, isSsg, 'html')\n            }\n\n            if (hasAmp && (!isSsg || (isSsg && !isDynamic))) {\n              const ampPage = `${file}.amp`\n              await moveExportedPage(page, ampPage, ampPage, isSsg, 'html')\n\n              if (isSsg) {\n                await moveExportedPage(page, ampPage, ampPage, isSsg, 'json')\n              }\n            }\n\n            if (isSsg) {\n              // For a non-dynamic SSG page, we must copy its data file\n              // from export, we already moved the HTML file above\n              if (!isDynamic) {\n                await moveExportedPage(page, page, file, isSsg, 'json')\n\n                if (i18n) {\n                  // TODO: do we want to show all locale variants in build output\n                  for (const locale of i18n.locales) {\n                    const localePage = `/${locale}${page === '/' ? '' : page}`\n\n                    const cacheControl = getCacheControl(localePage)\n\n                    prerenderManifest.routes[localePage] = {\n                      initialRevalidateSeconds: cacheControl.revalidate,\n                      initialExpireSeconds: cacheControl.expire,\n                      experimentalPPR: undefined,\n                      renderingMode: undefined,\n                      srcRoute: null,\n                      dataRoute: path.posix.join(\n                        '/_next/data',\n                        buildId,\n                        `${file}.json`\n                      ),\n                      prefetchDataRoute: undefined,\n                      allowHeader: ALLOWED_HEADERS,\n                    }\n                  }\n                } else {\n                  const cacheControl = getCacheControl(page)\n\n                  prerenderManifest.routes[page] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: null,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${file}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n                }\n                if (pageInfo) {\n                  pageInfo.initialCacheControl = getCacheControl(page)\n                }\n              } else {\n                // For a dynamic SSG page, we did not copy its data exports and only\n                // copy the fallback HTML file (if present).\n                // We must also copy specific versions of this page as defined by\n                // `getStaticPaths` (additionalSsgPaths).\n                for (const route of additionalPaths.get(page) ?? []) {\n                  const pageFile = normalizePagePath(route.pathname)\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'html',\n                    true\n                  )\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'json',\n                    true\n                  )\n\n                  if (hasAmp) {\n                    const ampPage = `${pageFile}.amp`\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'html',\n                      true\n                    )\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'json',\n                      true\n                    )\n                  }\n\n                  const cacheControl = getCacheControl(route.pathname)\n\n                  prerenderManifest.routes[route.pathname] = {\n                    initialRevalidateSeconds: cacheControl.revalidate,\n                    initialExpireSeconds: cacheControl.expire,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: page,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${normalizePagePath(route.pathname)}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n\n                  if (pageInfo) {\n                    pageInfo.initialCacheControl = cacheControl\n                  }\n                }\n              }\n            }\n          }\n\n          // remove temporary export folder\n          await fs.rm(outdir, { recursive: true, force: true })\n          await writeManifest(pagesManifestPath, pagesManifest)\n\n          if (config.experimental.clientSegmentCache) {\n            for (const route of [\n              ...routesManifest.staticRoutes,\n              ...routesManifest.dynamicRoutes,\n            ]) {\n              // If the segment paths aren't defined, we need to insert a\n              // reverse routing rule so that there isn't any conflicts\n              // with other dynamic routes for the prefetch segment\n              // routes. This is true for any route that is not PPR-enabled,\n              // including all routes defined by Pages Router.\n\n              // We don't need to add the prefetch segment data routes if it was\n              // added due to a page that was already generated. This would have\n              // happened if the page was static or partially static.\n              if (route.prefetchSegmentDataRoutes) {\n                continue\n              }\n\n              route.prefetchSegmentDataRoutes = [\n                buildInversePrefetchSegmentDataRoute(\n                  route.page,\n                  // We use the special segment path of `/_tree` because it's\n                  // the first one sent by the client router so it's the only\n                  // one we need to rewrite to the regular prefetch RSC route.\n                  '/_tree'\n                ),\n              ]\n            }\n          }\n        })\n\n        // We need to write the manifest with rewrites after build as it might\n        // have been modified.\n        await nextBuildSpan\n          .traceChild('write-routes-manifest')\n          .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n      }\n\n      const postBuildSpinner = createSpinner('Finalizing page optimization')\n      let buildTracesSpinner = createSpinner(`Collecting build traces`)\n\n      // ensure the worker is not left hanging\n      worker.end()\n\n      const analysisEnd = process.hrtime(analysisBegin)\n      telemetry.record(\n        eventBuildOptimize(pagesPaths, {\n          durationInSeconds: analysisEnd[0],\n          staticPageCount: staticPages.size,\n          staticPropsPageCount: ssgPages.size,\n          serverPropsPageCount: serverPropsPages.size,\n          ssrPageCount:\n            pagesPaths.length -\n            (staticPages.size + ssgPages.size + serverPropsPages.size),\n          hasStatic404: useStaticPages404,\n          hasReportWebVitals:\n            namedExports?.includes('reportWebVitals') ?? false,\n          rewritesCount: combinedRewrites.length,\n          headersCount: headers.length,\n          redirectsCount: redirects.length - 1, // reduce one for trailing slash\n          headersWithHasCount: headers.filter((r: any) => !!r.has).length,\n          rewritesWithHasCount: combinedRewrites.filter((r: any) => !!r.has)\n            .length,\n          redirectsWithHasCount: redirects.filter((r: any) => !!r.has).length,\n          middlewareCount: hasMiddlewareFile ? 1 : 0,\n          totalAppPagesCount,\n          staticAppPagesCount,\n          serverAppPagesCount,\n          edgeRuntimeAppCount,\n          edgeRuntimePagesCount,\n        })\n      )\n\n      if (NextBuildContext.telemetryState) {\n        const events = eventBuildFeatureUsage(\n          NextBuildContext.telemetryState.usages\n        )\n        telemetry.record(events)\n        telemetry.record(\n          eventPackageUsedInGetServerSideProps(\n            NextBuildContext.telemetryState.packagesUsedInServerSideProps\n          )\n        )\n        const useCacheTracker = NextBuildContext.telemetryState.useCacheTracker\n\n        for (const [key, value] of Object.entries(useCacheTracker)) {\n          telemetry.record(\n            eventBuildFeatureUsage([\n              {\n                featureName: key as UseCacheTrackerKey,\n                invocationCount: value,\n              },\n            ])\n          )\n        }\n      }\n\n      if (ssgPages.size > 0 || appDir) {\n        tbdPrerenderRoutes.forEach((tbdRoute) => {\n          const normalizedRoute = normalizePagePath(tbdRoute)\n          const dataRoute = path.posix.join(\n            '/_next/data',\n            buildId,\n            `${normalizedRoute}.json`\n          )\n\n          prerenderManifest.dynamicRoutes[tbdRoute] = {\n            routeRegex: normalizeRouteRegex(\n              getNamedRouteRegex(tbdRoute, {\n                prefixRouteKeys: false,\n              }).re.source\n            ),\n            experimentalPPR: undefined,\n            renderingMode: undefined,\n            dataRoute,\n            fallback: ssgBlockingFallbackPages.has(tbdRoute)\n              ? null\n              : ssgStaticFallbackPages.has(tbdRoute)\n                ? `${normalizedRoute}.html`\n                : false,\n            fallbackRevalidate: undefined,\n            fallbackExpire: undefined,\n            fallbackSourceRoute: undefined,\n            fallbackRootParams: undefined,\n            dataRouteRegex: normalizeRouteRegex(\n              getNamedRouteRegex(dataRoute, {\n                prefixRouteKeys: true,\n                includeSuffix: true,\n                excludeOptionalTrailingSlash: true,\n              }).re.source\n            ),\n            // Pages does not have a prefetch data route.\n            prefetchDataRoute: undefined,\n            prefetchDataRouteRegex: undefined,\n            allowHeader: ALLOWED_HEADERS,\n          }\n        })\n\n        NextBuildContext.previewModeId = previewProps.previewModeId\n        NextBuildContext.fetchCacheKeyPrefix =\n          config.experimental.fetchCacheKeyPrefix\n        NextBuildContext.allowedRevalidateHeaderKeys =\n          config.experimental.allowedRevalidateHeaderKeys\n\n        await writePrerenderManifest(distDir, prerenderManifest)\n        await writeClientSsgManifest(prerenderManifest, {\n          distDir,\n          buildId,\n          locales: config.i18n?.locales,\n        })\n      } else {\n        await writePrerenderManifest(distDir, {\n          version: 4,\n          routes: {},\n          dynamicRoutes: {},\n          preview: previewProps,\n          notFoundRoutes: [],\n        })\n      }\n\n      await writeImagesManifest(distDir, config)\n      await writeManifest(path.join(distDir, EXPORT_MARKER), {\n        version: 1,\n        hasExportPathMap: typeof config.exportPathMap === 'function',\n        exportTrailingSlash: config.trailingSlash === true,\n        isNextImageImported: isNextImageImported === true,\n      })\n      await fs.unlink(path.join(distDir, EXPORT_DETAIL)).catch((err) => {\n        if (err.code === 'ENOENT') {\n          return Promise.resolve()\n        }\n        return Promise.reject(err)\n      })\n\n      if (Boolean(config.experimental.nextScriptWorkers)) {\n        await nextBuildSpan\n          .traceChild('verify-partytown-setup')\n          .traceAsyncFn(async () => {\n            await verifyPartytownSetup(\n              dir,\n              path.join(distDir, CLIENT_STATIC_FILES_PATH)\n            )\n          })\n      }\n\n      await buildTracesPromise\n\n      if (buildTracesSpinner) {\n        buildTracesSpinner.stopAndPersist()\n        buildTracesSpinner = undefined\n      }\n\n      if (isCompileMode) {\n        Log.info(\n          `Build ran with \"compile\" mode, to finalize the build run either \"generate\" or \"generate-env\" mode as well`\n        )\n      }\n\n      if (config.output === 'export') {\n        await writeFullyStaticExport(\n          config,\n          dir,\n          enabledDirectories,\n          configOutDir,\n          nextBuildSpan\n        )\n      }\n\n      if (config.experimental.adapterPath) {\n        await handleBuildComplete({\n          dir,\n          distDir,\n          tracingRoot: outputFileTracingRoot,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          adapterPath: config.experimental.adapterPath,\n          pageKeys: pageKeys.pages,\n          appPageKeys: denormalizedAppPages,\n          routesManifest,\n          prerenderManifest,\n          middlewareManifest,\n          functionsConfigManifest,\n          requiredServerFiles: requiredServerFilesManifest.files,\n        })\n      }\n\n      if (config.output === 'standalone') {\n        await writeStandaloneDirectory(\n          nextBuildSpan,\n          distDir,\n          pageKeys,\n          denormalizedAppPages,\n          outputFileTracingRoot,\n          requiredServerFilesManifest,\n          middlewareManifest,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          staticPages,\n          loadedEnvFiles,\n          appDir\n        )\n      }\n\n      if (postBuildSpinner) postBuildSpinner.stopAndPersist()\n      console.log()\n\n      if (debugOutput) {\n        nextBuildSpan\n          .traceChild('print-custom-routes')\n          .traceFn(() => printCustomRoutes({ redirects, rewrites, headers }))\n      }\n\n      await nextBuildSpan.traceChild('print-tree-view').traceAsyncFn(() =>\n        printTreeView(pageKeys, pageInfos, {\n          distPath: distDir,\n          buildId: buildId,\n          pagesDir,\n          useStaticPages404,\n          pageExtensions: config.pageExtensions,\n          appBuildManifest,\n          buildManifest,\n          middlewareManifest,\n          gzipSize: config.experimental.gzipSize,\n        })\n      )\n\n      await nextBuildSpan\n        .traceChild('telemetry-flush')\n        .traceAsyncFn(() => telemetry.flush())\n\n      await shutdownPromise\n    })\n  } catch (e) {\n    const telemetry: Telemetry | undefined = traceGlobals.get('telemetry')\n    if (telemetry) {\n      telemetry.record(\n        eventBuildFailed({\n          bundler: getBundlerForTelemetry(isTurbopack),\n          errorCode: getErrorCodeForTelemetry(e),\n          durationInSeconds: Math.floor((Date.now() - buildStartTime) / 1000),\n        })\n      )\n    }\n    throw e\n  } finally {\n    // Ensure we wait for lockfile patching if present\n    await lockfilePatchPromise.cur\n\n    if (isTurbopack && !process.env.__NEXT_TEST_MODE) {\n      warnAboutTurbopackBuilds(loadedConfig)\n    }\n\n    // Ensure all traces are flushed before finishing the command\n    await flushAllTraces()\n    teardownTraceSubscriber()\n\n    if (traceUploadUrl && loadedConfig) {\n      uploadTrace({\n        traceUploadUrl,\n        mode: 'build',\n        projectDir: dir,\n        distDir: loadedConfig.distDir,\n        isTurboSession: isTurbopack,\n        sync: true,\n      })\n    }\n  }\n}\n\nfunction errorFromUnsupportedSegmentConfig(): never {\n  Log.error(\n    `Invalid segment configuration export detected. This can cause unexpected behavior from the configs not being applied. You should see the relevant failures in the logs above. Please fix them to continue.`\n  )\n  process.exit(1)\n}\n\nfunction warnAboutTurbopackBuilds(config?: NextConfigComplete) {\n  let warningStr =\n    `Support for Turbopack builds is experimental. ` +\n    bold(\n      `We don't recommend deploying mission-critical applications to production.`\n    )\n  warningStr +=\n    '\\n\\n- ' +\n    bold(\n      'Turbopack currently always builds production source maps for the browser. This will include project source code if deployed to production.'\n    )\n  warningStr +=\n    '\\n- It is expected that your bundle size might be different from `next build` with webpack. This will be improved as we work towards stability.'\n\n  if (!config?.experimental.turbopackPersistentCaching) {\n    warningStr +=\n      '\\n- This build is without disk caching; subsequent builds will become faster when disk caching becomes available.'\n  }\n\n  warningStr +=\n    '\\n- When comparing output to webpack builds, make sure to first clear the Next.js cache by deleting the `.next` directory.'\n  warningStr +=\n    '\\n\\nProvide feedback for Turbopack builds at https://github.com/vercel/next.js/discussions/77721'\n\n  Log.warn(warningStr)\n}\n\nfunction getBundlerForTelemetry(isTurbopack: boolean) {\n  if (isTurbopack) {\n    return 'turbopack'\n  }\n\n  if (process.env.NEXT_RSPACK) {\n    return 'rspack'\n  }\n\n  return 'webpack'\n}\n\nfunction getErrorCodeForTelemetry(err: unknown) {\n  const code = extractNextErrorCode(err)\n  if (code != null) {\n    return code\n  }\n\n  if (err instanceof Error && 'code' in err && typeof err.code === 'string') {\n    return err.code\n  }\n\n  if (err instanceof Error) {\n    return err.name\n  }\n\n  return 'Unknown'\n}\n"], "names": ["RouteType", "createStaticWorker", "build", "ALLOWED_HEADERS", "MATCHED_PATH_HEADER", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "pageToRoute", "page", "routeRegex", "getNamedRouteRegex", "prefixRouteKeys", "regex", "normalizeRouteRegex", "re", "source", "routeKeys", "namedRegex", "getCacheDir", "distDir", "cacheDir", "path", "join", "ciEnvironment", "isCI", "hasNextSupport", "<PERSON><PERSON><PERSON>", "existsSync", "console", "log", "Log", "prefixes", "warn", "writeFileUtf8", "filePath", "content", "fs", "writeFile", "readFileUtf8", "readFile", "writeManifest", "manifest", "formatManifest", "readManifest", "JSON", "parse", "writePrerenderManifest", "PRERENDER_MANIFEST", "writeClientSsgManifest", "prerenderManifest", "buildId", "locales", "ssgPages", "Set", "Object", "entries", "routes", "filter", "srcRoute", "map", "route", "normalizeLocalePath", "pathname", "keys", "dynamicRoutes", "sort", "clientSsgManifestContent", "devalue", "CLIENT_STATIC_FILES_PATH", "writeFunctionsConfigManifest", "SERVER_DIRECTORY", "FUNCTIONS_CONFIG_MANIFEST", "writeRequiredServerFilesManifest", "requiredServerFiles", "SERVER_FILES_MANIFEST", "writeImagesManifest", "config", "images", "deviceSizes", "imageSizes", "sizes", "remotePatterns", "p", "protocol", "replace", "hostname", "makeRe", "port", "dot", "search", "localPatterns", "IMAGES_MANIFEST", "version", "STANDALONE_DIRECTORY", "writeStandaloneDirectory", "nextBuildSpan", "pageKeys", "denormalizedAppPages", "outputFileTracingRoot", "middlewareManifest", "hasNodeMiddleware", "hasInstrumentationHook", "staticPages", "loadedEnvFiles", "appDir", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "copyTracedFiles", "pages", "file", "files", "reduce", "acc", "envFile", "includes", "push", "outputPath", "relative", "mkdir", "dirname", "recursive", "copyFile", "middlewareOutput", "recursiveCopy", "overwrite", "originalServerApp", "getNumberOfWorkers", "experimental", "cpus", "defaultConfig", "memoryBasedWorkersCount", "Math", "max", "min", "floor", "os", "freemem", "staticWorkerPath", "require", "resolve", "staticWorkerExposedMethods", "options", "debuggerPortOffset", "progress", "Worker", "logger", "numWorkers", "onActivity", "run", "onActivityAbort", "clear", "enableSourceMaps", "enablePrerenderSourceMaps", "isolated<PERSON><PERSON><PERSON>", "enableWorkerThreads", "workerThreads", "exposedMethods", "writeFullyStaticExport", "dir", "enabledDirectories", "configOutDir", "exportApp", "default", "buildExport", "nextConfig", "silent", "outdir", "getBuildId", "isGenerateMode", "generateBuildId", "nanoid", "reactProductionProfiling", "debugOutput", "debugPrerender", "runLint", "noMangling", "appDirOnly", "isTurbopack", "experimentalBuildMode", "traceUploadUrl", "isCompileMode", "NextBuildContext", "buildStartTime", "Date", "now", "loadedConfig", "trace", "undefined", "buildMode", "isTurboBuild", "String", "process", "env", "__NEXT_VERSION", "mappedPages", "traceFn", "loadEnvConfig", "turborepoAccessTraceResult", "TurborepoAccessTraceResult", "turborepoTraceAccess", "loadConfig", "PHASE_PRODUCTION_BUILD", "NEXT_DEPLOYMENT_ID", "deploymentId", "hasCustomExportOutput", "setGlobal", "exit", "info", "inlineStaticEnv", "flushAllTraces", "teardownTraceSubscriber", "populateStaticEnv", "customRoutes", "loadCustomRoutes", "headers", "rewrites", "redirects", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "hasRewrites", "length", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "telemetry", "Telemetry", "publicDir", "pagesDir", "findPagesDir", "app", "<PERSON><PERSON><PERSON>", "generateEncryptionKeyBase64", "isBuild", "isSrcDir", "startsWith", "hasPublicDir", "record", "eventCliSession", "webpackVersion", "cliCommand", "has<PERSON>ow<PERSON><PERSON>", "findUp", "cwd", "isCustomServer", "turboFlag", "eventNextPlugins", "then", "events", "eventSwcPlugins", "envInfo", "experimentalFeatures", "getStartServerInfo", "dev", "logStartInfo", "networkUrl", "appUrl", "ignoreESLint", "Boolean", "eslint", "ignoreDuringBuilds", "shouldLint", "typeCheckingOptions", "distDirCreated", "err", "isError", "code", "isWriteable", "Error", "cleanDistDir", "recursiveDelete", "startTypeChecking", "error", "flush", "buildLintEvent", "featureName", "invocationCount", "eventName", "EVENT_BUILD_FEATURE_USAGE", "payload", "validFile<PERSON><PERSON><PERSON>", "createValidFileMatcher", "pageExtensions", "providedPagePaths", "NEXT_PRIVATE_PAGE_PATHS", "pagesPaths", "recursiveReadDir", "pathnameFilter", "isPageFile", "middlewareDetectionRegExp", "RegExp", "MIDDLEWARE_FILENAME", "instrumentationHookDetectionRegExp", "INSTRUMENTATION_HOOK_FILENAME", "rootDir", "rootPaths", "Array", "from", "getFilesInDir", "some", "include", "test", "sortByPageExts", "hasMiddlewareFile", "previewProps", "generatePreviewKeys", "createPagesMapping", "isDev", "pagesType", "PAGE_TYPES", "PAGES", "pagePaths", "mappedAppPages", "providedAppPaths", "NEXT_PRIVATE_APP_PATHS", "appPaths", "absolutePath", "isAppRouterPage", "isRootNotFound", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "APP", "mappedRootPaths", "ROOT", "pagesPageKeys", "conflictingAppPagePaths", "appPageKeys", "appKey", "normalizedAppPageKey", "normalizeAppPath", "pagePath", "appPath", "add", "generateInterceptionRoutesRewrites", "basePath", "totalAppPagesCount", "numConflictingAppPaths", "conflictingPublicFiles", "hasPages404", "PAGES_DIR_ALIAS", "hasApp404", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "hasCustomErrorPage", "hasPublicUnderScoreNextDir", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "hasPublicPageFile", "fileExists", "FileType", "File", "numConflicting", "nestedReservedPages", "match", "restrictedRedirectPaths", "isAppDynamicIOEnabled", "dynamicIO", "isAuthInterruptsEnabled", "authInterrupts", "isAppPPREnabled", "checkIsAppPPREnabled", "ppr", "routesManifestPath", "ROUTES_MANIFEST", "routesManifest", "sortedRoutes", "getSortedRoutes", "staticRoutes", "isDynamicRoute", "isReservedPage", "pages404", "caseSensitive", "caseSensitiveRoutes", "r", "buildCustomRoute", "dataRoutes", "i18n", "rsc", "header", "RSC_HEADER", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "prefetch<PERSON><PERSON><PERSON>", "didPostponeHeader", "NEXT_DID_POSTPONE_HEADER", "contentTypeHeader", "RSC_CONTENT_TYPE_HEADER", "suffix", "RSC_SUFFIX", "prefetchSuffix", "RSC_PREFETCH_SUFFIX", "prefetchSegmentHeader", "prefetchSegmentSuffix", "RSC_SEGMENT_SUFFIX", "prefetchSegmentDirSuffix", "RSC_SEGMENTS_DIR_SUFFIX", "rewriteHeaders", "pathHeader", "NEXT_REWRITTEN_PATH_HEADER", "query<PERSON>eader", "NEXT_REWRITTEN_QUERY_HEADER", "skipMiddlewareUrlNormalize", "chain", "NEXT_RESUME_HEADER", "clientRouterFilters", "clientRouterFilter", "nonInternalRedirects", "internal", "createClientRouterFilter", "clientRouterFilterRedirects", "clientRouterFilterAllowedRate", "recordFrameworkVersion", "updateBuildDiagnostics", "buildStage", "pagesManifestPath", "PAGES_MANIFEST", "buildTraceContext", "buildTracesPromise", "useBuildWorker", "webpackBuildWorker", "webpack", "runServerAndEdgeInParallel", "parallelServerCompiles", "collectServerBuildTracesInParallel", "parallelServerBuildTraces", "setAttribute", "traceMemoryUsage", "buildOptions", "shutdownPromise", "Promise", "duration", "compilerDuration", "rest", "turbopackBuild", "NEXT_TURBOPACK_USE_WORKER", "durationString", "durationToString", "event", "eventBuildCompleted", "bundler", "durationInSeconds", "round", "serverBuildPromise", "webpackBuild", "res", "buildTraceWorker", "collectBuildTraces", "edgeRuntimeRoutes", "collectRoutesUsingEdgeRuntime", "Map", "hasSsrAmpPages", "catch", "edgeBuildPromise", "getBundlerForTelemetry", "runAfterProductionCompile", "buildSpan", "metadata", "projectDir", "postCompileSpinner", "createSpinner", "buildManifestPath", "BUILD_MANIFEST", "appBuildManifestPath", "APP_BUILD_MANIFEST", "staticAppPagesCount", "serverAppPagesCount", "edgeRuntimeAppCount", "edgeRuntimePagesCount", "ssgStaticFallbackPages", "ssgBlockingFallbackPages", "invalidPages", "hybridAmpPages", "serverPropsPages", "additionalPaths", "staticPaths", "appNormalizedPaths", "fallbackModes", "appDefaultConfigs", "pageInfos", "pagesManifest", "buildManifest", "appBuildManifest", "appPathRoutes", "appPathsManifest", "APP_PATHS_MANIFEST", "key", "APP_PATH_ROUTES_MANIFEST", "NEXT_PHASE", "worker", "analysisBegin", "hrtime", "staticCheckSpan", "functionsConfigManifest", "functions", "customAppGetInitialProps", "namedExports", "isNextImageImported", "hasNonStaticErrorPage", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "runtimeEnvConfig", "sriEnabled", "sri", "algorithm", "nonStaticErrorPageSpan", "errorPageHasCustomGetInitialProps", "hasCustomGetInitialProps", "checkingApp", "errorPageStaticResult", "isPageStatic", "httpAgentOptions", "defaultLocale", "nextConfigOutput", "output", "pprConfig", "cacheLifeProfiles", "cacheLife", "appPageToCheck", "customAppGetInitialPropsPromise", "namedExportsPromise", "getDefinedNamedExports", "computedManifestData", "computeFromManifest", "gzipSize", "MIDDLEWARE_MANIFEST", "actionManifest", "SERVER_REFERENCE_MANIFEST", "entriesWithAction", "id", "node", "entry", "workers", "edge", "all", "pageType", "checkPageSpan", "actualPage", "normalizePagePath", "size", "totalSize", "getJsPageSizeInKb", "isRoutePPREnabled", "isSSG", "isStatic", "isServerComponent", "isHybridAmp", "ssgPageRoutes", "find", "normalizePathSep", "originalAppPath", "originalPath", "normalizedPath", "pageFilePath", "isAppBuiltinNotFoundPage", "isInsideAppDir", "staticInfo", "getStaticInfoIncludingLayouts", "hadUnsupportedValue", "errorFromUnsupportedSegmentConfig", "runtime", "maxDuration", "preferredRegion", "regions", "pageRuntime", "RSC_MODULE_TYPES", "client", "edgeInfo", "isEdgeRuntime", "manifest<PERSON>ey", "isPageStaticSpan", "workerResult", "parentId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "isrFlushToDisk", "maxMemoryCacheSize", "cacheMaxMemorySize", "set", "warnOnce", "isDynamic", "prerenderedRoutes", "appConfig", "revalidate", "hasGenerateStaticParams", "params", "encodedPathname", "fallbackRouteParams", "fallbackMode", "prerenderFallbackMode", "fallbackRootParams", "throwOnEmptyStaticShell", "dynamic", "hasStaticProps", "isAmpOnly", "FallbackMode", "BLOCKING_STATIC_RENDER", "PRERENDER", "hasServerProps", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "delete", "STATIC_STATUS_PAGES", "message", "initialCacheControl", "pageDuration", "ssgPageDurations", "hasEmptyStaticShell", "errorPageResult", "nonStaticErrorPage", "returnValue", "stopAndPersist", "bold", "yellow", "instrumentationHookEntryFiles", "requiredServerFilesManifest", "normalizedCacheHandlers", "value", "serverFilesManifest", "configFile", "compress", "trustHostHeader", "isExperimentalCompile", "relativeAppDir", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "REACT_LOADABLE_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "DYNAMIC_CSS_MANIFEST", "BUILD_ID_FILE", "NEXT_FONT_MANIFEST", "nonNullable", "ignore", "middlewareFile", "matchers", "middleware", "regexp", "originalSource", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "buildDataRoute", "useStaticPages404", "pg", "writeBuildId", "optimizeCss", "globOrig", "cssFilePaths", "reject", "features", "nextScriptWorkers", "isPersistentCachingEnabled", "feature", "notFoundRoutes", "preview", "tbdPrerenderRoutes", "usedStaticStatusPages", "for<PERSON>ach", "has", "hasPages500", "useDefaultStatic500", "combinedPages", "isApp404Static", "hasStaticApp404", "staticGenerationSpan", "detectConflictingPaths", "exportConfig", "exportPathMap", "defaultMap", "_pagesFallback", "_ssgPath", "get", "isDynamicError", "checkIsRoutePPREnabled", "_fallbackRouteParams", "_isDynamicError", "_isAppDir", "_isRoutePPREnabled", "_allowEmptyStaticShell", "isSsg", "<PERSON><PERSON><PERSON><PERSON>", "locale", "_locale", "exportResult", "statusMessage", "getFallbackMode", "by<PERSON><PERSON>", "NOT_FOUND", "getCacheControl", "exportPath", "defaultRevalidate", "cacheControl", "expire", "expireTime", "NEXT_SSG_FETCH_METRICS", "recordFetchMetrics", "writeTurborepoAccessTraceResult", "traces", "turborepoAccessTraceResults", "values", "ssgNotFoundPaths", "serverBundle", "getPagePath", "unlink", "InvariantError", "hasRevalidateZero", "isAppRouteHandler", "isAppRouteRoute", "htmlBotsRegexString", "htmlLimitedBots", "HTML_LIMITED_BOT_UA_RE_STRING", "bypassFor", "type", "ACTION_HEADER", "unknown<PERSON>rerender<PERSON><PERSON><PERSON>", "knownPrerenderRoutes", "prerenderedRoute", "getSortedRouteObjects", "UNDERSCORE_NOT_FOUND_ROUTE", "hasPostponed", "normalizedRoute", "dataRoute", "posix", "prefetchDataRoute", "meta", "collectMeta", "initialStatus", "status", "initialHeaders", "renderingMode", "RenderingMode", "PARTIALLY_STATIC", "STATIC", "experimentalPPR", "experimentalBypassFor", "initialRevalidateSeconds", "initialExpireSeconds", "allow<PERSON>eader", "segmentPaths", "dynamicRoute", "prefetchSegmentDataRoutes", "segmentPath", "buildPrefetchSegmentDataRoute", "isDynamicAppRoute", "fallbackCacheControl", "fallbackModeToFallbackField", "fallbackRevalidate", "fallbackExpire", "fallback<PERSON><PERSON><PERSON>", "fallbackHeaders", "fallbackSourceRoute", "dataRouteRegex", "includeSuffix", "excludeOptionalTrailingSlash", "prefetchDataRouteRegex", "moveExportedPage", "originPage", "ext", "additionalSsgFile", "orig", "relativeDest", "slice", "split", "dest", "isNotFound", "rename", "localeExt", "extname", "relativeDestNoPages", "curPath", "updatedRelativeDest", "updatedOrig", "updatedDest", "moveExportedAppNotFoundTo404", "isStaticSsgFallback", "hasAmp", "pageInfo", "durationInfo", "byPage", "durationsByPath", "hasHtmlOutput", "ampPage", "localePage", "pageFile", "rm", "force", "clientSegmentCache", "buildInversePrefetchSegmentDataRoute", "postBuildSpinner", "buildTracesSpinner", "end", "analysisEnd", "eventBuildOptimize", "staticPageCount", "staticPropsPageCount", "serverPropsPageCount", "ssrPageCount", "hasStatic404", "hasReportWebVitals", "rewritesCount", "headersCount", "redirectsCount", "headersWithHasCount", "rewritesWithHasCount", "redirectsWithHasCount", "middlewareCount", "telemetryState", "eventBuildFeatureUsage", "usages", "eventPackageUsedInGetServerSideProps", "packagesUsedInServerSideProps", "useCacheTracker", "tbdRoute", "previewModeId", "fetchCacheKeyPrefix", "allowedRevalidateHeaderKeys", "EXPORT_MARKER", "hasExportPathMap", "exportTrailingSlash", "trailingSlash", "EXPORT_DETAIL", "verifyPartytownSetup", "adapterPath", "handleBuildComplete", "tracingRoot", "printCustomRoutes", "printTreeView", "distPath", "e", "traceGlobals", "eventBuildFailed", "errorCode", "getErrorCodeForTelemetry", "lockfilePatchPromise", "cur", "__NEXT_TEST_MODE", "warnAboutTurbopackBuilds", "uploadTrace", "mode", "isTurboSession", "sync", "warningStr", "turbopackPersistentCaching", "NEXT_RSPACK", "extractNextErrorCode", "name"], "mappings": ";;;;;;;;;;;;;;;;IA0WYA,SAAS;eAATA;;IA0ZIC,kBAAkB;eAAlBA;;IAmEhB,OA67FC;eA77F6BC;;;QAh0BvB;qBAE4C;4BACtB;2BACN;oBACoB;2DAC5B;wBACQ;8BACO;gEACV;+DACD;0BACI;6DACN;2BAiBV;4BAC8B;8BACR;0EAGtB;6BAQqB;iCACI;sCACK;4BA+B9B;uBAKA;+DAEgB;mCAEW;yBACN;gEACG;sCAKxB;wBAWA;yBAEmB;yBAKnB;2BACoB;iCACK;6BACJ;6DACP;gEACK;uBACkC;wBAYrD;8BAIsB;qCACO;gEAChB;+BAEU;+BACA;kCACG;qBAC6B;4BAC3B;+BACL;4BACE;0BACC;kCAW1B;8BACsB;8BACsB;kCAClB;iCACD;0CACS;8BACF;2BACL;oDACiB;gCAEpB;oCACI;gCAEJ;kCAKxB;4BAC0C;wBAEX;kCACL;wBACA;uCACW;oEAEpB;qBAIjB;0BACmD;+BAC5B;gCACC;uBACe;+CAMvC;gCAEwB;wBACY;iCACX;2BACE;kCACD;wBACJ;qCACQ;wCACK;iCACN;+BACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2HpC;;;CAGC,GACD,MAAMC,kBAA4B;IAChC;IACAC,8BAAmB;IACnBC,sCAA2B;IAC3BC,qDAA0C;IAC1CC,6CAAkC;IAClCC,iDAAsC;CACvC;AAiBM,IAAA,AAAKR,mCAAAA;IACV;;GAEC;IAED;;GAEC;IAED;;;GAGC;IAED;;;GAGC;IAGD;;GAEC,GACD,mBAAmB;IAEnB;;GAEC;;WA3BSA;;AA+GZ,SAASS,YAAYC,IAAY;IAC/B,MAAMC,aAAaC,IAAAA,8BAAkB,EAACF,MAAM;QAC1CG,iBAAiB;IACnB;IACA,OAAO;QACLH;QACAI,OAAOC,IAAAA,qCAAmB,EAACJ,WAAWK,EAAE,CAACC,MAAM;QAC/CC,WAAWP,WAAWO,SAAS;QAC/BC,YAAYR,WAAWQ,UAAU;IACnC;AACF;AAEA,SAASC,YAAYC,OAAe;IAClC,MAAMC,WAAWC,aAAI,CAACC,IAAI,CAACH,SAAS;IACpC,IAAII,QAAcC,IAAI,IAAI,CAACD,QAAcE,cAAc,EAAE;QACvD,MAAMC,WAAWC,IAAAA,cAAU,EAACP;QAE5B,IAAI,CAACM,UAAU;YACb,kGAAkG;YAClG,sBAAsB;YACtBE,QAAQC,GAAG,CACT,GAAGC,KAAIC,QAAQ,CAACC,IAAI,CAAC,+HAA+H,CAAC;QAEzJ;IACF;IACA,OAAOZ;AACT;AAEA,eAAea,cAAcC,QAAgB,EAAEC,OAAe;IAC5D,MAAMC,YAAE,CAACC,SAAS,CAACH,UAAUC,SAAS;AACxC;AAEA,SAASG,aAAaJ,QAAgB;IACpC,OAAOE,YAAE,CAACG,QAAQ,CAACL,UAAU;AAC/B;AAEA,eAAeM,cACbN,QAAgB,EAChBO,QAAW;IAEX,MAAMR,cAAcC,UAAUQ,IAAAA,8BAAc,EAACD;AAC/C;AAEA,eAAeE,aAA+BT,QAAgB;IAC5D,OAAOU,KAAKC,KAAK,CAAC,MAAMP,aAAaJ;AACvC;AAEA,eAAeY,uBACb3B,OAAe,EACfsB,QAAyC;IAEzC,MAAMD,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAAS4B,8BAAkB,GAAGN;AAC9D;AAEA,eAAeO,uBACbC,iBAAkD,EAClD,EACEC,OAAO,EACP/B,OAAO,EACPgC,OAAO,EAKR;IAED,MAAMC,WAAW,IAAIC,IACnB;WACKC,OAAOC,OAAO,CAACN,kBAAkBO,MAAM,CACxC,4BAA4B;SAC3BC,MAAM,CAAC,CAAC,GAAG,EAAEC,QAAQ,EAAE,CAAC,GAAKA,YAAY,MACzCC,GAAG,CAAC,CAAC,CAACC,MAAM,GAAKC,IAAAA,wCAAmB,EAACD,OAAOT,SAASW,QAAQ;WAC7DR,OAAOS,IAAI,CAACd,kBAAkBe,aAAa;KAC/C,CAACC,IAAI;IAGR,MAAMC,2BAA2B,CAAC,oBAAoB,EAAEC,IAAAA,gBAAO,EAC7Df,UACA,iDAAiD,CAAC;IAEpD,MAAMnB,cACJZ,aAAI,CAACC,IAAI,CAACH,SAASiD,oCAAwB,EAAElB,SAAS,oBACtDgB;AAEJ;AAoBA,eAAeG,6BACblD,OAAe,EACfsB,QAAiC;IAEjC,MAAMD,cACJnB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEC,qCAAyB,GAC9D9B;AAEJ;AAWA,eAAe+B,iCACbrD,OAAe,EACfsD,mBAAgD;IAEhD,MAAMjC,cACJnB,aAAI,CAACC,IAAI,CAACH,SAASuD,iCAAqB,GACxCD;AAEJ;AAEA,eAAeE,oBACbxD,OAAe,EACfyD,MAA0B;QAODA,gBAUrBA;IAfJ,MAAMC,SAAS;QAAE,GAAGD,OAAOC,MAAM;IAAC;IAClC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAE,GAAGF;IAClCA,OAAeG,KAAK,GAAG;WAAIF;WAAgBC;KAAW;IAExD,8DAA8D;IAC9DF,OAAOI,cAAc,GAAG,AAACL,CAAAA,CAAAA,2BAAAA,iBAAAA,OAAQC,MAAM,qBAAdD,eAAgBK,cAAc,KAAI,EAAE,AAAD,EAAGtB,GAAG,CAAC,CAACuB;YAExDA;eAF+D;YACzE,iEAAiE;YACjEC,QAAQ,GAAED,cAAAA,EAAEC,QAAQ,qBAAVD,YAAYE,OAAO,CAAC,MAAM;YACpCC,UAAUC,IAAAA,iBAAM,EAACJ,EAAEG,QAAQ,EAAEtE,MAAM;YACnCwE,MAAML,EAAEK,IAAI;YACZzB,UAAUwB,IAAAA,iBAAM,EAACJ,EAAEpB,QAAQ,IAAI,MAAM;gBAAE0B,KAAK;YAAK,GAAGzE,MAAM;YAC1D0E,QAAQP,EAAEO,MAAM;QAClB;;IAEA,oEAAoE;IACpE,IAAIb,2BAAAA,kBAAAA,OAAQC,MAAM,qBAAdD,gBAAgBc,aAAa,EAAE;QACjCb,OAAOa,aAAa,GAAGd,OAAOC,MAAM,CAACa,aAAa,CAAC/B,GAAG,CAAC,CAACuB,IAAO,CAAA;gBAC7D,gEAAgE;gBAChEpB,UAAUwB,IAAAA,iBAAM,EAACJ,EAAEpB,QAAQ,IAAI,MAAM;oBAAE0B,KAAK;gBAAK,GAAGzE,MAAM;gBAC1D0E,QAAQP,EAAEO,MAAM;YAClB,CAAA;IACF;IAEA,MAAMjD,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAASwE,2BAAe,GAAG;QACvDC,SAAS;QACTf;IACF;AACF;AAEA,MAAMgB,uBAAuB;AAC7B,eAAeC,yBACbC,aAAmB,EACnB5E,OAAe,EACf6E,QAAwD,EACxDC,oBAA0C,EAC1CC,qBAA6B,EAC7BzB,mBAAgD,EAChD0B,kBAAsC,EACtCC,iBAA0B,EAC1BC,sBAA+B,EAC/BC,WAAwB,EACxBC,cAA8B,EAC9BC,MAA0B;IAE1B,MAAMT,cACHU,UAAU,CAAC,8BACXC,YAAY,CAAC;QACZ,MAAMC,IAAAA,uBAAe,EACnB,kFAAkF;QAClFlC,oBAAoB+B,MAAM,EAC1BrF,SACA6E,SAASY,KAAK,EACdX,sBACAC,uBACAzB,oBAAoBG,MAAM,EAC1BuB,oBACAC,mBACAC,wBACAC;QAGF,KAAK,MAAMO,QAAQ;eACdpC,oBAAoBqC,KAAK;YAC5BzF,aAAI,CAACC,IAAI,CAACmD,oBAAoBG,MAAM,CAACzD,OAAO,EAAEuD,iCAAqB;eAChE6B,eAAeQ,MAAM,CAAW,CAACC,KAAKC;gBACvC,IAAI;oBAAC;oBAAQ;iBAAkB,CAACC,QAAQ,CAACD,QAAQ5F,IAAI,GAAG;oBACtD2F,IAAIG,IAAI,CAACF,QAAQ5F,IAAI;gBACvB;gBACA,OAAO2F;YACT,GAAG,EAAE;SACN,CAAE;YACD,kFAAkF;YAClF,MAAM9E,WAAWb,aAAI,CAACC,IAAI,CAACmD,oBAAoB+B,MAAM,EAAEK;YACvD,MAAMO,aAAa/F,aAAI,CAACC,IAAI,CAC1BH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuBhE;YAEvC,MAAME,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAACH,aAAa;gBACvCI,WAAW;YACb;YACA,MAAMpF,YAAE,CAACqF,QAAQ,CAACvF,UAAUkF;QAC9B;QAEA,IAAIhB,mBAAmB;YACrB,MAAMsB,mBAAmBrG,aAAI,CAACC,IAAI,CAChCH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB/E,UACrCmD,4BAAgB,EAChB;YAGF,MAAMlC,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAACG,mBAAmB;gBAAEF,WAAW;YAAK;YACjE,MAAMpF,YAAE,CAACqF,QAAQ,CACfpG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE,kBACrCoD;QAEJ;QAEA,MAAMC,IAAAA,4BAAa,EACjBtG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE,UACrCjD,aAAI,CAACC,IAAI,CACPH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB/E,UACrCmD,4BAAgB,EAChB,UAEF;YAAEsD,WAAW;QAAK;QAEpB,IAAIpB,QAAQ;YACV,MAAMqB,oBAAoBxG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE;YAC/D,IAAI3C,IAAAA,cAAU,EAACkG,oBAAoB;gBACjC,MAAMF,IAAAA,4BAAa,EACjBE,mBACAxG,aAAI,CAACC,IAAI,CACPH,SACA0E,sBACAxE,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB/E,UACrCmD,4BAAgB,EAChB,QAEF;oBAAEsD,WAAW;gBAAK;YAEtB;QACF;IACF;AACJ;AAEA,SAASE,mBAAmBlD,MAA0B;IACpD,IACEA,OAAOmD,YAAY,CAACC,IAAI,IACxBpD,OAAOmD,YAAY,CAACC,IAAI,KAAKC,2BAAa,CAACF,YAAY,CAAEC,IAAI,EAC7D;QACA,OAAOpD,OAAOmD,YAAY,CAACC,IAAI;IACjC;IAEA,IAAIpD,OAAOmD,YAAY,CAACG,uBAAuB,EAAE;QAC/C,OAAOC,KAAKC,GAAG,CACbD,KAAKE,GAAG,CAACzD,OAAOmD,YAAY,CAACC,IAAI,IAAI,GAAGG,KAAKG,KAAK,CAACC,WAAE,CAACC,OAAO,KAAK,OAClE,iCAAiC;QACjC;IAEJ;IAEA,IAAI5D,OAAOmD,YAAY,CAACC,IAAI,EAAE;QAC5B,OAAOpD,OAAOmD,YAAY,CAACC,IAAI;IACjC;IAEA,qDAAqD;IACrD,OAAO;AACT;AAEA,MAAMS,mBAAmBC,QAAQC,OAAO,CAAC;AACzC,MAAMC,6BAA6B;IACjC;IACA;IACA;IACA;CACD;AAEM,SAAS7I,mBACd6E,MAA0B,EAC1BiE,OAMC;IAED,MAAM,EAAEC,kBAAkB,EAAEC,QAAQ,EAAE,GAAGF;IACzC,OAAO,IAAIG,cAAM,CAACP,kBAAkB;QAClCQ,QAAQnH;QACRoH,YAAYpB,mBAAmBlD;QAC/BuE,YAAY;YACVJ,4BAAAA,SAAUK,GAAG;QACf;QACAC,iBAAiB;YACfN,4BAAAA,SAAUO,KAAK;QACjB;QACAR;QACAS,kBAAkB3E,OAAOmD,YAAY,CAACyB,yBAAyB;QAC/D,kEAAkE;QAClEC,gBAAgB;QAChBC,qBAAqB9E,OAAOmD,YAAY,CAAC4B,aAAa;QACtDC,gBAAgBhB;IAClB;AACF;AAEA,eAAeiB,uBACbjF,MAA0B,EAC1BkF,GAAW,EACXC,kBAA0C,EAC1CC,YAAoB,EACpBjE,aAAmB;IAEnB,MAAMkE,YAAY,AAACvB,QAAQ,aACxBwB,OAAO;IAEV,MAAMD,UACJH,KACA;QACEK,aAAa;QACbC,YAAYxF;QACZmF;QACAM,QAAQ;QACRC,QAAQjJ,aAAI,CAACC,IAAI,CAACwI,KAAKE;QACvBd,YAAYpB,mBAAmBlD;IACjC,GACAmB;AAEJ;AAEA,eAAewE,WACbC,cAAuB,EACvBrJ,OAAe,EACf4E,aAAmB,EACnBnB,MAA0B;IAE1B,IAAI4F,gBAAgB;QAClB,OAAO,MAAMpI,YAAE,CAACG,QAAQ,CAAClB,aAAI,CAACC,IAAI,CAACH,SAAS,aAAa;IAC3D;IACA,OAAO,MAAM4E,cACVU,UAAU,CAAC,oBACXC,YAAY,CAAC,IAAM+D,IAAAA,gCAAe,EAAC7F,OAAO6F,eAAe,EAAEC,gBAAM;AACtE;AAEe,eAAe1K,MAC5B8J,GAAW,EACXa,2BAA2B,KAAK,EAChCC,cAAc,KAAK,EACnBC,iBAAiB,KAAK,EACtBC,UAAU,IAAI,EACdC,aAAa,KAAK,EAClBC,aAAa,KAAK,EAClBC,cAAc,KAAK,EACnBC,qBAA0E,EAC1EC,cAAkC;IAElC,MAAMC,gBAAgBF,0BAA0B;IAChD,MAAMV,iBAAiBU,0BAA0B;IACjDG,8BAAgB,CAACD,aAAa,GAAGA;IACjC,MAAME,iBAAiBC,KAAKC,GAAG;IAE/B,IAAIC;IACJ,IAAI;QACF,MAAM1F,gBAAgB2F,IAAAA,YAAK,EAAC,cAAcC,WAAW;YACnDC,WAAWV;YACXW,cAAcC,OAAOb;YACrBrF,SAASmG,QAAQC,GAAG,CAACC,cAAc;QACrC;QAEAZ,8BAAgB,CAACtF,aAAa,GAAGA;QACjCsF,8BAAgB,CAACvB,GAAG,GAAGA;QACvBuB,8BAAgB,CAACL,UAAU,GAAGA;QAC9BK,8BAAgB,CAACV,wBAAwB,GAAGA;QAC5CU,8BAAgB,CAACN,UAAU,GAAGA;QAC9BM,8BAAgB,CAACR,cAAc,GAAGA;QAElC,MAAM9E,cAAcW,YAAY,CAAC;gBAiYXwF;YAhYpB,4EAA4E;YAC5E,MAAM,EAAE3F,cAAc,EAAE,GAAGR,cACxBU,UAAU,CAAC,eACX0F,OAAO,CAAC,IAAMC,IAAAA,kBAAa,EAACtC,KAAK,OAAOhI;YAC3CuJ,8BAAgB,CAAC9E,cAAc,GAAGA;YAElC,MAAM8F,6BAA6B,IAAIC,gDAA0B;YACjE,MAAM1H,SAA6B,MAAMmB,cACtCU,UAAU,CAAC,oBACXC,YAAY,CAAC,IACZ6F,IAAAA,0CAAoB,EAClB,IACEC,IAAAA,eAAU,EAACC,kCAAsB,EAAE3C,KAAK;wBACtC,sCAAsC;wBACtCO,QAAQ;wBACRM;wBACAE;oBACF,IACFwB;YAGNZ,eAAe7G;YAEfmH,QAAQC,GAAG,CAACU,kBAAkB,GAAG9H,OAAO+H,YAAY,IAAI;YACxDtB,8BAAgB,CAACzG,MAAM,GAAGA;YAE1B,IAAIoF,eAAe;YACnB,IAAI4C,IAAAA,6BAAqB,EAAChI,SAAS;gBACjCoF,eAAepF,OAAOzD,OAAO;gBAC7ByD,OAAOzD,OAAO,GAAG;YACnB;YACA,MAAMA,UAAUE,aAAI,CAACC,IAAI,CAACwI,KAAKlF,OAAOzD,OAAO;YAC7CkK,8BAAgB,CAAClK,OAAO,GAAGA;YAC3B0L,IAAAA,gBAAS,EAAC,SAASJ,kCAAsB;YACzCI,IAAAA,gBAAS,EAAC,WAAW1L;YAErB,MAAM+B,UAAU,MAAMqH,WACpBC,gBACArJ,SACA4E,eACAnB;YAEFyG,8BAAgB,CAACnI,OAAO,GAAGA;YAE3B,IAAIgI,0BAA0B,gBAAgB;gBAC5C,IAAID,aAAa;oBACfnJ,KAAIE,IAAI,CAAC;oBACT+J,QAAQe,IAAI,CAAC;gBACf;gBACAhL,KAAIiL,IAAI,CAAC;gBACT,MAAMhH,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAMsG,IAAAA,gCAAe,EAAC;wBACpB7L;wBACAyD;oBACF;gBACF;gBAEF9C,KAAIiL,IAAI,CAAC;gBACT,MAAME,IAAAA,qBAAc;gBACpBC,IAAAA,4BAAuB;gBACvBnB,QAAQe,IAAI,CAAC;YACf;YAEA,yDAAyD;YACzD,yCAAyC;YACzC,IAAI1B,iBAAiBZ,gBAAgB;gBACnC2C,IAAAA,4BAAiB,EAACvI;YACpB;YAEA,MAAMwI,eAA6B,MAAMrH,cACtCU,UAAU,CAAC,sBACXC,YAAY,CAAC,IAAM2G,IAAAA,yBAAgB,EAACzI;YAEvC,MAAM,EAAE0I,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGJ;YACzC,MAAMK,mBAA8B;mBAC/BF,SAASG,WAAW;mBACpBH,SAASI,UAAU;mBACnBJ,SAASK,QAAQ;aACrB;YACD,MAAMC,cAAcJ,iBAAiBK,MAAM,GAAG;YAC9CzC,8BAAgB,CAACwC,WAAW,GAAGA;YAC/BxC,8BAAgB,CAAC0C,gBAAgB,GAAGnJ,OAAOoJ,iBAAiB;YAC5D3C,8BAAgB,CAAC4C,iBAAiB,GAAGrJ,OAAOsJ,kBAAkB;YAE9D,MAAM9M,WAAWF,YAAYC;YAE7B,MAAMgN,YAAY,IAAIC,kBAAS,CAAC;gBAAEjN;YAAQ;YAE1C0L,IAAAA,gBAAS,EAAC,aAAasB;YAEvB,MAAME,YAAYhN,aAAI,CAACC,IAAI,CAACwI,KAAK;YACjC,MAAM,EAAEwE,QAAQ,EAAE9H,MAAM,EAAE,GAAG+H,IAAAA,0BAAY,EAACzE;YAC1CuB,8BAAgB,CAACiD,QAAQ,GAAGA;YAC5BjD,8BAAgB,CAAC7E,MAAM,GAAGA;YAE1B,MAAMuD,qBAA6C;gBACjDyE,KAAK,OAAOhI,WAAW;gBACvBI,OAAO,OAAO0H,aAAa;YAC7B;YAEA,mDAAmD;YACnD,wFAAwF;YACxF,MAAMG,gBAAgB,MAAMC,IAAAA,kDAA2B,EAAC;gBACtDC,SAAS;gBACTxN;YACF;YACAkK,8BAAgB,CAACoD,aAAa,GAAGA;YAEjC,MAAMG,WAAWvN,aAAI,CAClBgG,QAAQ,CAACyC,KAAKwE,YAAY9H,UAAU,IACpCqI,UAAU,CAAC;YACd,MAAMC,eAAenN,IAAAA,cAAU,EAAC0M;YAEhCF,UAAUY,MAAM,CACdC,IAAAA,uBAAe,EAAClF,KAAKlF,QAAQ;gBAC3BqK,gBAAgB;gBAChBC,YAAY;gBACZN;gBACAO,YAAY,CAAC,CAAE,MAAMC,IAAAA,eAAM,EAAC,YAAY;oBAAEC,KAAKvF;gBAAI;gBACnDwF,gBAAgB;gBAChBC,WAAW;gBACXjB,UAAU,CAAC,CAACA;gBACZ9H,QAAQ,CAAC,CAACA;YACZ;YAGFgJ,IAAAA,wBAAgB,EAACnO,aAAI,CAACsH,OAAO,CAACmB,MAAM2F,IAAI,CAAC,CAACC,SACxCvB,UAAUY,MAAM,CAACW;YAGnBC,IAAAA,2BAAe,EAACtO,aAAI,CAACsH,OAAO,CAACmB,MAAMlF,QAAQ6K,IAAI,CAAC,CAACC,SAC/CvB,UAAUY,MAAM,CAACW;YAGnB,qDAAqD;YACrD,MAAM,EAAEE,OAAO,EAAEC,oBAAoB,EAAE,GAAG,MAAMC,IAAAA,8BAAkB,EAAC;gBACjEhG;gBACAiG,KAAK;gBACLlF;YACF;YAEAmF,IAAAA,wBAAY,EAAC;gBACXC,YAAY;gBACZC,QAAQ;gBACRN;gBACAC;YACF;YAEA,MAAMM,eAAeC,QAAQxL,OAAOyL,MAAM,CAACC,kBAAkB;YAC7D,MAAMC,aAAa,CAACJ,gBAAgBrF;YAEpC,MAAM0F,sBAA+D;gBACnE1G;gBACAtD;gBACA8H;gBACAxD;gBACAyF;gBACAJ;gBACAhC;gBACApI;gBACAnB;gBACAxD;YACF;YAEA,MAAMqP,iBAAiB,MAAM1K,cAC1BU,UAAU,CAAC,mBACXC,YAAY,CAAC;gBACZ,IAAI;oBACF,MAAMtE,YAAE,CAACkF,KAAK,CAACnG,SAAS;wBAAEqG,WAAW;oBAAK;oBAC1C,OAAO;gBACT,EAAE,OAAOkJ,KAAK;oBACZ,IAAIC,IAAAA,gBAAO,EAACD,QAAQA,IAAIE,IAAI,KAAK,SAAS;wBACxC,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YAEF,IAAI,CAACD,kBAAkB,CAAE,MAAMI,IAAAA,wBAAW,EAAC1P,UAAW;gBACpD,MAAM,qBAEL,CAFK,IAAI2P,MACR,iGADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIlM,OAAOmM,YAAY,IAAI,CAACvG,gBAAgB;gBAC1C,MAAMwG,IAAAA,gCAAe,EAAC7P,SAAS;YACjC;YAEA,sEAAsE;YACtE,oEAAoE;YACpE,aAAa;YACb,IAAI,CAACqF,UAAU,CAAC4E,eACd,MAAM6F,IAAAA,4BAAiB,EAACT;YAE1B,IAAIhK,UAAU,mBAAmB5B,QAAQ;gBACvC9C,KAAIoP,KAAK,CACP;gBAEF,MAAM/C,UAAUgD,KAAK;gBACrBpF,QAAQe,IAAI,CAAC;YACf;YAEA,MAAMsE,iBAAyC;gBAC7CC,aAAa;gBACbC,iBAAiBf,aAAa,IAAI;YACpC;YACApC,UAAUY,MAAM,CAAC;gBACfwC,WAAWC,iCAAyB;gBACpCC,SAASL;YACX;YAEA,MAAMM,mBAAmBC,IAAAA,oCAAsB,EAC7C/M,OAAOgN,cAAc,EACrBpL;YAGF,MAAMqL,oBAA8BjP,KAAKC,KAAK,CAC5CkJ,QAAQC,GAAG,CAAC8F,uBAAuB,IAAI;YAGzC,IAAIC,aAAa3B,QAAQrE,QAAQC,GAAG,CAAC8F,uBAAuB,IACxDD,oBACA,CAAC7G,cAAcsD,WACb,MAAMvI,cAAcU,UAAU,CAAC,iBAAiBC,YAAY,CAAC,IAC3DsL,IAAAA,kCAAgB,EAAC1D,UAAU;oBACzB2D,gBAAgBP,iBAAiBQ,UAAU;gBAC7C,MAEF,EAAE;YAER,MAAMC,4BAA4B,IAAIC,OACpC,CAAC,CAAC,EAAEC,8BAAmB,CAAC,MAAM,EAAEzN,OAAOgN,cAAc,CAACtQ,IAAI,CAAC,KAAK,EAAE,CAAC;YAGrE,MAAMgR,qCAAqC,IAAIF,OAC7C,CAAC,CAAC,EAAEG,wCAA6B,CAAC,MAAM,EAAE3N,OAAOgN,cAAc,CAACtQ,IAAI,CAClE,KACA,EAAE,CAAC;YAGP,MAAMkR,UAAUnR,aAAI,CAACC,IAAI,CAAEgN,YAAY9H,QAAU;YACjD,MAAMU,WAAW;gBACfiL;gBACAG;aACD;YAED,MAAMG,YAAYC,MAAMC,IAAI,CAAC,MAAMC,IAAAA,4BAAa,EAACJ,UAC9C/O,MAAM,CAAC,CAACoD,OAASK,SAAS2L,IAAI,CAAC,CAACC,UAAYA,QAAQC,IAAI,CAAClM,QACzD5C,IAAI,CAAC+O,IAAAA,uBAAc,EAACpO,OAAOgN,cAAc,GACzCjO,GAAG,CAAC,CAACkD,OAASxF,aAAI,CAACC,IAAI,CAACkR,SAAS3L,MAAMzB,OAAO,CAAC0E,KAAK;YAEvD,MAAMzD,yBAAyBoM,UAAUI,IAAI,CAAC,CAAC3N,IAC7CA,EAAEgC,QAAQ,CAACqL,wCAA6B;YAE1C,MAAMU,oBAAoBR,UAAUI,IAAI,CAAC,CAAC3N,IACxCA,EAAEgC,QAAQ,CAACmL,8BAAmB;YAGhChH,8BAAgB,CAAChF,sBAAsB,GAAGA;YAE1C,MAAM6M,eAAkC,MAAMC,IAAAA,oCAAmB,EAAC;gBAChExE,SAAS;gBACTxN;YACF;YACAkK,8BAAgB,CAAC6H,YAAY,GAAGA;YAEhC,MAAMhH,cAAc,MAAMnG,cACvBU,UAAU,CAAC,wBACXC,YAAY,CAAC,IACZ0M,IAAAA,2BAAkB,EAAC;oBACjBC,OAAO;oBACPzB,gBAAgBhN,OAAOgN,cAAc;oBACrC0B,WAAWC,qBAAU,CAACC,KAAK;oBAC3BC,WAAW1B;oBACXzD;oBACA9H;gBACF;YAEJ6E,8BAAgB,CAACa,WAAW,GAAGA;YAE/B,IAAIwH;YACJ,IAAIzN;YAEJ,IAAIO,QAAQ;gBACV,MAAMmN,mBAA6B/Q,KAAKC,KAAK,CAC3CkJ,QAAQC,GAAG,CAAC4H,sBAAsB,IAAI;gBAGxC,IAAIC,WAAWzD,QAAQrE,QAAQC,GAAG,CAAC4H,sBAAsB,IACrDD,mBACA,MAAM5N,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC,IACZsL,IAAAA,kCAAgB,EAACxL,QAAQ;wBACvByL,gBAAgB,CAAC6B,eACfpC,iBAAiBqC,eAAe,CAACD,iBACjC,8DAA8D;4BAC9D,gCAAgC;4BAChCpC,iBAAiBsC,cAAc,CAACF;wBAClCG,kBAAkB,CAACC,OAASA,KAAKrF,UAAU,CAAC;oBAC9C;gBAGR6E,iBAAiB,MAAM3N,cACpBU,UAAU,CAAC,sBACXC,YAAY,CAAC,IACZ0M,IAAAA,2BAAkB,EAAC;wBACjBK,WAAWI;wBACXR,OAAO;wBACPC,WAAWC,qBAAU,CAACY,GAAG;wBACzBvC,gBAAgBhN,OAAOgN,cAAc;wBACrCtD;wBACA9H;oBACF;gBAGJ6E,8BAAgB,CAACqI,cAAc,GAAGA;YACpC;YAEA,MAAMU,kBAAkB,MAAMhB,IAAAA,2BAAkB,EAAC;gBAC/CC,OAAO;gBACPzB,gBAAgBhN,OAAOgN,cAAc;gBACrC6B,WAAWhB;gBACXa,WAAWC,qBAAU,CAACc,IAAI;gBAC1B/F,UAAUA;gBACV9H;YACF;YACA6E,8BAAgB,CAAC+I,eAAe,GAAGA;YAEnC,MAAME,gBAAgBhR,OAAOS,IAAI,CAACmI;YAElC,MAAMqI,0BAAiE,EAAE;YACzE,MAAMC,cAAc,IAAInR;YACxB,IAAIqQ,gBAAgB;gBAClBzN,uBAAuB3C,OAAOS,IAAI,CAAC2P;gBACnC,KAAK,MAAMe,UAAUxO,qBAAsB;oBACzC,MAAMyO,uBAAuBC,IAAAA,0BAAgB,EAACF;oBAC9C,MAAMG,WAAW1I,WAAW,CAACwI,qBAAqB;oBAClD,IAAIE,UAAU;wBACZ,MAAMC,UAAUnB,cAAc,CAACe,OAAO;wBACtCF,wBAAwBpN,IAAI,CAAC;4BAC3ByN,SAASxP,OAAO,CAAC,uBAAuB;4BACxCyP,QAAQzP,OAAO,CAAC,yBAAyB;yBAC1C;oBACH;oBACAoP,YAAYM,GAAG,CAACJ;gBAClB;YACF;YAEA,MAAMb,WAAWnB,MAAMC,IAAI,CAAC6B;YAC5B,2DAA2D;YAC3DjH,SAASG,WAAW,CAACvG,IAAI,IACpB4N,IAAAA,sEAAkC,EAAClB,UAAUjP,OAAOoQ,QAAQ;YAGjE3J,8BAAgB,CAACkC,QAAQ,GAAGA;YAE5B,MAAM0H,qBAAqBpB,SAAS/F,MAAM;YAE1C,MAAM9H,WAAW;gBACfY,OAAO0N;gBACP9F,KAAKqF,SAAS/F,MAAM,GAAG,IAAI+F,WAAWlI;YACxC;YAEA,6DAA6D;YAC7D,IAAI,CAACV,aAAa;gBAChB,MAAMiK,yBAAyBX,wBAAwBzG,MAAM;gBAC7D,IAAI4F,kBAAkBwB,yBAAyB,GAAG;oBAChDpT,KAAIoP,KAAK,CACP,CAAC,6BAA6B,EAC5BgE,2BAA2B,IAAI,SAAS,SACzC,wDAAwD,CAAC;oBAE5D,KAAK,MAAM,CAACN,UAAUC,QAAQ,IAAIN,wBAAyB;wBACzDzS,KAAIoP,KAAK,CAAC,CAAC,GAAG,EAAE0D,SAAS,KAAK,EAAEC,QAAQ,CAAC,CAAC;oBAC5C;oBACA,MAAM1G,UAAUgD,KAAK;oBACrBpF,QAAQe,IAAI,CAAC;gBACf;YACF;YAEA,MAAMqI,yBAAmC,EAAE;YAC3C,MAAMC,eAAclJ,mBAAAA,WAAW,CAAC,OAAO,qBAAnBA,iBAAqB2C,UAAU,CAACwG,0BAAe;YACnE,MAAMC,YAAY,CAAC,EAAC5B,kCAAAA,cAAgB,CAAC6B,4CAAgC,CAAC;YACtE,MAAMC,qBACJtJ,WAAW,CAAC,UAAU,CAAC2C,UAAU,CAACwG,0BAAe;YAEnD,IAAIvG,cAAc;gBAChB,MAAM2G,6BAA6B9T,IAAAA,cAAU,EAC3CN,aAAI,CAACC,IAAI,CAAC+M,WAAW;gBAEvB,IAAIoH,4BAA4B;oBAC9B,MAAM,qBAAyC,CAAzC,IAAI3E,MAAM4E,yCAA8B,GAAxC,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwC;gBAChD;YACF;YAEA,MAAM3P,cACHU,UAAU,CAAC,6BACXC,YAAY,CAAC;gBACZ,iDAAiD;gBACjD,sDAAsD;gBACtD,IAAK,MAAMlG,QAAQ0L,YAAa;oBAC9B,MAAMyJ,oBAAoB,MAAMC,IAAAA,sBAAU,EACxCvU,aAAI,CAACC,IAAI,CAAC+M,WAAW7N,SAAS,MAAM,WAAWA,OAC/CqV,oBAAQ,CAACC,IAAI;oBAEf,IAAIH,mBAAmB;wBACrBR,uBAAuBhO,IAAI,CAAC3G;oBAC9B;gBACF;gBAEA,MAAMuV,iBAAiBZ,uBAAuBrH,MAAM;gBAEpD,IAAIiI,gBAAgB;oBAClB,MAAM,qBAML,CANK,IAAIjF,MACR,CAAC,gCAAgC,EAC/BiF,mBAAmB,IAAI,SAAS,SACjC,uEAAuE,EAAEZ,uBAAuB7T,IAAI,CACnG,OACC,GALC,qBAAA;+BAAA;oCAAA;sCAAA;oBAMN;gBACF;YACF;YAEF,MAAM0U,sBAAsBhQ,SAASY,KAAK,CAACnD,MAAM,CAAC,CAACjD;gBACjD,OACEA,KAAKyV,KAAK,CAAC,iCAAiC5U,aAAI,CAACkG,OAAO,CAAC/G,UAAU;YAEvE;YAEA,IAAIwV,oBAAoBlI,MAAM,EAAE;gBAC9BhM,KAAIE,IAAI,CACN,CAAC,4FAA4F,CAAC,GAC5FgU,oBAAoB1U,IAAI,CAAC,QACzB,CAAC,6EAA6E,CAAC;YAErF;YAEA,MAAM4U,0BAA0B;gBAAC;aAAS,CAACvS,GAAG,CAAC,CAACuB,IAC9CN,OAAOoQ,QAAQ,GAAG,GAAGpQ,OAAOoQ,QAAQ,GAAG9P,GAAG,GAAGA;YAG/C,MAAMiR,wBAAwB/F,QAAQxL,OAAOmD,YAAY,CAACqO,SAAS;YACnE,MAAMC,0BAA0BjG,QAC9BxL,OAAOmD,YAAY,CAACuO,cAAc;YAEpC,MAAMC,kBAAkBC,IAAAA,yBAAoB,EAAC5R,OAAOmD,YAAY,CAAC0O,GAAG;YAEpE,MAAMC,qBAAqBrV,aAAI,CAACC,IAAI,CAACH,SAASwV,2BAAe;YAC7D,MAAMC,iBAAiC7Q,cACpCU,UAAU,CAAC,4BACX0F,OAAO,CAAC;gBACP,MAAM0K,eAAeC,IAAAA,sBAAe,EAAC;uBAChC9Q,SAASY,KAAK;uBACbZ,SAASwI,GAAG,IAAI,EAAE;iBACvB;gBACD,MAAMxK,gBAAsC,EAAE;gBAC9C,MAAM+S,eAAqC,EAAE;gBAE7C,KAAK,MAAMnT,SAASiT,aAAc;oBAChC,IAAIG,IAAAA,qBAAc,EAACpT,QAAQ;wBACzBI,cAAcmD,IAAI,CAAC5G,YAAYqD;oBACjC,OAAO,IAAI,CAACqT,IAAAA,sBAAc,EAACrT,QAAQ;wBACjCmT,aAAa5P,IAAI,CAAC5G,YAAYqD;oBAChC;gBACF;gBAEA,OAAO;oBACLgC,SAAS;oBACTsR,UAAU;oBACVC,eAAe,CAAC,CAACvS,OAAOmD,YAAY,CAACqP,mBAAmB;oBACxDpC,UAAUpQ,OAAOoQ,QAAQ;oBACzBxH,WAAWA,UAAU7J,GAAG,CAAC,CAAC0T,IACxBC,IAAAA,kCAAgB,EAAC,YAAYD,GAAGnB;oBAElC5I,SAASA,QAAQ3J,GAAG,CAAC,CAAC0T,IAAMC,IAAAA,kCAAgB,EAAC,UAAUD;oBACvD9J,UAAU;wBACRG,aAAa,EAAE;wBACfC,YAAY,EAAE;wBACdC,UAAU,EAAE;oBACd;oBACA5J;oBACA+S;oBACAQ,YAAY,EAAE;oBACdC,MAAM5S,OAAO4S,IAAI,IAAI7L;oBACrB8L,KAAK;wBACHC,QAAQC,4BAAU;wBAClB,yFAAyF;wBACzF,4DAA4D;wBAC5DC,YAAY,GAAGD,4BAAU,CAAC,EAAE,EAAEE,+CAA6B,CAAC,EAAE,EAAEC,6CAA2B,CAAC,EAAE,EAAEC,qDAAmC,EAAE;wBACrIC,gBAAgBF,6CAA2B;wBAC3CG,mBAAmBC,0CAAwB;wBAC3CC,mBAAmBC,yCAAuB;wBAC1CC,QAAQC,qBAAU;wBAClBC,gBAAgBC,8BAAmB;wBACnCC,uBAAuBV,qDAAmC;wBAC1DW,uBAAuBC,6BAAkB;wBACzCC,0BAA0BC,kCAAuB;oBACnD;oBACAC,gBAAgB;wBACdC,YAAYC,4CAA0B;wBACtCC,aAAaC,6CAA2B;oBAC1C;oBACAC,4BAA4BvU,OAAOuU,0BAA0B;oBAC7D1C,KAAKF,kBACD;wBACE6C,OAAO;4BACL9L,SAAS;gCACP,CAAC+L,6BAAkB,CAAC,EAAE;4BACxB;wBACF;oBACF,IACA1N;gBACN;YACF;YAEFiL,eAAerJ,QAAQ,GAAG;gBACxBG,aAAaH,SAASG,WAAW,CAAC/J,GAAG,CAAC,CAAC0T,IACrCC,IAAAA,kCAAgB,EAAC,WAAWD;gBAE9B1J,YAAYJ,SAASI,UAAU,CAAChK,GAAG,CAAC,CAAC0T,IACnCC,IAAAA,kCAAgB,EAAC,WAAWD;gBAE9BzJ,UAAUL,SAASK,QAAQ,CAACjK,GAAG,CAAC,CAAC0T,IAAMC,IAAAA,kCAAgB,EAAC,WAAWD;YACrE;YAEA,IAAIiC;YAIJ,IAAI1U,OAAOmD,YAAY,CAACwR,kBAAkB,EAAE;gBAC1C,MAAMC,uBAAuB,AAAC5U,CAAAA,OAAOsJ,kBAAkB,IAAI,EAAE,AAAD,EAAGzK,MAAM,CACnE,CAAC4T,IAAW,CAACA,EAAEoC,QAAQ;gBAEzBH,sBAAsBI,IAAAA,kDAAwB,EAC5C;uBAAI7F;iBAAS,EACbjP,OAAOmD,YAAY,CAAC4R,2BAA2B,GAC3CH,uBACA,EAAE,EACN5U,OAAOmD,YAAY,CAAC6R,6BAA6B;gBAEnDvO,8BAAgB,CAACiO,mBAAmB,GAAGA;YACzC;YAEA,8EAA8E;YAC9E,uDAAuD;YACvD,MAAMrX,cACJZ,aAAI,CAACC,IAAI,CAACH,SAAS,iBACnB;YAGF,yFAAyF;YACzF,MAAM0Y,IAAAA,wCAAsB,EAAC9N,QAAQC,GAAG,CAACC,cAAc;YACvD,MAAM6N,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;YACd;YAEA,MAAM7T,wBAAwBtB,OAAOsB,qBAAqB,IAAI4D;YAE9D,MAAMkQ,oBAAoB3Y,aAAI,CAACC,IAAI,CACjCH,SACAmD,4BAAgB,EAChB2V,0BAAc;YAGhB,IAAIC;YACJ,IAAIC,qBAA+CxO;YAEnD,uEAAuE;YACvE,4CAA4C;YAC5C,MAAMyO,iBACJxV,OAAOmD,YAAY,CAACsS,kBAAkB,IACrCzV,OAAOmD,YAAY,CAACsS,kBAAkB,KAAK1O,aAC1C,CAAC/G,OAAO0V,OAAO;YACnB,MAAMC,6BACJ3V,OAAOmD,YAAY,CAACyS,sBAAsB;YAC5C,MAAMC,qCACJ7V,OAAOmD,YAAY,CAAC2S,yBAAyB,IAC5C9V,OAAOmD,YAAY,CAAC2S,yBAAyB,KAAK/O,aACjDP;YAEJrF,cAAc4U,YAAY,CACxB,6BACA7O,OAAO,CAAC,CAAClH,OAAO0V,OAAO;YAEzBvU,cAAc4U,YAAY,CAAC,oBAAoB7O,OAAOsO;YAEtD,IACE,CAACA,kBACAG,CAAAA,8BAA8BE,kCAAiC,GAChE;gBACA,MAAM,qBAEL,CAFK,IAAI3J,MACR,oMADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAhP,KAAIiL,IAAI,CAAC;YACT6N,IAAAA,wBAAgB,EAAC,kBAAkB7U;YAEnC,MAAM+T,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;gBACZc,cAAc;oBACZT,gBAAgBtO,OAAOsO;gBACzB;YACF;YAEA,IAAIU,kBAAkBC,QAAQpS,OAAO;YACrC,IAAI,CAAC6B,gBAAgB;gBACnB,IAAIS,aAAa;oBACf,MAAM,EACJ+P,UAAUC,gBAAgB,EAC1BH,iBAAiB5V,CAAC,EAClB,GAAGgW,MACJ,GAAG,MAAMC,IAAAA,8BAAc,EACtBpP,QAAQC,GAAG,CAACoP,yBAAyB,KAAKzP,aACxCI,QAAQC,GAAG,CAACoP,yBAAyB,KAAK;oBAE9CN,kBAAkB5V;oBAClB0V,IAAAA,wBAAgB,EAAC,kBAAkB7U;oBAEnCmU,oBAAoBgB,KAAKhB,iBAAiB;oBAE1C,MAAMmB,iBAAiBC,IAAAA,kCAAgB,EAACL;oBACxCnZ,KAAIyZ,KAAK,CAAC,CAAC,yBAAyB,EAAEF,gBAAgB;oBAEtDlN,UAAUY,MAAM,CACdyM,IAAAA,2BAAmB,EAACzJ,YAAY;wBAC9B0J,SAAS;wBACTC,mBAAmBvT,KAAKwT,KAAK,CAACV;wBAC9BhG;oBACF;gBAEJ,OAAO;oBACL,IACEsF,8BACAE,oCACA;wBACA,IAAIiB,oBAAoB;wBAExB,MAAM5B,IAAAA,wCAAsB,EAAC;4BAC3BC,YAAY;wBACd;wBAEA,MAAM6B,qBAAqBC,IAAAA,0BAAY,EAACzB,gBAAgB;4BACtD;yBACD,EAAE3K,IAAI,CAAC,CAACqM;4BACPlB,IAAAA,wBAAgB,EAAC,+BAA+B7U;4BAChDmU,oBAAoB4B,IAAI5B,iBAAiB;4BACzCwB,qBAAqBI,IAAId,QAAQ;4BAEjC,IAAIP,oCAAoC;gCACtC,MAAMsB,mBAAmB,IAAI/S,cAAM,CACjCN,QAAQC,OAAO,CAAC,2BAChB;oCACEG,oBAAoB,CAAC;oCACrBW,gBAAgB;oCAChBP,YAAY;oCACZU,gBAAgB;wCAAC;qCAAqB;gCACxC;gCAGFuQ,qBAAqB4B,iBAClBC,kBAAkB,CAAC;oCAClBlS;oCACAlF;oCACAzD;oCACA,+CAA+C;oCAC/C8a,mBAAmBC,IAAAA,qCAA6B,EAAC,IAAIC;oCACrD7V,aAAa,EAAE;oCACf8V,gBAAgB;oCAChBlC;oCACAhU;oCACA+E,aAAa;gCACf,GACCoR,KAAK,CAAC,CAAC3L;oCACN9O,QAAQsP,KAAK,CAACR;oCACd3E,QAAQe,IAAI,CAAC;gCACf;4BACJ;wBACF;wBACA,IAAI,CAACyN,4BAA4B;4BAC/B,MAAMqB;4BACN,MAAM9B,IAAAA,wCAAsB,EAAC;gCAC3BC,YAAY;4BACd;wBACF;wBAEA,MAAMuC,mBAAmBT,IAAAA,0BAAY,EAACzB,gBAAgB;4BACpD;yBACD,EAAE3K,IAAI,CAAC,CAACqM;4BACPJ,qBAAqBI,IAAId,QAAQ;4BACjCJ,IAAAA,wBAAgB,EACd,oCACA7U;wBAEJ;wBACA,IAAIwU,4BAA4B;4BAC9B,MAAMqB;4BACN,MAAM9B,IAAAA,wCAAsB,EAAC;gCAC3BC,YAAY;4BACd;wBACF;wBACA,MAAMuC;wBAEN,MAAMxC,IAAAA,wCAAsB,EAAC;4BAC3BC,YAAY;wBACd;wBAEA,MAAM8B,IAAAA,0BAAY,EAACzB,gBAAgB;4BAAC;yBAAS,EAAE3K,IAAI,CAAC,CAACqM;4BACnDJ,qBAAqBI,IAAId,QAAQ;4BACjCJ,IAAAA,wBAAgB,EAAC,+BAA+B7U;wBAClD;wBAEA,MAAMsV,iBAAiBC,IAAAA,kCAAgB,EAACI;wBACxC5Z,KAAIyZ,KAAK,CAAC,CAAC,yBAAyB,EAAEF,gBAAgB;wBAEtDlN,UAAUY,MAAM,CACdyM,IAAAA,2BAAmB,EAACzJ,YAAY;4BAC9B0J,SAASc,uBAAuBtR;4BAChCyQ;4BACAzG;wBACF;oBAEJ,OAAO;wBACL,MAAM,EAAE+F,UAAUC,gBAAgB,EAAE,GAAGC,MAAM,GAAG,MAAMW,IAAAA,0BAAY,EAChEzB,gBACA;wBAEFQ,IAAAA,wBAAgB,EAAC,kBAAkB7U;wBAEnCmU,oBAAoBgB,KAAKhB,iBAAiB;wBAE1C/L,UAAUY,MAAM,CACdyM,IAAAA,2BAAmB,EAACzJ,YAAY;4BAC9B0J,SAASc,uBAAuBtR;4BAChCyQ,mBAAmBT;4BACnBhG;wBACF;oBAEJ;gBACF;gBACA,MAAMuH,IAAAA,iDAAyB,EAAC;oBAC9B5X;oBACA6X,WAAW1W;oBACXoI;oBACAuO,UAAU;wBACRC,YAAY7S;wBACZ3I;oBACF;gBACF;YACF;YAEA,uDAAuD;YACvD,IAAIqF,UAAU,CAAC4E,iBAAiB,CAACZ,gBAAgB;gBAC/C,MAAMsP,IAAAA,wCAAsB,EAAC;oBAC3BC,YAAY;gBACd;gBACA,MAAM9I,IAAAA,4BAAiB,EAACT;gBACxBoK,IAAAA,wBAAgB,EAAC,0BAA0B7U;YAC7C;YAEA,MAAM6W,qBAAqBC,IAAAA,gBAAa,EAAC;YAEzC,MAAMC,oBAAoBzb,aAAI,CAACC,IAAI,CAACH,SAAS4b,0BAAc;YAC3D,MAAMC,uBAAuB3b,aAAI,CAACC,IAAI,CAACH,SAAS8b,8BAAkB;YAElE,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,wBAAwB;YAC5B,MAAMja,WAAW,IAAIC;YACrB,MAAMia,yBAAyB,IAAIja;YACnC,MAAMka,2BAA2B,IAAIla;YACrC,MAAMiD,cAAc,IAAIjD;YACxB,MAAMma,eAAe,IAAIna;YACzB,MAAMoa,iBAAiB,IAAIpa;YAC3B,MAAMqa,mBAAmB,IAAIra;YAC7B,MAAMsa,kBAAkB,IAAIxB;YAC5B,MAAMyB,cAAc,IAAIzB;YACxB,MAAM0B,qBAAqB,IAAI1B;YAC/B,MAAM2B,gBAAgB,IAAI3B;YAC1B,MAAM4B,oBAAoB,IAAI5B;YAC9B,MAAM6B,YAAuB,IAAI7B;YACjC,IAAI8B,gBAAgB,MAAMtb,aAA4BqX;YACtD,MAAMkE,gBAAgB,MAAMvb,aAA4Bma;YACxD,MAAMqB,mBAAmB3X,SACrB,MAAM7D,aAA+Bqa,wBACrCrR;YAEJ,MAAMyS,gBAAwC,CAAC;YAE/C,IAAI5X,QAAQ;gBACV,MAAM6X,mBAAmB,MAAM1b,aAC7BtB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEga,8BAAkB;gBAGzD,IAAK,MAAMC,OAAOF,iBAAkB;oBAClCD,aAAa,CAACG,IAAI,GAAG5J,IAAAA,0BAAgB,EAAC4J;gBACxC;gBAEA,MAAM/b,cACJnB,aAAI,CAACC,IAAI,CAACH,SAASqd,oCAAwB,GAC3CJ;YAEJ;YAEArS,QAAQC,GAAG,CAACyS,UAAU,GAAGhS,kCAAsB;YAE/C,MAAMiS,SAAS3e,mBAAmB6E,QAAQ;gBAAEkE,oBAAoB,CAAC;YAAE;YAEnE,MAAM6V,gBAAgB5S,QAAQ6S,MAAM;YACpC,MAAMC,kBAAkB9Y,cAAcU,UAAU,CAAC;YAEjD,MAAMqY,0BAAmD;gBACvDlZ,SAAS;gBACTmZ,WAAW,CAAC;YACd;YAEA,MAAM,EACJC,wBAAwB,EACxBC,YAAY,EACZC,mBAAmB,EACnB9C,cAAc,EACd+C,qBAAqB,EACtB,GAAG,MAAMN,gBAAgBnY,YAAY,CAAC;oBAcV9B;gBAb3B,IAAIwG,eAAe;oBACjB,OAAO;wBACL4T,0BAA0B;wBAC1BC,cAAc,EAAE;wBAChBC,qBAAqB;wBACrB9C,gBAAgB,CAAC,CAAC9N;wBAClB6Q,uBAAuB;oBACzB;gBACF;gBAEA,MAAM,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAE,GAChE1a;gBACF,MAAM2a,mBAAmB;oBAAEF;oBAAqBC;gBAAoB;gBACpE,MAAME,aAAapP,SAAQxL,2BAAAA,OAAOmD,YAAY,CAAC0X,GAAG,qBAAvB7a,yBAAyB8a,SAAS;gBAE7D,MAAMC,yBAAyBd,gBAAgBpY,UAAU,CACvD;gBAEF,MAAMmZ,oCACJD,uBAAuBjZ,YAAY,CACjC,UACE8O,sBACC,MAAMkJ,OAAOmB,wBAAwB,CAAC;wBACrCrf,MAAM;wBACNW;wBACAoe;wBACAO,aAAa;wBACbN;oBACF;gBAGN,MAAMO,wBAAwBJ,uBAAuBjZ,YAAY,CAC/D;wBAWa9B,cACMA;2BAXjB4Q,sBACAkJ,OAAOsB,YAAY,CAAC;wBAClBlW;wBACAtJ,MAAM;wBACNW;wBACAie;wBACAG;wBACAnJ,WAAWD;wBACXG,gBAAgBD;wBAChB4J,kBAAkBrb,OAAOqb,gBAAgB;wBACzC9c,OAAO,GAAEyB,eAAAA,OAAO4S,IAAI,qBAAX5S,aAAazB,OAAO;wBAC7B+c,aAAa,GAAEtb,gBAAAA,OAAO4S,IAAI,qBAAX5S,cAAasb,aAAa;wBACzCC,kBAAkBvb,OAAOwb,MAAM;wBAC/BC,WAAWzb,OAAOmD,YAAY,CAAC0O,GAAG;wBAClC6J,mBAAmB1b,OAAOmD,YAAY,CAACwY,SAAS;wBAChDrd;wBACAsc;oBACF;;gBAGJ,MAAMgB,iBAAiB;gBAEvB,MAAMC,kCAAkC/B,OAAOmB,wBAAwB,CACrE;oBACErf,MAAMggB;oBACNrf;oBACAoe;oBACAO,aAAa;oBACbN;gBACF;gBAGF,MAAMkB,sBAAsBhC,OAAOiC,sBAAsB,CAAC;oBACxDngB,MAAMggB;oBACNrf;oBACAoe;oBACAC;gBACF;gBAEA,wDAAwD;gBACxD,IAAIN;gBACJ,wDAAwD;gBACxD,IAAI9C,iBAAiB;gBAErB,MAAMwE,uBAAuB,MAAMC,IAAAA,2BAAmB,EACpD;oBAAE7gB,OAAOke;oBAAe1P,KAAK2P;gBAAiB,GAC9Chd,SACAyD,OAAOmD,YAAY,CAAC+Y,QAAQ;gBAG9B,MAAM3a,qBAAyCuC,QAC7CrH,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEyc,+BAAmB;gBAG1D,MAAMC,iBAAiBxa,SAClBkC,QACCrH,aAAI,CAACC,IAAI,CACPH,SACAmD,4BAAgB,EAChB2c,qCAAyB,GAAG,YAGhC;gBACJ,MAAMC,oBAAoBF,iBAAiB,IAAI3d,QAAQ;gBACvD,IAAI2d,kBAAkBE,mBAAmB;oBACvC,IAAK,MAAMC,MAAMH,eAAeI,IAAI,CAAE;wBACpC,IAAK,MAAMC,SAASL,eAAeI,IAAI,CAACD,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBpM,GAAG,CAACuM;wBACxB;oBACF;oBACA,IAAK,MAAMF,MAAMH,eAAeO,IAAI,CAAE;wBACpC,IAAK,MAAMF,SAASL,eAAeO,IAAI,CAACJ,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkBpM,GAAG,CAACuM;wBACxB;oBACF;gBACF;gBAEA,KAAK,MAAM9C,OAAOjb,OAAOS,IAAI,CAACoC,sCAAAA,mBAAoB4Y,SAAS,EAAG;oBAC5D,IAAIR,IAAI1P,UAAU,CAAC,SAAS;wBAC1BwO;oBACF;gBACF;gBAEA,MAAMtC,QAAQyG,GAAG,CACfle,OAAOC,OAAO,CAACyC,UACZe,MAAM,CACL,CAACC,KAAK,CAACuX,KAAKzX,MAAM;oBAChB,IAAI,CAACA,OAAO;wBACV,OAAOE;oBACT;oBAEA,MAAMya,WAAWlD;oBAEjB,KAAK,MAAM/d,QAAQsG,MAAO;wBACxBE,IAAIG,IAAI,CAAC;4BAAEsa;4BAAUjhB;wBAAK;oBAC5B;oBAEA,OAAOwG;gBACT,GACA,EAAE,EAEHrD,GAAG,CAAC,CAAC,EAAE8d,QAAQ,EAAEjhB,IAAI,EAAE;oBACtB,MAAMkhB,gBAAgB7C,gBAAgBpY,UAAU,CAAC,cAAc;wBAC7DjG;oBACF;oBACA,OAAOkhB,cAAchb,YAAY,CAAC;wBAChC,MAAMib,aAAaC,IAAAA,oCAAiB,EAACphB;wBACrC,MAAM,CAACqhB,MAAMC,UAAU,GAAG,MAAMC,IAAAA,yBAAiB,EAC/CN,UACAE,YACAxgB,SACA+c,eACAC,kBACAvZ,OAAOmD,YAAY,CAAC+Y,QAAQ,EAC5BF;wBAGF,IAAIoB,oBAAoB;wBACxB,IAAIC,QAAQ;wBACZ,IAAIC,WAAW;wBACf,IAAIC,oBAAoB;wBACxB,IAAIC,cAAc;wBAClB,IAAIC,gBAAiC;wBACrC,IAAIzN,WAAW;wBAEf,IAAI6M,aAAa,SAAS;4BACxB7M,WACE7C,WAAWuQ,IAAI,CAAC,CAACpd;gCACfA,IAAIqd,IAAAA,kCAAgB,EAACrd;gCACrB,OACEA,EAAE2J,UAAU,CAAC8S,aAAa,QAC1Bzc,EAAE2J,UAAU,CAAC8S,aAAa;4BAE9B,MAAM;wBACV;wBACA,IAAIa;wBAEJ,IAAIf,aAAa,SAAS/N,gBAAgB;4BACxC,KAAK,MAAM,CAAC+O,cAAcC,eAAe,IAAIpf,OAAOC,OAAO,CACzD6a,eACC;gCACD,IAAIsE,mBAAmBliB,MAAM;oCAC3BoU,WAAWlB,cAAc,CAAC+O,aAAa,CAACrd,OAAO,CAC7C,yBACA;oCAEFod,kBAAkBC;oCAClB;gCACF;4BACF;wBACF;wBAEA,MAAME,eAAeC,IAAAA,gCAAwB,EAAChO,YAC1ClM,QAAQC,OAAO,CACb,mDAEFtH,aAAI,CAACC,IAAI,CACP,AAACmgB,CAAAA,aAAa,UAAUnT,WAAW9H,MAAK,KAAM,IAC9CoO;wBAGN,MAAMiO,iBAAiBpB,aAAa;wBACpC,MAAMqB,aAAalO,WACf,MAAMmO,IAAAA,sCAA6B,EAAC;4BAClCF;4BACAF;4BACA/Q,gBAAgBhN,OAAOgN,cAAc;4BACrCpL;4BACA5B;4BACAyO,OAAO;4BACP,yDAAyD;4BACzD,4DAA4D;4BAC5D,gEAAgE;4BAChE7S,MAAMqiB,iBAAiBL,kBAAmBhiB;wBAC5C,KACAmL;wBAEJ,IAAImX,8BAAAA,WAAYE,mBAAmB,EAAE;4BACnCC;wBACF;wBAEA,8DAA8D;wBAC9D,oDAAoD;wBACpD,IACE,QAAOH,8BAAAA,WAAYI,OAAO,MAAK,eAC/B,QAAOJ,8BAAAA,WAAYK,WAAW,MAAK,eACnC,QAAOL,8BAAAA,WAAYM,eAAe,MAAK,aACvC;4BACA,MAAMC,UAAUP,CAAAA,8BAAAA,WAAYM,eAAe,IACvC,OAAON,WAAWM,eAAe,KAAK,WACpC;gCAACN,WAAWM,eAAe;6BAAC,GAC5BN,WAAWM,eAAe,GAC5BzX;4BAEJmT,wBAAwBC,SAAS,CAACve,KAAK,GAAG;gCACxC2iB,WAAW,EAAEL,8BAAAA,WAAYK,WAAW;gCACpC,GAAIE,WAAW;oCAAEA;gCAAQ,CAAC;4BAC5B;wBACF;wBAEA,MAAMC,cAAcnd,mBAAmB4Y,SAAS,CAC9CyD,mBAAmBhiB,KACpB,GACG,SACAsiB,8BAAAA,WAAYI,OAAO;wBAEvB,IAAI,CAAC9X,eAAe;4BAClB+W,oBACEV,aAAa,SACbqB,CAAAA,8BAAAA,WAAYrL,GAAG,MAAK8L,4BAAgB,CAACC,MAAM;4BAE7C,IAAI/B,aAAa,SAAS,CAACxK,IAAAA,sBAAc,EAACzW,OAAO;gCAC/C,IAAI;oCACF,IAAIijB;oCAEJ,IAAIC,IAAAA,4BAAa,EAACJ,cAAc;wCAC9B,IAAI7B,aAAa,OAAO;4CACtBrE;wCACF,OAAO;4CACLC;wCACF;wCAEA,MAAMsG,cACJlC,aAAa,UAAUjhB,OAAOgiB,mBAAmB;wCAEnDiB,WAAWtd,mBAAmB4Y,SAAS,CAAC4E,YAAY;oCACtD;oCAEA,IAAIC,mBACFlC,cAAcjb,UAAU,CAAC;oCAC3B,IAAIod,eAAe,MAAMD,iBAAiBld,YAAY,CACpD;4CASa9B,cACMA;wCATjB,OAAO8Z,OAAOsB,YAAY,CAAC;4CACzBlW;4CACAtJ;4CACAgiB;4CACArhB;4CACAie;4CACAG;4CACAU,kBAAkBrb,OAAOqb,gBAAgB;4CACzC9c,OAAO,GAAEyB,eAAAA,OAAO4S,IAAI,qBAAX5S,aAAazB,OAAO;4CAC7B+c,aAAa,GAAEtb,gBAAAA,OAAO4S,IAAI,qBAAX5S,cAAasb,aAAa;4CACzC4D,UAAUF,iBAAiBG,KAAK;4CAChCT;4CACAG;4CACAhC;4CACArL,WAAWD;4CACXG,gBAAgBD;4CAChB2N,cAAcpf,OAAOof,YAAY;4CACjCC,eAAerf,OAAOmD,YAAY,CAACkc,aAAa;4CAChDC,gBAAgB3iB,QAAcE,cAAc,GACxC,QACAmD,OAAOmD,YAAY,CAACmc,cAAc;4CACtCC,oBAAoBvf,OAAOwf,kBAAkB;4CAC7CjE,kBAAkBvb,OAAOwb,MAAM;4CAC/BC,WAAWzb,OAAOmD,YAAY,CAAC0O,GAAG;4CAClC6J,mBAAmB1b,OAAOmD,YAAY,CAACwY,SAAS;4CAChDrd;4CACAsc;wCACF;oCACF;oCAGF,IAAIiC,aAAa,SAASe,iBAAiB;wCACzC3E,mBAAmBwG,GAAG,CAAC7B,iBAAiBhiB;wCACxC,0CAA0C;wCAC1C,IAAIkjB,IAAAA,4BAAa,EAACJ,cAAc;4CAC9BpB,WAAW;4CACXD,QAAQ;4CAERngB,KAAIwiB,QAAQ,CACV,CAAC,+EAA+E,CAAC;wCAErF,OAAO;4CACL,MAAMC,YAAYvN,IAAAA,qBAAc,EAACxW;4CAEjC,IACE,OAAOqjB,aAAa7B,iBAAiB,KAAK,WAC1C;gDACAA,oBAAoB6B,aAAa7B,iBAAiB;4CACpD;4CAEA,oDAAoD;4CACpD,0CAA0C;4CAC1C,yBAAyB;4CACzB,IAAI6B,aAAa7B,iBAAiB,EAAE;gDAClCC,QAAQ;gDACRC,WAAW;gDAEXtE,YAAYyG,GAAG,CAAC7B,iBAAiB,EAAE;4CACrC;4CAEA,IAAIqB,aAAaW,iBAAiB,EAAE;gDAClC5G,YAAYyG,GAAG,CACb7B,iBACAqB,aAAaW,iBAAiB;gDAEhCnC,gBAAgBwB,aAAaW,iBAAiB,CAAC7gB,GAAG,CAChD,CAACC,QAAUA,MAAME,QAAQ;gDAE3Bme,QAAQ;4CACV;4CAEA,MAAMwC,YAAYZ,aAAaY,SAAS,IAAI,CAAC;4CAC7C,IAAIA,UAAUC,UAAU,KAAK,GAAG;gDAC9B,MAAMC,0BACJd,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAAC1W,MAAM,GAAG;gDAE1C,IACElJ,OAAOwb,MAAM,KAAK,YAClBmE,aACA,CAACI,yBACD;oDACA,MAAM,qBAEL,CAFK,IAAI7T,MACR,CAAC,MAAM,EAAEtQ,KAAK,wFAAwF,CAAC,GADnG,qBAAA;+DAAA;oEAAA;sEAAA;oDAEN;gDACF;gDAEA,6BAA6B;gDAC7B,4BAA4B;gDAC5B,iEAAiE;gDACjE,8BAA8B;gDAC9B,IAAI,CAAC+jB,WAAW;oDACd3G,YAAYyG,GAAG,CAAC7B,iBAAiB;wDAC/B;4DACEoC,QAAQ,CAAC;4DACT9gB,UAAUtD;4DACVqkB,iBAAiBrkB;4DACjBskB,qBAAqB,EAAE;4DACvBC,cACElB,aAAamB,qBAAqB;4DACpCC,oBAAoB,EAAE;4DACtBC,yBAAyB;wDAC3B;qDACD;oDACDhD,WAAW;gDACb,OAAO,IACL,CAACyC,2BACAF,CAAAA,UAAUU,OAAO,KAAK,WACrBV,UAAUU,OAAO,KAAK,cAAa,GACrC;oDACAvH,YAAYyG,GAAG,CAAC7B,iBAAiB,EAAE;oDACnCN,WAAW;oDACXF,oBAAoB;gDACtB;4CACF;4CAEA,IAAI6B,aAAamB,qBAAqB,EAAE;gDACtClH,cAAcuG,GAAG,CACf7B,iBACAqB,aAAamB,qBAAqB;4CAEtC;4CAEAjH,kBAAkBsG,GAAG,CAAC7B,iBAAiBiC;wCACzC;oCACF,OAAO;wCACL,IAAIf,IAAAA,4BAAa,EAACJ,cAAc;4CAC9B,IAAIO,aAAauB,cAAc,EAAE;gDAC/BxjB,QAAQI,IAAI,CACV,CAAC,kFAAkF,EAAExB,MAAM;4CAE/F;4CACAqjB,aAAa3B,QAAQ,GAAG;4CACxB2B,aAAauB,cAAc,GAAG;wCAChC;wCAEA,IACEvB,aAAa3B,QAAQ,KAAK,SACzB2B,CAAAA,aAAazB,WAAW,IAAIyB,aAAawB,SAAS,AAAD,GAClD;4CACAjJ,iBAAiB;wCACnB;wCAEA,IAAIyH,aAAazB,WAAW,EAAE;4CAC5BA,cAAc;4CACd3E,eAAe3I,GAAG,CAACtU;wCACrB;wCAEA,IAAIqjB,aAAa3E,mBAAmB,EAAE;4CACpCA,sBAAsB;wCACxB;wCAEA,IAAI2E,aAAauB,cAAc,EAAE;4CAC/BhiB,SAAS0R,GAAG,CAACtU;4CACbyhB,QAAQ;4CAER,IACE4B,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAAC1W,MAAM,GAAG,GACxC;gDACA6P,gBAAgB0G,GAAG,CACjB7jB,MACAqjB,aAAaW,iBAAiB;gDAEhCnC,gBAAgBwB,aAAaW,iBAAiB,CAAC7gB,GAAG,CAChD,CAACC,QAAUA,MAAME,QAAQ;4CAE7B;4CAEA,IACE+f,aAAamB,qBAAqB,KAClCM,sBAAY,CAACC,sBAAsB,EACnC;gDACAhI,yBAAyBzI,GAAG,CAACtU;4CAC/B,OAAO,IACLqjB,aAAamB,qBAAqB,KAClCM,sBAAY,CAACE,SAAS,EACtB;gDACAlI,uBAAuBxI,GAAG,CAACtU;4CAC7B;wCACF,OAAO,IAAIqjB,aAAa4B,cAAc,EAAE;4CACtC/H,iBAAiB5I,GAAG,CAACtU;wCACvB,OAAO,IACLqjB,aAAa3B,QAAQ,IACrB,CAACC,qBACD,AAAC,MAAM1B,oCAAqC,OAC5C;4CACAna,YAAYwO,GAAG,CAACtU;4CAChB0hB,WAAW;wCACb,OAAO,IAAIC,mBAAmB;4CAC5B,2DAA2D;4CAC3D,gDAAgD;4CAChD/e,SAAS0R,GAAG,CAACtU;4CACbyhB,QAAQ;wCACV;wCAEA,IAAI7M,eAAe5U,SAAS,QAAQ;4CAClC,IACE,CAACqjB,aAAa3B,QAAQ,IACtB,CAAC2B,aAAauB,cAAc,EAC5B;gDACA,MAAM,qBAEL,CAFK,IAAItU,MACR,CAAC,cAAc,EAAE4U,qDAA0C,EAAE,GADzD,qBAAA;2DAAA;gEAAA;kEAAA;gDAEN;4CACF;4CACA,2DAA2D;4CAC3D,mCAAmC;4CACnC,IACE,AAAC,MAAMjF,mCACP,CAACoD,aAAauB,cAAc,EAC5B;gDACA9e,YAAYqf,MAAM,CAACnlB;4CACrB;wCACF;wCAEA,IACEolB,+BAAmB,CAAC1e,QAAQ,CAAC1G,SAC7B,CAACqjB,aAAa3B,QAAQ,IACtB,CAAC2B,aAAauB,cAAc,EAC5B;4CACA,MAAM,qBAEL,CAFK,IAAItU,MACR,CAAC,OAAO,EAAEtQ,KAAK,GAAG,EAAEklB,qDAA0C,EAAE,GAD5D,qBAAA;uDAAA;4DAAA;8DAAA;4CAEN;wCACF;oCACF;gCACF,EAAE,OAAOhV,KAAK;oCACZ,IACE,CAACC,IAAAA,gBAAO,EAACD,QACTA,IAAImV,OAAO,KAAK,0BAEhB,MAAMnV;oCACR8M,aAAa1I,GAAG,CAACtU;gCACnB;4BACF;4BAEA,IAAIihB,aAAa,OAAO;gCACtB,IAAIQ,SAASC,UAAU;oCACrBhF;gCACF,OAAO;oCACLC;gCACF;4BACF;wBACF;wBAEAa,UAAUqG,GAAG,CAAC7jB,MAAM;4BAClBgiB;4BACAX;4BACAC;4BACAI;4BACAD;4BACAD;4BACAI;4BACAC;4BACAyD,qBAAqBna;4BACrBuX,SAASI;4BACTyC,cAAcpa;4BACdqa,kBAAkBra;4BAClBsa,qBAAqBta;wBACvB;oBACF;gBACF;gBAGJ,MAAMua,kBAAkB,MAAMnG;gBAC9B,MAAMoG,qBACJ,AAAC,MAAMvG,qCACNsG,mBAAmBA,gBAAgBT,cAAc;gBAEpD,MAAMW,cAAc;oBAClBpH,0BAA0B,MAAMyB;oBAChCxB,cAAc,MAAMyB;oBACpBxB;oBACA9C;oBACA+C,uBAAuBgH;gBACzB;gBAEA,OAAOC;YACT;YAEA,IAAIxJ,oBAAoBA,mBAAmByJ,cAAc;YACzDzL,IAAAA,wBAAgB,EAAC,iCAAiC7U;YAElD,IAAIiZ,0BAA0B;gBAC5Bpd,QAAQI,IAAI,CACVskB,IAAAA,gBAAI,EAACC,IAAAA,kBAAM,EAAC,CAAC,SAAS,CAAC,KACrBA,IAAAA,kBAAM,EACJ,CAAC,qJAAqJ,CAAC;gBAG7J3kB,QAAQI,IAAI,CACV;YAEJ;YAEA,MAAM,EAAEgiB,YAAY,EAAE,GAAGpf;YAEzB,MAAM4hB,gCAA0C,EAAE;YAClD,IAAIngB,wBAAwB;gBAC1BmgB,8BAA8Brf,IAAI,CAChC9F,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAE,GAAGiO,wCAA6B,CAAC,GAAG,CAAC;gBAEnE,+DAA+D;gBAC/D,8FAA8F;gBAC9F,IAAI,CAACtH,eAAgBmS,CAAAA,uBAAuBC,qBAAoB,GAAI;oBAClEmJ,8BAA8Brf,IAAI,CAChC9F,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB,CAAC,KAAK,EAAEiO,wCAA6B,CAAC,GAAG,CAAC;gBAGhD;YACF;YAEA,MAAMkU,8BAA8B1gB,cACjCU,UAAU,CAAC,kCACX0F,OAAO,CAAC;gBACP,MAAMua,0BAAkD,CAAC;gBAEzD,KAAK,MAAM,CAACnI,KAAKoI,MAAM,IAAIrjB,OAAOC,OAAO,CACvCqB,OAAOmD,YAAY,CAACkc,aAAa,IAAI,CAAC,GACrC;oBACD,IAAI1F,OAAOoI,OAAO;wBAChBD,uBAAuB,CAACnI,IAAI,GAAGld,aAAI,CAACgG,QAAQ,CAAClG,SAASwlB;oBACxD;gBACF;gBAEA,MAAMC,sBAAmD;oBACvDhhB,SAAS;oBACThB,QAAQ;wBACN,GAAGA,MAAM;wBACTiiB,YAAYlb;wBACZ,GAAIpK,QAAcE,cAAc,GAC5B;4BACEqlB,UAAU;wBACZ,IACA,CAAC,CAAC;wBACN9C,cAAcA,eACV3iB,aAAI,CAACgG,QAAQ,CAAClG,SAAS6iB,gBACvBpf,OAAOof,YAAY;wBACvBjc,cAAc;4BACZ,GAAGnD,OAAOmD,YAAY;4BACtBkc,eAAeyC;4BACfK,iBAAiBxlB,QAAcE,cAAc;4BAC7CulB,uBAAuB5b;wBACzB;oBACF;oBACA5E,QAAQsD;oBACRmd,gBAAgB5lB,aAAI,CAACgG,QAAQ,CAACnB,uBAAuB4D;oBACrDhD,OAAO;wBACL6P,2BAAe;wBACftV,aAAI,CAACgG,QAAQ,CAAClG,SAAS6Y;wBACvB+C,0BAAc;wBACdha,8BAAkB;wBAClB1B,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEC,qCAAyB;wBACrDlD,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEyc,+BAAmB;wBAC/C1f,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAE4iB,qCAAyB,GAAG;2BACpD,CAACjc,cACD;4BACE5J,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB6iB,8CAAkC,GAAG;4BAEvCC,mCAAuB;yBACxB,GACD,EAAE;2BACF5gB,SACA;+BACM5B,OAAOmD,YAAY,CAAC0X,GAAG,GACvB;gCACEpe,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB+iB,0CAA8B,GAAG;gCAEnChmB,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB+iB,0CAA8B,GAAG;6BAEpC,GACD,EAAE;4BACNhmB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEga,8BAAkB;4BAC9Cjd,aAAI,CAACC,IAAI,CAACkd,oCAAwB;4BAClCvB,8BAAkB;4BAClB5b,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB2c,qCAAyB,GAAG;4BAE9B5f,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB2c,qCAAyB,GAAG;yBAE/B,GACD,EAAE;2BACF3S,YAAY,CAACrD,cACb;4BACEqc,gCAAoB,GAAG;4BACvBjmB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEgjB,gCAAoB,GAAG;yBACpD,GACD,EAAE;wBACNC,yBAAa;wBACblmB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEkjB,8BAAkB,GAAG;wBACjDnmB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEkjB,8BAAkB,GAAG;wBACjD9iB,iCAAqB;2BAClB8hB;qBACJ,CACE/iB,MAAM,CAACgkB,wBAAW,EAClB9jB,GAAG,CAAC,CAACkD,OAASxF,aAAI,CAACC,IAAI,CAACsD,OAAOzD,OAAO,EAAE0F;oBAC3C6gB,QAAQ,EAAE;gBACZ;gBAEA,OAAOd;YACT;YAEF,IAAI,CAACxK,gBAAgB;gBACnBqK,4BAA4BiB,MAAM,CAACvgB,IAAI,CACrC9F,aAAI,CAACgG,QAAQ,CACXyC,KACAzI,aAAI,CAACC,IAAI,CACPD,aAAI,CAACkG,OAAO,CACVmB,QAAQC,OAAO,CACb,sDAGJ;YAIR;YAEA,MAAMgf,iBAAiBlV,UAAU6P,IAAI,CAAC,CAACpd,IACrCA,EAAEgC,QAAQ,CAACmL,8BAAmB;YAEhC,IAAIjM,oBAAoB;YAExB,IAAIuhB,gBAAgB;gBAClB,MAAM7E,aAAa,MAAMC,IAAAA,sCAA6B,EAAC;oBACrDF,gBAAgB;oBAChBF,cAActhB,aAAI,CAACC,IAAI,CAACwI,KAAK6d;oBAC7B/iB;oBACA4B;oBACAoL,gBAAgBhN,OAAOgN,cAAc;oBACrCyB,OAAO;oBACP7S,MAAM;gBACR;gBAEA,IAAIsiB,WAAWE,mBAAmB,EAAE;oBAClCC;gBACF;gBAEA,IAAIH,WAAWI,OAAO,KAAK,UAAU;wBAIvBJ;oBAHZ1c,oBAAoB;oBACpB0Y,wBAAwBC,SAAS,CAAC,eAAe,GAAG;wBAClDmE,SAASJ,WAAWI,OAAO;wBAC3B0E,UAAU9E,EAAAA,yBAAAA,WAAW+E,UAAU,qBAArB/E,uBAAuB8E,QAAQ,KAAI;4BAC3C;gCACEE,QAAQ;gCACRC,gBAAgB;4BAClB;yBACD;oBACH;oBAEA,IAAI9c,aAAa;wBACf,MAAMzI,cACJnB,aAAI,CAACC,IAAI,CACPH,SACA,UACA+B,SACA8kB,gDAAoC,GAEtClJ,wBAAwBC,SAAS,CAAC,eAAe,CAAC6I,QAAQ,IAAI,EAAE;oBAEpE;gBACF;YACF;YAEA,MAAMvjB,6BAA6BlD,SAAS2d;YAE5C,IAAI,CAACtU,kBAAkB,CAAC2P,oBAAoB;gBAC1CA,qBAAqB6B,IAAAA,sCAAkB,EAAC;oBACtClS;oBACAlF;oBACAzD;oBACA8a,mBAAmBC,IAAAA,qCAA6B,EAAC8B;oBACjD1X,aAAa;2BAAIA;qBAAY;oBAC7BP;oBACAqW;oBACAlC;oBACAhU;oBACA+E,aAAa;gBACf,GAAGoR,KAAK,CAAC,CAAC3L;oBACR9O,QAAQsP,KAAK,CAACR;oBACd3E,QAAQe,IAAI,CAAC;gBACf;YACF;YAEA,IAAI4Q,iBAAiBmE,IAAI,GAAG,KAAKze,SAASye,IAAI,GAAG,GAAG;gBAClD,yDAAyD;gBACzD,+DAA+D;gBAC/DjL,eAAeW,UAAU,GAAGT,IAAAA,sBAAe,EAAC;uBACvC4G;uBACAta;iBACJ,EAAEO,GAAG,CAAC,CAACnD;oBACN,OAAOynB,IAAAA,8BAAc,EAACznB,MAAM0C;gBAC9B;YACF;YAEA,2DAA2D;YAC3D,MAAM6C,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMlE,cAAckU,oBAAoBE;YAExD,iHAAiH;YACjH,8DAA8D;YAC9D,MAAMsR,oBACJ,CAAClJ,4BAA6B,CAAA,CAACG,yBAAyB/J,WAAU;YAEpE,IAAIoI,aAAaqE,IAAI,GAAG,GAAG;gBACzB,MAAMnR,MAAM,qBAQX,CARW,IAAII,MACd,CAAC,qCAAqC,EACpC0M,aAAaqE,IAAI,KAAK,IAAI,KAAK,IAChC,kDAAkD,EAAE;uBAAIrE;iBAAa,CACnE7Z,GAAG,CAAC,CAACwkB,KAAO,CAAC,KAAK,EAAEA,IAAI,EACxB7mB,IAAI,CACH,MACA,sFAAsF,CAAC,GAPjF,qBAAA;2BAAA;gCAAA;kCAAA;gBAQZ;gBACAoP,IAAIE,IAAI,GAAG;gBACX,MAAMF;YACR;YAEA,MAAM0X,IAAAA,0BAAY,EAACjnB,SAAS+B;YAE5B,IAAI0B,OAAOmD,YAAY,CAACsgB,WAAW,EAAE;gBACnC,MAAMC,WACJ5f,QAAQ;gBAEV,MAAM6f,eAAe,MAAM,IAAIxN,QAAkB,CAACpS,SAAS6f;oBACzDF,SACE,YACA;wBAAEjZ,KAAKhO,aAAI,CAACC,IAAI,CAACH,SAAS;oBAAU,GACpC,CAACuP,KAAK5J;wBACJ,IAAI4J,KAAK;4BACP,OAAO8X,OAAO9X;wBAChB;wBACA/H,QAAQ7B;oBACV;gBAEJ;gBAEA2f,4BAA4B3f,KAAK,CAACK,IAAI,IACjCohB,aAAa5kB,GAAG,CAAC,CAACzB,WACnBb,aAAI,CAACC,IAAI,CAACsD,OAAOzD,OAAO,EAAE,UAAUe;YAG1C;YAEA,MAAMumB,WAAqC;gBACzC;oBACEpX,aAAa;oBACbC,iBAAiB1M,OAAOmD,YAAY,CAACqO,SAAS,GAAG,IAAI;gBACvD;gBACA;oBACE/E,aAAa;oBACbC,iBAAiB1M,OAAOmD,YAAY,CAACsgB,WAAW,GAAG,IAAI;gBACzD;gBACA;oBACEhX,aAAa;oBACbC,iBAAiB1M,OAAOmD,YAAY,CAAC2gB,iBAAiB,GAAG,IAAI;gBAC/D;gBACA;oBACErX,aAAa;oBACbC,iBAAiB1M,OAAOmD,YAAY,CAAC0O,GAAG,GAAG,IAAI;gBACjD;gBACA;oBACEpF,aAAa;oBACbC,iBAAiBqX,IAAAA,kCAA0B,EAAC/jB,UAAU,IAAI;gBAC5D;aACD;YACDuJ,UAAUY,MAAM,CACd0Z,SAAS9kB,GAAG,CAAC,CAACilB;gBACZ,OAAO;oBACLrX,WAAWC,iCAAyB;oBACpCC,SAASmX;gBACX;YACF;YAGF,MAAMpkB,iCACJrD,SACAslB;YAGF,iDAAiD;YACjD,sDAAsD;YACtD,IAAIjc,kBAAkB,CAACS,aAAa;gBAClCnJ,KAAIiL,IAAI,CAAC;gBAET,MAAMhH,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC;oBACZ,MAAMsG,IAAAA,gCAAe,EAAC;wBACpB7L;wBACAyD;oBACF;gBACF;YACJ;YAEA,MAAMuB,qBAAyC,MAAMxD,aACnDtB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEyc,+BAAmB;YAG1D,MAAM9d,oBAAuC;gBAC3C2C,SAAS;gBACTpC,QAAQ,CAAC;gBACTQ,eAAe,CAAC;gBAChB6kB,gBAAgB,EAAE;gBAClBC,SAAS5V;YACX;YAEA,MAAM6V,qBAA+B,EAAE;YAEvC,MAAM,EAAEvR,IAAI,EAAE,GAAG5S;YAEjB,MAAMokB,wBAAwBpD,+BAAmB,CAACniB,MAAM,CACtD,CAACjD,OACC0L,WAAW,CAAC1L,KAAK,IACjB0L,WAAW,CAAC1L,KAAK,CAACqO,UAAU,CAAC;YAEjCma,sBAAsBC,OAAO,CAAC,CAACzoB;gBAC7B,IAAI,CAAC4C,SAAS8lB,GAAG,CAAC1oB,SAAS,CAACwe,0BAA0B;oBACpD1Y,YAAYwO,GAAG,CAACtU;gBAClB;YACF;YAEA,MAAM2oB,cAAcH,sBAAsB9hB,QAAQ,CAAC;YACnD,MAAMkiB,sBACJ,CAACD,eAAe,CAAChK,yBAAyB,CAACH;YAE7C,MAAMqK,gBAAgB;mBAAI/iB;mBAAgBlD;aAAS;YACnD,MAAMkmB,iBAAiB1L,YAAYsL,GAAG,CAAC3T,4CAAgC;YACvE,MAAMgU,kBAAkBjU,aAAagU;YAErC,MAAMxP,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;YACd;YAEA,sDAAsD;YACtD,mBAAmB;YACnB,yBAAyB;YACzB,gCAAgC;YAChC,IACE,CAAC3O,iBACAie,CAAAA,cAAcvb,MAAM,GAAG,KACtBoa,qBACAkB,uBACA5iB,MAAK,GACP;gBACA,MAAMgjB,uBACJzjB,cAAcU,UAAU,CAAC;gBAC3B,MAAM+iB,qBAAqB9iB,YAAY,CAAC;oBACtC+iB,IAAAA,8BAAsB,EACpB;2BACKJ;2BACArjB,SAASY,KAAK,CAACnD,MAAM,CAAC,CAACjD,OAAS,CAAC6oB,cAAcniB,QAAQ,CAAC1G;qBAC5D,EACD4C,UACA,IAAI+Y,IACFzJ,MAAMC,IAAI,CAACgL,gBAAgBpa,OAAO,IAAII,GAAG,CACvC,CAAC,CAACnD,MAAMgD,OAAO;wBACb,OAAO;4BAAChD;4BAAMgD,OAAOG,GAAG,CAAC,CAACC,QAAUA,MAAME,QAAQ;yBAAE;oBACtD;oBAKN,MAAMmG,YAAY,AAACvB,QAAQ,aACxBwB,OAAO;oBAEV,MAAMwf,eAAmC;wBACvC,GAAG9kB,MAAM;wBACT,sEAAsE;wBACtE,+BAA+B;wBAC/B,wEAAwE;wBACxE,6DAA6D;wBAC7D+kB,eAAe,CAACC;4BACd,+DAA+D;4BAC/D,iEAAiE;4BACjE,uEAAuE;4BACvE,UAAU;4BACV,EAAE;4BACF,6DAA6D;4BAC7DxmB,SAAS6lB,OAAO,CAAC,CAACzoB;gCAChB,IAAIwW,IAAAA,qBAAc,EAACxW,OAAO;oCACxBuoB,mBAAmB5hB,IAAI,CAAC3G;oCAExB,IAAI8c,uBAAuB4L,GAAG,CAAC1oB,OAAO;wCACpC,iEAAiE;wCACjE,mBAAmB;wCACnB,IAAIgX,MAAM;4CACRoS,UAAU,CAAC,CAAC,CAAC,EAAEpS,KAAK0I,aAAa,GAAG1f,MAAM,CAAC,GAAG;gDAC5CA;gDACAqpB,gBAAgB;4CAClB;wCACF,OAAO;4CACLD,UAAU,CAACppB,KAAK,GAAG;gDACjBA;gDACAqpB,gBAAgB;4CAClB;wCACF;oCACF,OAAO;wCACL,iEAAiE;wCACjE,iCAAiC;wCACjC,OAAOD,UAAU,CAACppB,KAAK;oCACzB;gCACF;4BACF;4BAEA,oEAAoE;4BACpE,cAAc;4BACdmd,gBAAgBsL,OAAO,CAAC,CAACzlB,QAAQhD;gCAC/BgD,OAAOylB,OAAO,CAAC,CAACrlB;oCACdgmB,UAAU,CAAChmB,MAAME,QAAQ,CAAC,GAAG;wCAC3BtD;wCACAspB,UAAUlmB,MAAMihB,eAAe;oCACjC;gCACF;4BACF;4BAEA,IAAIqD,mBAAmB;gCACrB0B,UAAU,CAAC,OAAO,GAAG;oCACnBppB,MAAM4U,cAAc,SAAS;gCAC/B;4BACF;4BAEA,IAAIgU,qBAAqB;gCACvBQ,UAAU,CAAC,OAAO,GAAG;oCACnBppB,MAAM;gCACR;4BACF;4BAEA,wDAAwD;4BACxD,gDAAgD;4BAChDod,YAAYqL,OAAO,CAAC,CAACzlB,QAAQgf;gCAC3B,MAAMiC,YAAY1G,kBAAkBgM,GAAG,CAACvH;gCACxC,MAAMwH,iBAAiBvF,CAAAA,6BAAAA,UAAWU,OAAO,MAAK;gCAE9C,MAAMnD,oBAA6ByC,YAC/BwF,IAAAA,2BAAsB,EAACrlB,OAAOmD,YAAY,CAAC0O,GAAG,EAAEgO,aAChD;gCAEJjhB,OAAOylB,OAAO,CAAC,CAACrlB;oCACd,8DAA8D;oCAC9D,wDAAwD;oCACxD,0DAA0D;oCAC1D,IACEA,MAAMqhB,kBAAkB,IACxBrhB,MAAMqhB,kBAAkB,CAACnX,MAAM,GAAG,GAClC;wCACA;oCACF;oCAEA8b,UAAU,CAAChmB,MAAME,QAAQ,CAAC,GAAG;wCAC3BtD,MAAMgiB;wCACNsH,UAAUlmB,MAAMihB,eAAe;wCAC/BqF,sBAAsBtmB,MAAMkhB,mBAAmB;wCAC/CqF,iBAAiBH;wCACjBI,WAAW;wCACXC,oBAAoBrI;wCACpBsI,wBAAwB,CAAC1mB,MAAMshB,uBAAuB;oCACxD;gCACF;4BACF;4BAEA,IAAI1N,MAAM;gCACR,KAAK,MAAMhX,QAAQ;uCACd8F;uCACAlD;uCACC8kB,oBAAoB;wCAAC;qCAAO,GAAG,EAAE;uCACjCkB,sBAAsB;wCAAC;qCAAO,GAAG,EAAE;iCACxC,CAAE;oCACD,MAAMmB,QAAQnnB,SAAS8lB,GAAG,CAAC1oB;oCAC3B,MAAM+jB,YAAYvN,IAAAA,qBAAc,EAACxW;oCACjC,MAAMgqB,aAAaD,SAASjN,uBAAuB4L,GAAG,CAAC1oB;oCAEvD,KAAK,MAAMiqB,UAAUjT,KAAKrU,OAAO,CAAE;4CAMzBymB;wCALR,+DAA+D;wCAC/D,IAAIW,SAAShG,aAAa,CAACiG,YAAY;wCACvC,MAAMpjB,aAAa,CAAC,CAAC,EAAEqjB,SAASjqB,SAAS,MAAM,KAAKA,MAAM;wCAE1DopB,UAAU,CAACxiB,WAAW,GAAG;4CACvB5G,MAAMopB,EAAAA,mBAAAA,UAAU,CAACppB,KAAK,qBAAhBopB,iBAAkBppB,IAAI,KAAIA;4CAChCkqB,SAASD;4CACTZ,gBAAgBW;wCAClB;oCACF;oCAEA,IAAID,OAAO;wCACT,qDAAqD;wCACrD,OAAOX,UAAU,CAACppB,KAAK;oCACzB;gCACF;4BACF;4BAEA,OAAOopB;wBACT;oBACF;oBAEA,MAAMtf,SAASjJ,aAAI,CAACC,IAAI,CAACH,SAAS;oBAClC,MAAMwpB,eAAe,MAAM1gB,UACzBH,KACA;wBACEM,YAAYsf;wBACZ3f;wBACAM,QAAQ;wBACRF,aAAa;wBACbS;wBACAC;wBACAjE,OAAOyiB;wBACP/e;wBACAsgB,eAAe;wBACf1hB,YAAYpB,mBAAmB4hB;oBACjC,GACA3jB;oBAGF,sDAAsD;oBACtD,IAAI,CAAC4kB,cAAc;oBAEnB,MAAME,kBAAkB,CAACjnB;4BACK+mB;wBAA5B,MAAM1E,uBAAsB0E,2BAAAA,aAAaG,MAAM,CAACf,GAAG,CACjDnmB,MAAME,QAAQ,sBADY6mB,yBAEzB1E,mBAAmB;wBAEtB,kEAAkE;wBAClE,+DAA+D;wBAC/D,sBAAsB;wBACtB,IACEA,uBACA,CAACriB,MAAMshB,uBAAuB,IAC9BthB,MAAMmhB,YAAY,KAAKO,sBAAY,CAACE,SAAS,EAC7C;4BACA,OAAOF,sBAAY,CAACC,sBAAsB;wBAC5C;wBAEA,4DAA4D;wBAC5D,6BAA6B;wBAC7B,IAAI,CAAC3hB,MAAMmhB,YAAY,EAAE;4BACvB,OAAOO,sBAAY,CAACyF,SAAS;wBAC/B;wBAEA,OAAOnnB,MAAMmhB,YAAY;oBAC3B;oBAEA,MAAMiG,kBAAkB,CACtBC,YACAC,oBAAgC,KAAK;4BAGnCP;wBADF,MAAMQ,gBACJR,2BAAAA,aAAaG,MAAM,CAACf,GAAG,CAACkB,gCAAxBN,yBAAqCQ,YAAY;wBAEnD,IAAI,CAACA,cAAc;4BACjB,OAAO;gCAAEzG,YAAYwG;gCAAmBE,QAAQzf;4BAAU;wBAC5D;wBAEA,IACEwf,aAAazG,UAAU,KAAK,SAC5ByG,aAAazG,UAAU,GAAG,KAC1ByG,aAAaC,MAAM,KAAKzf,WACxB;4BACA,OAAO;gCACL+Y,YAAYyG,aAAazG,UAAU;gCACnC0G,QAAQxmB,OAAOymB,UAAU;4BAC3B;wBACF;wBAEA,OAAOF;oBACT;oBAEA,IAAIvgB,eAAemB,QAAQC,GAAG,CAACsf,sBAAsB,KAAK,KAAK;wBAC7DC,IAAAA,oCAAkB,EAACZ;oBACrB;oBAEAa,IAAAA,qDAA+B,EAAC;wBAC9BrqB,SAASyD,OAAOzD,OAAO;wBACvBsqB,QAAQ;4BACNpf;+BACGse,aAAae,2BAA2B,CAACC,MAAM;yBACnD;oBACH;oBAEA1oB,kBAAkB4lB,cAAc,GAAGnW,MAAMC,IAAI,CAC3CgY,aAAaiB,gBAAgB;oBAG/B,2CAA2C;oBAC3C,KAAK,MAAMprB,QAAQ8F,YAAa;wBAC9B,MAAMulB,eAAeC,IAAAA,oBAAW,EAACtrB,MAAMW,SAASwK,WAAW;wBAC3D,MAAMvJ,YAAE,CAAC2pB,MAAM,CAACF;oBAClB;oBAEAjO,YAAYqL,OAAO,CAAC,CAACzE,mBAAmBhC;4BAWbxE;wBAVzB,MAAMxd,OAAOqd,mBAAmBkM,GAAG,CAACvH;wBACpC,IAAI,CAAChiB,MAAM,MAAM,qBAAoC,CAApC,IAAIwrB,8BAAc,CAAC,mBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAmC;wBAEpD,MAAMvH,YAAY1G,kBAAkBgM,GAAG,CAACvH;wBACxC,IAAI,CAACiC,WAAW,MAAM,qBAA0C,CAA1C,IAAIuH,8BAAc,CAAC,yBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyC;wBAE/D,IAAIC,oBACFxH,UAAUC,UAAU,KAAK,KACzBsG,gBAAgBxqB,MAAMkkB,UAAU,KAAK;wBAEvC,IAAIuH,uBAAqBjO,iBAAAA,UAAU+L,GAAG,CAACvpB,0BAAdwd,eAAqBkE,QAAQ,GAAE;4BACtD,uEAAuE;4BACvE,qFAAqF;4BACrFlE,UAAUqG,GAAG,CAAC7jB,MAAM;gCAClB,GAAIwd,UAAU+L,GAAG,CAACvpB,KAAK;gCACvB0hB,UAAU;gCACVD,OAAO;4BACT;wBACF;wBAEA,MAAMiK,oBAAoBC,IAAAA,gCAAe,EAAC3J;wBAE1C,kEAAkE;wBAClE,yBAAyB;wBACzB,MAAMR,oBACJ,CAACkK,qBACDjC,IAAAA,2BAAsB,EAACrlB,OAAOmD,YAAY,CAAC0O,GAAG,EAAEgO,aAC5C,OACA9Y;wBAEN,MAAMygB,sBACJ,uEAAuE;wBACvExnB,OAAOynB,eAAe,IAAIC,oCAA6B;wBAEzD,0FAA0F;wBAC1F,4CAA4C;wBAC5C,MAAMC,YAAwB;4BAC5B;gCAAEC,MAAM;gCAAUjO,KAAKkO,+BAAa;4BAAC;4BACrC;gCACED,MAAM;gCACNjO,KAAK;gCACLoI,OAAO;4BACT;4BACA,iGAAiG;4BACjG,iGAAiG;+BAC7F3E,oBACA;gCACE;oCACEwK,MAAM;oCACNjO,KAAK;oCACLoI,OAAOyF;gCACT;6BACD,GACD,EAAE;yBACP;wBAED,mEAAmE;wBACnE,6DAA6D;wBAC7D,mEAAmE;wBACnE,8DAA8D;wBAC9D,2BAA2B;wBAC3B,MAAM5oB,SAA6B,EAAE;wBACrC,MAAMQ,gBAAoC,EAAE;wBAE5C,mEAAmE;wBACnE,iEAAiE;wBACjE,+DAA+D;wBAC/D,iEAAiE;wBACjE,mDAAmD;wBACnD,IAAI0oB,yBAA6C,EAAE;wBACnD,IAAIC,uBAA2C,EAAE;wBACjD,KAAK,MAAMC,oBAAoBpI,kBAAmB;4BAChD,IACEoI,iBAAiB9H,mBAAmB,IACpC8H,iBAAiB9H,mBAAmB,CAAChX,MAAM,GAAG,GAC9C;gCACA4e,uBAAuBvlB,IAAI,CAACylB;4BAC9B,OAAO;gCACLD,qBAAqBxlB,IAAI,CAACylB;4BAC5B;wBACF;wBAEAF,yBAAyBG,IAAAA,4BAAqB,EAC5CH,wBACA,CAACE,mBAAqBA,iBAAiB9oB,QAAQ;wBAEjD6oB,uBAAuBE,IAAAA,4BAAqB,EAC1CF,sBACA,CAACC,mBAAqBA,iBAAiB9oB,QAAQ;wBAGjD0gB,oBAAoB;+BACfmI;+BACAD;yBACJ;wBAED,KAAK,MAAME,oBAAoBpI,kBAAmB;4BAChD,+BAA+B;4BAC/B,iCAAiC;4BACjC,IAAIoI,iBAAiB9oB,QAAQ,KAAKgpB,sCAA0B,EAAE;gCAC5D;4BACF;4BAEA,IACE9K,qBACA4K,iBAAiB9H,mBAAmB,IACpC8H,iBAAiB9H,mBAAmB,CAAChX,MAAM,GAAG,GAC9C;gCACA,6DAA6D;gCAC7D,8BAA8B;gCAC9B9J,cAAcmD,IAAI,CAACylB;4BACrB,OAAO;gCACL,4DAA4D;gCAC5D,gCAAgC;gCAChCppB,OAAO2D,IAAI,CAACylB;4BACd;wBACF;wBAEA,gCAAgC;wBAChC,KAAK,MAAMhpB,SAASJ,OAAQ;4BAC1B,IAAIwT,IAAAA,qBAAc,EAACxW,SAASoD,MAAME,QAAQ,KAAKtD,MAAM;4BACrD,IAAIoD,MAAME,QAAQ,KAAKgpB,sCAA0B,EAAE;4BAEnD,MAAM,EACJpQ,WAAW,CAAC,CAAC,EACbuJ,mBAAmB,EACnB8G,YAAY,EACb,GAAGpC,aAAaG,MAAM,CAACf,GAAG,CAACnmB,MAAME,QAAQ,KAAK,CAAC;4BAEhD,MAAMqnB,eAAeH,gBACnBpnB,MAAME,QAAQ,EACd2gB,UAAUC,UAAU;4BAGtB1G,UAAUqG,GAAG,CAACzgB,MAAME,QAAQ,EAAE;gCAC5B,GAAIka,UAAU+L,GAAG,CAACnmB,MAAME,QAAQ,CAAC;gCACjCipB;gCACA9G;gCACAH,qBAAqBqF;4BACvB;4BAEA,uEAAuE;4BACvEnN,UAAUqG,GAAG,CAAC7jB,MAAM;gCAClB,GAAIwd,UAAU+L,GAAG,CAACvpB,KAAK;gCACvBusB;gCACA9G;gCACAH,qBAAqBqF;4BACvB;4BAEA,IAAIA,aAAazG,UAAU,KAAK,GAAG;gCACjC,MAAMsI,kBAAkBpL,IAAAA,oCAAiB,EAAChe,MAAME,QAAQ;gCAExD,IAAImpB;gCACJ,IAAIf,mBAAmB;oCACrBe,YAAY;gCACd,OAAO;oCACLA,YAAY5rB,aAAI,CAAC6rB,KAAK,CAAC5rB,IAAI,CAAC,GAAG0rB,kBAAkB1U,qBAAU,EAAE;gCAC/D;gCAEA,IAAI6U;gCACJ,6DAA6D;gCAC7D,6DAA6D;gCAC7D,6DAA6D;gCAC7D,uBAAuB;gCACvB,IAAI,CAACjB,qBAAqB3V,iBAAiB;oCACzC4W,oBAAoB9rB,aAAI,CAAC6rB,KAAK,CAAC5rB,IAAI,CACjC,GAAG0rB,kBAAkBxU,8BAAmB,EAAE;gCAE9C;gCAEA,MAAM4U,OAAOC,IAAAA,mBAAW,EAAC3Q;gCAEzBzZ,kBAAkBO,MAAM,CAACI,MAAME,QAAQ,CAAC,GAAG;oCACzCwpB,eAAeF,KAAKG,MAAM;oCAC1BC,gBAAgBJ,KAAK9f,OAAO;oCAC5BmgB,eAAelX,kBACXyL,oBACE0L,4BAAa,CAACC,gBAAgB,GAC9BD,4BAAa,CAACE,MAAM,GACtBjiB;oCACJkiB,iBAAiB7L;oCACjB8L,uBAAuBvB;oCACvBwB,0BAA0B5C,aAAazG,UAAU;oCACjDsJ,sBAAsB7C,aAAaC,MAAM;oCACzC1nB,UAAUlD;oCACVysB;oCACAE;oCACAc,aAAahuB;gCACf;4BACF,OAAO;gCACLgsB,oBAAoB;gCACpB,8DAA8D;gCAC9D,oBAAoB;gCACpBjO,UAAUqG,GAAG,CAACzgB,MAAME,QAAQ,EAAE;oCAC5B,GAAIka,UAAU+L,GAAG,CAACnmB,MAAME,QAAQ,CAAC;oCACjCme,OAAO;oCACPC,UAAU;gCACZ;4BACF;wBACF;wBAEA,IAAI,CAAC+J,qBAAqBjV,IAAAA,qBAAc,EAACxW,OAAO;4BAC9C,iEAAiE;4BACjE,0DAA0D;4BAC1D,sBAAsB;4BACtB,IAAI,CAACwhB,mBAAmB;gCACtBhe,cAAcmD,IAAI,CAAC;oCACjByd,QAAQ,CAAC;oCACT9gB,UAAUtD;oCACVqkB,iBAAiBrkB;oCACjBskB,qBAAqB,EAAE;oCACvBC,cACEjH,cAAciM,GAAG,CAACvH,oBAClB8C,sBAAY,CAACyF,SAAS;oCACxB9F,oBAAoB,EAAE;oCACtBC,yBAAyB;gCAC3B;4BACF;4BAEA,KAAK,MAAMthB,SAASI,cAAe;oCAGhB2mB,0BAuFM/mB;gCAzFvB,MAAMopB,kBAAkBpL,IAAAA,oCAAiB,EAAChe,MAAME,QAAQ;gCAExD,MAAM4Y,YAAWiO,2BAAAA,aAAaG,MAAM,CAACf,GAAG,CACtCnmB,MAAME,QAAQ,sBADC6mB,yBAEdjO,QAAQ;gCAEX,MAAMyO,eAAeH,gBAAgBpnB,MAAME,QAAQ;gCAEnD,IAAImpB,YAA2B;gCAC/B,IAAI,CAACf,mBAAmB;oCACtBe,YAAY5rB,aAAI,CAAC6rB,KAAK,CAAC5rB,IAAI,CAAC,GAAG0rB,kBAAkB1U,qBAAU,EAAE;gCAC/D;gCAEA,IAAI6U;gCACJ,IAAI,CAACjB,qBAAqB3V,iBAAiB;oCACzC4W,oBAAoB9rB,aAAI,CAAC6rB,KAAK,CAAC5rB,IAAI,CACjC,GAAG0rB,kBAAkBxU,8BAAmB,EAAE;gCAE9C;gCAEA,IAAI,CAAC0T,sBAAqBxP,4BAAAA,SAAUwR,YAAY,GAAE;oCAChD,MAAMC,eAAevX,eAAe5S,aAAa,CAACse,IAAI,CACpD,CAACjL,IAAMA,EAAE7W,IAAI,KAAKA;oCAEpB,IAAI,CAAC2tB,cAAc;wCACjB,MAAM,qBAAoC,CAApC,IAAIrd,MAAM,4BAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAmC;oCAC3C;oCAEAqd,aAAaC,yBAAyB,KAAK,EAAE;oCAC7C,KAAK,MAAMC,eAAe3R,SAASwR,YAAY,CAAE;wCAC/CC,aAAaC,yBAAyB,CAACjnB,IAAI,CACzCmnB,IAAAA,4DAA6B,EAAC1qB,MAAME,QAAQ,EAAEuqB;oCAElD;gCACF;gCAEArQ,UAAUqG,GAAG,CAACzgB,MAAME,QAAQ,EAAE;oCAC5B,GAAIka,UAAU+L,GAAG,CAACnmB,MAAME,QAAQ,CAAC;oCACjCyqB,mBAAmB;oCACnB,gEAAgE;oCAChE,2CAA2C;oCAC3CxB,cAAc/K;gCAChB;gCAEA,MAAM+C,eAAe8F,gBAAgBjnB;gCAErC,+DAA+D;gCAC/D,+DAA+D;gCAC/D,oDAAoD;gCACpD,iDAAiD;gCACjD,MAAM4qB,uBACJxM,qBAAqB+C,iBAAiBO,sBAAY,CAACE,SAAS,GACxD2F,eACAxf;gCAEN,MAAMiC,WAAqB6gB,IAAAA,qCAA2B,EACpD1J,cACAnhB,MAAME,QAAQ;gCAGhB,MAAMspB,OACJ1Q,YACAsF,qBACA+C,iBAAiBO,sBAAY,CAACE,SAAS,GACnC6H,IAAAA,mBAAW,EAAC3Q,YACZ,CAAC;gCAEPzZ,kBAAkBe,aAAa,CAACJ,MAAME,QAAQ,CAAC,GAAG;oCAChD+pB,iBAAiB7L;oCACjByL,eAAelX,kBACXyL,oBACE0L,4BAAa,CAACC,gBAAgB,GAC9BD,4BAAa,CAACE,MAAM,GACtBjiB;oCACJmiB,uBAAuBvB;oCACvB9rB,YAAYI,IAAAA,qCAAmB,EAC7BH,IAAAA,8BAAkB,EAACkD,MAAME,QAAQ,EAAE;wCACjCnD,iBAAiB;oCACnB,GAAGG,EAAE,CAACC,MAAM;oCAEdksB;oCACArf;oCACA8gB,kBAAkB,EAAEF,wCAAAA,qBAAsB9J,UAAU;oCACpDiK,cAAc,EAAEH,wCAAAA,qBAAsBpD,MAAM;oCAC5CwD,gBAAgBxB,KAAKG,MAAM;oCAC3BsB,iBAAiBzB,KAAK9f,OAAO;oCAC7B2X,oBAAoBrX,WAChBhK,MAAMqhB,kBAAkB,GACxBtZ;oCACJmjB,qBAAqBlrB,EAAAA,6BAAAA,MAAMkhB,mBAAmB,qBAAzBlhB,2BAA2BkK,MAAM,IAClDtN,OACAmL;oCACJojB,gBAAgB,CAAC9B,YACb,OACApsB,IAAAA,qCAAmB,EACjBH,IAAAA,8BAAkB,EAACusB,WAAW;wCAC5BtsB,iBAAiB;wCACjBquB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGnuB,EAAE,CAACC,MAAM;oCAElBosB;oCACA+B,wBAAwB,CAAC/B,oBACrBxhB,YACA9K,IAAAA,qCAAmB,EACjBH,IAAAA,8BAAkB,EAACysB,mBAAmB;wCACpCxsB,iBAAiB;wCACjBquB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGnuB,EAAE,CAACC,MAAM;oCAElBktB,aAAahuB;gCACf;4BACF;wBACF;oBACF;oBAEA,MAAMkvB,mBAAmB,OACvBC,YACA5uB,MACAqG,MACA0jB,OACA8E,KACAC,oBAAoB,KAAK;wBAEzB,OAAO9F,qBACJ/iB,UAAU,CAAC,sBACXC,YAAY,CAAC;4BACZG,OAAO,GAAGA,KAAK,CAAC,EAAEwoB,KAAK;4BACvB,MAAME,OAAOluB,aAAI,CAACC,IAAI,CAACgJ,QAAQzD;4BAC/B,MAAM+N,WAAWkX,IAAAA,oBAAW,EAC1BsD,YACAjuB,SACAwK,WACA;4BAGF,MAAM6jB,eAAenuB,aAAI,CACtBgG,QAAQ,CACPhG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,GACnCjD,aAAI,CAACC,IAAI,CACPD,aAAI,CAACC,IAAI,CACPsT,UACA,yDAAyD;4BACzD,4BAA4B;4BAC5Bwa,WACGK,KAAK,CAAC,GACNC,KAAK,CAAC,KACN/rB,GAAG,CAAC,IAAM,MACVrC,IAAI,CAAC,OAEVuF,OAGHzB,OAAO,CAAC,OAAO;4BAElB,IACE,CAACmlB,SACD,CACE,mDAAmD;4BACnD,kDAAkD;4BAEhD3E,CAAAA,+BAAmB,CAAC1e,QAAQ,CAAC1G,SAC7B,CAACwoB,sBAAsB9hB,QAAQ,CAAC1G,KAAI,GAGxC;gCACAyd,aAAa,CAACzd,KAAK,GAAGgvB;4BACxB;4BAEA,MAAMG,OAAOtuB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEkrB;4BAClD,MAAMI,aACJ3sB,kBAAkB4lB,cAAc,CAAC3hB,QAAQ,CAAC1G;4BAE5C,2DAA2D;4BAC3D,0DAA0D;4BAC1D,qBAAqB;4BACrB,IAAI,AAAC,CAAA,CAACgX,QAAQ8X,iBAAgB,KAAM,CAACM,YAAY;gCAC/C,MAAMxtB,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAACooB,OAAO;oCAAEnoB,WAAW;gCAAK;gCACrD,MAAMpF,YAAE,CAACytB,MAAM,CAACN,MAAMI;4BACxB,OAAO,IAAInY,QAAQ,CAAC+S,OAAO;gCACzB,wDAAwD;gCACxD,oDAAoD;gCACpD,OAAOtM,aAAa,CAACzd,KAAK;4BAC5B;4BAEA,IAAIgX,MAAM;gCACR,IAAI8X,mBAAmB;gCAEvB,MAAMQ,YAAYtvB,SAAS,MAAMa,aAAI,CAAC0uB,OAAO,CAAClpB,QAAQ;gCACtD,MAAMmpB,sBAAsBR,aAAaC,KAAK,CAC5C,SAAS3hB,MAAM;gCAGjB,KAAK,MAAM2c,UAAUjT,KAAKrU,OAAO,CAAE;oCACjC,MAAM8sB,UAAU,CAAC,CAAC,EAAExF,SAASjqB,SAAS,MAAM,KAAKA,MAAM;oCAEvD,IACE+pB,SACAtnB,kBAAkB4lB,cAAc,CAAC3hB,QAAQ,CAAC+oB,UAC1C;wCACA;oCACF;oCAEA,MAAMC,sBAAsB7uB,aAAI,CAC7BC,IAAI,CACH,SACAmpB,SAASqF,WACT,8DAA8D;oCAC9D,+BAA+B;oCAC/BtvB,SAAS,MAAM,KAAKwvB,qBAErB5qB,OAAO,CAAC,OAAO;oCAElB,MAAM+qB,cAAc9uB,aAAI,CAACC,IAAI,CAC3BgJ,QACAmgB,SAASqF,WACTtvB,SAAS,MAAM,KAAKqG;oCAEtB,MAAMupB,cAAc/uB,aAAI,CAACC,IAAI,CAC3BH,SACAmD,4BAAgB,EAChB4rB;oCAGF,IAAI,CAAC3F,OAAO;wCACVtM,aAAa,CAACgS,QAAQ,GAAGC;oCAC3B;oCACA,MAAM9tB,YAAE,CAACkF,KAAK,CAACjG,aAAI,CAACkG,OAAO,CAAC6oB,cAAc;wCACxC5oB,WAAW;oCACb;oCACA,MAAMpF,YAAE,CAACytB,MAAM,CAACM,aAAaC;gCAC/B;4BACF;wBACF;oBACJ;oBAEA,eAAeC;wBACb,OAAO7G,qBACJ/iB,UAAU,CAAC,gCACXC,YAAY,CAAC;4BACZ,MAAM6oB,OAAOluB,aAAI,CAACC,IAAI,CACpBH,SACA,UACA,OACA;4BAEF,MAAM+uB,sBAAsB7uB,aAAI,CAC7BC,IAAI,CAAC,SAAS,YACd8D,OAAO,CAAC,OAAO;4BAElB,IAAIzD,IAAAA,cAAU,EAAC4tB,OAAO;gCACpB,MAAMntB,YAAE,CAACqF,QAAQ,CACf8nB,MACAluB,aAAI,CAACC,IAAI,CAACH,SAAS,UAAU+uB;gCAG/B,mEAAmE;gCACnE,yEAAyE;gCACzE,IAAI1Y,MAAM;oCACR,KAAK,MAAMiT,UAAUjT,KAAKrU,OAAO,CAAE;wCACjC,MAAM8sB,UAAU,CAAC,CAAC,EAAExF,OAAO,IAAI,CAAC;wCAChCxM,aAAa,CAACgS,QAAQ,GAAGC;oCAC3B;gCACF;gCAEAjS,aAAa,CAAC,OAAO,GAAGiS;4BAC1B;wBACF;oBACJ;oBAEA,oEAAoE;oBACpE,IAAI3G,iBAAiB;wBACnB,MAAM8G;oBACR,OAAO;wBACL,sGAAsG;wBACtG,IAAI,CAACjb,eAAe,CAACE,aAAa4S,mBAAmB;4BACnD,MAAMiH,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;wBAC3D;oBACF;oBAEA,IAAI/F,qBAAqB;wBACvB,MAAM+F,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;oBAC3D;oBAEA,KAAK,MAAM3uB,QAAQ6oB,cAAe;wBAChC,MAAMkB,QAAQnnB,SAAS8lB,GAAG,CAAC1oB;wBAC3B,MAAM8vB,sBAAsBhT,uBAAuB4L,GAAG,CAAC1oB;wBACvD,MAAM+jB,YAAYvN,IAAAA,qBAAc,EAACxW;wBACjC,MAAM+vB,SAAS9S,eAAeyL,GAAG,CAAC1oB;wBAClC,MAAMqG,OAAO+a,IAAAA,oCAAiB,EAACphB;wBAE/B,MAAMgwB,WAAWxS,UAAU+L,GAAG,CAACvpB;wBAC/B,MAAMiwB,eAAe9F,aAAa+F,MAAM,CAAC3G,GAAG,CAACvpB;wBAC7C,IAAIgwB,YAAYC,cAAc;4BAC5B,qBAAqB;4BACrB,IAAID,SAASnO,aAAa,EAAE;gCAC1BmO,SAASxK,gBAAgB,GAAGwK,SAASnO,aAAa,CAAC1e,GAAG,CACpD,CAACiR;oCACC,MAAMoG,WAAWyV,aAAaE,eAAe,CAAC5G,GAAG,CAACnV;oCAClD,IAAI,OAAOoG,aAAa,aAAa;wCACnC,MAAM,qBAAyC,CAAzC,IAAIlK,MAAM,iCAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAwC;oCAChD;oCAEA,OAAOkK;gCACT;4BAEJ;4BACAwV,SAASzK,YAAY,GAAG0K,aAAaE,eAAe,CAAC5G,GAAG,CAACvpB;wBAC3D;wBAEA,+DAA+D;wBAC/D,gEAAgE;wBAChE,YAAY;wBACZ,MAAMowB,gBAAgB,CAAErG,CAAAA,SAAShG,aAAa,CAAC+L,mBAAkB;wBAEjE,IAAIM,eAAe;4BACjB,MAAMzB,iBAAiB3uB,MAAMA,MAAMqG,MAAM0jB,OAAO;wBAClD;wBAEA,IAAIgG,UAAW,CAAA,CAAChG,SAAUA,SAAS,CAAChG,SAAS,GAAI;4BAC/C,MAAMsM,UAAU,GAAGhqB,KAAK,IAAI,CAAC;4BAC7B,MAAMsoB,iBAAiB3uB,MAAMqwB,SAASA,SAAStG,OAAO;4BAEtD,IAAIA,OAAO;gCACT,MAAM4E,iBAAiB3uB,MAAMqwB,SAASA,SAAStG,OAAO;4BACxD;wBACF;wBAEA,IAAIA,OAAO;4BACT,yDAAyD;4BACzD,oDAAoD;4BACpD,IAAI,CAAChG,WAAW;gCACd,MAAM4K,iBAAiB3uB,MAAMA,MAAMqG,MAAM0jB,OAAO;gCAEhD,IAAI/S,MAAM;oCACR,+DAA+D;oCAC/D,KAAK,MAAMiT,UAAUjT,KAAKrU,OAAO,CAAE;wCACjC,MAAM2tB,aAAa,CAAC,CAAC,EAAErG,SAASjqB,SAAS,MAAM,KAAKA,MAAM;wCAE1D,MAAM2qB,eAAeH,gBAAgB8F;wCAErC7tB,kBAAkBO,MAAM,CAACstB,WAAW,GAAG;4CACrC/C,0BAA0B5C,aAAazG,UAAU;4CACjDsJ,sBAAsB7C,aAAaC,MAAM;4CACzCyC,iBAAiBliB;4CACjB8hB,eAAe9hB;4CACfjI,UAAU;4CACVupB,WAAW5rB,aAAI,CAAC6rB,KAAK,CAAC5rB,IAAI,CACxB,eACA4B,SACA,GAAG2D,KAAK,KAAK,CAAC;4CAEhBsmB,mBAAmBxhB;4CACnBsiB,aAAahuB;wCACf;oCACF;gCACF,OAAO;oCACL,MAAMkrB,eAAeH,gBAAgBxqB;oCAErCyC,kBAAkBO,MAAM,CAAChD,KAAK,GAAG;wCAC/ButB,0BAA0B5C,aAAazG,UAAU;wCACjDsJ,sBAAsB7C,aAAaC,MAAM;wCACzCyC,iBAAiBliB;wCACjB8hB,eAAe9hB;wCACfjI,UAAU;wCACVupB,WAAW5rB,aAAI,CAAC6rB,KAAK,CAAC5rB,IAAI,CACxB,eACA4B,SACA,GAAG2D,KAAK,KAAK,CAAC;wCAEhB,6CAA6C;wCAC7CsmB,mBAAmBxhB;wCACnBsiB,aAAahuB;oCACf;gCACF;gCACA,IAAIuwB,UAAU;oCACZA,SAAS1K,mBAAmB,GAAGkF,gBAAgBxqB;gCACjD;4BACF,OAAO;gCACL,oEAAoE;gCACpE,4CAA4C;gCAC5C,iEAAiE;gCACjE,yCAAyC;gCACzC,KAAK,MAAMoD,SAAS+Z,gBAAgBoM,GAAG,CAACvpB,SAAS,EAAE,CAAE;oCACnD,MAAMuwB,WAAWnP,IAAAA,oCAAiB,EAAChe,MAAME,QAAQ;oCACjD,MAAMqrB,iBACJ3uB,MACAoD,MAAME,QAAQ,EACditB,UACAxG,OACA,QACA;oCAEF,MAAM4E,iBACJ3uB,MACAoD,MAAME,QAAQ,EACditB,UACAxG,OACA,QACA;oCAGF,IAAIgG,QAAQ;wCACV,MAAMM,UAAU,GAAGE,SAAS,IAAI,CAAC;wCACjC,MAAM5B,iBACJ3uB,MACAqwB,SACAA,SACAtG,OACA,QACA;wCAEF,MAAM4E,iBACJ3uB,MACAqwB,SACAA,SACAtG,OACA,QACA;oCAEJ;oCAEA,MAAMY,eAAeH,gBAAgBpnB,MAAME,QAAQ;oCAEnDb,kBAAkBO,MAAM,CAACI,MAAME,QAAQ,CAAC,GAAG;wCACzCiqB,0BAA0B5C,aAAazG,UAAU;wCACjDsJ,sBAAsB7C,aAAaC,MAAM;wCACzCyC,iBAAiBliB;wCACjB8hB,eAAe9hB;wCACfjI,UAAUlD;wCACVysB,WAAW5rB,aAAI,CAAC6rB,KAAK,CAAC5rB,IAAI,CACxB,eACA4B,SACA,GAAG0e,IAAAA,oCAAiB,EAAChe,MAAME,QAAQ,EAAE,KAAK,CAAC;wCAE7C,6CAA6C;wCAC7CqpB,mBAAmBxhB;wCACnBsiB,aAAahuB;oCACf;oCAEA,IAAIuwB,UAAU;wCACZA,SAAS1K,mBAAmB,GAAGqF;oCACjC;gCACF;4BACF;wBACF;oBACF;oBAEA,iCAAiC;oBACjC,MAAM/oB,YAAE,CAAC4uB,EAAE,CAAC1mB,QAAQ;wBAAE9C,WAAW;wBAAMypB,OAAO;oBAAK;oBACnD,MAAMzuB,cAAcwX,mBAAmBiE;oBAEvC,IAAIrZ,OAAOmD,YAAY,CAACmpB,kBAAkB,EAAE;wBAC1C,KAAK,MAAMttB,SAAS;+BACfgT,eAAeG,YAAY;+BAC3BH,eAAe5S,aAAa;yBAChC,CAAE;4BACD,2DAA2D;4BAC3D,yDAAyD;4BACzD,qDAAqD;4BACrD,8DAA8D;4BAC9D,gDAAgD;4BAEhD,kEAAkE;4BAClE,kEAAkE;4BAClE,uDAAuD;4BACvD,IAAIJ,MAAMwqB,yBAAyB,EAAE;gCACnC;4BACF;4BAEAxqB,MAAMwqB,yBAAyB,GAAG;gCAChC+C,IAAAA,mEAAoC,EAClCvtB,MAAMpD,IAAI,EACV,2DAA2D;gCAC3D,2DAA2D;gCAC3D,4DAA4D;gCAC5D;6BAEH;wBACH;oBACF;gBACF;gBAEA,sEAAsE;gBACtE,sBAAsB;gBACtB,MAAMuF,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMlE,cAAckU,oBAAoBE;YAC1D;YAEA,MAAMwa,mBAAmBvU,IAAAA,gBAAa,EAAC;YACvC,IAAIwU,qBAAqBxU,IAAAA,gBAAa,EAAC,CAAC,uBAAuB,CAAC;YAEhE,wCAAwC;YACxC6B,OAAO4S,GAAG;YAEV,MAAMC,cAAcxlB,QAAQ6S,MAAM,CAACD;YACnCxQ,UAAUY,MAAM,CACdyiB,IAAAA,0BAAkB,EAACzf,YAAY;gBAC7B2J,mBAAmB6V,WAAW,CAAC,EAAE;gBACjCE,iBAAiBnrB,YAAYub,IAAI;gBACjC6P,sBAAsBtuB,SAASye,IAAI;gBACnC8P,sBAAsBjU,iBAAiBmE,IAAI;gBAC3C+P,cACE7f,WAAWjE,MAAM,GAChBxH,CAAAA,YAAYub,IAAI,GAAGze,SAASye,IAAI,GAAGnE,iBAAiBmE,IAAI,AAAD;gBAC1DgQ,cAAc3J;gBACd4J,oBACE7S,CAAAA,gCAAAA,aAAc/X,QAAQ,CAAC,uBAAsB;gBAC/C6qB,eAAetkB,iBAAiBK,MAAM;gBACtCkkB,cAAc1kB,QAAQQ,MAAM;gBAC5BmkB,gBAAgBzkB,UAAUM,MAAM,GAAG;gBACnCokB,qBAAqB5kB,QAAQ7J,MAAM,CAAC,CAAC4T,IAAW,CAAC,CAACA,EAAE6R,GAAG,EAAEpb,MAAM;gBAC/DqkB,sBAAsB1kB,iBAAiBhK,MAAM,CAAC,CAAC4T,IAAW,CAAC,CAACA,EAAE6R,GAAG,EAC9Dpb,MAAM;gBACTskB,uBAAuB5kB,UAAU/J,MAAM,CAAC,CAAC4T,IAAW,CAAC,CAACA,EAAE6R,GAAG,EAAEpb,MAAM;gBACnEukB,iBAAiBpf,oBAAoB,IAAI;gBACzCgC;gBACAiI;gBACAC;gBACAC;gBACAC;YACF;YAGF,IAAIhS,8BAAgB,CAACinB,cAAc,EAAE;gBACnC,MAAM5iB,SAAS6iB,IAAAA,8BAAsB,EACnClnB,8BAAgB,CAACinB,cAAc,CAACE,MAAM;gBAExCrkB,UAAUY,MAAM,CAACW;gBACjBvB,UAAUY,MAAM,CACd0jB,IAAAA,4CAAoC,EAClCpnB,8BAAgB,CAACinB,cAAc,CAACI,6BAA6B;gBAGjE,MAAMC,kBAAkBtnB,8BAAgB,CAACinB,cAAc,CAACK,eAAe;gBAEvE,KAAK,MAAM,CAACpU,KAAKoI,MAAM,IAAIrjB,OAAOC,OAAO,CAACovB,iBAAkB;oBAC1DxkB,UAAUY,MAAM,CACdwjB,IAAAA,8BAAsB,EAAC;wBACrB;4BACElhB,aAAakN;4BACbjN,iBAAiBqV;wBACnB;qBACD;gBAEL;YACF;YAEA,IAAIvjB,SAASye,IAAI,GAAG,KAAKrb,QAAQ;oBAmDpB5B;gBAlDXmkB,mBAAmBE,OAAO,CAAC,CAAC2J;oBAC1B,MAAM5F,kBAAkBpL,IAAAA,oCAAiB,EAACgR;oBAC1C,MAAM3F,YAAY5rB,aAAI,CAAC6rB,KAAK,CAAC5rB,IAAI,CAC/B,eACA4B,SACA,GAAG8pB,gBAAgB,KAAK,CAAC;oBAG3B/pB,kBAAkBe,aAAa,CAAC4uB,SAAS,GAAG;wBAC1CnyB,YAAYI,IAAAA,qCAAmB,EAC7BH,IAAAA,8BAAkB,EAACkyB,UAAU;4BAC3BjyB,iBAAiB;wBACnB,GAAGG,EAAE,CAACC,MAAM;wBAEd8sB,iBAAiBliB;wBACjB8hB,eAAe9hB;wBACfshB;wBACArf,UAAU2P,yBAAyB2L,GAAG,CAAC0J,YACnC,OACAtV,uBAAuB4L,GAAG,CAAC0J,YACzB,GAAG5F,gBAAgB,KAAK,CAAC,GACzB;wBACN0B,oBAAoB/iB;wBACpBgjB,gBAAgBhjB;wBAChBmjB,qBAAqBnjB;wBACrBsZ,oBAAoBtZ;wBACpBojB,gBAAgBluB,IAAAA,qCAAmB,EACjCH,IAAAA,8BAAkB,EAACusB,WAAW;4BAC5BtsB,iBAAiB;4BACjBquB,eAAe;4BACfC,8BAA8B;wBAChC,GAAGnuB,EAAE,CAACC,MAAM;wBAEd,6CAA6C;wBAC7CosB,mBAAmBxhB;wBACnBujB,wBAAwBvjB;wBACxBsiB,aAAahuB;oBACf;gBACF;gBAEAoL,8BAAgB,CAACwnB,aAAa,GAAG3f,aAAa2f,aAAa;gBAC3DxnB,8BAAgB,CAACynB,mBAAmB,GAClCluB,OAAOmD,YAAY,CAAC+qB,mBAAmB;gBACzCznB,8BAAgB,CAAC0nB,2BAA2B,GAC1CnuB,OAAOmD,YAAY,CAACgrB,2BAA2B;gBAEjD,MAAMjwB,uBAAuB3B,SAAS8B;gBACtC,MAAMD,uBAAuBC,mBAAmB;oBAC9C9B;oBACA+B;oBACAC,OAAO,GAAEyB,eAAAA,OAAO4S,IAAI,qBAAX5S,aAAazB,OAAO;gBAC/B;YACF,OAAO;gBACL,MAAML,uBAAuB3B,SAAS;oBACpCyE,SAAS;oBACTpC,QAAQ,CAAC;oBACTQ,eAAe,CAAC;oBAChB8kB,SAAS5V;oBACT2V,gBAAgB,EAAE;gBACpB;YACF;YAEA,MAAMlkB,oBAAoBxD,SAASyD;YACnC,MAAMpC,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAAS6xB,yBAAa,GAAG;gBACrDptB,SAAS;gBACTqtB,kBAAkB,OAAOruB,OAAO+kB,aAAa,KAAK;gBAClDuJ,qBAAqBtuB,OAAOuuB,aAAa,KAAK;gBAC9CjU,qBAAqBA,wBAAwB;YAC/C;YACA,MAAM9c,YAAE,CAAC2pB,MAAM,CAAC1qB,aAAI,CAACC,IAAI,CAACH,SAASiyB,yBAAa,GAAG/W,KAAK,CAAC,CAAC3L;gBACxD,IAAIA,IAAIE,IAAI,KAAK,UAAU;oBACzB,OAAOmK,QAAQpS,OAAO;gBACxB;gBACA,OAAOoS,QAAQyN,MAAM,CAAC9X;YACxB;YAEA,IAAIN,QAAQxL,OAAOmD,YAAY,CAAC2gB,iBAAiB,GAAG;gBAClD,MAAM3iB,cACHU,UAAU,CAAC,0BACXC,YAAY,CAAC;oBACZ,MAAM2sB,IAAAA,0CAAoB,EACxBvpB,KACAzI,aAAI,CAACC,IAAI,CAACH,SAASiD,oCAAwB;gBAE/C;YACJ;YAEA,MAAM+V;YAEN,IAAIkX,oBAAoB;gBACtBA,mBAAmBhL,cAAc;gBACjCgL,qBAAqB1lB;YACvB;YAEA,IAAIP,eAAe;gBACjBtJ,KAAIiL,IAAI,CACN,CAAC,yGAAyG,CAAC;YAE/G;YAEA,IAAInI,OAAOwb,MAAM,KAAK,UAAU;gBAC9B,MAAMvW,uBACJjF,QACAkF,KACAC,oBACAC,cACAjE;YAEJ;YAEA,IAAInB,OAAOmD,YAAY,CAACurB,WAAW,EAAE;gBACnC,MAAMC,IAAAA,kCAAmB,EAAC;oBACxBzpB;oBACA3I;oBACAqyB,aAAattB;oBACbE;oBACAC;oBACAitB,aAAa1uB,OAAOmD,YAAY,CAACurB,WAAW;oBAC5CttB,UAAUA,SAASY,KAAK;oBACxB4N,aAAavO;oBACb2Q;oBACA3T;oBACAkD;oBACA2Y;oBACAra,qBAAqBgiB,4BAA4B3f,KAAK;gBACxD;YACF;YAEA,IAAIlC,OAAOwb,MAAM,KAAK,cAAc;gBAClC,MAAMta,yBACJC,eACA5E,SACA6E,UACAC,sBACAC,uBACAugB,6BACAtgB,oBACAC,mBACAC,wBACAC,aACAC,gBACAC;YAEJ;YAEA,IAAI4qB,kBAAkBA,iBAAiB/K,cAAc;YACrDzkB,QAAQC,GAAG;YAEX,IAAI+I,aAAa;gBACf7E,cACGU,UAAU,CAAC,uBACX0F,OAAO,CAAC,IAAMsnB,IAAAA,yBAAiB,EAAC;wBAAEjmB;wBAAWD;wBAAUD;oBAAQ;YACpE;YAEA,MAAMvH,cAAcU,UAAU,CAAC,mBAAmBC,YAAY,CAAC,IAC7DgtB,IAAAA,qBAAa,EAAC1tB,UAAUgY,WAAW;oBACjC2V,UAAUxyB;oBACV+B,SAASA;oBACToL;oBACA4Z;oBACAtW,gBAAgBhN,OAAOgN,cAAc;oBACrCuM;oBACAD;oBACA/X;oBACA2a,UAAUlc,OAAOmD,YAAY,CAAC+Y,QAAQ;gBACxC;YAGF,MAAM/a,cACHU,UAAU,CAAC,mBACXC,YAAY,CAAC,IAAMyH,UAAUgD,KAAK;YAErC,MAAM2J;QACR;IACF,EAAE,OAAO8Y,GAAG;QACV,MAAMzlB,YAAmC0lB,oBAAY,CAAC9J,GAAG,CAAC;QAC1D,IAAI5b,WAAW;YACbA,UAAUY,MAAM,CACd+kB,IAAAA,wBAAgB,EAAC;gBACfrY,SAASc,uBAAuBtR;gBAChC8oB,WAAWC,yBAAyBJ;gBACpClY,mBAAmBvT,KAAKG,KAAK,CAAC,AAACiD,CAAAA,KAAKC,GAAG,KAAKF,cAAa,IAAK;YAChE;QAEJ;QACA,MAAMsoB;IACR,SAAU;QACR,kDAAkD;QAClD,MAAMK,yBAAoB,CAACC,GAAG;QAE9B,IAAIjpB,eAAe,CAACc,QAAQC,GAAG,CAACmoB,gBAAgB,EAAE;YAChDC,yBAAyB3oB;QAC3B;QAEA,6DAA6D;QAC7D,MAAMwB,IAAAA,qBAAc;QACpBC,IAAAA,4BAAuB;QAEvB,IAAI/B,kBAAkBM,cAAc;YAClC4oB,IAAAA,oBAAW,EAAC;gBACVlpB;gBACAmpB,MAAM;gBACN3X,YAAY7S;gBACZ3I,SAASsK,aAAatK,OAAO;gBAC7BozB,gBAAgBtpB;gBAChBupB,MAAM;YACR;QACF;IACF;AACF;AAEA,SAASvR;IACPnhB,KAAIoP,KAAK,CACP,CAAC,0MAA0M,CAAC;IAE9MnF,QAAQe,IAAI,CAAC;AACf;AAEA,SAASsnB,yBAAyBxvB,MAA2B;IAC3D,IAAI6vB,aACF,CAAC,8CAA8C,CAAC,GAChDnO,IAAAA,gBAAI,EACF,CAAC,yEAAyE,CAAC;IAE/EmO,cACE,WACAnO,IAAAA,gBAAI,EACF;IAEJmO,cACE;IAEF,IAAI,EAAC7vB,0BAAAA,OAAQmD,YAAY,CAAC2sB,0BAA0B,GAAE;QACpDD,cACE;IACJ;IAEAA,cACE;IACFA,cACE;IAEF3yB,KAAIE,IAAI,CAACyyB;AACX;AAEA,SAASlY,uBAAuBtR,WAAoB;IAClD,IAAIA,aAAa;QACf,OAAO;IACT;IAEA,IAAIc,QAAQC,GAAG,CAAC2oB,WAAW,EAAE;QAC3B,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASX,yBAAyBtjB,GAAY;IAC5C,MAAME,OAAOgkB,IAAAA,yCAAoB,EAAClkB;IAClC,IAAIE,QAAQ,MAAM;QAChB,OAAOA;IACT;IAEA,IAAIF,eAAeI,SAAS,UAAUJ,OAAO,OAAOA,IAAIE,IAAI,KAAK,UAAU;QACzE,OAAOF,IAAIE,IAAI;IACjB;IAEA,IAAIF,eAAeI,OAAO;QACxB,OAAOJ,IAAImkB,IAAI;IACjB;IAEA,OAAO;AACT", "ignoreList": [0]}