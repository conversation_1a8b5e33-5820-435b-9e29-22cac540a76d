"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-table-header@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-table-header@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-table-header@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-table-header/dist/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-table-header@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-table-header/dist/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TableHeader: () => (/* binding */ TableHeader),\n/* harmony export */   \"default\": () => (/* binding */ TableHeader)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * This extension allows you to create table headers.\n * @see https://www.tiptap.dev/api/nodes/table-header\n */\nconst TableHeader = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'tableHeader',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    content: 'block+',\n    addAttributes() {\n        return {\n            colspan: {\n                default: 1,\n            },\n            rowspan: {\n                default: 1,\n            },\n            colwidth: {\n                default: null,\n                parseHTML: element => {\n                    const colwidth = element.getAttribute('colwidth');\n                    const value = colwidth\n                        ? colwidth.split(',').map(width => parseInt(width, 10))\n                        : null;\n                    return value;\n                },\n            },\n        };\n    },\n    tableRole: 'header_cell',\n    isolating: true,\n    parseHTML() {\n        return [\n            { tag: 'th' },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['th', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-table-header@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-table-header/dist/index.js\n");

/***/ })

};
;