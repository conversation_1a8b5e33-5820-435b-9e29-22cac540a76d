"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-underline@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-underline@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-underline@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-underline/dist/index.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-underline@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-underline/dist/index.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Underline: () => (/* binding */ Underline),\n/* harmony export */   \"default\": () => (/* binding */ Underline)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * This extension allows you to create underline text.\n * @see https://www.tiptap.dev/api/marks/underline\n */\nconst Underline = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Mark.create({\n    name: 'underline',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'u',\n            },\n            {\n                style: 'text-decoration',\n                consuming: false,\n                getAttrs: style => (style.includes('underline') ? {} : false),\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['u', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setUnderline: () => ({ commands }) => {\n                return commands.setMark(this.name);\n            },\n            toggleUnderline: () => ({ commands }) => {\n                return commands.toggleMark(this.name);\n            },\n            unsetUnderline: () => ({ commands }) => {\n                return commands.unsetMark(this.name);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-u': () => this.editor.commands.toggleUnderline(),\n            'Mod-U': () => this.editor.commands.toggleUnderline(),\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-underline@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-underline/dist/index.js\n");

/***/ })

};
;