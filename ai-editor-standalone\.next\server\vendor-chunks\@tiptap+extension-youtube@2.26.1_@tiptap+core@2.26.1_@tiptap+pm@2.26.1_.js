"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-youtube@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-youtube@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-youtube@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-youtube/dist/index.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-youtube@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-youtube/dist/index.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Youtube: () => (/* binding */ Youtube),\n/* harmony export */   \"default\": () => (/* binding */ Youtube)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\nconst YOUTUBE_REGEX = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu\\.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/;\nconst YOUTUBE_REGEX_GLOBAL = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu\\.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/g;\nconst isValidYoutubeUrl = (url) => {\n    return url.match(YOUTUBE_REGEX);\n};\nconst getYoutubeEmbedUrl = (nocookie, isPlaylist) => {\n    if (isPlaylist) {\n        return 'https://www.youtube-nocookie.com/embed/videoseries?list=';\n    }\n    return nocookie ? 'https://www.youtube-nocookie.com/embed/' : 'https://www.youtube.com/embed/';\n};\nconst getYoutubeVideoOrPlaylistId = (url) => {\n    if (url.searchParams.has('v')) {\n        return { id: url.searchParams.get('v') };\n    }\n    if (url.hostname === 'youtu.be'\n        || url.pathname.includes('shorts')\n        || url.pathname.includes('live')) {\n        return { id: url.pathname.split('/').pop() };\n    }\n    if (url.searchParams.has('list')) {\n        return { id: url.searchParams.get('list'), isPlaylist: true };\n    }\n    return null;\n};\nconst getEmbedUrlFromYoutubeUrl = (options) => {\n    var _a;\n    const { url, allowFullscreen, autoplay, ccLanguage, ccLoadPolicy, controls, disableKBcontrols, enableIFrameApi, endTime, interfaceLanguage, ivLoadPolicy, loop, modestBranding, nocookie, origin, playlist, progressBarColor, startAt, rel, } = options;\n    if (!isValidYoutubeUrl(url)) {\n        return null;\n    }\n    // if is already an embed url, return it\n    if (url.includes('/embed/')) {\n        return url;\n    }\n    const urlObject = new URL(url);\n    const { id, isPlaylist } = (_a = getYoutubeVideoOrPlaylistId(urlObject)) !== null && _a !== void 0 ? _a : {};\n    if (!id) {\n        return null;\n    }\n    const embedUrl = new URL(`${getYoutubeEmbedUrl(nocookie, isPlaylist)}${id}`);\n    if (urlObject.searchParams.has('t')) {\n        embedUrl.searchParams.set('start', urlObject.searchParams.get('t').replaceAll('s', ''));\n    }\n    if (allowFullscreen === false) {\n        embedUrl.searchParams.set('fs', '0');\n    }\n    if (autoplay) {\n        embedUrl.searchParams.set('autoplay', '1');\n    }\n    if (ccLanguage) {\n        embedUrl.searchParams.set('cc_lang_pref', ccLanguage);\n    }\n    if (ccLoadPolicy) {\n        embedUrl.searchParams.set('cc_load_policy', '1');\n    }\n    if (!controls) {\n        embedUrl.searchParams.set('controls', '0');\n    }\n    if (disableKBcontrols) {\n        embedUrl.searchParams.set('disablekb', '1');\n    }\n    if (enableIFrameApi) {\n        embedUrl.searchParams.set('enablejsapi', '1');\n    }\n    if (endTime) {\n        embedUrl.searchParams.set('end', endTime.toString());\n    }\n    if (interfaceLanguage) {\n        embedUrl.searchParams.set('hl', interfaceLanguage);\n    }\n    if (ivLoadPolicy) {\n        embedUrl.searchParams.set('iv_load_policy', ivLoadPolicy.toString());\n    }\n    if (loop) {\n        embedUrl.searchParams.set('loop', '1');\n    }\n    if (modestBranding) {\n        embedUrl.searchParams.set('modestbranding', '1');\n    }\n    if (origin) {\n        embedUrl.searchParams.set('origin', origin);\n    }\n    if (playlist) {\n        embedUrl.searchParams.set('playlist', playlist);\n    }\n    if (startAt) {\n        embedUrl.searchParams.set('start', startAt.toString());\n    }\n    if (progressBarColor) {\n        embedUrl.searchParams.set('color', progressBarColor);\n    }\n    if (rel !== undefined) {\n        embedUrl.searchParams.set('rel', rel.toString());\n    }\n    return embedUrl.toString();\n};\n\n/**\n * This extension adds support for youtube videos.\n * @see https://www.tiptap.dev/api/nodes/youtube\n */\nconst Youtube = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Node.create({\n    name: 'youtube',\n    addOptions() {\n        return {\n            addPasteHandler: true,\n            allowFullscreen: true,\n            autoplay: false,\n            ccLanguage: undefined,\n            ccLoadPolicy: undefined,\n            controls: true,\n            disableKBcontrols: false,\n            enableIFrameApi: false,\n            endTime: 0,\n            height: 480,\n            interfaceLanguage: undefined,\n            ivLoadPolicy: 0,\n            loop: false,\n            modestBranding: false,\n            HTMLAttributes: {},\n            inline: false,\n            nocookie: false,\n            origin: '',\n            playlist: '',\n            progressBarColor: undefined,\n            width: 640,\n            rel: 1,\n        };\n    },\n    inline() {\n        return this.options.inline;\n    },\n    group() {\n        return this.options.inline ? 'inline' : 'block';\n    },\n    draggable: true,\n    addAttributes() {\n        return {\n            src: {\n                default: null,\n            },\n            start: {\n                default: 0,\n            },\n            width: {\n                default: this.options.width,\n            },\n            height: {\n                default: this.options.height,\n            },\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'div[data-youtube-video] iframe',\n            },\n        ];\n    },\n    addCommands() {\n        return {\n            setYoutubeVideo: (options) => ({ commands }) => {\n                if (!isValidYoutubeUrl(options.src)) {\n                    return false;\n                }\n                return commands.insertContent({\n                    type: this.name,\n                    attrs: options,\n                });\n            },\n        };\n    },\n    addPasteRules() {\n        if (!this.options.addPasteHandler) {\n            return [];\n        }\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.nodePasteRule)({\n                find: YOUTUBE_REGEX_GLOBAL,\n                type: this.type,\n                getAttributes: match => {\n                    return { src: match.input };\n                },\n            }),\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        const embedUrl = getEmbedUrlFromYoutubeUrl({\n            url: HTMLAttributes.src,\n            allowFullscreen: this.options.allowFullscreen,\n            autoplay: this.options.autoplay,\n            ccLanguage: this.options.ccLanguage,\n            ccLoadPolicy: this.options.ccLoadPolicy,\n            controls: this.options.controls,\n            disableKBcontrols: this.options.disableKBcontrols,\n            enableIFrameApi: this.options.enableIFrameApi,\n            endTime: this.options.endTime,\n            interfaceLanguage: this.options.interfaceLanguage,\n            ivLoadPolicy: this.options.ivLoadPolicy,\n            loop: this.options.loop,\n            modestBranding: this.options.modestBranding,\n            nocookie: this.options.nocookie,\n            origin: this.options.origin,\n            playlist: this.options.playlist,\n            progressBarColor: this.options.progressBarColor,\n            startAt: HTMLAttributes.start || 0,\n            rel: this.options.rel,\n        });\n        HTMLAttributes.src = embedUrl;\n        return [\n            'div',\n            { 'data-youtube-video': '' },\n            [\n                'iframe',\n                (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, {\n                    width: this.options.width,\n                    height: this.options.height,\n                    allowfullscreen: this.options.allowFullscreen,\n                    autoplay: this.options.autoplay,\n                    ccLanguage: this.options.ccLanguage,\n                    ccLoadPolicy: this.options.ccLoadPolicy,\n                    disableKBcontrols: this.options.disableKBcontrols,\n                    enableIFrameApi: this.options.enableIFrameApi,\n                    endTime: this.options.endTime,\n                    interfaceLanguage: this.options.interfaceLanguage,\n                    ivLoadPolicy: this.options.ivLoadPolicy,\n                    loop: this.options.loop,\n                    modestBranding: this.options.modestBranding,\n                    origin: this.options.origin,\n                    playlist: this.options.playlist,\n                    progressBarColor: this.options.progressBarColor,\n                    rel: this.options.rel,\n                }, HTMLAttributes),\n            ],\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRpcHRhcCtleHRlbnNpb24teW91dHViZUAyLjI2LjFfQHRpcHRhcCtjb3JlQDIuMjYuMV9AdGlwdGFwK3BtQDIuMjYuMV8vbm9kZV9tb2R1bGVzL0B0aXB0YXAvZXh0ZW5zaW9uLXlvdXR1YmUvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0U7O0FBRXBFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksc09BQXNPO0FBQ2xQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGlCQUFpQjtBQUM3QjtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MseUNBQXlDLEVBQUUsR0FBRztBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLDhDQUFJO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsNkNBQTZDLFVBQVU7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSwyREFBYTtBQUN6QjtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0IsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYjtBQUNBLEtBQUs7QUFDTCxpQkFBaUIsZ0JBQWdCO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsY0FBYywwQkFBMEI7QUFDeEM7QUFDQTtBQUNBLGdCQUFnQiw2REFBZTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsQ0FBQzs7QUFFc0M7QUFDdkMiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXGludGVsbGlnZW50X3JlcG9ydF9nZW5lcmF0b3JcXGFpLWVkaXRvci1zdGFuZGFsb25lXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAdGlwdGFwK2V4dGVuc2lvbi15b3V0dWJlQDIuMjYuMV9AdGlwdGFwK2NvcmVAMi4yNi4xX0B0aXB0YXArcG1AMi4yNi4xX1xcbm9kZV9tb2R1bGVzXFxAdGlwdGFwXFxleHRlbnNpb24teW91dHViZVxcZGlzdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTm9kZSwgbm9kZVBhc3RlUnVsZSwgbWVyZ2VBdHRyaWJ1dGVzIH0gZnJvbSAnQHRpcHRhcC9jb3JlJztcblxuY29uc3QgWU9VVFVCRV9SRUdFWCA9IC9eKCg/Omh0dHBzPzopP1xcL1xcLyk/KCg/Ond3d3xtfG11c2ljKVxcLik/KCg/OnlvdXR1YmVcXC5jb218eW91dHVcXC5iZXx5b3V0dWJlLW5vY29va2llXFwuY29tKSkoXFwvKD86W1xcdy1dK1xcP3Y9fGVtYmVkXFwvfHZcXC8pPykoW1xcdy1dKykoXFxTKyk/JC87XG5jb25zdCBZT1VUVUJFX1JFR0VYX0dMT0JBTCA9IC9eKCg/Omh0dHBzPzopP1xcL1xcLyk/KCg/Ond3d3xtfG11c2ljKVxcLik/KCg/OnlvdXR1YmVcXC5jb218eW91dHVcXC5iZXx5b3V0dWJlLW5vY29va2llXFwuY29tKSkoXFwvKD86W1xcdy1dK1xcP3Y9fGVtYmVkXFwvfHZcXC8pPykoW1xcdy1dKykoXFxTKyk/JC9nO1xuY29uc3QgaXNWYWxpZFlvdXR1YmVVcmwgPSAodXJsKSA9PiB7XG4gICAgcmV0dXJuIHVybC5tYXRjaChZT1VUVUJFX1JFR0VYKTtcbn07XG5jb25zdCBnZXRZb3V0dWJlRW1iZWRVcmwgPSAobm9jb29raWUsIGlzUGxheWxpc3QpID0+IHtcbiAgICBpZiAoaXNQbGF5bGlzdCkge1xuICAgICAgICByZXR1cm4gJ2h0dHBzOi8vd3d3LnlvdXR1YmUtbm9jb29raWUuY29tL2VtYmVkL3ZpZGVvc2VyaWVzP2xpc3Q9JztcbiAgICB9XG4gICAgcmV0dXJuIG5vY29va2llID8gJ2h0dHBzOi8vd3d3LnlvdXR1YmUtbm9jb29raWUuY29tL2VtYmVkLycgOiAnaHR0cHM6Ly93d3cueW91dHViZS5jb20vZW1iZWQvJztcbn07XG5jb25zdCBnZXRZb3V0dWJlVmlkZW9PclBsYXlsaXN0SWQgPSAodXJsKSA9PiB7XG4gICAgaWYgKHVybC5zZWFyY2hQYXJhbXMuaGFzKCd2JykpIHtcbiAgICAgICAgcmV0dXJuIHsgaWQ6IHVybC5zZWFyY2hQYXJhbXMuZ2V0KCd2JykgfTtcbiAgICB9XG4gICAgaWYgKHVybC5ob3N0bmFtZSA9PT0gJ3lvdXR1LmJlJ1xuICAgICAgICB8fCB1cmwucGF0aG5hbWUuaW5jbHVkZXMoJ3Nob3J0cycpXG4gICAgICAgIHx8IHVybC5wYXRobmFtZS5pbmNsdWRlcygnbGl2ZScpKSB7XG4gICAgICAgIHJldHVybiB7IGlkOiB1cmwucGF0aG5hbWUuc3BsaXQoJy8nKS5wb3AoKSB9O1xuICAgIH1cbiAgICBpZiAodXJsLnNlYXJjaFBhcmFtcy5oYXMoJ2xpc3QnKSkge1xuICAgICAgICByZXR1cm4geyBpZDogdXJsLnNlYXJjaFBhcmFtcy5nZXQoJ2xpc3QnKSwgaXNQbGF5bGlzdDogdHJ1ZSB9O1xuICAgIH1cbiAgICByZXR1cm4gbnVsbDtcbn07XG5jb25zdCBnZXRFbWJlZFVybEZyb21Zb3V0dWJlVXJsID0gKG9wdGlvbnMpID0+IHtcbiAgICB2YXIgX2E7XG4gICAgY29uc3QgeyB1cmwsIGFsbG93RnVsbHNjcmVlbiwgYXV0b3BsYXksIGNjTGFuZ3VhZ2UsIGNjTG9hZFBvbGljeSwgY29udHJvbHMsIGRpc2FibGVLQmNvbnRyb2xzLCBlbmFibGVJRnJhbWVBcGksIGVuZFRpbWUsIGludGVyZmFjZUxhbmd1YWdlLCBpdkxvYWRQb2xpY3ksIGxvb3AsIG1vZGVzdEJyYW5kaW5nLCBub2Nvb2tpZSwgb3JpZ2luLCBwbGF5bGlzdCwgcHJvZ3Jlc3NCYXJDb2xvciwgc3RhcnRBdCwgcmVsLCB9ID0gb3B0aW9ucztcbiAgICBpZiAoIWlzVmFsaWRZb3V0dWJlVXJsKHVybCkpIHtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIC8vIGlmIGlzIGFscmVhZHkgYW4gZW1iZWQgdXJsLCByZXR1cm4gaXRcbiAgICBpZiAodXJsLmluY2x1ZGVzKCcvZW1iZWQvJykpIHtcbiAgICAgICAgcmV0dXJuIHVybDtcbiAgICB9XG4gICAgY29uc3QgdXJsT2JqZWN0ID0gbmV3IFVSTCh1cmwpO1xuICAgIGNvbnN0IHsgaWQsIGlzUGxheWxpc3QgfSA9IChfYSA9IGdldFlvdXR1YmVWaWRlb09yUGxheWxpc3RJZCh1cmxPYmplY3QpKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiB7fTtcbiAgICBpZiAoIWlkKSB7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICBjb25zdCBlbWJlZFVybCA9IG5ldyBVUkwoYCR7Z2V0WW91dHViZUVtYmVkVXJsKG5vY29va2llLCBpc1BsYXlsaXN0KX0ke2lkfWApO1xuICAgIGlmICh1cmxPYmplY3Quc2VhcmNoUGFyYW1zLmhhcygndCcpKSB7XG4gICAgICAgIGVtYmVkVXJsLnNlYXJjaFBhcmFtcy5zZXQoJ3N0YXJ0JywgdXJsT2JqZWN0LnNlYXJjaFBhcmFtcy5nZXQoJ3QnKS5yZXBsYWNlQWxsKCdzJywgJycpKTtcbiAgICB9XG4gICAgaWYgKGFsbG93RnVsbHNjcmVlbiA9PT0gZmFsc2UpIHtcbiAgICAgICAgZW1iZWRVcmwuc2VhcmNoUGFyYW1zLnNldCgnZnMnLCAnMCcpO1xuICAgIH1cbiAgICBpZiAoYXV0b3BsYXkpIHtcbiAgICAgICAgZW1iZWRVcmwuc2VhcmNoUGFyYW1zLnNldCgnYXV0b3BsYXknLCAnMScpO1xuICAgIH1cbiAgICBpZiAoY2NMYW5ndWFnZSkge1xuICAgICAgICBlbWJlZFVybC5zZWFyY2hQYXJhbXMuc2V0KCdjY19sYW5nX3ByZWYnLCBjY0xhbmd1YWdlKTtcbiAgICB9XG4gICAgaWYgKGNjTG9hZFBvbGljeSkge1xuICAgICAgICBlbWJlZFVybC5zZWFyY2hQYXJhbXMuc2V0KCdjY19sb2FkX3BvbGljeScsICcxJyk7XG4gICAgfVxuICAgIGlmICghY29udHJvbHMpIHtcbiAgICAgICAgZW1iZWRVcmwuc2VhcmNoUGFyYW1zLnNldCgnY29udHJvbHMnLCAnMCcpO1xuICAgIH1cbiAgICBpZiAoZGlzYWJsZUtCY29udHJvbHMpIHtcbiAgICAgICAgZW1iZWRVcmwuc2VhcmNoUGFyYW1zLnNldCgnZGlzYWJsZWtiJywgJzEnKTtcbiAgICB9XG4gICAgaWYgKGVuYWJsZUlGcmFtZUFwaSkge1xuICAgICAgICBlbWJlZFVybC5zZWFyY2hQYXJhbXMuc2V0KCdlbmFibGVqc2FwaScsICcxJyk7XG4gICAgfVxuICAgIGlmIChlbmRUaW1lKSB7XG4gICAgICAgIGVtYmVkVXJsLnNlYXJjaFBhcmFtcy5zZXQoJ2VuZCcsIGVuZFRpbWUudG9TdHJpbmcoKSk7XG4gICAgfVxuICAgIGlmIChpbnRlcmZhY2VMYW5ndWFnZSkge1xuICAgICAgICBlbWJlZFVybC5zZWFyY2hQYXJhbXMuc2V0KCdobCcsIGludGVyZmFjZUxhbmd1YWdlKTtcbiAgICB9XG4gICAgaWYgKGl2TG9hZFBvbGljeSkge1xuICAgICAgICBlbWJlZFVybC5zZWFyY2hQYXJhbXMuc2V0KCdpdl9sb2FkX3BvbGljeScsIGl2TG9hZFBvbGljeS50b1N0cmluZygpKTtcbiAgICB9XG4gICAgaWYgKGxvb3ApIHtcbiAgICAgICAgZW1iZWRVcmwuc2VhcmNoUGFyYW1zLnNldCgnbG9vcCcsICcxJyk7XG4gICAgfVxuICAgIGlmIChtb2Rlc3RCcmFuZGluZykge1xuICAgICAgICBlbWJlZFVybC5zZWFyY2hQYXJhbXMuc2V0KCdtb2Rlc3RicmFuZGluZycsICcxJyk7XG4gICAgfVxuICAgIGlmIChvcmlnaW4pIHtcbiAgICAgICAgZW1iZWRVcmwuc2VhcmNoUGFyYW1zLnNldCgnb3JpZ2luJywgb3JpZ2luKTtcbiAgICB9XG4gICAgaWYgKHBsYXlsaXN0KSB7XG4gICAgICAgIGVtYmVkVXJsLnNlYXJjaFBhcmFtcy5zZXQoJ3BsYXlsaXN0JywgcGxheWxpc3QpO1xuICAgIH1cbiAgICBpZiAoc3RhcnRBdCkge1xuICAgICAgICBlbWJlZFVybC5zZWFyY2hQYXJhbXMuc2V0KCdzdGFydCcsIHN0YXJ0QXQudG9TdHJpbmcoKSk7XG4gICAgfVxuICAgIGlmIChwcm9ncmVzc0JhckNvbG9yKSB7XG4gICAgICAgIGVtYmVkVXJsLnNlYXJjaFBhcmFtcy5zZXQoJ2NvbG9yJywgcHJvZ3Jlc3NCYXJDb2xvcik7XG4gICAgfVxuICAgIGlmIChyZWwgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICBlbWJlZFVybC5zZWFyY2hQYXJhbXMuc2V0KCdyZWwnLCByZWwudG9TdHJpbmcoKSk7XG4gICAgfVxuICAgIHJldHVybiBlbWJlZFVybC50b1N0cmluZygpO1xufTtcblxuLyoqXG4gKiBUaGlzIGV4dGVuc2lvbiBhZGRzIHN1cHBvcnQgZm9yIHlvdXR1YmUgdmlkZW9zLlxuICogQHNlZSBodHRwczovL3d3dy50aXB0YXAuZGV2L2FwaS9ub2Rlcy95b3V0dWJlXG4gKi9cbmNvbnN0IFlvdXR1YmUgPSBOb2RlLmNyZWF0ZSh7XG4gICAgbmFtZTogJ3lvdXR1YmUnLFxuICAgIGFkZE9wdGlvbnMoKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBhZGRQYXN0ZUhhbmRsZXI6IHRydWUsXG4gICAgICAgICAgICBhbGxvd0Z1bGxzY3JlZW46IHRydWUsXG4gICAgICAgICAgICBhdXRvcGxheTogZmFsc2UsXG4gICAgICAgICAgICBjY0xhbmd1YWdlOiB1bmRlZmluZWQsXG4gICAgICAgICAgICBjY0xvYWRQb2xpY3k6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIGNvbnRyb2xzOiB0cnVlLFxuICAgICAgICAgICAgZGlzYWJsZUtCY29udHJvbHM6IGZhbHNlLFxuICAgICAgICAgICAgZW5hYmxlSUZyYW1lQXBpOiBmYWxzZSxcbiAgICAgICAgICAgIGVuZFRpbWU6IDAsXG4gICAgICAgICAgICBoZWlnaHQ6IDQ4MCxcbiAgICAgICAgICAgIGludGVyZmFjZUxhbmd1YWdlOiB1bmRlZmluZWQsXG4gICAgICAgICAgICBpdkxvYWRQb2xpY3k6IDAsXG4gICAgICAgICAgICBsb29wOiBmYWxzZSxcbiAgICAgICAgICAgIG1vZGVzdEJyYW5kaW5nOiBmYWxzZSxcbiAgICAgICAgICAgIEhUTUxBdHRyaWJ1dGVzOiB7fSxcbiAgICAgICAgICAgIGlubGluZTogZmFsc2UsXG4gICAgICAgICAgICBub2Nvb2tpZTogZmFsc2UsXG4gICAgICAgICAgICBvcmlnaW46ICcnLFxuICAgICAgICAgICAgcGxheWxpc3Q6ICcnLFxuICAgICAgICAgICAgcHJvZ3Jlc3NCYXJDb2xvcjogdW5kZWZpbmVkLFxuICAgICAgICAgICAgd2lkdGg6IDY0MCxcbiAgICAgICAgICAgIHJlbDogMSxcbiAgICAgICAgfTtcbiAgICB9LFxuICAgIGlubGluZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMub3B0aW9ucy5pbmxpbmU7XG4gICAgfSxcbiAgICBncm91cCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMub3B0aW9ucy5pbmxpbmUgPyAnaW5saW5lJyA6ICdibG9jayc7XG4gICAgfSxcbiAgICBkcmFnZ2FibGU6IHRydWUsXG4gICAgYWRkQXR0cmlidXRlcygpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHNyYzoge1xuICAgICAgICAgICAgICAgIGRlZmF1bHQ6IG51bGwsXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgc3RhcnQ6IHtcbiAgICAgICAgICAgICAgICBkZWZhdWx0OiAwLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHdpZHRoOiB7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDogdGhpcy5vcHRpb25zLndpZHRoLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGhlaWdodDoge1xuICAgICAgICAgICAgICAgIGRlZmF1bHQ6IHRoaXMub3B0aW9ucy5oZWlnaHQsXG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgIH0sXG4gICAgcGFyc2VIVE1MKCkge1xuICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIHRhZzogJ2RpdltkYXRhLXlvdXR1YmUtdmlkZW9dIGlmcmFtZScsXG4gICAgICAgICAgICB9LFxuICAgICAgICBdO1xuICAgIH0sXG4gICAgYWRkQ29tbWFuZHMoKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBzZXRZb3V0dWJlVmlkZW86IChvcHRpb25zKSA9PiAoeyBjb21tYW5kcyB9KSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKCFpc1ZhbGlkWW91dHViZVVybChvcHRpb25zLnNyYykpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gY29tbWFuZHMuaW5zZXJ0Q29udGVudCh7XG4gICAgICAgICAgICAgICAgICAgIHR5cGU6IHRoaXMubmFtZSxcbiAgICAgICAgICAgICAgICAgICAgYXR0cnM6IG9wdGlvbnMsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgIH0sXG4gICAgYWRkUGFzdGVSdWxlcygpIHtcbiAgICAgICAgaWYgKCF0aGlzLm9wdGlvbnMuYWRkUGFzdGVIYW5kbGVyKSB7XG4gICAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIG5vZGVQYXN0ZVJ1bGUoe1xuICAgICAgICAgICAgICAgIGZpbmQ6IFlPVVRVQkVfUkVHRVhfR0xPQkFMLFxuICAgICAgICAgICAgICAgIHR5cGU6IHRoaXMudHlwZSxcbiAgICAgICAgICAgICAgICBnZXRBdHRyaWJ1dGVzOiBtYXRjaCA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7IHNyYzogbWF0Y2guaW5wdXQgfTtcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfSksXG4gICAgICAgIF07XG4gICAgfSxcbiAgICByZW5kZXJIVE1MKHsgSFRNTEF0dHJpYnV0ZXMgfSkge1xuICAgICAgICBjb25zdCBlbWJlZFVybCA9IGdldEVtYmVkVXJsRnJvbVlvdXR1YmVVcmwoe1xuICAgICAgICAgICAgdXJsOiBIVE1MQXR0cmlidXRlcy5zcmMsXG4gICAgICAgICAgICBhbGxvd0Z1bGxzY3JlZW46IHRoaXMub3B0aW9ucy5hbGxvd0Z1bGxzY3JlZW4sXG4gICAgICAgICAgICBhdXRvcGxheTogdGhpcy5vcHRpb25zLmF1dG9wbGF5LFxuICAgICAgICAgICAgY2NMYW5ndWFnZTogdGhpcy5vcHRpb25zLmNjTGFuZ3VhZ2UsXG4gICAgICAgICAgICBjY0xvYWRQb2xpY3k6IHRoaXMub3B0aW9ucy5jY0xvYWRQb2xpY3ksXG4gICAgICAgICAgICBjb250cm9sczogdGhpcy5vcHRpb25zLmNvbnRyb2xzLFxuICAgICAgICAgICAgZGlzYWJsZUtCY29udHJvbHM6IHRoaXMub3B0aW9ucy5kaXNhYmxlS0Jjb250cm9scyxcbiAgICAgICAgICAgIGVuYWJsZUlGcmFtZUFwaTogdGhpcy5vcHRpb25zLmVuYWJsZUlGcmFtZUFwaSxcbiAgICAgICAgICAgIGVuZFRpbWU6IHRoaXMub3B0aW9ucy5lbmRUaW1lLFxuICAgICAgICAgICAgaW50ZXJmYWNlTGFuZ3VhZ2U6IHRoaXMub3B0aW9ucy5pbnRlcmZhY2VMYW5ndWFnZSxcbiAgICAgICAgICAgIGl2TG9hZFBvbGljeTogdGhpcy5vcHRpb25zLml2TG9hZFBvbGljeSxcbiAgICAgICAgICAgIGxvb3A6IHRoaXMub3B0aW9ucy5sb29wLFxuICAgICAgICAgICAgbW9kZXN0QnJhbmRpbmc6IHRoaXMub3B0aW9ucy5tb2Rlc3RCcmFuZGluZyxcbiAgICAgICAgICAgIG5vY29va2llOiB0aGlzLm9wdGlvbnMubm9jb29raWUsXG4gICAgICAgICAgICBvcmlnaW46IHRoaXMub3B0aW9ucy5vcmlnaW4sXG4gICAgICAgICAgICBwbGF5bGlzdDogdGhpcy5vcHRpb25zLnBsYXlsaXN0LFxuICAgICAgICAgICAgcHJvZ3Jlc3NCYXJDb2xvcjogdGhpcy5vcHRpb25zLnByb2dyZXNzQmFyQ29sb3IsXG4gICAgICAgICAgICBzdGFydEF0OiBIVE1MQXR0cmlidXRlcy5zdGFydCB8fCAwLFxuICAgICAgICAgICAgcmVsOiB0aGlzLm9wdGlvbnMucmVsLFxuICAgICAgICB9KTtcbiAgICAgICAgSFRNTEF0dHJpYnV0ZXMuc3JjID0gZW1iZWRVcmw7XG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICAnZGl2JyxcbiAgICAgICAgICAgIHsgJ2RhdGEteW91dHViZS12aWRlbyc6ICcnIH0sXG4gICAgICAgICAgICBbXG4gICAgICAgICAgICAgICAgJ2lmcmFtZScsXG4gICAgICAgICAgICAgICAgbWVyZ2VBdHRyaWJ1dGVzKHRoaXMub3B0aW9ucy5IVE1MQXR0cmlidXRlcywge1xuICAgICAgICAgICAgICAgICAgICB3aWR0aDogdGhpcy5vcHRpb25zLndpZHRoLFxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IHRoaXMub3B0aW9ucy5oZWlnaHQsXG4gICAgICAgICAgICAgICAgICAgIGFsbG93ZnVsbHNjcmVlbjogdGhpcy5vcHRpb25zLmFsbG93RnVsbHNjcmVlbixcbiAgICAgICAgICAgICAgICAgICAgYXV0b3BsYXk6IHRoaXMub3B0aW9ucy5hdXRvcGxheSxcbiAgICAgICAgICAgICAgICAgICAgY2NMYW5ndWFnZTogdGhpcy5vcHRpb25zLmNjTGFuZ3VhZ2UsXG4gICAgICAgICAgICAgICAgICAgIGNjTG9hZFBvbGljeTogdGhpcy5vcHRpb25zLmNjTG9hZFBvbGljeSxcbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZUtCY29udHJvbHM6IHRoaXMub3B0aW9ucy5kaXNhYmxlS0Jjb250cm9scyxcbiAgICAgICAgICAgICAgICAgICAgZW5hYmxlSUZyYW1lQXBpOiB0aGlzLm9wdGlvbnMuZW5hYmxlSUZyYW1lQXBpLFxuICAgICAgICAgICAgICAgICAgICBlbmRUaW1lOiB0aGlzLm9wdGlvbnMuZW5kVGltZSxcbiAgICAgICAgICAgICAgICAgICAgaW50ZXJmYWNlTGFuZ3VhZ2U6IHRoaXMub3B0aW9ucy5pbnRlcmZhY2VMYW5ndWFnZSxcbiAgICAgICAgICAgICAgICAgICAgaXZMb2FkUG9saWN5OiB0aGlzLm9wdGlvbnMuaXZMb2FkUG9saWN5LFxuICAgICAgICAgICAgICAgICAgICBsb29wOiB0aGlzLm9wdGlvbnMubG9vcCxcbiAgICAgICAgICAgICAgICAgICAgbW9kZXN0QnJhbmRpbmc6IHRoaXMub3B0aW9ucy5tb2Rlc3RCcmFuZGluZyxcbiAgICAgICAgICAgICAgICAgICAgb3JpZ2luOiB0aGlzLm9wdGlvbnMub3JpZ2luLFxuICAgICAgICAgICAgICAgICAgICBwbGF5bGlzdDogdGhpcy5vcHRpb25zLnBsYXlsaXN0LFxuICAgICAgICAgICAgICAgICAgICBwcm9ncmVzc0JhckNvbG9yOiB0aGlzLm9wdGlvbnMucHJvZ3Jlc3NCYXJDb2xvcixcbiAgICAgICAgICAgICAgICAgICAgcmVsOiB0aGlzLm9wdGlvbnMucmVsLFxuICAgICAgICAgICAgICAgIH0sIEhUTUxBdHRyaWJ1dGVzKSxcbiAgICAgICAgICAgIF0sXG4gICAgICAgIF07XG4gICAgfSxcbn0pO1xuXG5leHBvcnQgeyBZb3V0dWJlLCBZb3V0dWJlIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-youtube@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-youtube/dist/index.js\n");

/***/ })

};
;