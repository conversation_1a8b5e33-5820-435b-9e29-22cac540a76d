"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bold/dist/index.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bold/dist/index.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bold: () => (/* binding */ Bold),\n/* harmony export */   \"default\": () => (/* binding */ Bold),\n/* harmony export */   starInputRegex: () => (/* binding */ starInputRegex),\n/* harmony export */   starPasteRegex: () => (/* binding */ starPasteRegex),\n/* harmony export */   underscoreInputRegex: () => (/* binding */ underscoreInputRegex),\n/* harmony export */   underscorePasteRegex: () => (/* binding */ underscorePasteRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * Matches bold text via `**` as input.\n */\nconst starInputRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))$/;\n/**\n * Matches bold text via `**` while pasting.\n */\nconst starPasteRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))/g;\n/**\n * Matches bold text via `__` as input.\n */\nconst underscoreInputRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))$/;\n/**\n * Matches bold text via `__` while pasting.\n */\nconst underscorePasteRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))/g;\n/**\n * This extension allows you to mark text as bold.\n * @see https://tiptap.dev/api/marks/bold\n */\nconst Bold = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Mark.create({\n    name: 'bold',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    parseHTML() {\n        return [\n            {\n                tag: 'strong',\n            },\n            {\n                tag: 'b',\n                getAttrs: node => node.style.fontWeight !== 'normal' && null,\n            },\n            {\n                style: 'font-weight=400',\n                clearMark: mark => mark.type.name === this.name,\n            },\n            {\n                style: 'font-weight',\n                getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value) && null,\n            },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['strong', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setBold: () => ({ commands }) => {\n                return commands.setMark(this.name);\n            },\n            toggleBold: () => ({ commands }) => {\n                return commands.toggleMark(this.name);\n            },\n            unsetBold: () => ({ commands }) => {\n                return commands.unsetMark(this.name);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-b': () => this.editor.commands.toggleBold(),\n            'Mod-B': () => this.editor.commands.toggleBold(),\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markInputRule)({\n                find: starInputRegex,\n                type: this.type,\n            }),\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markInputRule)({\n                find: underscoreInputRegex,\n                type: this.type,\n            }),\n        ];\n    },\n    addPasteRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markPasteRule)({\n                find: starPasteRegex,\n                type: this.type,\n            }),\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markPasteRule)({\n                find: underscorePasteRegex,\n                type: this.type,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-bold@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-bold/dist/index.js\n");

/***/ })

};
;