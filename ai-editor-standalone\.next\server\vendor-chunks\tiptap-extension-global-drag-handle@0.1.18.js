"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tiptap-extension-global-drag-handle@0.1.18";
exports.ids = ["vendor-chunks/tiptap-extension-global-drag-handle@0.1.18"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/tiptap-extension-global-drag-handle@0.1.18/node_modules/tiptap-extension-global-drag-handle/dist/index.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/tiptap-extension-global-drag-handle@0.1.18/node_modules/tiptap-extension-global-drag-handle/dist/index.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DragHandlePlugin: () => (/* binding */ DragHandlePlugin),\n/* harmony export */   \"default\": () => (/* binding */ GlobalDragHandle)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var _tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/model */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/model/dist/index.js\");\n/* harmony import */ var _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/pm/view */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/view/dist/index.js\");\n\n\n\n\n\nfunction getPmView() {\n    try {\n        return _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_2__;\n    }\n    catch (error) {\n        return null;\n    }\n}\nfunction serializeForClipboard(view, slice) {\n    // Newer Tiptap/ProseMirror\n    // @ts-ignore\n    if (view && typeof view.serializeForClipboard === 'function') {\n        return view.serializeForClipboard(slice);\n    }\n    // Older version fallback\n    const proseMirrorView = getPmView();\n    // @ts-ignore\n    if (proseMirrorView && typeof proseMirrorView?.__serializeForClipboard === 'function') {\n        // @ts-ignore\n        return proseMirrorView.__serializeForClipboard(view, slice);\n    }\n    throw new Error('No supported clipboard serialization method found.');\n}\n\nfunction absoluteRect(node) {\n    const data = node.getBoundingClientRect();\n    const modal = node.closest('[role=\"dialog\"]');\n    if (modal && window.getComputedStyle(modal).transform !== 'none') {\n        const modalRect = modal.getBoundingClientRect();\n        return {\n            top: data.top - modalRect.top,\n            left: data.left - modalRect.left,\n            width: data.width,\n        };\n    }\n    return {\n        top: data.top,\n        left: data.left,\n        width: data.width,\n    };\n}\nfunction nodeDOMAtCoords(coords, options) {\n    const selectors = [\n        'li',\n        'p:not(:first-child)',\n        'pre',\n        'blockquote',\n        'h1',\n        'h2',\n        'h3',\n        'h4',\n        'h5',\n        'h6',\n        ...options.customNodes.map((node) => `[data-type=${node}]`),\n    ].join(', ');\n    return document\n        .elementsFromPoint(coords.x, coords.y)\n        .find((elem) => elem.parentElement?.matches?.('.ProseMirror') ||\n        elem.matches(selectors));\n}\nfunction nodePosAtDOM(node, view, options) {\n    const boundingRect = node.getBoundingClientRect();\n    return view.posAtCoords({\n        left: boundingRect.left + 50 + options.dragHandleWidth,\n        top: boundingRect.top + 1,\n    })?.inside;\n}\nfunction calcNodePos(pos, view) {\n    const $pos = view.state.doc.resolve(pos);\n    if ($pos.depth > 1)\n        return $pos.before($pos.depth);\n    return pos;\n}\nfunction DragHandlePlugin(options) {\n    let listType = '';\n    function handleDragStart(event, view) {\n        view.focus();\n        if (!event.dataTransfer)\n            return;\n        const node = nodeDOMAtCoords({\n            x: event.clientX + 50 + options.dragHandleWidth,\n            y: event.clientY,\n        }, options);\n        if (!(node instanceof Element))\n            return;\n        let draggedNodePos = nodePosAtDOM(node, view, options);\n        if (draggedNodePos == null || draggedNodePos < 0)\n            return;\n        draggedNodePos = calcNodePos(draggedNodePos, view);\n        const { from, to } = view.state.selection;\n        const diff = from - to;\n        const fromSelectionPos = calcNodePos(from, view);\n        let differentNodeSelected = false;\n        const nodePos = view.state.doc.resolve(fromSelectionPos);\n        // Check if nodePos points to the top level node\n        if (nodePos.node().type.name === 'doc')\n            differentNodeSelected = true;\n        else {\n            const nodeSelection = _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.create(view.state.doc, nodePos.before());\n            // Check if the node where the drag event started is part of the current selection\n            differentNodeSelected = !(draggedNodePos + 1 >= nodeSelection.$from.pos &&\n                draggedNodePos <= nodeSelection.$to.pos);\n        }\n        let selection = view.state.selection;\n        if (!differentNodeSelected &&\n            diff !== 0 &&\n            !(view.state.selection instanceof _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection)) {\n            const endSelection = _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.create(view.state.doc, to - 1);\n            selection = _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(view.state.doc, draggedNodePos, endSelection.$to.pos);\n        }\n        else {\n            selection = _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.create(view.state.doc, draggedNodePos);\n            // if inline node is selected, e.g mention -> go to the parent node to select the whole node\n            // if table row is selected, go to the parent node to select the whole node\n            if (selection.node.type.isInline ||\n                selection.node.type.name === 'tableRow') {\n                let $pos = view.state.doc.resolve(selection.from);\n                selection = _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.create(view.state.doc, $pos.before());\n            }\n        }\n        view.dispatch(view.state.tr.setSelection(selection));\n        // If the selected node is a list item, we need to save the type of the wrapping list e.g. OL or UL\n        if (view.state.selection instanceof _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection &&\n            view.state.selection.node.type.name === 'listItem') {\n            listType = node.parentElement.tagName;\n        }\n        const slice = view.state.selection.content();\n        const { dom, text } = serializeForClipboard(view, slice);\n        event.dataTransfer.clearData();\n        event.dataTransfer.setData('text/html', dom.innerHTML);\n        event.dataTransfer.setData('text/plain', text);\n        event.dataTransfer.effectAllowed = 'copyMove';\n        event.dataTransfer.setDragImage(node, 0, 0);\n        view.dragging = { slice, move: event.ctrlKey };\n    }\n    let dragHandleElement = null;\n    function hideDragHandle() {\n        if (dragHandleElement) {\n            dragHandleElement.classList.add('hide');\n        }\n    }\n    function showDragHandle() {\n        if (dragHandleElement) {\n            dragHandleElement.classList.remove('hide');\n        }\n    }\n    function hideHandleOnEditorOut(event) {\n        if (event.target instanceof Element) {\n            // Check if the relatedTarget class is still inside the editor\n            const relatedTarget = event.relatedTarget;\n            const isInsideEditor = relatedTarget?.classList.contains('tiptap') ||\n                relatedTarget?.classList.contains('drag-handle');\n            if (isInsideEditor)\n                return;\n        }\n        hideDragHandle();\n    }\n    return new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n        key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey(options.pluginKey),\n        view: (view) => {\n            const handleBySelector = options.dragHandleSelector\n                ? document.querySelector(options.dragHandleSelector)\n                : null;\n            dragHandleElement = handleBySelector ?? document.createElement('div');\n            dragHandleElement.draggable = true;\n            dragHandleElement.dataset.dragHandle = '';\n            dragHandleElement.classList.add('drag-handle');\n            function onDragHandleDragStart(e) {\n                handleDragStart(e, view);\n            }\n            dragHandleElement.addEventListener('dragstart', onDragHandleDragStart);\n            function onDragHandleDrag(e) {\n                hideDragHandle();\n                let scrollY = window.scrollY;\n                if (e.clientY < options.scrollTreshold) {\n                    window.scrollTo({ top: scrollY - 30, behavior: 'smooth' });\n                }\n                else if (window.innerHeight - e.clientY < options.scrollTreshold) {\n                    window.scrollTo({ top: scrollY + 30, behavior: 'smooth' });\n                }\n            }\n            dragHandleElement.addEventListener('drag', onDragHandleDrag);\n            hideDragHandle();\n            if (!handleBySelector) {\n                view?.dom?.parentElement?.appendChild(dragHandleElement);\n            }\n            view?.dom?.parentElement?.addEventListener('mouseout', hideHandleOnEditorOut);\n            return {\n                destroy: () => {\n                    if (!handleBySelector) {\n                        dragHandleElement?.remove?.();\n                    }\n                    dragHandleElement?.removeEventListener('drag', onDragHandleDrag);\n                    dragHandleElement?.removeEventListener('dragstart', onDragHandleDragStart);\n                    dragHandleElement = null;\n                    view?.dom?.parentElement?.removeEventListener('mouseout', hideHandleOnEditorOut);\n                },\n            };\n        },\n        props: {\n            handleDOMEvents: {\n                mousemove: (view, event) => {\n                    if (!view.editable) {\n                        return;\n                    }\n                    const node = nodeDOMAtCoords({\n                        x: event.clientX + 50 + options.dragHandleWidth,\n                        y: event.clientY,\n                    }, options);\n                    const notDragging = node?.closest('.not-draggable');\n                    const excludedTagList = options.excludedTags\n                        .concat(['ol', 'ul'])\n                        .join(', ');\n                    if (!(node instanceof Element) ||\n                        node.matches(excludedTagList) ||\n                        notDragging) {\n                        hideDragHandle();\n                        return;\n                    }\n                    const compStyle = window.getComputedStyle(node);\n                    const parsedLineHeight = parseInt(compStyle.lineHeight, 10);\n                    const lineHeight = isNaN(parsedLineHeight)\n                        ? parseInt(compStyle.fontSize) * 1.2\n                        : parsedLineHeight;\n                    const paddingTop = parseInt(compStyle.paddingTop, 10);\n                    const rect = absoluteRect(node);\n                    rect.top += (lineHeight - 24) / 2;\n                    rect.top += paddingTop;\n                    // Li markers\n                    if (node.matches('ul:not([data-type=taskList]) li, ol li')) {\n                        rect.left -= options.dragHandleWidth;\n                    }\n                    rect.width = options.dragHandleWidth;\n                    if (!dragHandleElement)\n                        return;\n                    dragHandleElement.style.left = `${rect.left - rect.width}px`;\n                    dragHandleElement.style.top = `${rect.top}px`;\n                    showDragHandle();\n                },\n                keydown: () => {\n                    hideDragHandle();\n                },\n                mousewheel: () => {\n                    hideDragHandle();\n                },\n                // dragging class is used for CSS\n                dragstart: (view) => {\n                    view.dom.classList.add('dragging');\n                },\n                drop: (view, event) => {\n                    view.dom.classList.remove('dragging');\n                    hideDragHandle();\n                    let droppedNode = null;\n                    const dropPos = view.posAtCoords({\n                        left: event.clientX,\n                        top: event.clientY,\n                    });\n                    if (!dropPos)\n                        return;\n                    if (view.state.selection instanceof _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection) {\n                        droppedNode = view.state.selection.node;\n                    }\n                    if (!droppedNode)\n                        return;\n                    const resolvedPos = view.state.doc.resolve(dropPos.pos);\n                    const isDroppedInsideList = resolvedPos.parent.type.name === 'listItem';\n                    // If the selected node is a list item and is not dropped inside a list, we need to wrap it inside <ol> tag otherwise ol list items will be transformed into ul list item when dropped\n                    if (view.state.selection instanceof _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection &&\n                        view.state.selection.node.type.name === 'listItem' &&\n                        !isDroppedInsideList &&\n                        listType == 'OL') {\n                        const newList = view.state.schema.nodes.orderedList?.createAndFill(null, droppedNode);\n                        const slice = new _tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.Slice(_tiptap_pm_model__WEBPACK_IMPORTED_MODULE_1__.Fragment.from(newList), 0, 0);\n                        view.dragging = { slice, move: event.ctrlKey };\n                    }\n                },\n                dragend: (view) => {\n                    view.dom.classList.remove('dragging');\n                },\n            },\n        },\n    });\n}\nconst GlobalDragHandle = _tiptap_core__WEBPACK_IMPORTED_MODULE_3__.Extension.create({\n    name: 'globalDragHandle',\n    addOptions() {\n        return {\n            dragHandleWidth: 20,\n            scrollTreshold: 100,\n            excludedTags: [],\n            customNodes: [],\n        };\n    },\n    addProseMirrorPlugins() {\n        return [\n            DragHandlePlugin({\n                pluginKey: 'globalDragHandle',\n                dragHandleWidth: this.options.dragHandleWidth,\n                scrollTreshold: this.options.scrollTreshold,\n                dragHandleSelector: this.options.dragHandleSelector,\n                excludedTags: this.options.excludedTags,\n                customNodes: this.options.customNodes,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/tiptap-extension-global-drag-handle@0.1.18/node_modules/tiptap-extension-global-drag-handle/dist/index.js\n");

/***/ })

};
;