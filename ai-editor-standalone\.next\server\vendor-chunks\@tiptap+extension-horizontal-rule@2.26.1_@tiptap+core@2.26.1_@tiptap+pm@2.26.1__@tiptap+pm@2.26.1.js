"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-horizontal-rule/dist/index.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-horizontal-rule/dist/index.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HorizontalRule: () => (/* binding */ HorizontalRule),\n/* harmony export */   \"default\": () => (/* binding */ HorizontalRule)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n\n\n\n/**\n * This extension allows you to insert horizontal rules.\n * @see https://www.tiptap.dev/api/nodes/horizontal-rule\n */\nconst HorizontalRule = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Node.create({\n    name: 'horizontalRule',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    group: 'block',\n    parseHTML() {\n        return [{ tag: 'hr' }];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['hr', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes)];\n    },\n    addCommands() {\n        return {\n            setHorizontalRule: () => ({ chain, state }) => {\n                // Check if we can insert the node at the current selection\n                if (!(0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.canInsertNode)(state, state.schema.nodes[this.name])) {\n                    return false;\n                }\n                const { selection } = state;\n                const { $from: $originFrom, $to: $originTo } = selection;\n                const currentChain = chain();\n                if ($originFrom.parentOffset === 0) {\n                    currentChain.insertContentAt({\n                        from: Math.max($originFrom.pos - 1, 0),\n                        to: $originTo.pos,\n                    }, {\n                        type: this.name,\n                    });\n                }\n                else if ((0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.isNodeSelection)(selection)) {\n                    currentChain.insertContentAt($originTo.pos, {\n                        type: this.name,\n                    });\n                }\n                else {\n                    currentChain.insertContent({ type: this.name });\n                }\n                return (currentChain\n                    // set cursor after horizontal rule\n                    .command(({ tr, dispatch }) => {\n                    var _a;\n                    if (dispatch) {\n                        const { $to } = tr.selection;\n                        const posAfter = $to.end();\n                        if ($to.nodeAfter) {\n                            if ($to.nodeAfter.isTextblock) {\n                                tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(tr.doc, $to.pos + 1));\n                            }\n                            else if ($to.nodeAfter.isBlock) {\n                                tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.NodeSelection.create(tr.doc, $to.pos));\n                            }\n                            else {\n                                tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(tr.doc, $to.pos));\n                            }\n                        }\n                        else {\n                            // add node after horizontal rule if it’s the end of the document\n                            const node = (_a = $to.parent.type.contentMatch.defaultType) === null || _a === void 0 ? void 0 : _a.create();\n                            if (node) {\n                                tr.insert(posAfter, node);\n                                tr.setSelection(_tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.TextSelection.create(tr.doc, posAfter + 1));\n                            }\n                        }\n                        tr.scrollIntoView();\n                    }\n                    return true;\n                })\n                    .run());\n            },\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.nodeInputRule)({\n                find: /^(?:---|—-|___\\s|\\*\\*\\*\\s)$/,\n                type: this.type,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-horizontal-rule@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-horizontal-rule/dist/index.js\n");

/***/ })

};
;