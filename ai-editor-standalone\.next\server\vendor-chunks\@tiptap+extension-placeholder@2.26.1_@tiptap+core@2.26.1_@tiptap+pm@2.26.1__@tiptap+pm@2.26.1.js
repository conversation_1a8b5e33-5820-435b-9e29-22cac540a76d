"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-placeholder@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-placeholder@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-placeholder@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-placeholder/dist/index.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-placeholder@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-placeholder/dist/index.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Placeholder: () => (/* binding */ Placeholder),\n/* harmony export */   \"default\": () => (/* binding */ Placeholder)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/state */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/state/dist/index.js\");\n/* harmony import */ var _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/pm/view */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/view/dist/index.js\");\n\n\n\n\n/**\n * This extension allows you to add a placeholder to your editor.\n * A placeholder is a text that appears when the editor or a node is empty.\n * @see https://www.tiptap.dev/api/extensions/placeholder\n */\nconst Placeholder = _tiptap_core__WEBPACK_IMPORTED_MODULE_2__.Extension.create({\n    name: 'placeholder',\n    addOptions() {\n        return {\n            emptyEditorClass: 'is-editor-empty',\n            emptyNodeClass: 'is-empty',\n            placeholder: 'Write something …',\n            showOnlyWhenEditable: true,\n            showOnlyCurrent: true,\n            includeChildren: false,\n        };\n    },\n    addProseMirrorPlugins() {\n        return [\n            new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.Plugin({\n                key: new _tiptap_pm_state__WEBPACK_IMPORTED_MODULE_0__.PluginKey('placeholder'),\n                props: {\n                    decorations: ({ doc, selection }) => {\n                        const active = this.editor.isEditable || !this.options.showOnlyWhenEditable;\n                        const { anchor } = selection;\n                        const decorations = [];\n                        if (!active) {\n                            return null;\n                        }\n                        const isEmptyDoc = this.editor.isEmpty;\n                        doc.descendants((node, pos) => {\n                            const hasAnchor = anchor >= pos && anchor <= pos + node.nodeSize;\n                            const isEmpty = !node.isLeaf && (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_2__.isNodeEmpty)(node);\n                            if ((hasAnchor || !this.options.showOnlyCurrent) && isEmpty) {\n                                const classes = [this.options.emptyNodeClass];\n                                if (isEmptyDoc) {\n                                    classes.push(this.options.emptyEditorClass);\n                                }\n                                const decoration = _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__.Decoration.node(pos, pos + node.nodeSize, {\n                                    class: classes.join(' '),\n                                    'data-placeholder': typeof this.options.placeholder === 'function'\n                                        ? this.options.placeholder({\n                                            editor: this.editor,\n                                            node,\n                                            pos,\n                                            hasAnchor,\n                                        })\n                                        : this.options.placeholder,\n                                });\n                                decorations.push(decoration);\n                            }\n                            return this.options.includeChildren;\n                        });\n                        return _tiptap_pm_view__WEBPACK_IMPORTED_MODULE_1__.DecorationSet.create(doc, decorations);\n                    },\n                },\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-placeholder@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-placeholder/dist/index.js\n");

/***/ })

};
;