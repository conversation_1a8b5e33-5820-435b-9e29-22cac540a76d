"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-gapcursor/dist/index.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-gapcursor/dist/index.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Gapcursor: () => (/* binding */ Gapcursor),\n/* harmony export */   \"default\": () => (/* binding */ Gapcursor)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_gapcursor__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/gapcursor */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/gapcursor/dist/index.js\");\n\n\n\n/**\n * This extension allows you to add a gap cursor to your editor.\n * A gap cursor is a cursor that appears when you click on a place\n * where no content is present, for example inbetween nodes.\n * @see https://tiptap.dev/api/extensions/gapcursor\n */\nconst Gapcursor = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'gapCursor',\n    addProseMirrorPlugins() {\n        return [\n            (0,_tiptap_pm_gapcursor__WEBPACK_IMPORTED_MODULE_0__.gapCursor)(),\n        ];\n    },\n    extendNodeSchema(extension) {\n        var _a;\n        const context = {\n            name: extension.name,\n            options: extension.options,\n            storage: extension.storage,\n        };\n        return {\n            allowGapCursor: (_a = (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.callOrReturn)((0,_tiptap_core__WEBPACK_IMPORTED_MODULE_1__.getExtensionField)(extension, 'allowGapCursor', context))) !== null && _a !== void 0 ? _a : null,\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-gapcursor@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-gapcursor/dist/index.js\n");

/***/ })

};
;