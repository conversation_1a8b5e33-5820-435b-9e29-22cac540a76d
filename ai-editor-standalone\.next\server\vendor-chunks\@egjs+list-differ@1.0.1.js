"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@egjs+list-differ@1.0.1";
exports.ids = ["vendor-chunks/@egjs+list-differ@1.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@egjs+list-differ@1.0.1/node_modules/@egjs/list-differ/dist/list-differ.esm.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@egjs+list-differ@1.0.1/node_modules/@egjs/list-differ/dist/list-differ.esm.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   diff: () => (/* binding */ diff)\n/* harmony export */ });\n/*\nCopyright (c) 2019-present NAVER Corp.\nname: @egjs/list-differ\nlicense: MIT\nauthor: NAVER Corp.\nrepository: https://github.com/naver/egjs-list-differ\nversion: 1.0.1\n*/\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\nvar PolyMap =\n/*#__PURE__*/\nfunction () {\n  function PolyMap() {\n    this.keys = [];\n    this.values = [];\n  }\n\n  var __proto = PolyMap.prototype;\n\n  __proto.get = function (key) {\n    return this.values[this.keys.indexOf(key)];\n  };\n\n  __proto.set = function (key, value) {\n    var keys = this.keys;\n    var values = this.values;\n    var prevIndex = keys.indexOf(key);\n    var index = prevIndex === -1 ? keys.length : prevIndex;\n    keys[index] = key;\n    values[index] = value;\n  };\n\n  return PolyMap;\n}();\n\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\nvar HashMap =\n/*#__PURE__*/\nfunction () {\n  function HashMap() {\n    this.object = {};\n  }\n\n  var __proto = HashMap.prototype;\n\n  __proto.get = function (key) {\n    return this.object[key];\n  };\n\n  __proto.set = function (key, value) {\n    this.object[key] = value;\n  };\n\n  return HashMap;\n}();\n\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\nvar SUPPORT_MAP = typeof Map === \"function\";\n\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\nvar Link =\n/*#__PURE__*/\nfunction () {\n  function Link() {}\n\n  var __proto = Link.prototype;\n\n  __proto.connect = function (prevLink, nextLink) {\n    this.prev = prevLink;\n    this.next = nextLink;\n    prevLink && (prevLink.next = this);\n    nextLink && (nextLink.prev = this);\n  };\n\n  __proto.disconnect = function () {\n    // In double linked list, diconnect the interconnected relationship.\n    var prevLink = this.prev;\n    var nextLink = this.next;\n    prevLink && (prevLink.next = nextLink);\n    nextLink && (nextLink.prev = prevLink);\n  };\n\n  __proto.getIndex = function () {\n    var link = this;\n    var index = -1;\n\n    while (link) {\n      link = link.prev;\n      ++index;\n    }\n\n    return index;\n  };\n\n  return Link;\n}();\n\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\n\nfunction orderChanged(changed, fixed) {\n  // It is roughly in the order of these examples.\n  // 4, 6, 0, 2, 1, 3, 5, 7\n  var fromLinks = []; // 0, 1, 2, 3, 4, 5, 6, 7\n\n  var toLinks = [];\n  changed.forEach(function (_a) {\n    var from = _a[0],\n        to = _a[1];\n    var link = new Link();\n    fromLinks[from] = link;\n    toLinks[to] = link;\n  }); // `fromLinks` are connected to each other by double linked list.\n\n  fromLinks.forEach(function (link, i) {\n    link.connect(fromLinks[i - 1]);\n  });\n  return changed.filter(function (_, i) {\n    return !fixed[i];\n  }).map(function (_a, i) {\n    var from = _a[0],\n        to = _a[1];\n\n    if (from === to) {\n      return [0, 0];\n    }\n\n    var fromLink = fromLinks[from];\n    var toLink = toLinks[to - 1];\n    var fromIndex = fromLink.getIndex(); // Disconnect the link connected to `fromLink`.\n\n    fromLink.disconnect(); // Connect `fromLink` to the right of `toLink`.\n\n    if (!toLink) {\n      fromLink.connect(undefined, fromLinks[0]);\n    } else {\n      fromLink.connect(toLink, toLink.next);\n    }\n\n    var toIndex = fromLink.getIndex();\n    return [fromIndex, toIndex];\n  });\n}\n\nvar Result =\n/*#__PURE__*/\nfunction () {\n  function Result(prevList, list, added, removed, changed, maintained, changedBeforeAdded, fixed) {\n    this.prevList = prevList;\n    this.list = list;\n    this.added = added;\n    this.removed = removed;\n    this.changed = changed;\n    this.maintained = maintained;\n    this.changedBeforeAdded = changedBeforeAdded;\n    this.fixed = fixed;\n  }\n\n  var __proto = Result.prototype;\n  Object.defineProperty(__proto, \"ordered\", {\n    get: function () {\n      if (!this.cacheOrdered) {\n        this.caculateOrdered();\n      }\n\n      return this.cacheOrdered;\n    },\n    enumerable: true,\n    configurable: true\n  });\n  Object.defineProperty(__proto, \"pureChanged\", {\n    get: function () {\n      if (!this.cachePureChanged) {\n        this.caculateOrdered();\n      }\n\n      return this.cachePureChanged;\n    },\n    enumerable: true,\n    configurable: true\n  });\n\n  __proto.caculateOrdered = function () {\n    var ordered = orderChanged(this.changedBeforeAdded, this.fixed);\n    var changed = this.changed;\n    var pureChanged = [];\n    this.cacheOrdered = ordered.filter(function (_a, i) {\n      var from = _a[0],\n          to = _a[1];\n      var _b = changed[i],\n          fromBefore = _b[0],\n          toBefore = _b[1];\n\n      if (from !== to) {\n        pureChanged.push([fromBefore, toBefore]);\n        return true;\n      }\n    });\n    this.cachePureChanged = pureChanged;\n  };\n\n  return Result;\n}();\n\n/**\n *\n * @memberof eg.ListDiffer\n * @static\n * @function\n * @param - Previous List <ko> 이전 목록 </ko>\n * @param - List to Update <ko> 업데이트 할 목록 </ko>\n * @param - This callback function returns the key of the item. <ko> 아이템의 키를 반환하는 콜백 함수입니다.</ko>\n * @return - Returns the diff between `prevList` and `list` <ko> `prevList`와 `list`의 다른 점을 반환한다.</ko>\n * @example\n * import { diff } from \"@egjs/list-differ\";\n * // script => eg.ListDiffer.diff\n * const result = diff([0, 1, 2, 3, 4, 5], [7, 8, 0, 4, 3, 6, 2, 1], e => e);\n * // List before update\n * // [1, 2, 3, 4, 5]\n * console.log(result.prevList);\n * // Updated list\n * // [4, 3, 6, 2, 1]\n * console.log(result.list);\n * // Index array of values added to `list`\n * // [0, 1, 5]\n * console.log(result.added);\n * // Index array of values removed in `prevList`\n * // [5]\n * console.log(result.removed);\n * // An array of index pairs of `prevList` and `list` with different indexes from `prevList` and `list`\n * // [[0, 2], [4, 3], [3, 4], [2, 6], [1, 7]]\n * console.log(result.changed);\n * // The subset of `changed` and an array of index pairs that moved data directly. Indicate an array of absolute index pairs of `ordered`.(Formatted by: Array<[index of prevList, index of list]>)\n * // [[4, 3], [3, 4], [2, 6]]\n * console.log(result.pureChanged);\n * // An array of index pairs to be `ordered` that can synchronize `list` before adding data. (Formatted by: Array<[prevIndex, nextIndex]>)\n * // [[4, 1], [4, 2], [4, 3]]\n * console.log(result.ordered);\n * // An array of index pairs of `prevList` and `list` that have not been added/removed so data is preserved\n * // [[0, 2], [4, 3], [3, 4], [2, 6], [1, 7]]\n * console.log(result.maintained);\n */\n\nfunction diff(prevList, list, findKeyCallback) {\n  var mapClass = SUPPORT_MAP ? Map : findKeyCallback ? HashMap : PolyMap;\n\n  var callback = findKeyCallback || function (e) {\n    return e;\n  };\n\n  var added = [];\n  var removed = [];\n  var maintained = [];\n  var prevKeys = prevList.map(callback);\n  var keys = list.map(callback);\n  var prevKeyMap = new mapClass();\n  var keyMap = new mapClass();\n  var changedBeforeAdded = [];\n  var fixed = [];\n  var removedMap = {};\n  var changed = [];\n  var addedCount = 0;\n  var removedCount = 0; // Add prevKeys and keys to the hashmap.\n\n  prevKeys.forEach(function (key, prevListIndex) {\n    prevKeyMap.set(key, prevListIndex);\n  });\n  keys.forEach(function (key, listIndex) {\n    keyMap.set(key, listIndex);\n  }); // Compare `prevKeys` and `keys` and add them to `removed` if they are not in `keys`.\n\n  prevKeys.forEach(function (key, prevListIndex) {\n    var listIndex = keyMap.get(key); // In prevList, but not in list, it is removed.\n\n    if (typeof listIndex === \"undefined\") {\n      ++removedCount;\n      removed.push(prevListIndex);\n    } else {\n      removedMap[listIndex] = removedCount;\n    }\n  }); // Compare `prevKeys` and `keys` and add them to `added` if they are not in `prevKeys`.\n\n  keys.forEach(function (key, listIndex) {\n    var prevListIndex = prevKeyMap.get(key); // In list, but not in prevList, it is added.\n\n    if (typeof prevListIndex === \"undefined\") {\n      added.push(listIndex);\n      ++addedCount;\n    } else {\n      maintained.push([prevListIndex, listIndex]);\n      removedCount = removedMap[listIndex] || 0;\n      changedBeforeAdded.push([prevListIndex - removedCount, listIndex - addedCount]);\n      fixed.push(listIndex === prevListIndex);\n\n      if (prevListIndex !== listIndex) {\n        changed.push([prevListIndex, listIndex]);\n      }\n    }\n  }); // Sort by ascending order of 'to(list's index).\n\n  removed.reverse();\n  return new Result(prevList, list, added, removed, changed, maintained, changedBeforeAdded, fixed);\n}\n\n/**\n * A module that checks diff when values are added, removed, or changed in an array.\n * @ko 배열 또는 오브젝트에서 값이 추가되거나 삭제되거나 순서가 변경사항을 체크하는 모듈입니다.\n * @memberof eg\n */\n\nvar ListDiffer =\n/*#__PURE__*/\nfunction () {\n  /**\n   * @param - Initializing Data Array. <ko> 초기 설정할 데이터 배열.</ko>\n   * @param - This callback function returns the key of the item. <ko> 아이템의 키를 반환하는 콜백 함수입니다.</ko>\n   * @example\n   * import ListDiffer from \"@egjs/list-differ\";\n   * // script => eg.ListDiffer\n   * const differ = new ListDiffer([0, 1, 2, 3, 4, 5], e => e);\n   * const result = differ.update([7, 8, 0, 4, 3, 6, 2, 1]);\n   * // List before update\n   * // [1, 2, 3, 4, 5]\n   * console.log(result.prevList);\n   * // Updated list\n   * // [4, 3, 6, 2, 1]\n   * console.log(result.list);\n   * // Index array of values added to `list`.\n   * // [0, 1, 5]\n   * console.log(result.added);\n   * // Index array of values removed in `prevList`.\n   * // [5]\n   * console.log(result.removed);\n   * // An array of index pairs of `prevList` and `list` with different indexes from `prevList` and `list`.\n   * // [[0, 2], [4, 3], [3, 4], [2, 6], [1, 7]]\n   * console.log(result.changed);\n   * // The subset of `changed` and an array of index pairs that moved data directly. Indicate an array of absolute index pairs of `ordered`.(Formatted by: Array<[index of prevList, index of list]>)\n   * // [[4, 3], [3, 4], [2, 6]]\n   * console.log(result.pureChanged);\n   * // An array of index pairs to be `ordered` that can synchronize `list` before adding data. (Formatted by: Array<[prevIndex, nextIndex]>)\n   * // [[4, 1], [4, 2], [4, 3]]\n   * console.log(result.ordered);\n   * // An array of index pairs of `prevList` and `list` that have not been added/removed so data is preserved.\n   * // [[0, 2], [4, 3], [3, 4], [2, 6], [1, 7]]\n   * console.log(result.maintained);\n   */\n  function ListDiffer(list, findKeyCallback) {\n    if (list === void 0) {\n      list = [];\n    }\n\n    this.findKeyCallback = findKeyCallback;\n    this.list = [].slice.call(list);\n  }\n  /**\n   * Update list.\n   * @ko 리스트를 업데이트를 합니다.\n   * @param - List to update <ko> 업데이트할 리스트 </ko>\n   * @return - Returns the results of an update from `prevList` to `list`.<ko> `prevList`에서 `list`로 업데이트한 결과를 반환한다. </ko>\n   */\n\n\n  var __proto = ListDiffer.prototype;\n\n  __proto.update = function (list) {\n    var newData = [].slice.call(list);\n    var result = diff(this.list, newData, this.findKeyCallback);\n    this.list = newData;\n    return result;\n  };\n\n  return ListDiffer;\n}();\n\n/*\negjs-list-differ\nCopyright (c) 2019-present NAVER Corp.\nMIT license\n*/\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ListDiffer);\n\n//# sourceMappingURL=list-differ.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@egjs+list-differ@1.0.1/node_modules/@egjs/list-differ/dist/list-differ.esm.js\n");

/***/ })

};
;