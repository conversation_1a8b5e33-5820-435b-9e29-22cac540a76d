"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1";
exports.ids = ["vendor-chunks/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-history/dist/index.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-history/dist/index.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   History: () => (/* binding */ History),\n/* harmony export */   \"default\": () => (/* binding */ History)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n/* harmony import */ var _tiptap_pm_history__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/pm/history */ \"(ssr)/./node_modules/.pnpm/@tiptap+pm@2.26.1/node_modules/@tiptap/pm/history/dist/index.js\");\n\n\n\n/**\n * This extension allows you to undo and redo recent changes.\n * @see https://www.tiptap.dev/api/extensions/history\n *\n * **Important**: If the `@tiptap/extension-collaboration` package is used, make sure to remove\n * the `history` extension, as it is not compatible with the `collaboration` extension.\n *\n * `@tiptap/extension-collaboration` uses its own history implementation.\n */\nconst History = _tiptap_core__WEBPACK_IMPORTED_MODULE_1__.Extension.create({\n    name: 'history',\n    addOptions() {\n        return {\n            depth: 100,\n            newGroupDelay: 500,\n        };\n    },\n    addCommands() {\n        return {\n            undo: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_history__WEBPACK_IMPORTED_MODULE_0__.undo)(state, dispatch);\n            },\n            redo: () => ({ state, dispatch }) => {\n                return (0,_tiptap_pm_history__WEBPACK_IMPORTED_MODULE_0__.redo)(state, dispatch);\n            },\n        };\n    },\n    addProseMirrorPlugins() {\n        return [\n            (0,_tiptap_pm_history__WEBPACK_IMPORTED_MODULE_0__.history)(this.options),\n        ];\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-z': () => this.editor.commands.undo(),\n            'Shift-Mod-z': () => this.editor.commands.redo(),\n            'Mod-y': () => this.editor.commands.redo(),\n            // Russian keyboard layouts\n            'Mod-я': () => this.editor.commands.undo(),\n            'Shift-Mod-я': () => this.editor.commands.redo(),\n        };\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-history@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1__@tiptap+pm@2.26.1/node_modules/@tiptap/extension-history/dist/index.js\n");

/***/ })

};
;