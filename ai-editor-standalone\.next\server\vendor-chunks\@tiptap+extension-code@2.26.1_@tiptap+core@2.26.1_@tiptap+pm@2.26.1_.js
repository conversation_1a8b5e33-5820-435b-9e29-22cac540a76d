"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_";
exports.ids = ["vendor-chunks/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-code/dist/index.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-code/dist/index.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Code: () => (/* binding */ Code),\n/* harmony export */   \"default\": () => (/* binding */ Code),\n/* harmony export */   inputRegex: () => (/* binding */ inputRegex),\n/* harmony export */   pasteRegex: () => (/* binding */ pasteRegex)\n/* harmony export */ });\n/* harmony import */ var _tiptap_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tiptap/core */ \"(ssr)/./node_modules/.pnpm/@tiptap+core@2.26.1_@tiptap+pm@2.26.1/node_modules/@tiptap/core/dist/index.js\");\n\n\n/**\n * Regular expressions to match inline code blocks enclosed in backticks.\n *  It matches:\n *     - An opening backtick, followed by\n *     - Any text that doesn't include a backtick (captured for marking), followed by\n *     - A closing backtick.\n *  This ensures that any text between backticks is formatted as code,\n *  regardless of the surrounding characters (exception being another backtick).\n */\nconst inputRegex = /(^|[^`])`([^`]+)`(?!`)/;\n/**\n * Matches inline code while pasting.\n */\nconst pasteRegex = /(^|[^`])`([^`]+)`(?!`)/g;\n/**\n * This extension allows you to mark text as inline code.\n * @see https://tiptap.dev/api/marks/code\n */\nconst Code = _tiptap_core__WEBPACK_IMPORTED_MODULE_0__.Mark.create({\n    name: 'code',\n    addOptions() {\n        return {\n            HTMLAttributes: {},\n        };\n    },\n    excludes: '_',\n    code: true,\n    exitable: true,\n    parseHTML() {\n        return [\n            { tag: 'code' },\n        ];\n    },\n    renderHTML({ HTMLAttributes }) {\n        return ['code', (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.mergeAttributes)(this.options.HTMLAttributes, HTMLAttributes), 0];\n    },\n    addCommands() {\n        return {\n            setCode: () => ({ commands }) => {\n                return commands.setMark(this.name);\n            },\n            toggleCode: () => ({ commands }) => {\n                return commands.toggleMark(this.name);\n            },\n            unsetCode: () => ({ commands }) => {\n                return commands.unsetMark(this.name);\n            },\n        };\n    },\n    addKeyboardShortcuts() {\n        return {\n            'Mod-e': () => this.editor.commands.toggleCode(),\n        };\n    },\n    addInputRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markInputRule)({\n                find: inputRegex,\n                type: this.type,\n            }),\n        ];\n    },\n    addPasteRules() {\n        return [\n            (0,_tiptap_core__WEBPACK_IMPORTED_MODULE_0__.markPasteRule)({\n                find: pasteRegex,\n                type: this.type,\n            }),\n        ];\n    },\n});\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tiptap+extension-code@2.26.1_@tiptap+core@2.26.1_@tiptap+pm@2.26.1_/node_modules/@tiptap/extension-code/dist/index.js\n");

/***/ })

};
;